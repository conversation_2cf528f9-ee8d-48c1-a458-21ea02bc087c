<template>
    <z-paging ref="paging" class="container">
        <view class="main">
            <view class="top">
                <view style="font-size: 72rpx; margin-bottom: 12rpx">
                    <text style="font-size: 36rpx;">¥</text>
                    {{ state.payAmountSum }}
                </view>
                <view style="font-size: 24rpx; display: flex; align-items: center">
                    支付剩余时间
                    <uni-countdown :show-day="false" :minute="state.minute" :second="state.second" @timeup="timeup" />
                </view>
            </view>
            <view class="msg_box">
                <text class="l_box">订单信息</text>
                <text style="word-break: break-all">{{ state.title }}</text>
            </view>
            <radio-group v-if="state.list.length">
                <label class="group_box" v-for="item in state.list" :key="item.value" @click="radioChange(item)">
                    <view>
                        <radio color="#00d190" :value="item.configTypeId" :checked="item.checked" style="pointer-events: none" />
                    </view>
                    <view class="r_box">
                        <image style="width: 40rpx; height: 40rpx; margin-right: 15rpx" :src="iconType[item.payMethodId]"></image>
                        {{ item.payMethodName }}
                    </view>
                </label>
            </radio-group>
            <view v-else class="empty_msg">暂未配置收款信息，请联系老师</view>
        </view>
        <template #bottom>
            <view class="footer">
                <button type="default" :class="['btn', state.disabled || state.isOnPay ? 'disabled' : '', state.list.length == 0 ? 'disabled' : '']" :disabled="state.disabled || state.isOnPay || state.list.length == 0" @click="confirm">确定支付</button>
            </view>
        </template>
    </z-paging>
</template>
<script setup>
import dayjs from "dayjs"
import { checkPlatform } from "@/utils/sendAppEvent.js"

const { system, user } = store()
console.log(system, "systemsystemsystem")

const openid = computed(() => system.apps.canteenMachine.openId)
const platForm = checkPlatform()

const { VITE_BASE_WEBAPP } = import.meta.env

const state = reactive({
    payMode: "",
    totalMoney: 0,
    list: [],
    current: null,
    minute: 59,
    second: 59,
    disabled: true,
    isOnPay: false,
    studentId: null
})

onLoad((options) => {
    console.log("options:", options)
    Object.keys(options).forEach((key) => {
        state[key] = decodeURIComponent(options[key])
    })
    state.studentId = options.studentId
    prepay()
    getPaymentItem(state.merchantId, state.payType)
})

const iconType = {
    1: "@nginx/workbench/canteenMachine/weixin.png"
}

// 倒计时时间到
const timeup = () => {
    uni.showToast({
        title: "支付时间已到",
        icon: "none",
        duration: 2000
    })
    state.disabled = true
}

// 当前时间
const date = dayjs().format("YYYY-MM-DD HH:mm:ss")

const radioChange = (item) => {
    state.list.forEach((i) => (i.checked = false))
    item.checked = true
    state.configTypeId = item.configTypeId
    state.payMethodId = item.payMethodId
    state.disabled = false
}

// 校易付支付下单
function prepay() {
    const params = {
        orderNo: state.orderNo,
        title: state.title
    }
    http.post("/app/canteen/pay/prepay", params)
        .then((res) => {
            Object.assign(state, res.data)
            const payEndTime = dayjs(res.data.payEndTime)
            const time = dayjs(payEndTime.diff(date)).format("mm ss").split(" ")
            state.minute = parseInt(time[0])
            state.second = parseInt(time[1])
        })
        .catch((e) => {
            state.isOnPay = true
        })
}

// 获取缴费支付方式
const getPaymentItem = (merchantId, payType) => {
    http.post("/campuspay/mobile/general-pay-center/payMethodList", { merchantId, payType: payType == "undefined" ? null : payType }).then((res) => {
        state.list = res.data
    })
}

// 确认支付
const confirm = () => {
    if (!state.configTypeId) {
        uni.showToast({
            title: "请选择缴费方式",
            icon: "none",
            duration: 2000
        })
        return
    }
    // 处理嵌套钉钉
    if (platForm != "wx-miniprogram") {
        // 生成支付链接
        const currentUrl = `${VITE_BASE_WEBAPP}/#/canteenMachine`
        // 可以使用uni.setClipboardData复制链接到剪贴板
        uni.setClipboardData({
            data: currentUrl,
            success: () => {
                uni.showModal({
                    title: "提示",
                    content: "暂不支持支付，链接已复制请在微信中打开进行支付",
                    showCancel: false,
                    confirmColor: "#00D190"
                })
            }
        })
        return
    }
    state.disabled = true

    //#ifdef H5
    // 公众号支付
    publicPayment()
    //#endif

    //#ifdef MP-WEIXIN
    // 小程序支付
    // MiniPayment()
    //#endif
}

function onBridgeReady(params) {
    window.WeixinJSBridge.invoke("getBrandWCPayRequest", params, function (res) {
        if (res.err_msg == "get_brand_wcpay_request:ok") {
            console.log("支付成功")
            myIframe.value.postMessage({ result: "success" }, "*")
            setTimeout(() => {
                window.location.href = VITE_BASE_URL
            }, 600)
        } else {
            console.log("支付失败")
            myIframe.value.postMessage({ result: "error" }, "*")
        }
    })
}

// 公众号支付
const publicPayment = () => {
    const params = {
        tradeNo: state.tradeNo,
        recipientDataIds: state.recipientDataIds,
        payAmountSum: state.payAmountSum,
        configTypeId: state.configTypeId,
        payMethodId: state.payMethodId,
        payType: 1,
        paySource: "公众号",
        openid: openid
    }

    http.post("/campuspay/mobile/general-pay-center/paySubmit", params)
        .then((res) => {
            res.data.resultInfo.jsApiResult.package = res.data.resultInfo.jsApiResult.packageStr
            const data = res.data.resultInfo.jsApiResult

            // 微信公众号支付
            onBridgeReady(data)
            // window.parent.postMessage({ action: "ready", data }, "*")
        })
        .finally(() => {
            state.disabled = false
        })
}

//#ifdef H5
// 监听公众号支付状态
window.addEventListener("message", function (event) {
    if (event.data.result === "success") {
        uni.showToast({
            title: "订单支付成功",
            icon: "none",
            duration: 1000
        })
        // setTimeout(()=>{
        //   uni.reLaunch({
        //     url: '/pages/index/index',
        //   })
        // },1000)
    } else {
        uni.showToast({
            title: "订单支付失败",
            icon: "none",
            duration: 1000
        })
    }
})
//#endif

// 小程序支付
// const MiniPayment = () => {
//   const params = {
//     studentId: store.studentId,
//     tradeNo: state.tradeNo,
//     recipientDataIds: state.recipientDataIds,
//     payAmountSum: state.payAmountSum,
//     configTypeId: state.configTypeId,
//     payMethodId: state.payMethodId,
//     payType: 5,
//     paySource: '小程序',
//   }
//   wx.login({
//     success(res) {
//       const data = {
//         jsCode: res.code,
//         grant_type: 'authorization_code',
//       }
//       http.post('/campuspay/mobile/wechat/xcx/getOpenId', data).then(res => {
//         params.openId = res.data.openid
//         // 下单提交
//         http.post('/campuspay/mobile/payment-center/feePaySubmit', params).then(res => {
//           console.log('res:', res)
//           payMiniProgram(res.data.resultInfo.jsApiResult)
//         })
//       })
//     },
//     fail(err) {
//       console.log('err:', err)
//     },
//   })
// }

// 唤起小程序支付
// const payMiniProgram = params => {
//   params.package = params.packageStr
//   wx.requestPayment({
//     ...params,
//     success: function (res) {
//       uni.showToast({
//         title: '订单支付成功',
//         duration: 2000,
//       })
//       uni.reLaunch({
//         url: '/pages/index/index',
//       })
//       // navigateTo({
//       //   url: '/pages/index/index',
//       // })
//       console.log('success:', res)
//     },
//     fail: function (res) {
//       console.log('fail:', res)
//       uni.showToast({
//         title: '订单支付失败',
//         icon: 'none',
//         duration: 2000,
//       })
//       state.disabled = false
//     },
//     complete: function (res) {
//       console.log('complete:', res)
//     },
//   })
// }
</script>
<style scoped lang="scss">
.container {
    background: #f9faf9;

    .main {
        overflow-y: scroll;

        .top {
            height: 280rpx;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            background: #f9faf9;
        }

        .msg_box {
            min-height: 120rpx;
            background-color: #fff;
            display: flex;
            font-size: 30rpx;
            color: #333;
            align-items: center;
            justify-content: space-between;
            border-radius: 12rpx;
            box-sizing: border-box;
            margin: 24rpx 32rpx;
            padding: 0 28rpx;

            .l_box {
                width: 200rpx;
                color: #666;
                flex-shrink: 0;
            }
        }

        .empty_msg {
            font-size: 28rpx;
            color: var(--primary-color);
            text-align: center;
            padding-top: 60rpx;
        }

        .group_box {
            display: flex;
            margin: 30rpx;
            padding: 0 30rpx;
            flex-direction: row-reverse;
            justify-content: space-between;
            color: #666;
            height: 120rpx;
            background: #ffffff;
            border-radius: 12rpx;
            align-items: center;

            .r_box {
                display: flex;
                align-items: center;

                .icon_logo {
                    margin-right: 24rpx;
                }
            }
        }

        .left_box {
            height: 100%;
            display: flex;
            align-items: center;
        }
    }

    .footer {
        padding: 24rpx 30rpx 60rpx 30rpx;
        display: flex;
        align-items: center;
        background-color: #fff;

        .btn {
            width: 690rpx;
            height: 92rpx;
            line-height: 92rpx;
            background: #00d190;
            border-radius: 46rpx;
            color: #fff;
            font-size: 32rpx;
        }

        .disabled {
            background-color: #e3e3e3;
            color: #fff;

            &:after {
                border: none;
            }
        }
    }
}
</style>
