<template>
    <z-paging ref="paging" v-model="dataList" @query="queryList">
        <view class="container_box">
            <view class="main">
                <view class="main_box" :key="idx" v-for="(item, idx) in dataList" @click="handleDetail(item)">
                    <img class="img" :src="item.iconUrl" />
                    <view class="right">
                        <view class="top_box">
                            <view class="l">
                                <view class="text_top">{{ item.name }}</view>
                                <view class="text_btm">{{ item.memberCount }}人</view>
                            </view>
                            <view class="r">
                                <button class="btn_cls" v-if="item.memberStatus === 'none'" style="background: var(--primary-color)" @click.stop="handleJoin(item)"><uni-icons type="plusempty" size="16" color="#fff"></uni-icons> 加入</button>
                                <button @click.stop class="btn_cls" v-else-if="item.memberStatus === 'requested'" style="background: #ffad3c">审核中</button>
                                <view class="text" v-else>我加入的社团</view>
                            </view>
                        </view>
                        <view class="btm_box"> {{ item.slogan }} </view>
                    </view>
                </view>
            </view>
        </view>

        <uni-popup ref="alertDialog" type="dialog">
            <uni-popup-dialog class="dialog_box" type="info" cancelText="取消" confirmText="确定" content="确定要申请加入本社团吗?" @confirm="dialogConfirm" @close="dialogClose"> </uni-popup-dialog>
        </uni-popup>
    </z-paging>
</template>
<script setup>
const paging = ref(null)
const queryList = (pageNo, pageSize) => {
    const params = {
        pageNo,
        pageSize
    }
    http.post("/app/club/club/page", params)
        .then((res) => {
            paging.value.complete(res.data.list)
        })
        .catch((err) => {
            paging.value.complete(false)
        })
}

const dataList = ref([])

const alertDialog = ref(null)
// 暂存
let stash = {}
const handleJoin = (item) => {
    stash = item
    alertDialog.value.open()
}

// 弹窗确认
const dialogConfirm = () => {
    http.post("/app/club/join-request/create", { clubId: stash.id }).then((res) => {
        paging.value.reload()
        alertDialog.value.close()
    })
}

// 弹窗关闭
const dialogClose = () => {
    stash = {}
    alertDialog.value.close()
}

const handleDetail = (item) => {
    navigateTo({
        url: "/apps/clubManage/groupDetail/index",
        query: { id: item.id }
    })
}
</script>
<style lang="scss" scoped>
.container_box {
    padding-top: 20rpx;
    background-color: #f9faf9;
    .main {
        padding: 0 30rpx;
        background-color: $uni-text-color-inverse;
        .main_box {
            display: flex;
            padding-top: 40rpx;
            position: relative;
            .flag {
                font-size: 16rpx;
                color: $uni-text-color-inverse;
                position: absolute;
                width: 24rpx;
                height: 24rpx;
                text-align: center;
                border-radius: 12rpx 0 12rpx 0;
            }
            &:last-of-type {
                .btm_box {
                    border-bottom: none !important;
                }
            }
            .img {
                width: 100rpx;
                height: 100rpx;
                border-radius: 12rpx;
                overflow: hidden;
                margin-right: 16rpx;
                flex-shrink: 0;
            }
            .right {
                flex: 1;
                overflow: hidden;
                .top_box {
                    display: flex;
                    align-items: center;
                    height: 100rpx;
                    .l {
                        flex: 1;
                        overflow: hidden;
                        .text_top {
                            font-size: 32rpx;
                            color: #262626;
                            font-weight: 600;
                            margin-bottom: 8rpx;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                            overflow: hidden;
                        }
                        .text_btm {
                            font-size: 24rpx;
                            color: #999;
                        }
                    }
                    .r {
                        .btn_cls {
                            font-size: 28rpx;
                            color: $uni-text-color-inverse;
                            width: 132rpx;
                            height: 60rpx;
                            line-height: 60rpx;
                            padding: 0;
                            text-align: center;
                            &:after {
                                border: none;
                            }
                        }
                        .text {
                            font-size: 24rpx;
                            color: #999;
                        }
                    }
                }
                .btm_box {
                    font-size: 28rpx;
                    color: #595959;
                    border-bottom: 2rpx solid #e1e1e1;
                    padding-bottom: 32rpx;
                    line-height: 40rpx;
                    // text-overflow: ellipsis;
                    // white-space: normal;
                    // overflow: hidden;
                    // display: -webkit-box;
                    // -webkit-line-clamp: 2;
                    // -webkit-box-orient: vertical;
                }
            }
        }
    }
    .dialog_box {
        :deep(.uni-dialog-title) {
            display: none;
        }
        :deep(.uni-dialog-content) {
            padding-top: 88rpx;
            padding-bottom: 88rpx;
        }
        :deep(.uni-button-color) {
            color: var(--primary-color) !important;
        }
    }
}
</style>
