<template>
    <view>
        <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="routerBack" title="社团管理" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"></uni-nav-bar>
        <view class="container_box">
            <view class="header">
                <uni-easyinput class="search_box" prefixIcon="search" placeholder="搜索社团名称" @focus="toSearchGroup" primaryColor="var(--primary-color)"></uni-easyinput>
            </view>
            <view class="top">
                <view class="item" :key="index" v-for="(item, index) in state.groupList" :style="item.style" @click="handleClick(item.code)">
                    <img class="img" :src="item.imgSrc" :style="{ left: index === 2 ? '30rpx' : '40rpx' }" />
                    <view>{{ item.title }}</view>
                </view>
            </view>
            <view class="main">
                <view class="top">
                    <text>热门社团</text>
                    <text class="right" @click="viewMore">更多<uni-icons type="right" size="16" color="#8C8C8C"></uni-icons> </text>
                </view>
                <view class="main_box">
                    <view class="item_box" :key="index" v-for="(item, index) in state.list" @click="handleDetail(item)">
                        <img class="img" :src="item.iconUrl" />
                        <view class="r_box">
                            <view class="t">{{ item.name }}</view>
                            <view class="btm">{{ item.memberCount }}人</view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>
<script setup>
const state = reactive({
    groupList: [
        {
            title: "社团申请",
            code: "apply",
            imgSrc: "@nginx/workbench/groupManage/shenqing.png",
            style: { background: "linear-gradient( 180deg, #AFE9FF 0%, #EBF7FF 100%)" }
        },
        {
            title: "社团排行",
            code: "ranking",
            imgSrc: "@nginx/workbench/groupManage/paihang.png",
            style: { background: "linear-gradient( 180deg, #FFEAAF 0%, #FFF5DD 100%)" }
        },
        {
            title: "我的社团",
            code: "group",
            imgSrc: "@nginx/workbench/groupManage/shetuan.png",
            style: { background: "linear-gradient( 180deg, #CDFFA0 0%, #F0FAE4 100%)" }
        }
    ],
    list: []
})

http.post("/app/club/club/popular", { limit: 16 }).then((res) => {
    state.list = res.data
})

const codePath = {
    apply: "/apps/clubManage/applyGroup/index",
    ranking: "/apps/clubManage/ranking/index",
    group: "/apps/clubManage/myGroup/index"
}
// 头部点击
const handleClick = (code) => {
    navigateTo({
        url: codePath[code]
    })
}

// 更多
const viewMore = () => {
    navigateTo({
        url: "/apps/clubManage/allGroup/index"
    })
}

const handleDetail = (item) => {
    navigateTo({
        url: "/apps/clubManage/groupDetail/index",
        query: { id: item.id }
    })
}

const toSearchGroup = () => {
    uni.navigateTo({
        url: "/apps/clubManage/searchGroup/index"
    })
}
</script>
<style lang="scss" scoped>
.container_box {
    .header {
        padding: 30rpx;
        background-color: $uni-text-color-inverse;
        margin-bottom: 20rpx;
        .search_box {
            :deep(.is-input-border) {
                border: none;
                background-color: $uni-bg-color-grey !important;
                border-radius: 20px;
            }
        }
    }
    .top {
        padding: 20rpx;
        padding-top: 62rpx;
        background-color: $uni-text-color-inverse;
        display: flex;
        justify-content: space-around;
        margin-bottom: 20rpx;
        .item {
            position: relative;
            font-size: 32rpx;
            color: #262626;
            width: 212rpx;
            height: 172rpx;
            padding: 36rpx 0;
            box-sizing: border-box;
            border-radius: 40rpx;
            font-weight: 600;
            display: flex;
            align-items: flex-end;
            justify-content: center;
            .img {
                width: 125rpx;
                height: 100rpx;
                position: absolute;
                top: -40rpx;
            }
        }
    }
    .main {
        padding: 24rpx 30rpx;
        background-color: $uni-text-color-inverse;
        .top {
            display: flex;
            font-size: 28rpx;
            color: #262626;
            font-weight: 600;
            padding: 0rpx;
            justify-content: space-between;
            .right {
                color: #8c8c8c;
                font-weight: normal;
            }
        }
        .main_box {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            .item_box {
                width: 49%;
                display: flex;
                padding: 20rpx 0;
                position: relative;
                .flag {
                    font-size: 16rpx;
                    color: $uni-text-color-inverse;
                    position: absolute;
                    width: 24rpx;
                    height: 24rpx;
                    text-align: center;
                    border-radius: 12rpx 0 12rpx 0;
                }
                .img {
                    width: 88rpx;
                    height: 88rpx;
                    border-radius: 12rpx;
                    overflow: hidden;
                    margin-right: 16rpx;
                }
                .r_box {
                    display: flex;
                    flex-direction: column;
                    justify-content: space-around;
                    flex: 1;
                    overflow: hidden;
                    .t {
                        font-size: 32rpx;
                        color: #262626;
                        flex: 1;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        overflow: hidden;
                    }
                    .btm {
                        font-size: 24rpx;
                        color: #999999;
                    }
                }
            }
        }
    }
}
</style>
