<template>
    <z-paging class="container_box">
        <view class="main">
            <uni-forms :modelValue="state" label-position="top">
                <uni-forms-item label="标题">
                    <uni-easyinput v-model="state.title" primaryColor="var(--primary-color)" :inputBorder="false" maxlength="20" placeholder="请输入">
                        <template #right>
                            <view class="r_slot">{{ state.title.length }}/20</view>
                        </template>
                    </uni-easyinput>
                </uni-forms-item>
                <uni-forms-item label="内容" class="textarea_box">
                    <uni-easyinput v-model="state.content" type="textarea" primaryColor="var(--primary-color)" maxlength="5000" :inputBorder="false" placeholder="请输入"> </uni-easyinput>
                    <view class="textarea_count">{{ state.content.length }}/5000</view>
                </uni-forms-item>
            </uni-forms>
        </view>
        <template #bottom>
            <view class="footer">
                <button class="btn" @click="handleClick">完成</button>
            </view>
        </template>
    </z-paging>
</template>
<script setup>
const state = reactive({
    title: "",
    content: ""
})
onLoad((option) => {
    try {
        const data = JSON.parse(decodeURIComponent(option.data))
        Object.assign(state, data)
    } catch (error) {
        console.log(error)
    }
})
const handleClick = () => {
    const params = {
        id: state.id,
        title: state.title,
        content: state.content
    }
    http.post("/app/club/notice/announcement/update", params).then((res) => {
        uni.showToast({
            title: "编辑公告成功",
            icon: "none"
        })
        navigateTo({ url: "/apps/clubManage/myGroup/notice/index" })
    })
}
</script>
<style lang="scss" scoped>
.container_box {
    margin-top: 20rpx;
    .main {
        padding: 30rpx;
        background: $uni-bg-color;
        :deep(.uni-easyinput__content) {
            background: #f6f6f6 !important;
            border-radius: 10rpx;
        }
        .r_slot {
            color: #999;
            font-size: 28rpx;
            margin-right: 20rpx;
        }
        .textarea_box {
            position: relative;
            :deep(.uni-easyinput__content) {
                padding: 20rpx 0 40rpx 20rpx;
            }
            .textarea_count {
                position: absolute;
                display: inline-block;
                color: #999;
                font-size: 28rpx;
                right: 20rpx;
                bottom: 10rpx;
            }
        }
    }
    .footer {
        width: 100%;
        padding: 0 30rpx;
        background: $uni-bg-color;
        .btn {
            font-size: 32rpx;
            color: $uni-text-color-inverse;
            height: 92rpx;
            line-height: 92rpx;
            border-radius: 10rpx;
            background: var(--primary-color);
            margin-left: 0;
            margin-top: 30rpx;
            margin-bottom: 66rpx;
            &:after {
                border: none;
            }
        }
    }
}
</style>
