<template>
    <view class="container_box">
        <view class="header">
            <uni-easyinput class="search_box" prefixIcon="search" placeholder="搜索社团名称" primaryColor="var(--primary-color)" @focus="toSearchGroup"></uni-easyinput>
        </view>
        <view class="top_box">
            <uni-badge :text="state.joinRequest" absolute="rightTop" :offset="[14, 3]" size="small">
                <view class="top_item" @click="toApplyGroup">
                    <img class="img" src="@nginx/workbench/groupManage/newshenqing.png" />
                    <view>新的申请</view>
                </view>
            </uni-badge>

            <uni-badge :text="state.notice" absolute="rightTop" :offset="[14, 3]" size="small">
                <view class="top_item" @click="toNotice">
                    <img class="img" src="@nginx/workbench/groupManage/tongzhigonggao.png" />
                    <view>公告通知</view>
                </view>
            </uni-badge>
        </view>
        <view class="main">
            <view class="main_header">
                <uv-tabs
                    lineColor="var(--primary-color)"
                    :activeStyle="{ color: 'var(--primary-color)' }"
                    :inactiveStyle="{ color: '#999999' }"
                    :itemStyle="{
                        height: '50rpx',
                        flex: 1,
                        paddingBottom: '20rpx'
                    }"
                    :list="state.tabs"
                    @click="tabsClick"
                ></uv-tabs>
            </view>
            <view class="mian_box" v-if="groupList && groupList.length > 0">
                <view class="item_box" v-for="item in groupList" @click="handleClick(item)" :key="item.iconUrl">
                    <img class="img" :src="item.iconUrl" />
                    <view class="flag" :style="{ backgroundColor: item.level === 'department' ? 'var(--primary-color)' : '#FF992B' }">{{ item.level === "department" ? "院" : "校" }}</view>
                    <view class="right">
                        <view class="item_top_box">
                            <view class="l">
                                <view class="text_top">
                                    <view class="text_top_l">{{ item.name }}</view>
                                    <text
                                        v-if="state.actived === 'applied'"
                                        class="status_box"
                                        :style="{
                                            color: statusObj[item.approvalStatus]?.color || '',
                                            background: statusObj[item.approvalStatus]?.bg || ''
                                        }"
                                    >
                                        {{ statusObj[item.approvalStatus]?.text || "" }}
                                    </text>
                                </view>
                                <view class="text_btm">{{ item.memberCount }}人</view>
                            </view>
                            <view class="r" v-if="state.actived === 'applied'">
                                <button class="btn_cls" v-if="item.approvalStatus === 'rejected' || item.approvalStatus === 'cancelled'" style="background: #fff; color: var(--primary-color)" @click.stop="handleApply(item)">再次申请</button>
                                <button class="btn_cls" v-if="item.approvalStatus === 'pending'" style="background: var(--primary-color); color: #fff" @click.stop="handleCancel(item)">取消申请</button>
                            </view>
                        </view>
                        <view v-if="state.actived === 'applied' && item.approvalStatus === 'rejected'" class="btm_box">拒绝理由: {{ item.approvalComment }} </view>
                    </view>
                </view>
            </view>
            <yd-empty text="暂无数据" :isMargin="true" v-else />
        </view>
        <uni-popup ref="alertDialog" type="dialog">
            <uni-popup-dialog class="dialog_box" type="info" cancelText="取消" confirmText="确认" :title="state.flag === 'cancel' ? '取消申请' : '再次申请'" :content="state.content" @confirm="dialogConfirm" @close="dialogClose"> </uni-popup-dialog>
        </uni-popup>
    </view>
</template>
<script setup>
const statusObj = {
    rejected: {
        color: "#F5222D",
        bg: "#FFDFDF",
        text: "已拒绝"
    },
    pending: {
        color: "#F48F03",
        bg: "#FFEFD8",
        text: "待审核"
    },
    cancelled: {
        color: "#595959",
        bg: "#ECECEC",
        text: "已取消"
    }
}

const state = reactive({
    tabs: [
        {
            name: "管理的社团",
            id: "manged"
        },
        {
            name: "加入的社团",
            id: "joined"
        },
        {
            name: "创建的社团",
            id: "applied"
        }
    ],
    actived: "manged",
    joinRequest: 0,
    notice: 0,
    status: 0,
    content: "",
    sourceData: {},
    flag: "cancel"
})

// 初始化社团数据
const groupInit = () => {
    // 社团列表
    http.post("/app/club/my-clubs/list").then((res) => {
        state.sourceData = res.data
    })

    // 新申请和通知计数
    http.post("/app/club/my-clubs/unread").then((res) => {
        state.joinRequest = res.data.joinRequest
        state.notice = res.data.notice
    })
}

onShow(() => {
    groupInit()
})

const groupList = computed(() => {
    return state.sourceData[state.actived] || []
})

const handleClick = (item) => {
    navigateTo({
        url: "/apps/clubManage/myGroup/clubManage/index",
        query: {
            id: item.id,
            memberRole: item.memberRole
        }
    })
}

// 暂存数据
let stash = {}
const alertDialog = ref(null)
// 取消申请
const handleCancel = (item) => {
    state.flag = "cancel"
    state.content = `是否确认取消“${item.name}”的申请？`
    stash = item
    alertDialog.value.open()
}

// 再次申请
const handleApply = (item) => {
    state.flag = "again"
    state.content = "是否再次申请？"
    stash = item
    alertDialog.value.open()
}

// 弹窗确认
const dialogConfirm = () => {
    if (state.flag === "cancel") {
        http.post("/app/club/club-application/cancel", { id: stash.id }).then((res) => {
            uni.showToast({
                title: "取消申请成功",
                icon: "none"
            })
            groupInit()
            dialogClose()
        })
    } else {
        navigateTo({
            url: "/apps/clubManage/applyGroup/index",
            query: { oldId: stash.id, isEdit: true }
        })
    }
}

// 弹窗关闭
const dialogClose = () => {
    alertDialog.value.close()
}

function tabsClick(item) {
    state.actived = item.id
}

// 新的申请
const toApplyGroup = () => {
    navigateTo({
        url: "/apps/clubManage/myGroup/newApply/index"
    })
}

// 通知公告
const toNotice = () => {
    navigateTo({
        url: "/apps/clubManage/myGroup/notice/index"
    })
}

const toSearchGroup = () => {
    uni.navigateTo({
        url: "/apps/clubManage/searchGroup/index"
    })
}
</script>
<style lang="scss" scoped>
.container_box {
    .header {
        padding: 30rpx;
        background-color: $uni-text-color-inverse;
        margin-bottom: 20rpx;
        .search_box {
            :deep(.is-input-border) {
                border: none;
                background-color: $uni-bg-color-grey !important;
                border-radius: 20px;
            }
        }
    }
    .top_box {
        height: 180rpx;
        background-color: $uni-text-color-inverse;
        display: flex;
        color: #262626;
        font-size: 24rpx;
        align-items: center;
        justify-content: space-around;
        margin-bottom: 20rpx;
        .top_item {
            width: 96rpx;
            height: 115rpx;
            text-align: center;
        }
        .img {
            width: 64rpx;
            height: 64rpx;
            margin-bottom: 16rpx;
        }
    }
    .main {
        background-color: $uni-text-color-inverse;
        min-height: calc(100vh - 440rpx);
        .main_header {
            padding-top: 22rpx;
            padding-bottom: 11rpx;
            border-bottom: 2rpx solid #d9d9d9;
        }
        .mian_box {
            padding: 20rpx 30rpx;
            .item_box {
                display: flex;
                padding-top: 40rpx;
                position: relative;
                .flag {
                    font-size: 16rpx;
                    color: $uni-text-color-inverse;
                    position: absolute;
                    width: 24rpx;
                    height: 24rpx;
                    text-align: center;
                    border-radius: 12rpx 0 12rpx 0;
                }
                .img {
                    width: 100rpx;
                    height: 100rpx;
                    border-radius: 12rpx;
                    overflow: hidden;
                    margin-right: 16rpx;
                    flex-shrink: 0;
                }
                .right {
                    flex: 1;
                    overflow: hidden;
                    .item_top_box {
                        display: flex;
                        align-items: center;
                        height: 100rpx;
                        .l {
                            flex: 1;
                            overflow: hidden;
                            .text_top {
                                font-size: 32rpx;
                                color: #262626;
                                font-weight: 600;
                                margin-bottom: 8rpx;
                                overflow: hidden;
                                display: flex;
                                align-items: center;
                                .text_top_l {
                                    white-space: nowrap;
                                    text-overflow: ellipsis;
                                    overflow: hidden;
                                }
                                .status_box {
                                    width: 72rpx;
                                    height: 32rpx;
                                    flex-shrink: 0;
                                    display: inline-block;
                                    line-height: 32rpx;
                                    text-align: center;
                                    font-size: 20rpx;
                                    border-radius: 4rpx;
                                    margin-left: 8rpx;
                                    margin-right: 20rpx;
                                }
                            }
                            .text_btm {
                                font-size: 24rpx;
                                color: #999;
                            }
                        }
                        .r {
                            .btn_cls {
                                font-size: 28rpx;
                                width: 132rpx;
                                height: 60rpx;
                                line-height: 60rpx;
                                padding: 0;
                                text-align: center;
                                &:after {
                                    border-color: var(--primary-color);
                                }
                            }
                        }
                    }
                    .btm_box {
                        font-size: 28rpx;
                        color: #999999;
                        line-height: 40rpx;
                    }
                }
            }
        }
    }
    .dialog_box {
        :deep(.uni-dialog-button-text) {
            color: var(--primary-color) !important;
        }
    }
}
</style>
