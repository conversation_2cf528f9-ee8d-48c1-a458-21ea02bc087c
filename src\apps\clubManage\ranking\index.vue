<template>
    <z-paging ref="paging" class="container_box">
        <template #top>
            <view class="top">
                <uni-nav-bar title="" @clickLeft="back" :border="false" fixed statusBar backgroundColor="transparent" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()">
                    <template #left><uni-icons type="left" size="30" color="#fff" @click="back"></uni-icons> </template>
                </uni-nav-bar>
                <view class="img_box">
                    <img class="t_img" src="@nginx/workbench/groupManage/CLUB.png" />
                    <img class="b_img" src="@nginx/workbench/groupManage/shetuanpaihang.png" />
                    <img class="img" src="@nginx/workbench/groupManage/bg-jiangbei.png" />
                </view>
                <view class="modal_box"></view>
            </view>
        </template>
        <view class="main">
            <view class="item" v-if="state.list.length">
                <view class="item_box" v-for="(item, index) in state.list" @click="handleClick(item)" :key="index">
                    <img class="img" :src="item.iconUrl" />
                    <view class="flag" :style="{ backgroundColor: item.level === 'department' ? 'var(--primary-color)' : '#FF992B' }">{{ item.level === "department" ? "院" : "校" }}</view>
                    <view class="right_box">
                        <view class="r_box">
                            <view class="t">{{ item.name }}</view>
                            <view class="btm">{{ item.memberCount }}人</view>
                        </view>
                        <view class="l_box">
                            <img v-if="item.ranking <= 3" :src="`@nginx/workbench/groupManage/${item.ranking - 1}rank.png`" class="flag_img" />
                            <text v-else>{{ item.ranking }}</text>
                        </view>
                    </view>
                </view>
            </view>
            <view class="empty_box" v-else>
                <img class="empty_img" src="@nginx/workbench/groupManage/empty.png" alt="" />
                <view>暂无数据</view>
            </view>
        </view>
    </z-paging>
</template>
<script setup>
const state = reactive({
    list: []
})

const handleClick = (item) => {
    navigateTo({ url: "/apps/clubManage/groupDetail/index", query: { id: item.id } })
}

function queryRankingList() {
    http.post("/app/club/club/ranking", { limit: 100 }).then((res) => {
        state.list = res.data
    })
}

onLoad(() => {
    queryRankingList()
})

const back = () => {
    uni.navigateBack()
}
</script>
<style lang="scss" scoped>
.container_box {
    .top {
        width: 100%;
        height: 480rpx;
        background: linear-gradient(180deg, #ffc05e 0%, #ff901f 100%);
        box-sizing: border-box;
        position: relative;
        .img_box {
            padding: 0 30rpx;
            display: flex;
            height: calc(100% - 129rpx);
            overflow: hidden;
            position: relative;
            .t_img {
                width: 386rpx;
                height: 108rpx;
                position: absolute;
                left: 38rpx;
                top: 52rpx;
            }
            .b_img {
                width: 396rpx;
                height: 78rpx;
                position: absolute;
                left: 32rpx;
                top: 108rpx;
                z-index: 1;
            }
            .img {
                width: 248rpx;
                height: 284rpx;
                position: absolute;
                right: 30rpx;
                top: 50rpx;
            }
        }
        .modal_box {
            width: 100%;
            height: 40rpx;
            background: $uni-bg-color;
            position: absolute;
            bottom: 0;
            left: 0;
            border-radius: 40rpx 40rpx 0 0;
        }
    }
    .main {
        background-color: $uni-text-color-inverse;
        border-radius: 28rpx 28rpx 0rpx 0rpx;
        z-index: 9;
        padding: 20rpx 30rpx;
        padding-top: 0;
        .item_box {
            width: 100%;
            display: flex;
            padding: 20rpx 0;
            .img {
                width: 88rpx;
                height: 88rpx;
                border-radius: 12rpx;
                overflow: hidden;
                margin-right: 16rpx;
            }
            .flag {
                font-size: 16rpx;
                color: $uni-text-color-inverse;
                position: absolute;
                width: 24rpx;
                height: 24rpx;
                text-align: center;
                border-radius: 12rpx 0 12rpx 0;
            }
            .right_box {
                display: flex;
                flex: 1;
                align-items: center;
                overflow: hidden;
                .r_box {
                    display: flex;
                    flex-direction: column;
                    justify-content: space-around;
                    flex: 1;
                    overflow: hidden;
                    .t {
                        font-size: 32rpx;
                        color: #262626;
                        flex: 1;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        overflow: hidden;
                        font-weight: 600;
                        margin-bottom: 8rpx;
                    }
                    .btm {
                        font-size: 24rpx;
                        color: #999999;
                    }
                }
                .l_box {
                    width: 44rpx;
                    text-align: center;
                    font-size: 36rpx;
                    color: #999;
                    .flag_img {
                        width: 44rpx;
                        height: 46rpx;
                    }
                }
            }
        }
        .empty_box {
            width: 360rpx;
            height: 400rpx;
            color: #8c8c8c;
            font-size: 28rpx;
            text-align: center;
            margin: auto;
            padding-top: 40%;
            .empty_img {
                width: 360rpx;
            }
        }
    }
}
</style>
