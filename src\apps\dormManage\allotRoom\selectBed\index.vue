<template>
    <z-paging>
        <template #top>
            <view class="top_container">
                <view class="top_box">
                    <uv-steps :current="current">
                        <uv-steps-item :title="allotInfo.one"></uv-steps-item>
                        <uv-steps-item :title="allotInfo.two"></uv-steps-item>
                        <uv-steps-item :title="allotInfo.three"></uv-steps-item>
                    </uv-steps>
                </view>
                <!-- 内容信息 -->
                <view class="main_box">
                    <view class="title">请选择您要分配的床位 </view>
                    <view class="msg_box">
                        <view class="top_text" @click="openSelePopup">
                            <view class="l_text">{{ buildMsg.name }}</view>
                            <view><uni-icons type="right" size="22"></uni-icons></view>
                        </view>
                        <view class="btn_text">
                            <text>合计寝室：{{ buildMsg.totalRoomCount }}间</text>
                            <text>已住：{{ buildMsg.checkInPeopleCount }}人</text>
                            <text>剩余床位：{{ buildMsg.residueBedCount }}个</text>
                        </view>
                    </view>
                    <!-- 选择按钮 -->
                    <view class="select_box">
                        <selectBtn :list="btnList" v-model:value="selVal" @handleClick="selectClick"></selectBtn>
                    </view>
                </view>
            </view>
        </template>

        <!-- 选项卡开始 -->
        <uv-vtabs :chain="true" :list="list" :height="height" class="tabs_box" v-if="viewKey">
            <template v-for="(item, index) in list" :key="item.roomId">
                <uv-vtabs-item :index="index">
                    <bed-select v-model:value="item.bedValue" :list="item.children"></bed-select>
                </uv-vtabs-item>
            </template>
        </uv-vtabs>
        <!-- 选项卡结束 -->

        <template #bottom>
            <!-- 底部按钮 -->
            <div class="footer_box">
                <view class="btn_group">
                    <button class="yd_plain" @click="previous">上一步</button>
                    <button class="yd_btn_primary" @click="next">确认分配至该床位</button>
                </view>
            </div>
        </template>
    </z-paging>
    <selectPopup ref="selectPopupRef" title="请选择楼栋" :list="selectList" @closePopup="closePopup"></selectPopup>
    <TipPopup ref="tipDomePopup" title="您选择的床位" confirmBtnText="确认选择" @closePopup="tipClose" @confirm="tipConfirm">
        <template #tipText>
            <view>{{ tipMsg }}</view>
        </template>
    </TipPopup>
</template>
<script setup>
import selectPopup from "../../components/selectPopup.vue"
import selectBtn from "../../components/selectBtn.vue"
import bedSelect from "./components/bedSelect.vue"
import TipPopup from "../../components/tipPopup.vue"

onLoad((params) => {
    Object.keys(params).forEach((i) => (allotInfo[i] = decodeURIComponent(params[i])))
    allotInfo.classesIdList = JSON.parse(allotInfo.classesIdList)
    allotInfo.studentIdList = JSON.parse(allotInfo.studentIdList)
    getBuildList()
})

const height = computed(() => {
    const num = uni.getSystemInfoSync().screenHeight - 400
    return num
})
const viewKey = ref(true)
let current = ref(2)
// 选择按钮
const btnList = ref()
let tipMsg = ref("")
let allotInfo = reactive({})
let buildMsg = reactive({})
const selectList = ref()
let selVal = ref(1)
const list = ref()

const getBuildList = async () => {
    const { data } = await http.post("/app/dormitory/intelligentDormitorySeparation/dormitoryBuilding", {
        gender: allotInfo.studentGender
    })
    if (!data.length) return
    selectList.value = data.map((i, index) => ({ ...i, value: index }))
    Object.assign(buildMsg, data[0])
    getFloorList(buildMsg.id)
}

async function getFloorList(buildingId) {
    const { data } = await http.post("/app/dormitory/intelligentDormitorySeparation/dormitoryFloor", {
        buildingId,
        gender: allotInfo.studentGender
    })
    btnList.value = data.map((i) => ({ name: i.floorName, value: i.floor }))
    if (btnList.value.length) selVal.value = btnList.value[0].value
    getRoomList()
}

async function getRoomList() {
    const params = {
        buildingId: buildMsg.id,
        floor: selVal.value,
        gender: allotInfo.studentGender
    }
    const { data } = await http.post("/app/dormitory/intelligentDormitorySeparation/dormitoryRoom", params)
    list.value = data.map((i) => ({ ...i, name: i.roomNum }))
    console.log("list.value:", list.value)
    // viewKey.value = false
    setTimeout(() => {
        viewKey.value = true
    }, 300)
}

const selectClick = (item) => {
    getRoomList()
    console.log("ads ")
}

const previous = () => {
    uni.navigateBack()
}

const next = () => {
    let arr = []
    list.value.forEach((i) => {
        if (i.bedValue && i.bedValue.length) {
            arr.push(...i.bedValue)
        }
    })

    if (!arr.length) return
    allotInfo.bedNoList = arr.map((i) => i.roomId + "-" + i.bedSeq)

    const floorName = btnList.value.find((i) => i.value === selVal.value).name
    const roomName = arr.map((i) => floorName + "-" + i.bedNo).join()
    tipMsg.value = `${buildMsg.name} : ${roomName};共${arr.length}个床位`
    openPopup()
}

const selectPopupRef = ref(null)
const openSelePopup = () => {
    selectPopupRef.value.open()
}

const closePopup = (val) => {
    if (!val) return
    Object.assign(buildMsg, val)
    getFloorList(val.id)
}

const tipDomePopup = ref(null)
function openPopup() {
    tipDomePopup.value.open()
}

function tipClose() {
    tipDomePopup.value.closeDialog()
}

const goto = () => {
    navigateTo({
        url: "/apps/dormManage/dormInfo/index"
    })
}

async function tipConfirm() {
    const params = {
        bedNoList: allotInfo.bedNoList,
        classesIdList: allotInfo.classesIdList,
        studentIdList: allotInfo.studentIdList,
        gender: allotInfo.studentGender
    }
    if (allotInfo.bedNoList.length < allotInfo.studentIdList.length) {
        uni.showToast({
            title: "床位数小于待入住学生总数",
            icon: "none"
        })
        return
    }
    await http.post("/app/dormitory/intelligentDormitorySeparation/separationByBed", params)
    uni.showToast({
        title: "分寝成功",
        icon: "none"
    })
    tipClose()
    setTimeout(() => {
        goto()
    }, 1000)
}
</script>
<style lang="scss" scoped>
.top_container {
    background-color: $uni-bg-color-grey;

    .top_box {
        background-color: $uni-bg-color-grey;
        padding: 40rpx 0;
        :deep(.uv-steps-item__wrapper) {
            background-color: $uni-bg-color-grey;
        }
    }
    .main_box {
        background-color: #fff;
        border-radius: 40rpx 40rpx 0 0;
        padding: 40rpx 0rpx 0rpx 0rpx;
        display: flex;
        flex-wrap: wrap;
        flex-direction: column;
        .title {
            text-align: center;
            font-size: 32rpx;
            color: #333;
            font-weight: 600;
            padding-bottom: 40rpx;
            width: 100%;
        }
        .msg_box {
            color: #333333;
            border-bottom: 1rpx solid #d9d9d9;
            width: 100%;
            .top_text {
                padding: 0 28rpx;
                font-size: 32rpx;
                display: flex;
                justify-content: space-between;
                .l_text {
                    font-weight: 600;
                }
            }
            .btn_text {
                display: flex;
                justify-content: space-between;
                font-size: 28rpx;
                padding: 16rpx 28rpx 40rpx 28rpx;
            }
        }
        .select_box {
            border-bottom: 1rpx solid #d9d9d9;
        }

        .sele_btn_text {
            font-size: 28rpx;
            font-weight: normal;
            padding-top: 16rpx;
        }
    }
}
.tabs_box {
    flex: 1;
    width: 100%;
    .item_box {
        width: 200rpx;
        height: 200rpx;
    }
}

.footer_box {
    box-sizing: border-box;
    padding: 30rpx;
    background-color: #fff;

    .btn_group {
        display: flex;
        justify-content: space-between;
        .yd_plain {
            background-color: #fff;
            color: #4566d5;
            border: 1rpx solid #4566d5;
        }
        .yd_btn_primary {
            background-color: #4566d5;
            color: #fff;
        }
        button {
            width: 330rpx;
            font-size: 32rpx;
            margin-left: 0;
            margin-right: 0;
        }
    }
}
</style>
