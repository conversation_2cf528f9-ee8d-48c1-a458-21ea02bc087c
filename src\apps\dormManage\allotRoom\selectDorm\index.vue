<template>
    <view class="allot_room_container">
        <view class="top_box">
            <uv-steps :current="current">
                <uv-steps-item :title="allotInfo.one"></uv-steps-item>
                <uv-steps-item :title="allotInfo.two"></uv-steps-item>
                <uv-steps-item :title="allotInfo.three"></uv-steps-item>
            </uv-steps>
        </view>
        <!-- 内容信息 -->
        <view class="main_box">
            <view class="title">请选择您要分配的寝室 </view>
            <view class="msg_box" @click="openSelePopup">
                <view class="top_text">
                    <view class="l_text">{{ buildMsg.name }}</view>
                    <view><uni-icons type="right" size="22"></uni-icons></view>
                </view>
                <view class="btn_text">
                    <text>合计寝室：{{ buildMsg.totalRoomCount }}间</text>
                    <text>已住：{{ buildMsg.checkInPeopleCount }}人</text>
                    <text>剩余床位：{{ buildMsg.residueBedCount }}个</text>
                </view>
            </view>
            <!-- 选择按钮 -->
            <view class="select_box">
                <selectBtn :list="btnList" v-model:value="selVal" @handleClick="selectClick"></selectBtn>
            </view>

            <radioSelect v-model:value="dormValue" :list="dormList">
                <template #left="{ left: { roomNum, atLeastBedCount } }">
                    <view>
                        <view>{{ roomNum }}</view>
                        <view class="sele_btn_text">剩余床位：{{ atLeastBedCount }}个</view>
                    </view>
                </template>
            </radioSelect>
        </view>

        <!-- 底部按钮 -->
        <div class="footer_box">
            <view class="btn_group">
                <button class="yd_plain" @click="previous">上一步</button>
                <button class="yd_btn_primary" @click="next">确认分配至该寝室</button>
            </view>
        </div>
        <selectPopup ref="selectPopupRef" title="请选择楼栋" :list="selectList" @closePopup="closePopup"></selectPopup>
        <TipPopup ref="tipDomePopup" title="您选择的寝室" confirmBtnText="确认选择" @closePopup="tipClose" @confirm="tipConfirm">
            <template #tipText>
                <view>{{ tipMsg }}</view>
            </template>
        </TipPopup>
    </view>
</template>
<script setup>
import selectPopup from "../../components/selectPopup.vue"
import radioSelect from "./components/radioSelect.vue"
import selectBtn from "../../components/selectBtn.vue"
import TipPopup from "../../components/tipPopup.vue"

onLoad((params) => {
    Object.keys(params).forEach((key) => {
        params[key] = decodeURIComponent(params[key])
    })
    Object.keys(params).forEach((i) => (allotInfo[i] = params[i]))
    allotInfo.classesIdList = JSON.parse(allotInfo.classesIdList)
    allotInfo.studentIdList = JSON.parse(allotInfo.studentIdList)
    getBuildList()
})

let current = ref(2)
let dormValue = ref([])
let tipMsg = ref("")
let allotInfo = reactive({})
let buildMsg = reactive({})
const dormList = ref()
// 选择按钮
const btnList = ref([])
const selectList = ref([])

const getBuildList = async () => {
    const { data } = await http.post("/app/dormitory/intelligentDormitorySeparation/dormitoryBuilding", {
        gender: allotInfo.studentGender
    })
    if (!data.length) return
    selectList.value = data.map((i, index) => ({ ...i, value: index }))
    Object.assign(buildMsg, data[0])
    getFloorList(buildMsg.id)
}

async function getFloorList(buildingId) {
    const { data } = await http.post("/app/dormitory/intelligentDormitorySeparation/dormitoryFloor", {
        buildingId,
        gender: allotInfo.studentGender
    })
    btnList.value = data.map((i) => ({ name: i.floorName, value: i.floor }))
    getRoomList()
}

async function getRoomList() {
    const params = {
        buildingId: buildMsg.id,
        floor: selVal.value,
        gender: allotInfo.studentGender
    }
    const { data } = await http.post("/app/dormitory/intelligentDormitorySeparation/dormitoryRoom", params)
    dormList.value = data
}

let selVal = ref(1)
const selectClick = () => {
    getRoomList()
}

const previous = () => {
    uni.navigateBack()
}

const next = () => {
    if (!dormValue.value.length) return
    allotInfo.roomIdList = dormValue.value.map((i) => i.roomId)
    const floorName = btnList.value.find((i) => i.value === selVal.value).name
    const roomName = dormValue.value.map((i) => floorName + "-" + i.roomName).join()
    const bedTotal = dormValue.value.reduce((pre, curr) => {
        pre.atLeastBedCount += curr.atLeastBedCount
        return pre
    }).atLeastBedCount
    tipMsg.value = `${buildMsg.name} : ${roomName};共${bedTotal}个床位`
    openPopup()
}

const selectPopupRef = ref(null)
const openSelePopup = () => {
    selectPopupRef.value.open()
}

const closePopup = (val) => {
    if (!val) return
    Object.assign(buildMsg, val)
    getFloorList(val.id)
}

const tipDomePopup = ref(null)
function openPopup() {
    tipDomePopup.value.open()
}

function tipClose() {
    tipDomePopup.value.closeDialog()
    dormValue.value = []
    getRoomList()
}

const goto = () => {
    navigateTo({
        url: "/apps/dormManage/dormInfo/index"
    })
}

async function tipConfirm() {
    const params = {
        roomIdList: allotInfo.roomIdList,
        classesIdList: allotInfo.classesIdList,
        studentIdList: allotInfo.studentIdList,
        gender: allotInfo.studentGender
    }
    await http.post("/app/dormitory/intelligentDormitorySeparation/separationByRoom", params)
    uni.showToast({
        title: "分寝成功",
        icon: "none"
    })
    tipClose()
    setTimeout(() => {
        goto()
    }, 1000)
}
</script>
<style lang="scss" scoped>
.allot_room_container {
    background-color: $uni-bg-color-grey;
    .top_box {
        padding: 40rpx 0;
        :deep(.uv-steps-item__wrapper) {
            background-color: $uni-bg-color-grey;
        }
    }
    .main_box {
        background-color: #fff;
        border-radius: 40rpx 40rpx 0 0;
        padding: 40rpx 0rpx 150rpx 0rpx;
        .title {
            text-align: center;
            font-size: 32rpx;
            color: #333;
            font-weight: 600;
            padding-bottom: 40rpx;
        }
        .msg_box {
            color: #333333;
            padding: 0 28rpx;
            border-bottom: 1rpx solid #d9d9d9;
            .top_text {
                font-size: 32rpx;
                display: flex;
                justify-content: space-between;
                .l_text {
                    font-weight: 600;
                }
            }
            .btn_text {
                display: flex;
                justify-content: space-between;
                font-size: 28rpx;
                padding: 16rpx 0 40rpx 0;
            }
        }
        .select_box {
            border-bottom: 1rpx solid #d9d9d9;
        }
        .sele_btn_text {
            font-size: 28rpx;
            font-weight: normal;
            padding-top: 16rpx;
        }
    }

    .footer_box {
        position: fixed;
        width: 100%;
        box-sizing: border-box;
        bottom: 0rpx;
        padding: 30rpx;
        background-color: #fff;

        .btn_group {
            display: flex;
            justify-content: space-between;
            .yd_plain {
                background-color: #fff;
                color: #4566d5;
                border: 1rpx solid #4566d5;
            }
            .yd_btn_primary {
                background-color: #4566d5;
                color: #fff;
            }
            button {
                width: 330rpx;
                font-size: 32rpx;
                margin-left: 0;
                margin-right: 0;
            }
        }
    }
}
</style>
