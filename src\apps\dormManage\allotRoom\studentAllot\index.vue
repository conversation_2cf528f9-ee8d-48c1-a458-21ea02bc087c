<template>
    <view class="student_allot_container">
        <view class="search">
            <uv-input placeholder="搜索学生姓名" prefixIcon="search" shape="circle" prefixIconStyle="font-size: 22px;color: #909399" @focus="focus"></uv-input>
        </view>
        <view class="top">
            <img :src="schoolInfo.avatar || 'https://alicdn.1d1j.cn/1531914651808833538/default/26c5462c5b8f4dd7a49732cda6e1003f.png'" class="yd_img" />
            <text>{{ schoolInfo.name }}</text>
        </view>
        <view class="main_box">
            <treeList :flagList="['student']" flag="rollValue" :rightList="['right']" rightType="rightValue" :list="treeListData" :tree-click-item="treeClick"> </treeList>
        </view>
        <view class="footer_box">
            <view class="btn_group">
                <button class="yd_plain" @click="previous">上一步</button>
                <button :class="['yd_btn_primary', { btn_disable: !disable }]" @click="next">下一步</button>
            </view>
        </view>
    </view>
</template>
<script setup>
import treeList from "../../components/treeList.vue"
import { setCheck, getCheck } from "../../utils/treeMethod.js"

let gender = ""
let disable = computed(() => getCheck(treeListData.value, "rollValue", "student").length)

onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    gender = options.gender
    getTree()
})

const getTree = async () => {
    const {
        data: { list }
    } = await http.get("/app/roll/dormitory/v1/listTree", {
        gender,
        filterLivingSchool: true
    })
    treeListData.value = list
    schoolInfo.avatar = list[0].avatar
    schoolInfo.name = list[0].name
}
const schoolInfo = reactive({})

const getStudentList = (params) => {
    return new Promise(async (resolve, reject) => {
        const { data } = await http.post("/app/student/search", params)
        if (!data || data.length === 0) {
            uni.showToast({
                title: "暂无学生",
                duration: 2000,
                icon: "none"
            })
        }
        resolve(data)
    })
}

// 树组件
const treeListData = ref()

const treeClick = async (item) => {
    if (item.rollValue === "classes") {
        if (item.children.length || item.peopleCount === 0) return

        const params = {
            gender,
            classesId: item.id,
            filterLivingSchool: true,
            code: "smartDormitory",
            filterNonEltern: false,
            queryEltern: false,
            statusList: [1, 2, 4, 5, 12, 13, 99]
        }
        let data = await getStudentList(params)
        data = data.map((i) => ({ ...i, rollValue: "student", children: [] }))
        item.children.push(...data)
    } else if (item.rollValue === "student") {
        const params = {
            sourceArr: treeListData.value,
            obj: item,
            type: "rollValue",
            flag: "student",
            multiple: true
        }
        setCheck(params)
    }
}

const focus = () => {
    let avatarList = getCheck(treeListData.value, "rollValue", "student")
    const params = {
        selectFlag: "multiple",
        gender,
        filterLivingSchool: true,
        avatarList: JSON.stringify(avatarList) || "[]"
    }
    navigateTo({
        url: "/apps/dormManage/searchView/index",
        query: params
    })
}

const previous = () => {
    uni.navigateBack(1)
}

const next = () => {
    let arr = getCheck(treeListData.value, "rollValue", "student")
    if (!arr.length) return
    const studentIdList = arr.map((i) => i.id)
    const two = arr.length + "人"
    uni.navigateBack({
        success() {
            uni.$emit("nextStep", { step: 2, two, studentIdList, gender })
        }
    })
}
</script>
<style lang="scss" scoped>
.student_allot_container {
    background-color: $uni-bg-color-grey;
    margin-bottom: 20rpx;
    // padding-top: 20rpx;
    .search {
        background: #fff;
        padding: 30rpx;
        :deep(.uv-input--circle) {
            background-color: $uni-bg-color-grey;
        }
    }
    .top {
        padding: 14rpx 28rpx;
        background-color: #fff;
        display: flex;
        align-items: center;
        color: #333;
        font-size: 32rpx;
        font-weight: 600;
        margin-bottom: 20rpx;
        .yd_img {
            width: 64rpx;
            height: 64rpx;
            margin-right: 20rpx;
            border-radius: 50%;
        }
    }
    .main_box {
        background-color: #fff;
        padding-bottom: 160rpx;
        .tree_item_box {
            width: 100%;
            display: flex;
            justify-content: space-between;
            font-size: 32rpx;
            color: #333333;
            .left_box {
                display: flex;
                flex: 1;
                align-items: center;
                justify-content: space-between;
                .left {
                    display: flex;
                    align-items: center;
                    .img {
                        width: 60rpx;
                        height: 60rpx;
                        border-radius: 50%;
                        overflow: hidden;
                    }
                    .chart_at {
                        display: inline-block;
                        width: 60rpx;
                        height: 60rpx;
                        text-align: center;
                        line-height: 60rpx;
                        color: #fff;
                        font-size: 32rpx;
                        border-radius: 50%;
                        background-color: #4566d5;
                        font-weight: 600;
                    }
                    .text {
                        font-size: 28rpx;
                        color: #333;
                        margin-left: 24rpx;
                    }
                }
            }
            .text_right {
                font-size: 28rpx;
                color: #999999;
                margin: auto 0;
            }
        }
    }
    .footer_box {
        position: fixed;
        width: 100%;
        box-sizing: border-box;
        bottom: 0rpx;
        padding: 30rpx;
        background-color: #fff;

        .btn_group {
            display: flex;
            justify-content: space-between;
            .yd_plain {
                background-color: #fff;
                color: #4566d5;
                border: 1rpx solid #4566d5;
            }
            .yd_btn_primary {
                background-color: #4566d5;
                color: #fff;
            }
            .btn_disable {
                background-color: #d8d8d8;
                color: #fff;
                pointer-events: none;
            }
            button {
                width: 330rpx;
                font-size: 32rpx;
                margin-left: 0;
                margin-right: 0;
            }
        }
    }
}
</style>
