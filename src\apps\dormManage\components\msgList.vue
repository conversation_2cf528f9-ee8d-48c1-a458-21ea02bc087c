<template>
    <view class="list_msg_container">
        <template v-for="(item, index) in list" :key="index">
            <template v-if="item.slot">
                <slot :name="item.slot"></slot>
            </template>
            <view v-else class="item">
                <text class="left">{{ item.name }}</text>
                <text class="right">{{ item.filter ? item.filter(item.value) : item.value || "-" }}</text>
            </view>
        </template>
    </view>
</template>
<script setup>
defineProps({
    list: {
        type: Array,
        default: () => []
    }
})
</script>
<style lang="scss" scoped>
.list_msg_container {
    font-size: 28rpx;
    .item {
        display: flex;
        justify-content: space-between;
        padding: 40rpx 0rpx;
        // margin: 0 28rpx;
        background-color: #fff;
        border-bottom: 1rpx solid #d9d9d9;
        &:last-of-type {
            border-bottom: none;
        }
        .left {
            color: #999999;
        }
        .right {
            color: #333333;
        }
    }
}
</style>
