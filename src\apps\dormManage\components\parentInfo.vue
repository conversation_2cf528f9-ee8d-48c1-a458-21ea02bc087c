<template>
    <div class="parent_info" v-for="(item, index) in list" :key="index">
        <div class="user_info">
            <slot name="userLeft" :data="item">
                <div class="user_left">
                    <div class="avatar">{{ item.studentName?.charAt(0) || "-" }}</div>
                    <span class="user_class">{{ item.studentName }}-{{ item.className }}</span>
                </div>
            </slot>
            <slot name="userRight" :data="item">
                <div class="user_right">{{ item.bedNo }}</div>
            </slot>
        </div>
        <uni-list>
            <uni-list-item v-for="(detail, dIndex) in item?.pageList?.list" :key="index + dIndex">
                <template v-slot:header>
                    <slot name="left" :data="detail">
                        <text class="header">{{ detail.inOutTime }}</text>
                    </slot>
                </template>
                <template v-slot:footer>
                    <slot name="right" :data="detail">
                        <text class="footer">{{ detail.inOut }}</text>
                    </slot>
                </template>
            </uni-list-item>
        </uni-list>
    </div>
</template>

<script setup>
const props = defineProps({
    list: {
        type: Array,
        default: []
    }
})
</script>

<style lang="scss" scoped>
.parent_info {
    .user_info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 22rpx 0rpx;
        .user_left {
            display: flex;
            align-items: center;
            .avatar {
                width: 88rpx;
                height: 88rpx;
                background: var(--primary-color);
                border-radius: 50%;
                font-size: 28rpx;
                font-weight: 400;
                color: #ffffff;
                line-height: 88rpx;
                text-align: center;
                margin-right: 30rpx;
            }
            .user_class {
                font-size: 28rpx;
                font-weight: 400;
                color: #333333;
                line-height: 40rpx;
            }
        }
        .user_right {
            font-size: 28rpx;
            font-weight: 400;
            color: #333333;
            line-height: 40rpx;
        }
    }
    .header {
        font-size: 28rpx;
        font-weight: 400;
        color: #333333;
        line-height: 40rpx;
    }
    .footer {
        font-size: 28rpx;
        font-weight: 400;
        line-height: 40rpx;
    }
}
:deep(.uni-list-item__container) {
    padding: 26rpx 8rpx !important;
}
</style>
