<template>
    <div class="left">
        <view :class="['yd_select', { active_class: isCheck(item.value) }]" v-for="(item, index) in list" :key="index" @click="handleClick(item, index)">
            {{ item.name }}
        </view>
    </div>
</template>
<script setup>
const prop = defineProps({
    list: {
        type: Array,
        default: () => []
    },
    multiple: {
        type: Boolean,
        default: false
    },
    value: {
        type: [String, Array, <PERSON>olean, Number],
        default: ""
    }
})

const emit = defineEmits(["update:value", "handleClick"])

const isCheck = computed(() => (val) => {
    if (typeof prop.value === "object") {
        return prop.value.includes(val)
    } else {
        return prop.value == val
    }
})

const toEmpty = () => {
    prop.list.forEach((i) => (i.checked = false))
}

const getChecked = () => {
    return prop.list.filter((i) => i.checked).map((i) => i.value)
}

const handleClick = (item) => {
    if (prop.multiple) {
        item.checked = !item.checked
        emit("update:value", getChecked())
    } else {
        toEmpty()
        item.checked = !item.checked
        emit("update:value", item.value)
    }
    emit("handleClick", item)
}
</script>
<style lang="scss" scoped>
.left {
    width: 264rpx;
    height: 52rpx;
    background: $uni-bg-color-grey;
    border-radius: 4rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.yd_select {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80rpx;
    height: 44rpx;
    border-radius: 4rpx;
    font-size: 28rpx;
}

.active_class {
    background: #4566d5;
    color: #ffffff;
}
</style>
