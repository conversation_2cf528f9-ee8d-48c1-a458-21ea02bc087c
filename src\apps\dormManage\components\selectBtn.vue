<template>
    <view class="yd_select_box">
        <scroll-view scroll-x="true" style="width: 99vw; white-space: nowrap">
            <view :class="['yd_select', { active_class: isCheck(item.value) }]" v-for="(item, index) in list" :key="index" @click="handleClick(item, index)">
                <view class="item">{{ item.name }}</view>
            </view>
        </scroll-view>
    </view>
</template>
<script setup>
const prop = defineProps({
    list: {
        type: Array,
        default: () => []
    },
    multiple: {
        type: Boolean,
        default: false
    },
    value: {
        type: [String, <PERSON>rra<PERSON>, <PERSON><PERSON><PERSON>, Number],
        default: ""
    }
})

const emit = defineEmits(["update:value", "handleClick"])

const isCheck = computed(() => (val) => {
    if (typeof prop.value === "object") {
        return prop.value.includes(val)
    } else {
        return prop.value == val
    }
})

const toEmpty = () => {
    prop.list.forEach((i) => (i.checked = false))
}

const getChecked = () => {
    return prop.list.filter((i) => i.checked).map((i) => i.value)
}

const handleClick = (item) => {
    if (prop.multiple) {
        item.checked = !item.checked
        emit("update:value", getChecked())
    } else {
        toEmpty()
        item.checked = !item.checked
        emit("update:value", item.value)
    }
    emit("handleClick", item)
}
</script>
<style lang="scss" scoped>
.yd_select_box {
    background-color: #ffffff;
    color: #333333;

    .yd_select {
        display: inline-block;
        cursor: pointer;
        margin: 32rpx 0;
        margin-left: 32rpx;
        &:last-of-type {
            margin-right: 32rpx;
        }
        .item {
            font-size: 28rpx;
            text-align: center;
            padding: 10rpx 34rpx;
            background-color: #fff;
            border-radius: 30rpx;
            border: 1rpx solid #999999;
        }
    }

    .active_class {
        color: #ffffff;
        .item {
            background-color: #4566d5;
            border: 1rpx solid #4566d5;
        }
    }
}
</style>
