<template>
    <view>
        <uni-popup ref="selectPopup" @click.stop type="bottom" :is-mask-click="false" :safe-area="false">
            <div class="week_popup">
                <div class="title">
                    <span class="text">{{ title }}</span>
                    <img class="image" @click="closeFn" src="https://alicdn.1d1j.cn/announcement/20230706/193b257d495e428e9cbe74ff02432f93.png" alt="" />
                </div>
                <uni-list :border="false" v-if="list && list.length > 0">
                    <uni-list-item @click="changeWeek(item)" clickable v-for="(item, index) in list" :key="index">
                        <template v-slot:header>
                            <slot :item="item">
                                <text class="list_item">{{ item.name }}</text>
                            </slot>
                        </template>
                        <template v-slot:footer>
                            <image v-if="item.isCheck" class="select" src="https://alicdn.1d1j.cn/announcement/20230724/85135100b09d4cd88646d4c3803fa0b5.png"></image>
                        </template>
                    </uni-list-item>
                </uni-list>
                <div v-else style="text-align: center; line-height: 140rpx"><yd-empty text="暂无数据" /></div>
            </div>
        </uni-popup>
    </view>
</template>

<script setup>
const selectPopup = ref(null)
const $emit = defineEmits(["closePopup"])
const $props = defineProps({
    list: {
        type: Array,
        default: () => []
    },
    title: {
        type: String,
        default: ""
    },
    multiple: {
        type: Boolean,
        default: false
    }
})

function changeWeek(data) {
    if (!$props.multiple) {
        $props.list.forEach((i) => {
            if (i.name === data.name) {
                return
            } else {
                i.isCheck = false
            }
        })
        data.isCheck = !data.isCheck
    } else {
        data.isCheck = !data.isCheck
    }
    // 如果是单选的时候 选中后则关闭
    !$props.multiple && closeFn()
}

const open = () => {
    selectPopup.value.open()
}

defineExpose({ open })

let value = null
function closeFn() {
    if (!$props.multiple) {
        value = $props.list.find((i) => i.isCheck)
    } else {
        value = $props.list.filter((i) => i.isCheck)
    }
    selectPopup.value.close()
    $emit("closePopup", value)
}
</script>

<style lang="scss" scoped>
// 选择周
.week_popup {
    min-height: 300rpx;
    max-height: 750rpx;
    overflow-y: auto;
    background: #fff;
    border-radius: 20rpx 20rpx 0rpx 0rpx;
    padding-bottom: 120rpx;

    .title {
        height: 100rpx;
        display: flex;
        align-items: center;

        .text {
            font-size: 34rpx;
            font-weight: 600;
            color: #333333;
            line-height: 48rpx;
            width: 100vh;
            text-align: center;
            margin-left: 62rpx;
        }

        .image {
            height: 44rpx;
            width: 44rpx;
            display: inline-block;
            flex-shrink: 0;
            margin-right: 18rpx;
        }
    }

    .select {
        height: 40rpx;
        width: 40rpx;
    }

    .text {
        font-size: 28rpx;
        font-family:
            PingFangSC-Regular,
            PingFang SC;
        font-weight: 400;
        color: #333333;

        .time {
            color: #999999;
        }
    }

    .list_item {
        font-size: 28rpx;
        font-weight: 400;
        color: #333333;
        line-height: 40rpx;
    }
}
</style>
