<template>
    <view class="swiper_wrap_box">
        <view class="swiper">
            <view class="swiper-item">
                <view class="main yd_circularProgress">
                    <l-circular-progress canvasId="canvasId" bgCanvasId="bgCanvasId" :fontShow="false" :percent="parseInt((swiperData.normalNum / swiperData.totalNum) * 100)" type="halfCircular" :lineWidth="14" progressColor="#4566D5" bgColor="#f0f2f5" :boxWidth="240" :boxHeight="150">
                        <view class="yd_circular_box">
                            <view>
                                <text class="left">正常归寝 {{ swiperData.normalNum }}</text> /
                                <text>应归 {{ swiperData.totalNum }}</text>
                            </view>
                        </view>
                    </l-circular-progress>
                </view>
                <view class="footer">
                    <view class="footer_box"
                        >晚归 <text class="font_weight">{{ swiperData.beLateNum }}</text> 人
                    </view>
                    <view class="footer_box"
                        >请假 <text class="font_weight">{{ swiperData.leaveNum }}</text> 人</view
                    >
                    <view class="footer_box danger" :style="{ color: dayjs().isBefore(dayjs(swiperData.endTime)) ? '#f5222d' : '#5534F0' }"
                        >{{ dayjs().isBefore(dayjs(swiperData.endTime)) ? "未归寝" : "未打卡" }}
                        <text class="font_weight">{{ dayjs().isBefore(dayjs(swiperData.endTime)) ? swiperData.absenteeismNum : swiperData.notSignInNum }}</text>
                        人</view
                    >
                </view>
            </view>
        </view>
    </view>
</template>

<script setup>
import LCircularProgress from "@/subModules/components/l-circular-progress/components/l-circular-progress/l-circular-progress.vue"
import dayjs from "dayjs"

defineProps({
    swiperData: {
        type: Object,
        default: () => ({})
    }
})

const current = ref(0)
const indicatorDots = ref(true)

const change = (e) => {
    current.value = e.detail.current
}
</script>

<style lang="scss">
.swiper_wrap_box {
    padding: 30rpx;
    padding-bottom: 0rpx;
    background: #fff;
    margin-bottom: 30rpx;

    .swiper {
        height: 350rpx;
        .swiper-item {
            position: relative;
            .top {
                display: flex;
                justify-content: space-between;
                .right_text {
                    position: relative;
                    text-align: right;
                    width: 250rpx;
                    padding-right: 30rpx;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    -o-text-overflow: ellipsis;
                    &::after {
                        content: "";
                        width: 0;
                        height: 0;
                        display: inline-block;
                        border: 14rpx solid transparent;
                        border-top-color: #4566d5;
                        position: absolute;
                        right: 0rpx;
                        top: 15rpx;
                    }
                }
            }
            .main {
                position: relative;
                .top_msg {
                    text-align: center;
                    margin-bottom: 10rpx;
                }
                .yd_circularProgress {
                    margin: 0 auto;
                    position: relative;
                }
            }
            .footer {
                display: flex;
                position: absolute;
                bottom: -50rpx;
                left: 0;
                .footer_box {
                    width: 230rpx;
                    text-align: center;
                    .font_weight {
                        font-weight: 600;
                    }
                }
                .center_box {
                    border-left: 1px solid #d9d9d9;
                    border-right: 1px solid #d9d9d9;
                }
            }
        }
    }
}
</style>
