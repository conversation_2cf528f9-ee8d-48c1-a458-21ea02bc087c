<template>
  <view>
    <uni-popup style="border-radius: 20px" ref="tipPopup" @click.stop background-color="#fff" :is-mask-click="false" :safe-area="false">
      <view class="tip_popup">
        <div class="title" v-if="showTitle">{{ title }}</div>
        <div class="tip_text">
          <slot name="tipText">{{ tipText }}</slot>
        </div>
        <div class="footer">
          <div class="btn" @click="close">{{ closeBtnText }}</div>
          <div class="divider"></div>
          <div class="btn" @click="confirm">{{ confirmBtnText }}</div>
        </div>
      </view>
    </uni-popup>
    <!-- 
        // 参数
        // showTitle      |  是否显示标题  |  默认false       | 布尔值
        // ——————————————————————————————————————————————————————————————
        // tipText        |  提示文案      |  默认为提示内容   | 字符串
        // ——————————————————————————————————————————————————————————————
        // title          |  标题          |  默认为空        | 字符串
        // ——————————————————————————————————————————————————————————————
        // closeBtnText   |  取消按钮文案   |  默认为空        | 字符串
        // ——————————————————————————————————————————————————————————————
        // confirmBtnText |  确认按钮文案   |  默认为空        | 字符串

        // 事件
        // close   | 取消按钮事件
        // ——————————————————————————————————————————————————————————————
        // confirm | 确认按钮事件
        
        // 自定义 插槽 
        // tipText  | 中间文案插槽名称
     -->
  </view>
</template>

<script setup>
import { computed, ref } from 'vue'
const tipPopup = ref(null)
const $emit = defineEmits(['closePopup', 'confirm'])
const $props = defineProps({
  showTitle: {
    type: Boolean,
    default: false,
  },
  tipText: {
    type: String,
    default: '提示内容',
  },
  title: {
    type: String,
    default: '',
  },
  closeBtnText: {
    type: String,
    default: '取消',
  },
  confirmBtnText: {
    type: String,
    default: '确认',
  },
})

const showTitle = computed(() => {
  return $props.showTitle
})

const tipText = computed(() => {
  return $props.tipText
})

const title = computed(() => {
  return $props.title
})

const closeBtnText = computed(() => {
  return $props.closeBtnText
})

const confirmBtnText = computed(() => {
  return $props.confirmBtnText
})

const open = () => {
  tipPopup.value.open()
}

const closeDialog = () => {
  tipPopup.value.close()
}

defineExpose({ open, closeDialog })

function close() {
  $emit('closePopup')
}

function confirm() {
  $emit('confirm')
}
</script>

<style lang="scss" scoped>
.tip_popup {
  width: 690rpx;
  min-height: 100rpx;
  background: #ffffff;
  border-radius: 20rpx;

  .title {
    margin-top: 40rpx;
    width: 100%;
    background: #ffffff;
    border-radius: 20rpx 20rpx 0rpx 0rpx;
    text-align: center;
    font-size: 34rpx;
    font-weight: 600;
    color: #333333;
  }
  .tip_text {
    margin: 40rpx 0rpx 40rpx 0rpx;
    min-height: 108rpx;
    padding: 0rpx 40rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    font-size: 34rpx;
    font-weight: 400;
    color: #333333;
    line-height: 48rpx;
  }
  .footer {
    height: 100rpx;
    background: #ffffff;
    border-radius: 0rpx 0rpx 20rpx 20rpx;
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    font-size: 28rpx;
    font-weight: 400;
    color: #4566d5;
    line-height: 40rpx;
    border-top: 1rpx solid #d8d8d8;
    .btn {
      width: 49%;
      text-align: center;
    }
    .divider {
      width: 2rpx;
      background: #d8d8d8;
      height: 100%;
    }
  }
}
:deep(.uni-popup__wrapper) {
  border-radius: 20rpx;
}
</style>
