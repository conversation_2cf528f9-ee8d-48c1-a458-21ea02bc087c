<template>
    <div class="score_list">
        <div class="my_class_score">
            <div class="left">
                <span class="text">本班排名：</span>
                <span class="num">{{ myClass?.ranking }}</span>
            </div>
            <div class="middle">
                <img class="image" :src="myClass.avatar ? myClass.avatar : 'https://alicdn.1d1j.cn/announcement/20230706/406e9511ae104f879ccf65ef3aac30c1.png'" alt="" />
                <span class="gradeclass">{{ myClass?.name }}</span>
            </div>
            <div class="right">
                <span class="fraction">{{ myClass?.score || "" }}分</span>
            </div>
        </div>
        <div class="score_item" @click="clickItem(item)" v-for="(item, index) in list" :key="index">
            <div class="left">
                <span class="week">{{ item.week }}</span>
                <span class="time">（{{ item.date }}）</span>
            </div>
            <div class="middle" :class="item.score < 0 ? 'reduce' : item.score > 0 ? 'add' : 'middle'">{{ item.score }}分</div>
            <div class="right">
                <span class="text"> {{ item.totalScore }}</span>
                <img class="image" src="https://alicdn.1d1j.cn/announcement/20230706/a54929238cee49178b6210e4c1b42dce.png" alt="" />
            </div>
        </div>
    </div>
</template>

<script setup>
import { shallowRef, watch, ref, computed } from "vue"
const props = defineProps({
    list: {
        type: Array,
        default: () => []
    },
    myClass: {
        type: Object,
        default: () => {}
    }
})
const emit = defineEmits(["clickItem"])

const list = computed(() => {
    return props.list
})

const myClass = computed(() => {
    return props.myClass
})
console.log(myClass.value)
function clickItem(item) {
    emit("clickItem", item)
}
</script>

<style lang="scss" scoped>
.score_list {
    background: $uni-bg-color-grey;
    min-height: calc(100vh - 150rpx);
    max-height: calc(100vh - 150rpx);
    overflow-y: auto;

    padding: 30rpx;
    .my_class_score {
        height: 40rpx;
        background: #4566d5;
        border-radius: 50rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 30rpx;
        .left {
            display: flex;
            align-items: center;

            .text {
                font-size: 28rpx;
                font-family:
                    PingFangSC-Regular,
                    PingFang SC;
                font-weight: 400;
                color: #ffffff;
            }

            .num {
                font-size: 44rpx;
                font-family: YouSheBiaoTiYuan;
                color: #ffffff;
            }
        }

        .middle {
            display: flex;
            align-items: center;
            max-width: 300rpx;

            .image {
                width: 50rpx;
                height: 50rpx;
                border-radius: 50%;
                margin-right: 20rpx;
            }

            .gradeclass {
                font-size: 27rpx;
                font-family:
                    PingFangSC-Regular,
                    PingFang SC;
                font-weight: 400;
                color: #ffffff;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
                word-break: break-all;
            }
        }

        .right {
            display: flex;
            align-items: center;

            .fraction {
                font-size: 30rpx;
                font-family:
                    PingFangSC-Regular,
                    PingFang SC;
                font-weight: 400;
                color: #ffffff;
            }
        }
    }
    .score_item {
        margin: 30rpx 0rpx;
        height: 40rpx;
        background: #ffffff;
        border-radius: 50rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 30rpx;
        .left {
            height: 100%;
            display: flex;
            align-items: center;
            .week {
                font-size: 28rpx;
                font-family:
                    PingFangSC-Regular,
                    PingFang SC;
                font-weight: 400;
                color: #333333;
                line-height: 40rpx;
            }
            .time {
                font-size: 28rpx;
                font-family:
                    PingFangSC-Regular,
                    PingFang SC;
                font-weight: 400;
                color: #999999;
                line-height: 40rpx;
            }
        }
        .middle {
            display: flex;
            align-items: center;
            height: 100%;
            font-size: 28rpx;
            font-family:
                PingFangSC-Regular,
                PingFang SC;
            font-weight: 400;
            line-height: 40rpx;
            color: #333333;
        }
        .add {
            color: #4566d5;
        }
        .reduce {
            color: #f5222d;
        }
        .right {
            height: 100%;
            display: flex;
            align-items: center;
            .text {
                font-size: 28rpx;
                font-family:
                    PingFangSC-Regular,
                    PingFang SC;
                font-weight: 400;
                color: #333333;
                line-height: 40rpx;
            }
            .image {
                width: 36rpx;
                height: 36rpx;
            }
        }
    }
}
</style>
