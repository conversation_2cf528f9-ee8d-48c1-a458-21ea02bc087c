<template>
    <div>
        <uni-popup ref="selectPopup" type="bottom" :mask-click="false" :safe-area="false">
            <div class="week_popup" @touchmove.stop @touch.stop>
                <div class="title">
                    <span class="text"> {{ cycleType === 1 ? "周次选择" : "月次选择" }}</span>
                    <uni-icons class="close_icons" type="closeempty" size="22" color="#333" @click="closeFn"></uni-icons>
                </div>
                <uni-list :border="false" class="list">
                    <uni-list-item class="list_item" @click="changeWeek(item)" clickable v-for="(item, index) in list" :key="index">
                        <template v-slot:header>
                            <span class="text"
                                >{{ item.seq || "" }}
                                <span class="time">（{{ item.startTime + "-" + item.endTime }}）</span>
                            </span>
                        </template>
                        <template v-slot:footer>
                            <image v-if="item.checked" class="select" src="https://alicdn.1d1j.cn/announcement/20230724/85135100b09d4cd88646d4c3803fa0b5.png"></image>
                        </template>
                    </uni-list-item>
                </uni-list>
            </div>
        </uni-popup>
    </div>
</template>

<script setup>
import { ref, reactive, watch, computed, shallowRef, toRaw } from "vue"
const state = reactive({})
const selectPopup = ref()
const $emit = defineEmits(["closePopup", "selectPopup"])
const $props = defineProps({
    isShow: {
        type: Boolean,
        default: false
    },
    list: {
        type: Array,
        default: () => []
    },
    // 月排行还是周排行 1-周 2-月
    cycleType: {
        type: Number,
        default: 1
    }
})

const list = computed(() => {
    return $props.list
})

function changeWeek(data) {
    let title = $props.cycleType === 1 ? "本周德育评价暂未开始" : "本月德育评价暂未开始"
    if (data.id === -1) {
        uni.showToast({
            title,
            duration: 2000,
            icon: "none"
        })
    } else {
        $emit("selectPopup", data)
    }
}

const cycleType = computed(() => {
    return $props.cycleType
})

watch(
    () => $props.isShow,
    (value) => {
        value ? selectPopup.value.open() : selectPopup.value.close()
    }
)

function closeFn() {
    $emit("closePopup", false)
}
</script>

<style lang="scss" scoped>
// 选择周
.week_popup {
    min-height: 300rpx;
    max-height: 750rpx;
    background: #fff;
    border-radius: 20rpx 20rpx 0rpx 0rpx;
    padding-bottom: 90rpx;

    .title {
        height: 100rpx;
        display: flex;
        align-items: center;

        .text {
            font-size: 34rpx;
            font-weight: 500;
            color: #333333;
            line-height: 48rpx;
            width: 100vh;
            text-align: center;
        }

        .close_icons {
            margin-right: 18rpx;
        }
    }
    .list {
        max-height: 700rpx;
        overflow-y: auto;
        .list_item {
            min-height: 84rpx;
        }
    }

    .select {
        height: 40rpx;
        width: 40rpx;
    }

    .text {
        font-size: 28rpx;
        font-family:
            PingFangSC-Regular,
            PingFang SC;
        font-weight: 400;
        color: #333333;
        line-height: 40rpx;

        .time {
            color: #999999;
        }
    }
}
</style>
