<template>
    <div class="dorm_ranking">
        <div class="ranking_box">
            <div class="ranking_background"></div>
            <div class="title"></div>
            <div class="change_week" v-if="state.newest" @click="selectWeekFn">
                <span class="text">{{ state.newest }}</span>
                <img class="image" src="https://alicdn.1d1j.cn/announcement/20230725/75cd791b18ff4ce289e965ef9998665b.png" alt="" />
            </div>
            <div class="first_ranking">
                <swiper class="swiper" v-if="state.firstRanking && state.firstRanking.length > 0" circular :indicator-dots="false" :autoplay="true" :interval="3000" :vertical="true">
                    <swiper-item v-for="(item, index) in state.firstRanking" :key="item?.id || index" @click="weekInfo(item)">
                        <div class="score">{{ item.score ? item.score + "分" : "-" }}</div>
                        <div class="room">{{ item.name || "-" }}</div>
                    </swiper-item>
                </swiper>
            </div>
            <div class="second_ranking">
                <swiper class="swiper" v-if="state.secondRanking && state.secondRanking.length > 0" circular :indicator-dots="false" :autoplay="true" :interval="3000" :vertical="true">
                    <swiper-item v-for="(item, index) in state.secondRanking" :key="item?.id || index" @click="weekInfo(item)">
                        <div class="score">{{ item.score ? item.score + "分" : "-" }}</div>
                        <div class="room">{{ item.name || "-" }}</div>
                    </swiper-item>
                </swiper>
            </div>
            <div class="third_ranking">
                <swiper class="swiper" v-if="state.thirdRanking && state.thirdRanking.length > 0" circular :indicator-dots="false" :autoplay="true" :interval="3000" :vertical="true">
                    <swiper-item v-for="(item, index) in state.thirdRanking" :key="item?.id || index" @click="weekInfo(item)">
                        <div class="score">{{ item.score ? item.score + "分" : "-" }}</div>
                        <div class="room">{{ item.name || "-" }}</div>
                    </swiper-item>
                </swiper>
            </div>
            <scroll-view class="ranking_list" :scroll-y="true" :scrollTop="state.scrollTop" :show-scrollbar="false" :lower-threshold="50" :scroll-with-animation="true">
                <div class="item_th">
                    <span class="text left">排名</span>
                    <span class="text middle">宿舍号</span>
                    <span class="text right">得分</span>
                </div>
                <uni-list class="list" @touchmove.stop @touch.stop :border="false" v-if="state.rankingList && state.rankingList.length > 0">
                    <uni-list-item v-for="(item, index) in state.rankingList" style="min-height: 90rpx" :key="index">
                        <template v-slot:header>
                            <view class="item_left">{{ item.ranking }}</view>
                        </template>
                        <template v-slot:body>
                            <text class="item_body">{{ item.name }}</text>
                        </template>
                        <template v-slot:footer>
                            <div class="item_right" @click="weekInfo(item)">
                                <span class="text">{{ item.score }}分</span>
                                <uni-icons type="forward" size="18" color="#6D8FFF" />
                            </div>
                        </template>
                    </uni-list-item>
                </uni-list>
                <div class="not_set" v-else>
                    <span class="text">暂时还没有排名哦</span>
                </div>
            </scroll-view>
        </div>
        <div class="footer_box" v-if="state.info.canScore && state.info.identityType === 1">
            <button @click="dormScore">去评分</button>
        </div>
        <SelectPopup :cycleType="state.info.cycleType" :isShow="state.isShow" :list="state.info.cycleList" @closePopup="closeFn" @selectPopup="changeWeek" />
    </div>
</template>

<script setup>
import SelectPopup from "./components/selectPopup.vue"
import { onPullDownRefresh } from "@dcloudio/uni-app"

const state = reactive({
    scrollTop: 0,
    firstRanking: [],
    secondRanking: [],
    thirdRanking: [],
    rankingList: [],
    info: {
        identityType: 1, // 0=学生 1=老师 2=家长
        schoolId: "", // 学校id
        fromGroupId: "", // 评分组标识
        fromPersonId: "", // 评分人标识
        activityId: "", // 活动标识
        scoreTargetList: [], // 班级列表
        cycleType: 1, // 1-周 2-月
        canScore: false, // 是否可以评分
        cycleId: "", // 周数/月数
        cycleList: [] // 切换周列表
    },
    isShow: false, // 弹框状态
    newest: "" // 当前周
})
const list = [
    // { ranking: "1", room: "A210", score: 90 },
    // { ranking: "2", room: "A209", score: 80 },
    // { ranking: "3", room: "A202", score: 70 },
    // { ranking: "3", room: "A202", score: 70 },
    // { ranking: "3", room: "A202", score: 70 },
    // { ranking: "3", room: "A202", score: 70 },
]

const selectWeekRef = ref(null)
const weekList = ref([
    { name: "第一周", value: 1 },
    { name: "第二周", value: 2 }
])

function weekNewestFn(list) {
    let week = []
    list.forEach((item) => {
        if (item.newest) {
            week.push(item.seq)
        }
    })
    return week[0]
}

function rankingFn(list, ranking) {
    let arr = []
    list.map((item) => {
        if (item.ranking == ranking) {
            arr.push(item)
        }
    })
    return arr
}

function removeTopThree(list) {
    let arr = []
    let topThreeArr = ["1", "2", "3"]
    list.map((item) => {
        if (!topThreeArr.includes(item.ranking)) {
            arr.push(item)
        }
    })
    return arr
}

function getDataFn(cycleId, title) {
    const params = {
        cycleId: cycleId ? cycleId : state.info.cycleId, // 周数/月数
        type: 2 // type 评价类型(1-班级德育 2-宿舍德育)
    }
    http.post("/app/moralEducationActivityMobile/competition", params)
        .then((res) => {
            if (res?.data) {
                state.notSet = false
                const { cycleList, currentObjList, resultList } = res?.data
                state.info = res.data
                state.info.cycleId = cycleId ? cycleId : res.data.cycleId
                state.firstRanking = rankingFn(resultList, "1")
                state.secondRanking = rankingFn(resultList, "2")
                state.thirdRanking = rankingFn(resultList, "3")
                state.newest = !cycleId ? weekNewestFn(cycleList || []) : title
                state.classId = currentObjList[0]?.childrenId || ""
                state.rankingList = removeTopThree(resultList) || []
                // state.rankingList = resultList || [];
            } else {
                state.notSet = true
            }
        })
        .catch(() => {
            state.notSet = true
        })
}
function selectWeekFn() {
    if (!state.notSet) {
        state.isShow = true
    } else {
        uni.showToast({
            title: "管理员暂未设置德育评价",
            duration: 2000,
            icon: "none"
        })
    }
}

function closeFn() {
    state.isShow = false
}

function changeWeek(data) {
    state.info.cycleId = data.id
    state.info.cycleList = state.info.cycleList.map((item) => {
        return {
            ...item,
            checked: item.id === data.id
        }
    })
    getDataFn(data.id, data.seq)
    setTimeout(() => {
        state.isShow = false
    }, 500)
}

function weekInfo(item) {
    let params = {
        ...item,
        activityId: state.info.activityId, // 活动标识
        cycleId: state.info.cycleId, // 周期标识
        targetId: item.id, // 我的班级
        title: state.newest, // 第几周？
        cycleType: state.info.cycleType // 当前为月？/周？
    }
    navigateTo({
        url: "/apps/dormManage/dormRanking/weekInfo",
        query: params
    })
}

function dormScore() {
    navigateTo({
        url: "/apps/dormManage/dormRanking/score"
    })
}

onPullDownRefresh(() => {
    getDataFn()
    setTimeout(function () {
        uni.stopPullDownRefresh()
    }, 1000)
})

onShow(() => {
    getDataFn()
})
</script>

<style lang="scss" scoped>
// 头部
.head {
    .title_box {
        width: 100%;
        text-align: center;
        line-height: 88rpx;
        font-size: 34rpx;
        font-weight: 500;
        color: #333333;
    }
}
.ranking_box {
    height: 460rpx;
    background: #bacaff;
    position: relative;
    .change_week {
        position: absolute;
        top: 20rpx;
        right: 0rpx;
        width: 152rpx;
        height: 56rpx;
        background: #ffffff;
        border-radius: 28rpx 0rpx 0rpx 28rpx;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        .text {
            font-size: 28rpx;
            font-weight: 400;
            color: #333333;
            line-height: 40rpx;
        }

        .image {
            width: 44rpx;
            height: 44rpx;
        }
    }
    .ranking_background {
        width: 334rpx;
        height: 176rpx;
        background: url("https://alicdn.1d1j.cn/announcement/20230725/c7252b16993f474c99f0f340690952bb.png") no-repeat;
        background-size: cover;
        position: absolute;
        top: 40rpx;
        right: 30rpx;
    }
    .title {
        width: 480rpx;
        height: 46rpx;

        background: url("https://alicdn.1d1j.cn/announcement/20230725/e92eb6d4d0e94ecca41ea3f1c2dc2fb9.png") no-repeat;
        background-size: cover;
        position: absolute;
        top: 68rpx;
        left: 52rpx;
    }
    .first_ranking,
    .second_ranking,
    .third_ranking {
        .swiper {
            // pointer-events: none;
        }

        .score {
            max-width: 96%;
            margin: 8rpx auto;
            text-align: center;
            font-size: 29rpx;
            font-weight: 600;
            color: #262626;
            line-height: 40rpx;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            word-break: break-all;
        }
        .room {
            max-width: 96%;
            margin: auto;
            text-align: center;
            font-size: 24rpx;
            font-weight: 400;
            color: #262626;
            line-height: 34rpx;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            word-break: break-all;
        }
        .photo_item {
            overflow: hidden;
            position: absolute;
            border-radius: 50%;
            font-size: 6rpx;
            font-weight: 400;
            color: #bfbfbf;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;
            flex-direction: column;
        }
    }
    .first_ranking {
        width: 210rpx;
        height: 236rpx;
        background: url("https://alicdn.1d1j.cn/announcement/20230824/9b4de5a2367c4adc8ce3ebc96d936843.png") no-repeat;
        background-size: cover;
        position: absolute;
        bottom: 30rpx;
        left: 272rpx;
        // .room {
        //   color: #6d8fff;
        // }
        .swiper {
            height: 46%;
            margin-top: 110rpx;
        }
        .photo_item {
            top: 2%;
            left: 21%;
            height: 120rpx;
            width: 120rpx;
        }
    }
    .second_ranking {
        width: 210rpx;
        height: 236rpx;
        background: url("https://alicdn.1d1j.cn/announcement/20230824/90d896a78eaa4089a24c6ae97bb57e05.png") no-repeat;
        background-size: cover;
        position: absolute;
        bottom: 10rpx;
        left: 30rpx;
        // .room {
        //   top: 40rpx;
        //   color: #ffdb6d;
        // }
        .swiper {
            height: 42%;
            margin-top: 94rpx;
        }
        .photo_item {
            top: 2%;
            left: 25%;
            height: 104rpx;
            width: 104rpx;
        }
    }
    .third_ranking {
        width: 210rpx;
        height: 236rpx;
        background: url("https://alicdn.1d1j.cn/announcement/20230824/f4c648e6dbfb48ceaa389aa91df68c5f.png") no-repeat;
        background-size: cover;
        position: absolute;
        bottom: 0rpx;
        right: 30rpx;
        // .room {
        //   color: #fca979;
        // }
        .swiper {
            height: 37%;
            margin-top: 90rpx;
        }
        .photo_item {
            top: 5%;
            left: 25%;
            height: 104rpx;
            width: 104rpx;
        }
    }
    .ranking_list {
        width: 92%;
        position: absolute;
        top: 420rpx;
        left: 0rpx;
        max-height: calc(100vh - 580rpx);
        background: #ffffff;
        border-radius: 40rpx 40rpx 0rpx 0rpx;
        padding: 30rpx;
        .item_th {
            width: 82%;
            height: 88rpx;
            background: #eef2ff;
            border-radius: 20rpx 20rpx 0rpx 0rpx;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0rpx 86rpx 0rpx 36rpx;
            .middle {
                text-align: center;
            }
            .right {
                text-align: right;
            }
            .left {
                text-align: left;
            }
            .text {
                font-size: 28rpx;
                width: 140rpx;
                font-family:
                    PingFangSC-Semibold,
                    PingFang SC;
                font-weight: 600;
                color: #262626;
                line-height: 40rpx;
            }
        }
        .list {
            max-height: calc(100vh - 790rpx);
            min-height: calc(100vh - 790rpx);
            overflow-y: auto;
        }
        .item_left {
            text-align: left;
            padding-left: 10rpx;
            width: 27%;
            font-size: 36rpx;
            font-weight: normal;
            color: #6d8fff;
            line-height: 54rpx;
        }
        .item_body {
            width: 35%;
            font-size: 28rpx;
            font-weight: 400;
            text-align: center;
            color: #595959;
            line-height: 54rpx;
        }
        .item_right {
            font-size: 28rpx;
            width: 35%;
            font-weight: 400;
            text-align: right;
            color: #595959;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            .text {
                margin-right: 20rpx;
            }
        }
    }
}
.dorm_ranking {
    min-height: 94vh;
    position: relative;
    .footer_box {
        width: 92%;
        height: 106rpx;
        background: #ffffff;
        position: absolute;
        bottom: 0rpx;
        left: 0rpx;
        padding: 30rpx;

        button {
            background: #4566d5;
            border-radius: 10rpx;
            font-size: 32rpx;
            height: 92rpx;
            font-weight: 400;
            color: #ffffff;
            line-height: 92rpx;
        }
    }
}
.not_set {
    height: 40vh;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    .image {
        width: 556rpx;
        height: 348rpx;
    }
    .text {
        font-size: 26rpx;
        font-weight: 400;
        color: #999999;
        line-height: 36rpx;
        padding-top: 110rpx;
    }
}
</style>
