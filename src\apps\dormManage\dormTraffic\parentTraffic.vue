<template>
    <div class="parent_traffic">
        <ParentInfo :list="state.trafficList" v-if="state.trafficList && state.trafficList.length > 0">
            <template #right="{ data }">
                <span class="info_right" :class="typeColor[data.inOut]"> {{ inOutList[data.inOut] + "寝" }}</span>
            </template>
        </ParentInfo>
        <uni-load-more iconType="auto" :status="status" />
    </div>
</template>

<script setup>
import { reactive } from "vue"
import { onLoad, onUnload } from "@dcloudio/uni-app"
import { inOutList } from "../utils/staticData"
import useQueryList from "../hooks/useQueryList.js"
import ParentInfo from "../components/parentInfo"

const { getList, status } = useQueryList()
const typeColor = { 1: "type_enter", 2: "type_leave" }
const state = reactive({
    trafficList: [],
    studentId: null
})

onLoad((params) => {
    Object.keys(params).forEach((key) => {
        params[key] = decodeURIComponent(params[key])
    })
    state.studentId = params.studentId || null
    getTraffic({ studentId: params.studentId })
})

function getPage(params) {
    return http.post("/app/v2/pass/app/pageAppHomeStudentDormPass", params)
}

const getTraffic = async (params) => {
    const { data } = await getList(getPage, params)
    if (data) {
        state.trafficList = [data]
    }
}
</script>

<style lang="scss" scoped>
.parent_traffic {
    padding: 0rpx 28rpx;
    .info_right {
        font-size: 28rpx;
        font-weight: 400;
        line-height: 40rpx;
    }
    .type_leave {
        color: var(--primary-color);
    }
    .type_enter {
        color: #faad14;
    }
}
</style>
