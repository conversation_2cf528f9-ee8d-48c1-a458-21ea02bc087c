<template>
    <div class="dorm_traffic_info">
        <div class="div">
            <div class="room_info">
                <span class="room_name"> 寝室号：{{ state.infoData.roomNum || "-" }}（{{ state.infoData.peppleNum || 0 }}人间） </span>
                <div class="traffic_info">
                    <div class="traffic_info_item">通行方向：{{ state.infoData.direction ? inOutList[state.infoData.direction] : "-" }}</div>
                    <div class="traffic_info_item">通行方式：{{ state.infoData.clockWay ? modePassageList[state.infoData.clockWay] : "-" }}</div>
                    <div class="traffic_info_item">设备类型：{{ state.infoData.equipmentType ? equipmentTypeList[state.infoData.equipmentType] : "-" }}</div>
                    <div class="traffic_info_item">通行规则：{{ state.infoData.ruleName || "-" }}</div>
                </div>
            </div>
        </div>

        <div class="count_info">
            <div class="number_people people_in_bed">
                <span class="num">{{ state.infoData.inCount || 0 }}</span>
                <span>当前在寝人数</span>
            </div>
            <div class="number_people people_not_present">
                <span class="num">{{ state.infoData.outCount || 0 }}</span>
                <span>当前出寝人数</span>
            </div>
        </div>
        <div class="info_box">
            <CollapseList :list="state.dormDetails">
                <template #left="{ data }">{{ data.roomNum }} </template>
                <template #body="{ data }">
                    <div class="body_text">{{ data.userName }}</div>
                </template>
                <template #right="{ data }">{{ data.className }}</template>
                <template #content="{ childList }">
                    <div class="info">
                        <div class="title_box">
                            <span class="text">详细记录</span>
                            <span class="count"> 今日通行{{ childList.trafficDetailList.length }}次 </span>
                        </div>
                        <div class="info_list">
                            <div class="info_item" v-for="(item, index) in childList.trafficDetailList" :key="index">
                                <span class="text" :style="index + 1 === childList.trafficDetailList.length ? 'border: none' : ''">
                                    <span class="type" :class="{ '1': 'type_enter', '2': 'type_leave' }[item.direction]">{{ inOutList[item.direction] }}</span>

                                    <div v-if="index == 0 || index === childList.trafficDetailList.length - 1" class="earliest_latest">
                                        {{ index && index === childList.trafficDetailList.length - 1 ? "最早" : "最晚" }}
                                    </div>

                                    <i class="icon" :class="{ '1': 'icon_enter', '2': 'icon_leave' }[item.direction]"></i>
                                </span>

                                <div class="content">
                                    <span class="content_text">{{ item.time }}</span>
                                    <span class="content_place">{{ item.deviceName }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
            </CollapseList>
        </div>
    </div>
</template>

<script setup>
import CollapseList from "../components/collapseList.vue"
import { inOutList, equipmentTypeList, modePassageList } from "../utils/staticData"
const state = reactive({
    dormSiteId: "",
    queryDate: "",
    infoData: {},
    dormDetails: []
})

onLoad((params) => {
    Object.keys(params).forEach((key) => {
        state[key] = decodeURIComponent(params[key])
    })
})

function getInfo() {
    http.post("/app/dorm/traffic/statistics/detail", {
        dormSiteId: state.dormSiteId,
        queryDate: state.queryDate
    }).then((res) => {
        state.infoData = res.data
    })
}

function getInfoList() {
    http.post("/app/dorm/traffic/statistics/detailList", {
        queryType: 4, // 固定值：代表宿舍 传4
        idType: 3, // 固定值 传3 （不知道是啥 后端说固定传）
        dormSiteId: state.dormSiteId, // 寝室ID
        queryDate: state.queryDate // 查询时间
    }).then(({ data }) => {
        state.dormDetails = data
    })
}

onMounted(() => {
    getInfo()
    getInfoList()
})
</script>

<style lang="scss" scoped>
.dorm_traffic_info {
    min-height: 94vh;
    background-color: $uni-bg-color-grey;

    .div {
        background-color: #fff;
    }

    .room_info {
        background-color: #edf1ff;
        height: 272rpx;
        border-radius: 0rpx 0rpx 20rpx 20rpx;

        .room_name {
            font-size: 28rpx;
            font-weight: 600;
            height: 100rpx;
            color: #333333;
            line-height: 100rpx;
            padding: 0rpx 30rpx;
        }

        .traffic_info {
            padding: 30rpx;
            display: flex;
            flex-wrap: wrap;
            border-top: 1rpx solid #d9d9d9;
            border-radius: 0rpx 0rpx 20rpx 20rpx;

            .traffic_info_item {
                width: 50%;
                font-size: 28rpx;
                font-weight: 400;
                color: #333333;
                line-height: 40rpx;
                margin-bottom: 16rpx;
            }
        }
    }

    .count_info {
        background: #ffffff;

        padding: 30rpx;
        display: flex;
        justify-content: space-between;

        .number_people {
            width: 330rpx;
            height: 164rpx;
            border-radius: 20rpx;
            font-size: 28rpx;
            font-weight: 400;
            line-height: 40rpx;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;

            .num {
                font-size: 40rpx;
                line-height: 58rpx;
            }
        }

        .people_in_bed {
            background: #edf1ff;
            color: #4566d5;
        }

        .people_not_present {
            background: #fff7e6;
            color: #faad14;
        }
    }

    .info_box {
        margin-top: 20rpx;

        .right_data {
            font-size: 28rpx;
            font-weight: 400;
            color: #faad14;
            line-height: 40rpx;
        }

        .info {
            min-height: 242rpx;
            background: $uni-bg-color-grey;
            border-radius: 20rpx;
            margin: 28rpx;

            .title_box {
                height: 88rpx;
                border-bottom: 1rpx solid #d9d9d9;
                display: flex;
                align-items: center;
                justify-content: space-between;
                color: #333333;
                padding: 0rpx 24rpx;

                .text {
                    font-size: 28rpx;
                    font-weight: 600;
                    line-height: 40rpx;
                }

                .count {
                    font-size: 26rpx;
                    font-weight: 400;
                    line-height: 36rpx;
                }
            }

            .info_list {
                display: flex;
                flex-direction: column;
                padding: 30rpx 28rpx 0rpx 28rpx;

                .info_item {
                    display: flex;
                    width: 100%;

                    .text {
                        font-size: 28rpx;
                        padding: 0rpx 36rpx;
                        font-weight: 400;
                        line-height: 40rpx;
                        border-right: 1rpx solid #d9d9d9;
                        position: relative;

                        .type {
                            position: absolute;
                            top: -5px;
                            left: 6rpx;
                            font-size: 28rpx;
                            font-weight: 400;
                            line-height: 40rpx;
                        }

                        .icon {
                            position: absolute;
                            top: 0rpx;
                            right: -9rpx;
                            width: 16rpx;
                            height: 16rpx;
                            border-radius: 50%;
                        }

                        .icon_leave {
                            background: #4566d5;
                        }

                        .icon_enter {
                            background: #faad14;
                        }

                        .earliest_latest {
                            position: absolute;
                            top: 18px;
                            left: -14rpx;
                            width: 64rpx;
                            height: 28rpx;
                            text-align: center;
                            border-radius: 14rpx;
                            border: 1rpx solid #333333;
                            font-size: 20rpx;
                            font-weight: 400;
                            color: #333333;
                            line-height: 28rpx;
                        }
                    }

                    .content {
                        display: flex;
                        flex-direction: column;
                        padding: 0rpx 0rpx 60rpx 28rpx;
                        margin-top: -12rpx;

                        .content_text {
                            font-size: 28rpx;
                            font-weight: 400;
                            color: #333333;
                            line-height: 40rpx;
                        }

                        .content_place {
                            font-size: 26rpx;
                            font-weight: 400;
                            color: #666666;
                            line-height: 36rpx;
                        }
                    }
                }
            }
        }

        .type_leave {
            color: #4566d5;
        }

        .type_enter {
            color: #faad14;
        }
    }
}
</style>
