<template>
    <view class="allot_room_container">
        <uni-nav-bar fixed statusBar @clickLeft="back" leftText="取消" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()">
            <view class="top_nav_bar">
                <view class="top_text">选择楼栋</view>
            </view>
        </uni-nav-bar>
        <view class="top_box">
            <uv-steps :current="current">
                <uv-steps-item v-if="allotInfo && allotInfo.one" :title="adjustTypeList[allotInfo.one]"></uv-steps-item>
                <uv-steps-item v-else title="-"></uv-steps-item>
                <uv-steps-item :title="allotInfo?.two || '-'"></uv-steps-item>
                <uv-steps-item :title="allotInfo?.three || '-'"></uv-steps-item>
            </uv-steps>
        </view>
        <!-- 内容信息 -->
        <view class="main_box">
            <view class="title">请选择您要分配的楼栋 </view>
            <radioSelect v-model:value="floorValue" :list="floorList">
                <template #left="{ left }">
                    <view class="left_box" v-if="left">
                        <view class="sel_top_box">
                            <img class="img" src="https://alicdn.1d1j.cn/1531914651808833538/default/0535d32d7f484e14b2a7c7235d0f2e51.png" />
                            <view>
                                <view>{{ left.name }}</view>
                                <view class="text_btm">合计寝室：{{ left.totalRoomCount }}间</view>
                            </view>
                        </view>
                        <view class="sel_btm_box">
                            <text class="text">已住：{{ left.checkInPeopleCount }}人</text>
                            <text class="text">剩余床位：{{ left.residueBedCount }}个</text>
                        </view>
                    </view>
                </template>
            </radioSelect>
        </view>
        <!-- 底部按钮 -->
        <div class="footer_box">
            <button class="yd_btn_primary" @click="next">下一步</button>
        </div>
    </view>
</template>
<script setup>
import radioSelect from "./components/radioSelect.vue"
import { adjustTypeList } from "../../utils/staticData"

let current = ref(2)
let floorValue = reactive({})

let allotInfo = reactive({
    one: "",
    two: "",
    three: "选择床位"
})

onLoad((params) => {
    Object.keys(params).forEach((key) => {
        params[key] = decodeURIComponent(params[key])
    })
    if (params) {
        allotInfo.one = params.adjustType
        allotInfo.two = params.originalStudent ? `${"换寝对象：" + params.originalStudent}` : "换寝对象：-"
    }
    getDormBuilding()
})

const floorList = ref([])

const back = () => {
    uni.navigateBack()
}

const next = () => {
    navigateTo({
        url: "/apps/dormManage/dormTransfer/selectBed/index",
        query: {
            ...floorValue,
            ...allotInfo
        }
    })
}

function getDormBuilding() {
    http.post("/app/dormitory/intelligentDormitorySeparation/dormitoryBuilding", {}).then((res) => {
        floorList.value = res.data || []
    })
}
</script>
<style lang="scss" scoped>
.allot_room_container {
    background-color: $uni-bg-color-grey;
    :deep(.uni-navbar__content) {
        border-bottom-color: transparent !important;
    }
    .top_nav_bar {
        text-align: center;
        margin: auto;
        .top_text {
            font-size: 32rpx;
            font-weight: 600;
        }
    }
    .top_box {
        padding: 40rpx 0;
        :deep(.uv-steps-item__wrapper) {
            background-color: $uni-bg-color-grey;
        }
    }
    .main_box {
        background-color: #fff;
        border-radius: 40rpx 40rpx 0 0;
        padding: 40rpx 0rpx 150rpx 0rpx;
        .title {
            text-align: center;
            font-size: 32rpx;
            color: #333;
            font-weight: 600;
        }
        .left_box {
            flex: 1;
            .sel_top_box {
                display: flex;
                align-items: center;
                .img {
                    flex-shrink: 0;
                    width: 100rpx;
                    height: 100rpx;
                    border-radius: 50%;
                    overflow: hidden;
                    border: 10rpx solid #cad6ff;
                    margin-right: 28rpx;
                }
                .text_btm {
                    font-size: 28rpx;
                    font-weight: normal;
                    padding-top: 16rpx;
                }
            }
            .sel_btm_box {
                display: flex;
                font-size: 28rpx;
                font-weight: normal;
                padding-top: 40rpx;
                .text {
                    width: 50%;
                    text-align: center;
                    &:first-of-type {
                        border-right: 1rpx solid #d9d9d9;
                    }
                }
            }
        }
    }

    .footer_box {
        position: fixed;
        width: 100%;
        box-sizing: border-box;
        bottom: 0rpx;
        padding: 30rpx;
        background-color: #fff;
        .yd_btn_primary {
            background-color: #4566d5;
            color: #fff;
        }
        button {
            font-size: 32rpx;
            margin-left: 0;
            margin-right: 0;
        }
    }
}
</style>
