<template>
    <view class="access_container" @click="handleClick">
        <view class="top">
            宿舍通行
            <uni-icons type="right" size="24" color="#8c8c8c"></uni-icons>
        </view>
        <view class="item_box">
            <view class="header headerO">
                <img class="img" src="@nginx/workbench/dormManage/student.png" />
                <text>学生</text>
            </view>
            <view class="btm_box">
                <view class="box">
                    <view class="line"></view>
                    <view>{{ student.inCount }}</view>
                    <view class="text_bottom">通行人数</view>
                </view>
                <view class="box">
                    <view class="line"></view>
                    <view>{{ student.outCount }}</view>
                    <view class="text_bottom">出校</view>
                </view>
                <view class="box">
                    <view>{{ student.passCount }}</view>
                    <view class="text_bottom">入校</view>
                </view>
            </view>
        </view>
        <view class="item_box">
            <view class="header headerT">
                <img class="img" src="@nginx/workbench/dormManage/teacher.png" />
                <text>老师</text>
            </view>
            <view class="btm_box">
                <view class="box">
                    <view class="line"></view>
                    <view>{{ teacher.inCount }}</view>
                    <view class="text_bottom">通行人数</view>
                </view>
                <view class="box">
                    <view class="line"></view>
                    <view>{{ teacher.outCount }}</view>
                    <view class="text_bottom">出校</view>
                </view>
                <view class="box">
                    <view>{{ teacher.passCount }}</view>
                    <view class="text_bottom">入校</view>
                </view>
            </view>
        </view>
    </view>
</template>
<script setup>
defineProps({
    teacher: {
        type: Object,
        default: {}
    },
    student: {
        type: Object,
        default: {}
    }
})

const handleClick = () => {
    navigateTo({
        url: "/apps/dormManage/dormTraffic/index"
    })
}
</script>

<style lang="scss" scoped>
.access_container {
    background: #fff;
    border-radius: 20rpx;
    padding: 30rpx;
    .top {
        font-size: 34rpx;
        display: flex;
        justify-content: space-between;
        margin-bottom: 28rpx;
    }
    .item_box {
        border-radius: 20rpx;
        overflow: hidden;
        margin-bottom: 20rpx;
        .header {
            height: 82rpx;
            display: flex;
            align-items: center;
            background-size: 100% 100%;
            background-repeat: no-repeat;
            font-size: 30rpx;
            color: #fff;
            .img {
                width: 42rpx;
                height: 42rpx;
                border-radius: 50%;
                margin-right: 10rpx;
                margin-left: 30rpx;
            }
        }
        .headerO {
            background-image: url("@nginx/workbench/dormManage/pic-teacher.png");
        }
        .headerT {
            background-image: url("@nginx/workbench/dormManage/pic-pupil.png");
        }
        .btm_box {
            background: #f9faf9;
            height: 152rpx;
            box-sizing: border-box;
            display: flex;
            font-size: 34rpx;
            color: #4566d5;
            .box {
                flex: 1;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                position: relative;
                .text_bottom {
                    margin-top: 20rpx;
                    font-size: 24rpx;
                    color: #666;
                }
                .line {
                    width: 2rpx;
                    height: 60rpx;
                    position: absolute;
                    right: 0;
                    top: 48rpx;
                    background: #d8d8d8;
                }
            }
        }
    }
}
</style>
