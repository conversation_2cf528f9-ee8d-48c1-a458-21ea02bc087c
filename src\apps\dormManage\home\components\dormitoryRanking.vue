<template>
	<div class="dormitory_ranking">
		<div class="ranking_top">
			<div class="ranking_background"></div>
			<div class="ranking_info">
				<span class="title">
					<img class="image"
						src="https://alicdn.1d1j.cn/announcement/20230725/96dc7775f78d4d81b46ae5df17771c90.png"
						alt="" />
				</span>
				<!-- TODO: 暂时不知数据详情先取currentDay字段 -->
				<span class="time">评比时间：{{ data.currentCycle }}</span>
				<div class="info">
					<span class="title" @click="lookInfo">查看详情</span>
					<uni-icons type="forward" size="12" color="#fff" />
				</div>
			</div>
			<div class="ranking_first" @click="lookInfo">
				<img class="image"
					src="https://alicdn.1d1j.cn/announcement/20230725/1b7ad419ec854558863517d507f8f84f.png" alt="" />
				<span class="text yd_ellipsis">{{ rankList.name }}</span>
				<span class="text">{{ rankList.goal }}分</span>
				<span class="text">第一名 <uni-icons type="forward" color="#fff" size="18" /></span>
			</div>
		</div>
		<div class="ranking_list">
			<span class="title">截止目前扣分寝室备份</span>
			<view v-if="data.lastList.length">
				<div class="ranking_item" @click="lookInfo" v-for="(item, index) in data.lastList" :key="index">
					<span class="one">{{ item.name }}</span>
					<span> {{ item.goal }} 分</span>
					<uni-icons type="forward" color="#333333" size="18" />
				</div>
			</view>
			<view v-else class="empty_text">哇喔,太棒了没有一个寝室扣分耶</view>
		</div>
	</div>
</template>

<script setup>
const emit = defineEmits(["lookInfo"])
const props = defineProps({
	data: {
		type: Object,
		default: {}
	}
})

const rankList = computed(() => {
	if (!props.data) return {}
	return props.data?.rankList[0] || {}
})

function lookInfo() {
	emit("lookInfo")
}
</script>

<style lang="scss" scoped>
.dormitory_ranking {
	margin: 28rpx 0rpx;

	.ranking_top {
		height: 298rpx;
		background: #bacaff;
		border-radius: 20rpx 20rpx 0rpx 0rpx;
		padding: 28rpx;
		position: relative;

		.ranking_info {
			display: flex;
			flex-direction: column;

			.title {
				.image {
					width: 160rpx;
					height: 32rpx;
				}
			}

			.time {
				font-size: 28rpx;
				font-weight: 400;
				color: #ffffff;
				line-height: 40rpx;
				margin: 16rpx 0rpx;
			}

			.info {
				width: 160rpx;
				height: 52rpx;
				border-radius: 28rpx;
				border: 1rpx solid #ffffff;
				display: flex;
				align-items: center;
				justify-content: center;

				.title {
					font-size: 24rpx;
					font-weight: 400;
					color: #ffffff;
				}
			}
		}

		.ranking_background {
			width: 334rpx;
			height: 176rpx;
			background: url("https://alicdn.1d1j.cn/announcement/20230725/c7252b16993f474c99f0f340690952bb.png") no-repeat;
			background-size: cover;
			position: absolute;
			top: 0rpx;
			right: 0rpx;
		}

		.ranking_first {
			margin-top: 28rpx;
			height: 104rpx;
			background: #a1b7ff;
			border-radius: 20rpx;
			display: flex;
			justify-content: space-between;
			padding: 0rpx 24rpx;
			align-items: center;

			.image {
				width: 100rpx;
				height: 64rpx;
			}

			.text {
				font-size: 32rpx;
				font-weight: 600;
				color: #ffffff;
				line-height: 44rpx;
				min-width: 140rpx;
			}
		}
	}

	.ranking_list {
		border-radius: 0rpx 0rpx 20rpx 20rpx;
		background: #ffffff;
		padding: 28rpx;

		.title {
			font-size: 28rpx;
			font-weight: 400;
			color: #666666;
			line-height: 40rpx;
		}

		.empty_text {
			font-size: 24rpx;
			color: #9d9d9d;
			margin: 50rpx 0;
			text-align: center;
		}

		.ranking_item {
			margin-top: 20rpx;
			height: 80rpx;
			background: #f4f6ff;
			border-radius: 40rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 0rpx 20rpx 0rpx 40rpx;
			font-size: 28rpx;
			font-weight: 400;
			color: #333333;
			line-height: 40rpx;

			.one {
				font-weight: 600;
			}
		}
	}

	.yd_ellipsis {
		display: inline-block;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}
}
</style>
