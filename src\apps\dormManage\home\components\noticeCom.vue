<template>
    <div class="notice" @click="handleClick">
        <div class="left">
            <img class="image" src="https://alicdn.1d1j.cn/announcement/20230724/3ce32f9152b24a7a870502a55a2a26d3.png" alt="" />
            <span class="content">通知公告：{{ data.title }}</span>
        </div>
        <uni-icons type="forward" size="16" color="#FAAD14" />
    </div>
</template>

<script setup>
const props = defineProps({
    data: {
        type: Object,
        default: {}
    }
})

const handleClick = () => {
    navigateTo({
        url: "/apps/dormManage/home/<USER>"
    })
}
</script>

<style lang="scss" scoped>
.notice {
    height: 80rpx;
    background: #fff5e1;
    display: flex;
    padding: 0rpx 16rpx 0rpx 28rpx;
    justify-content: space-between;
    align-items: center;
    .left {
        display: flex;
        align-items: center;

        .image {
            height: 36rpx;
            width: 36rpx;
        }
        .content {
            font-size: 26rpx;
            font-weight: 400;
            color: #faad14;
            line-height: 36rpx;
            margin-left: 8rpx;
        }
    }
}
</style>
