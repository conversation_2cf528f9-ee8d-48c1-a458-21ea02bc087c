import { ref } from 'vue'

export default function () {
  let pageNo = ref(1)
  let pageSize = ref(10)
  let total = ref(1)
  let dataList = ref([])
  let status = ref('more')
  const getList = (api, other) => {
    return new Promise((reslove, reject) => {
      const params = {
        pageNo: pageNo.value,
        pageSize: pageSize.value,
        ...other,
      }
      if (dataList.value.length >= total.value) {
        return
      } else {
        status.value = 'loading'
        api(params)
          .then(res => {
            if (res.data.pageList) {
              total.value = res.data.pageList.total || 1
              pageNo.value = res.data.pageList.pageNo + 1
              dataList.value.push(...res.data.pageList.list)
            } else {
              total.value = res.data.total || 1
              pageNo.value = res.data.pageNo + 1
              dataList.value.push(...res.data.list)
            }
            reslove(res)
          })
          .catch(err => {
            pageNo.value = 1
            pageSize.value = 10
            total.value = 1
            dataList.value = []
            status.value = 'noMore'
          })
          .finally(() => {
            if (dataList.value.length >= total.value) {
              status.value = 'noMore'
            } else {
              status.value = 'noMore'
            }
          })
      }
    })
  }

  return {
    pageNo,
    pageSize,
    dataList,
    total,
    getList,
    status,
  }
}
