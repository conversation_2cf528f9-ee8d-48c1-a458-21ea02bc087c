<template>
    <view class="serach_view_container">
        <view class="search">
            <uv-input placeholder="搜索学生姓名" prefixIcon="search" shape="circle" prefixIconStyle="font-size: 22px;color: #909399" clearable v-model="searchVal" @change="change"></uv-input>
        </view>
        <view class="avatar_box" v-if="avatarList.length">
            <view v-for="(item, index) in avatarList" :key="index" class="avatar_item">
                <img class="avatar" v-if="item.avatar" :src="item.avatar" />
                <view class="avatar" v-else>{{ item.name.slice(-2) }}</view>
                <uni-icons class="icon_dele" type="clear" size="24" color="#8C8C8C" @click="handleDelete(item, index)"></uni-icons>
            </view>
        </view>
        <view class="mian_box">
            <select-list :list="list" @itemClick="itemClick" :selectFlag="selectData.selectFlag"></select-list>
        </view>
        <view class="footer" v-if="selectData.selectFlag">
            <view>已选择: {{ avatarList.length }}人</view>
            <button @click="submit">完成</button>
        </view>
    </view>
</template>

<script setup>
import selectList from "../components/selectList.vue"

let selectData = reactive({})
let searchVal = ref("")
let avatarList = ref([])

onLoad((params) => {
    Object.keys(params).forEach((key) => {
        params[key] = decodeURIComponent(params[key])
    })
    Object.keys(params).forEach((i) => (selectData[i] = params[i]))
    if (params.avatarList) {
        avatarList.value = JSON.parse(params.avatarList)
    }
})

const list = ref([])

const getStudentList = async (params) => {
    const { data } = await http.post("/app/student/search", params)
    list.value = data
    if (!data || data.length === 0) {
        uni.showToast({
            title: "暂无学生",
            duration: 2000,
            icon: "none"
        })
    }
}
const change = debounce((val) => {
    list.value = []
    if (!val) return

    const params = {
        name: val,
        filterNonEltern: false,
        queryEltern: false,
        code: "smartDormitory",
        statusList: [1, 2, 4, 5, 12, 13, 99]
    }
    Object.assign(params, selectData)
    getStudentList(params)
})

const goto = (params) => {
    navigateTo({
        url: "/apps/dormManage/dormInfo/studentDetail/index",
        query: params
    })
}

const itemClick = (i) => {
    // 如果存在选择标识就选择否则就是跳转详情
    if (selectData.selectFlag) {
        if (i.checked && !avatarList.value.find((item) => item.id === i.id)) {
            if (selectData.selectFlag === "multiple") {
                avatarList.value.push(i)
            } else if (selectData.selectFlag === "radio") {
                avatarList.value.splice(0, 1, i)
            }
        } else if (i.checked && avatarList.value.find((item) => item.id === i.id)) {
            return
        } else {
            const index = avatarList.value.findIndex((item) => item.id === i.id)
            avatarList.value.splice(index, 1)
            i.checked = false
        }
    } else {
        goto({ id: i.id })
    }
}

const handleDelete = (item, i) => {
    avatarList.value.splice(i, 1)
    item.checked = false
}

const submit = () => {
    if (!avatarList.value.length) return
    const two = avatarList.value.length + "人"
    const studentIdList = avatarList.value.map((i) => i.id)
    const studentData = {
        originalStudent: avatarList.value[0].id,
        studentName: avatarList.value[0].name,
        avatar: avatarList.value[0].avatar
    }
    const businessId = []
    const businessName = []
    avatarList.value.forEach((item) => {
        businessId.push(item.id)
        businessName.push(item.name)
    })
    uni.navigateBack({
        delta: 2,
        success() {
            uni.$emit("nextStep", { step: 2, two, studentIdList })
            uni.$emit("dormTransfer", { studentData })
            uni.$emit("dormTraffic", { businessId, businessName })
        }
    })
}

function debounce(fn, time = 500) {
    let timer = null

    return function () {
        if (timer) clearTimeout(timer)

        timer = setTimeout(() => {
            fn.apply(this, arguments)
            timer = null
        }, time)
    }
}

// onUnload(() => {
//     if (selectData.buildingId) {
//         sendAppEvent("backApp", {});
//     }
// })
</script>

<style lang="scss" scoped>
.serach_view_container {
    padding-top: 120rpx;
    background-color: $uni-bg-color-grey;
    .search {
        position: fixed;
        top: 88rpx;
        /* #ifdef MP-WEIXIN */
        top: 0rpx;
        /* #endif */
        width: 100%;
        box-sizing: border-box;
        background: #fff;
        padding: 30rpx;
        z-index: 99;
        :deep(.uv-input--circle) {
            background-color: $uni-bg-color-grey;
        }
        :deep(.uv-input__content) {
            height: 33rpx;
        }
    }
    .avatar_box {
        padding: 30rpx;
        padding-bottom: 0;
        display: flex;
        flex-wrap: wrap;
        background-color: #fff;
        .avatar_item {
            position: relative;
            margin-right: 30rpx;
            margin-bottom: 30rpx;
            &:nth-of-type(8n) {
                margin-right: 0;
            }
            .avatar {
                display: inline-block;
                font-size: 24rpx;
                width: 60rpx;
                height: 60rpx;
                border-radius: 50%;
                color: #fff;
                background-color: #4566d5;
                line-height: 60rpx;
                text-align: center;
            }
            .icon_dele {
                position: absolute;
                top: -12px;
                right: -9px;
            }
        }
    }
    .mian_box {
        background-color: #fff;
        margin-top: 20rpx;
        padding-bottom: 132rpx;
    }
    .footer {
        display: flex;
        position: fixed;
        width: 100%;
        box-sizing: border-box;
        bottom: 0;
        padding: 36rpx 28rpx;
        background: #fff;
        align-items: center;
        justify-content: space-between;
        button {
            width: 200rpx;
            height: 60rpx;
            background: #4566d5;
            border-radius: 10rpx;
            font-size: 32rpx;
            color: #fff;
            line-height: 60rpx;
            margin: 0;
        }
    }
}
</style>
