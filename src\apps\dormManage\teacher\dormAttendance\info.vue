<template>
    <div class="attendance_info">
        <div class="count">
            <span class="title">寝室号：{{ infoData.roomNum }}（{{ infoData.peppleNum }}人间）</span>
            <div class="status_count">
                <div class="item" v-for="(item, index) in list" :key="index" :style="`${'color: ' + color[item.status] + ';background:' + background[item.status]}`">
                    <span class="frequency">{{ infoData[item.frequency] }}</span>
                    <span>{{ text[item.status] }}</span>
                </div>
            </div>
        </div>
        <div class="info_box">
            <CollapseList :list="pageList">
                <template #content="{ childList }">
                    <div class="info" v-for="(item, index) in childList.signInList" :key="index">
                        <view class="msg_box">
                            <view>
                                <span>{{ index + 1 }}. </span><span>签到时间: </span><span>{{ item.signInTime }}</span>
                            </view>
                            <view :style="{ color: changeColor[item.status] }">{{ changeText[item.status] }}</view>
                        </view>
                    </div>
                </template>
            </CollapseList>
        </div>
    </div>
</template>

<script setup>
import CollapseList from "../../components/collapseList.vue"

const text = ["正常/次", "晚归/次", "请假/次", "未归寝/次"]
const color = ["var(--primary-color)", "#FC941F", "#3896EA", "#F5222D"]
const background = ["#E3FAF3", "#FFF4E9", "#E9F5FF", "#FFEBEC"]

const changeText = {
    0: "正常",
    1: "未归寝",
    2: "晚归",
    5: "请假",
    6: "未打卡"
}

const changeColor = {
    0: "var(--primary-color)",
    1: "#F5222D",
    2: "#FC941F",
    5: "#3896EA",
    6: "#5534F0"
}

const list = [
    {
        frequency: "normalNum",
        status: 0
    },
    {
        frequency: "beLateNum",
        status: 1
    },
    {
        frequency: "leaveNum",
        status: 2
    },
    {
        frequency: "absenteeismNum",
        status: 3
    }
]

const pageList = ref([])

const infoData = reactive({})

onLoad((params) => {
    Object.keys(params).forEach((i) => (infoData[i] = decodeURIComponent(params[i])))
    getList(infoData)
})

const getList = async (params) => {
    const { data } = await http.post("/app/dormitory/attendance/master/dormitoryStudentList", params)
    pageList.value = data
}
</script>

<style lang="scss" scoped>
.attendance_info {
    min-height: 94vh;
    background: $uni-bg-color-grey;
    padding-top: 20rpx;
    .count {
        height: 234rpx;
        padding: 30rpx 15rpx;
        background: #ffffff;
        .title {
            font-size: 28rpx;
            font-weight: 600;
            color: #333333;
            line-height: 40rpx;
            margin-bottom: 30rpx;
        }
        .status_count {
            margin-top: 30rpx;
            display: flex;
            justify-content: space-around;
            .item {
                width: 156rpx;
                height: 164rpx;
                background: #e3faf3;
                border-radius: 20rpx;
                font-size: 28rpx;
                font-weight: 400;
                line-height: 40rpx;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;

                .frequency {
                    font-size: 40rpx;
                    line-height: 58rpx;
                    margin-bottom: 16rpx;
                }
            }
        }
    }
    .info_box {
        margin-top: 20rpx;
        .msg_box {
            display: flex;
            justify-content: space-between;
            padding: 30rpx 28rpx 30rpx 66rpx;
            border-top: 1rpx solid #d9d9d9;
            font-size: 28rpx;
            color: #333;
        }
    }
}
</style>
