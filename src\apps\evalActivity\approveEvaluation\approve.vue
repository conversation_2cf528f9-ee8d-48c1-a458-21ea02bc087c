<template>
    <div class="approve_evaluation">
        <yd-page-view ref="page" :clickLeft="clickLeft" v-model="frequencyList" @query="queryList" :refresherOnly="true" title="评价审核" :hideBottom="true" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()">
            <view class="page_container">
                <personnel-info :info="queryParams" :personDetails="personData" />
                <frequencyList :list="frequencyList" :isThisPageLook="false" @handlerDetails="handlerDetails"> </frequencyList>
                <div class="approve_total_score">
                    <text class="label">评价总分：</text>
                    <text class="score">{{ personData.totalScore || 0 }}分</text>
                </div>
                <div class="student_comments">
                    <text class="label">学生评语：</text>
                    <textarea class="textarea" placeholder-class="placeholder_class" v-model="personData.comment" placeholder="请输入" :maxlength="2000" auto-height></textarea>
                    <text class="max_count">{{ `${personData.comment?.length || 0} / 2000` }}</text>
                    <view class="footer_btn">
                        <button class="btn" :loading="loading" type="primary" @click="handlerSave">确定</button>
                    </view>
                </div>
            </view>
            <template #empty>
                <yd-empty text="暂无数据" />
            </template>
        </yd-page-view>
    </div>
</template>

<script setup>
import PersonnelInfo from "./components/personnelInfo.vue"
import FrequencyList from "./components/frequencyList.vue"
import { onShow } from "@dcloudio/uni-app"

const page = ref(null)
const queryParams = ref({})
const frequencyList = ref([])
const loading = ref(false)
const personData = ref({})

// 调用List数据
function queryList(pageNo, pageSize) {
    const { toPersonId, activityId = "" } = queryParams.value
    const parmas = {
        pageNo,
        pageSize,
        toPersonId,
        activityId
    }
    http.post("/app/evalDayRulePerson/pageDayPersonScore", parmas).then(({ data }) => {
        page.value?.paging.complete(data.list)
    })
}

// 查看详情
function handlerDetails(params) {
    navigateTo({
        url: "/apps/evalActivity/approveEvaluation/approveDetails",
        query: { ...queryParams?.value, ...params }
    })
}

// 提交评价活动评分
function handlerSave() {
    const params = {
        activityId: queryParams?.value.activityId,
        toPersonId: queryParams?.value.toPersonId,
        comment: personData.value.comment
    }
    loading.value = true
    http.post("/app/evalDayRulePerson/updateRulePersonScore", params)
        .then(({ message }) => {
            uni.showToast({
                title: message
            })
            uni.navigateBack()
        })
        .finally(() => {
            loading.value = false
        })
}

// 返回
function clickLeft() {
    uni.navigateBack()
}

async function getPersonData() {
    await http.post("/app/evalDayRulePerson/getEvalRulePersonDetails", { rulePersonId: queryParams.value.id }).then((res) => {
        personData.value = res.data
    })
}

onShow(async () => {
    await getPersonData()
})
onLoad(async (options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    queryParams.value = options
    nextTick(() => {
        page.value?.paging?.reload()
    })
})
</script>

<style lang="scss" scoped>
.page_container {
    min-height: calc(100vh - 152rpx);
    background: $uni-bg-color-grey;
    padding: 30rpx;
    .split_line {
        margin: 24rpx 0;
        height: 1rpx;
        width: 100%;
        background: $uni-border-color;
    }
    .score_label {
        font-weight: 400;
        font-size: 28rpx;
        color: $uni-text-color-grey;
        line-height: 40rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 20rpx;
        .value {
            font-weight: 400;
            font-size: 28rpx;
            color: $uni-text-color;
            line-height: 40rpx;
        }
        .this_score {
            color: var(--primary-color);
        }
    }
    .comment {
        display: flex;
        flex-direction: column;
        margin-top: 20rpx;
        font-weight: 400;
        font-size: 28rpx;
        color: $uni-text-color-grey;
        line-height: 40rpx;
        .comment_title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16rpx;
        }
        .more {
            color: var(--primary-color);
        }
    }
    .score_title {
        font-weight: 500;
        margin-top: 24rpx;
        font-size: 28rpx;
        color: var(--primary-color);
        line-height: 40rpx;
    }
}
.footer_btn {
    width: 100%;
    background: $uni-bg-color;
    height: 70rpx;

    .btn {
        width: 100%;
        height: 100%;
        color: $uni-bg-color;
        background: var(--primary-color);
        font-weight: 400;
        font-size: 32rpx;
        color: $uni-text-color-inverse;
        line-height: 44rpx;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
.approve_total_score {
    padding: 28rpx 30rpx;
    background: $uni-bg-color;
    border-radius: 20rpx;
    margin: 20rpx 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .label {
        font-weight: 500;
        font-size: 28rpx;
        color: $uni-text-color;
        line-height: 40rpx;
    }
    .score {
        font-weight: 500;
        font-size: 28rpx;
        color: var(--primary-color);
        line-height: 40rpx;
    }
}
.student_comments {
    padding: 28rpx 30rpx;
    background: $uni-bg-color;
    border-radius: 20rpx;
    position: relative;
    .label {
        font-weight: 500;
        font-size: 28rpx;
        color: $uni-text-color;
        line-height: 40rpx;
    }
    .textarea {
        margin-top: 10rpx;
        font-weight: 400;
        font-size: 28rpx;
        color: #595959;
        line-height: 40rpx;
        min-height: 400rpx;
        padding-bottom: 70rpx;
    }
    .max_count {
        position: absolute;
        font-weight: 400;
        font-size: 28rpx;
        color: #00000040;
        line-height: 40rpx;
        bottom: 120rpx;
        right: 20rpx;
    }
    :deep(.placeholder_class) {
        font-weight: 400;
        font-size: 28rpx;
        color: #00000040;
        line-height: 40rpx;
    }
}
</style>
