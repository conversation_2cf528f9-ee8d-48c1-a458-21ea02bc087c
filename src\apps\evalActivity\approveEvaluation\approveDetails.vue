<template>
    <view>
        <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="评价审核" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
        <view class="participants">
            <scroll-view scroll-y="true" scroll-top="0" enable-back-to-top class="evaluate">
                <evaluate-rule :list="participantsList" @changeScore="indicatorScoreChange" @changeData="changeData" :ruleData="ruleData" :isApprove="true" :isTextarea="true" :isUploadimage="false">
                    <template #info="{ data }">
                        <view class="score_label">
                            <text>本次评分：</text>
                            <text class="value this_score">{{ data.indicatorScore.thisIndicatorScore || 0 }}分</text>
                        </view>
                        <view class="score_label">
                            <text>他人评分：</text>
                            <text class="value">{{ data.indicatorScore.othersTotalScore || 0 }}分</text>
                        </view>
                        <view class="score_label">
                            <text>最后得分：</text>
                            <text class="value">{{ data.indicatorScore?.totalIndicatorScore || 0 }}分</text>
                        </view>
                        <!-- v-if="data.indicatorScore.comment || data.indicatorScore.imgPaths || data.indicatorScore.videoPaths" -->
                        <view class="comment">
                            <view class="comment_title">
                                <text>更多评分： </text>
                                <text class="more" @click="moreComment(data.indicatorScore)">
                                    查看
                                    <uni-icons type="right" size="16" color="var(--primary-color)"></uni-icons>
                                </text>
                            </view>
                        </view>
                        <view class="comment" v-if="data.indicatorScore.comment">
                            <view> 评语： </view>
                            <text class="value"> {{ data.indicatorScore.comment }} </text>
                        </view>
                        <!-- 上传图片/视频 -->
                        <video-image-com :data="data" :isEdit="false"> </video-image-com>
                        <view class="score_title">审核得分：</view>
                    </template>
                    <template #textarea="{ data }">
                        <view class="textarea">
                            <uni-easyinput :key="data.id" type="textarea" autoHeight v-model="data.indicatorScore.remark" placeholder="请输入文字" :maxlength="200" primaryColor="var(--primary-color)" @change="changeRemarksData(data)">
                                <template #right>
                                    <view class="input_count">{{ data.indicatorScore.remark?.length || 0 }}/200</view>
                                </template>
                            </uni-easyinput>
                        </view>
                    </template>
                </evaluate-rule>
            </scroll-view>
            <view class="evaluate_footer">
                <text class="aggregate_score">
                    合计得分：
                    <text class="score">{{ totalScore || 0 }}</text>
                </text>
                <view class="footer_btn">
                    <button class="footer_btn" :loading="loading" type="primary" @click="handlerSave">确定</button>
                </view>
            </view>
        </view>
        <div class="back_to_top" @click.stop="scrollToTop">
            <image class="image" src="@nginx/workbench/evalActivity/backToTop.png" mode="scaleToFill" />
        </div>
        <MoreComment ref="moreCommentRef" />
        <uni-popup ref="alertDialog" type="dialog">
            <uni-popup-dialog type="info" cancelText="取消" confirmText="确定" title="提示" :content="`还有${ruleData.notEvalScoreNum}人未评价，确认提交审核？`" @confirm="dialogConfirm" @close="dialogClose"></uni-popup-dialog>
        </uni-popup>
    </view>
</template>

<script setup>
import MoreComment from "./moreComment.vue"
import EvaluateRule from "../components/evaluateRule.vue"
import videoImageCom from "../components/videoImageCom.vue"

const alertDialog = ref(null)
const moreCommentRef = ref(null)
const queryParams = ref({})
const participantsList = ref([])
const loading = ref(false)
const myTotalScore = ref(0)
const averageTotalScore = ref(0)
const scoringList = ref([])
const ruleData = ref({})

function moreComment(data) {
    moreCommentRef.value.open(data.scoreRecordList)
}

// 总分判断是立即评价还是平均评价
const totalScore = computed(() => {
    //  settlementType: 结算方式【1.立即结算、2.活动结束后结算】
    // 立即结算使用正常评分加起来，活动后结算需要算平均分
    return Number(myTotalScore.value).toFixed(2)
})

// 计算总分值
function totalScoreCompt() {
    myTotalScore.value = 0
    averageTotalScore.value = 0
    participantsList.value.forEach((i) => {
        i.secondIndicators.forEach((j) => {
            myTotalScore.value += j.indicatorScore?.approveScore || j.indicatorScore.totalIndicatorScore
        })
    })
}

function dialogConfirm() {
    const params = {
        scoringList: scoringList.value
    }
    loading.value = true
    http.post("/app/evalDayRulePerson/approvePersonScore", params)
        .then(({ message }) => {
            uni.showToast({
                title: message
            })
            uni.navigateBack()
        })
        .finally(() => {
            loading.value = false
        })
}
function dialogClose() {
    alertDialog.value.close()
}

// 提交评价活动评分
function handlerSave() {
    console.log(scoringList.value, "scoringList.value")
    if (ruleData.value?.notEvalScoreNum && ruleData.value.notEvalScoreNum > 0) {
        alertDialog.value.open()
        return
    }
    dialogConfirm()
}

// 获取规则评价
function getRulePersonScoreListInfo() {
    const { activityId, toPersonId, rulePersonId } = queryParams.value
    const params = {
        activityId,
        toPersonId,
        rulePersonId: rulePersonId,
        isApprove: true,
        queryThisFrom: false
    }
    http.post("/app/evalDayRulePerson/getRulePersonScoreList", params).then(({ data }) => {
        scoringList.value = []
        participantsList.value = data?.map((i) => {
            return {
                ...i,
                secondIndicators: i.secondIndicators.map((j) => {
                    scoringList.value.push({
                        indicatorId: j.indicatorScore.id,
                        rulePersonId: j.indicatorScore.rulePersonId,
                        approveScore: j.indicatorScore.approveScore || j.indicatorScore.totalIndicatorScore,
                        remark: j.indicatorScore.remark
                    })
                    return {
                        ...j,
                        indicatorScore: {
                            ...j.indicatorScore,
                            approveScore: j.indicatorScore.approveScore,
                            imgPathsList: j.indicatorScore?.imgPaths ? j.indicatorScore?.imgPaths?.split(",") : []
                        }
                    }
                })
            }
        })
        totalScoreCompt()
    })
}

function changeData(j, obj) {
    scoringList.value = scoringList.value.map((i) => {
        const newObj = j.indicatorScore.id == i.indicatorId ? obj : i
        return {
            ...i,
            ...newObj
        }
    })
}

function changeRemarksData(data) {
    changeData(data, {
        remark: data.indicatorScore.remark || ""
    })
}

// 加减分数
function indicatorScoreChange(value, j) {
    j.indicatorScore.approveScore = value
    scoringList.value = scoringList.value.map((i) => {
        return {
            ...i,
            approveScore: j.indicatorScore.id == i.indicatorId ? value : i.approveScore
        }
    })
    totalScoreCompt()
}

// 返回
function clickLeft() {
    uni.navigateBack()
}

function scrollToTop() {
    uni.pageScrollTo({
        scrollTop: 0, // 滚动到页面的顶部位置
        duration: 300 // 滚动动画的时长，单位ms
    })
}

async function getRuleData() {
    await http.post("/app/evalDayRulePerson/getEvalRulePersonDetails", { rulePersonId: queryParams.value.rulePersonId }).then((res) => {
        ruleData.value = res.data
    })
}

onLoad(async (options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    queryParams.value = options
    console.log(queryParams.value)
    await getRuleData()
    getRulePersonScoreListInfo()
})
</script>

<style lang="scss" scoped>
.participants {
    min-height: calc(100vh - 148rpx);
    background: $uni-bg-color-grey;
    padding: 30rpx;
    padding-bottom: 200rpx;
    .evaluate {
        min-height: 80rpx;
        background: $uni-bg-color;
        border-radius: 20rpx;
        padding: 32rpx;
        width: calc(100% - 64rpx);
        .score_label {
            font-weight: 400;
            font-size: 28rpx;
            color: $uni-text-color-grey;
            line-height: 40rpx;
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 20rpx;
            .value {
                font-weight: 400;
                font-size: 28rpx;
                color: $uni-text-color;
                line-height: 40rpx;
            }
            .this_score {
                color: var(--primary-color);
            }
        }
        .comment {
            display: flex;
            flex-direction: column;
            margin-top: 20rpx;
            font-weight: 400;
            font-size: 28rpx;
            color: $uni-text-color-grey;
            line-height: 40rpx;
            .comment_title {
                display: flex;
                align-items: center;
                justify-content: space-between;
            }
            .value {
                padding-top: 10rpx;
            }
            .more {
                color: var(--primary-color);
            }
        }
        .score_title {
            font-weight: 500;
            margin-top: 24rpx;
            font-size: 28rpx;
            color: var(--primary-color);
            line-height: 40rpx;
        }
        .textarea {
            :deep(is-focused) {
                border-color: var(--primary-color) !important;
            }
            .input_count {
                display: inline-block;
                color: $uni-text-color-grey;
                font-size: 28rpx;
                right: 10rpx;
                padding-right: 10rpx;
                bottom: 16rpx;
                position: absolute;
                background: #fff;
            }
            :deep(.input-padding) {
                padding-bottom: 40rpx;
            }
        }
    }

    .evaluate_footer {
        z-index: 99;
        padding: 0rpx 30rpx;
        padding-bottom: 30rpx;
        height: 126rpx;
        background: $uni-bg-color;
        position: fixed;
        bottom: 0;
        left: 0;
        width: calc(100vw - 60rpx);
        display: flex;
        justify-content: space-between;
        align-items: center;

        .aggregate_score {
            font-weight: 400;
            font-size: 28rpx;
            color: $uni-text-color;
            line-height: 40rpx;

            .score {
                font-weight: 600;
                font-size: 36rpx;
                color: var(--primary-color);
                line-height: 50rpx;
            }
        }

        .footer_btn {
            width: 156rpx;
            height: 80rpx;
            background: var(--primary-color);
            text-align: center;
            border-radius: 10rpx;
            font-weight: 400;
            font-size: 28rpx;
            color: $uni-bg-color;
            line-height: 80rpx;
        }
    }
}
.back_to_top {
    position: fixed;
    bottom: 200rpx;
    right: 40rpx;
    .image {
        width: 140rpx;
        height: 140rpx;
    }
}
</style>
