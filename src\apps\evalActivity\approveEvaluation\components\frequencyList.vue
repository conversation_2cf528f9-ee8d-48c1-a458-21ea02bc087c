<template>
    <!-- 评价次数 -->
    <div class="frequency_list">
        <view class="frequency_item" v-for="(item, index) in list" :key="item.id">
            <view class="item_title">
                <text>{{ `第${toChinesNum(item.thisCount)}周期评价` }}</text>
                <view class="info_btn" @click="handleInfo(item, index)"> 详情 <uni-icons :type="item.isShow && isThisPageLook ? 'down' : 'right'" size="16" color="#8C8C8C"></uni-icons></view>
            </view>
            <view class="info_item" v-for="j in infoLabel" :key="j.value">
                <text class="label">{{ j.label }}：</text>
                <text
                    class="value"
                    :class="{
                        score: j.value == 'score'
                    }"
                >
                    {{ item[j.value] }}</text
                >
            </view>
            <slot name="ruleInfo" :data="item.rulesList" v-if="!!item.isShow"></slot>
        </view>
    </div>
</template>

<script setup>
import { toChinesNum } from "@/utils/getDate"

const emit = defineEmits(["handlerDetails"])
const props = defineProps({
    list: {
        type: Array,
        default: () => {
            return []
        }
    },
    isThisPageLook: {
        type: Boolean,
        default: true
    }
})
const list = computed(() => props.list)
const isThisPageLook = computed(() => props.isThisPageLook) // 是否在当前页面查看详情？
const infoLabel = [
    {
        value: "score",
        label: "最后得分"
    },
    {
        value: "scoreTime",
        label: "得分时间"
    }
]

async function handleInfo(item, index) {
    item.isShow = !item.isShow
    if (!item.isShow) {
        return
    }
    const params = {
        activityId: item.activityId,
        toPersonId: item.toPersonId,
        rulePersonId: item.rulePersonId,
        score: item.score,
        queryThisFrom: false,
        frequency: toChinesNum(item.thisCount)
    }
    if (!isThisPageLook.value) {
        emit("handlerDetails", params)
        return
    }
    await http.post("/app/evalDayRulePerson/getRulePersonScoreList", params).then(({ data }) => {
        item.rulesList = data?.map((i) => {
            return {
                ...i,
                secondIndicators: i.secondIndicators.map((j) => {
                    return {
                        ...j,
                        indicatorScore: {
                            ...j.indicatorScore,
                            score: j.indicatorScore.totalIndicatorScore
                        }
                    }
                })
            }
        })
    })
}
</script>

<style lang="scss" scoped>
.frequency_list {
    .frequency_item {
        margin-top: 20rpx;
        background: $uni-bg-color;
        border-radius: 12rpx;
        padding: 30rpx;
        .item_title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: 400;
            font-size: 28rpx;
            color: $uni-text-color;
            line-height: 40rpx;
            .info_btn {
                color: $uni-text-color-grey;
            }
        }
        .info_item {
            margin-top: 20rpx;
            font-weight: 400;
            font-size: 28rpx;
            color: $uni-text-color-grey;
            line-height: 40rpx;
            .value {
                color: $uni-text-color;
            }
            .score {
                font-weight: 500;
                font-size: 30rpx;
                color: var(--primary-color);
                line-height: 42rpx;
            }
        }
    }
}
</style>
