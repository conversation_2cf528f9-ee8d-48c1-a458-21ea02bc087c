<template>
    <view class="personnel_info">
        <view class="info_box">
            <div class="info">
                <div class="avatar">
                    <image mode="aspectFill" v-if="personDetails.avatar" class="avatar_img" :src="personDetails.avatar" alt="" />
                    <span v-else>{{ personDetails.toPersonName?.slice(0, 1) }}</span>
                </div>
                <div class="personnel_name">
                    <text class="name ellipsis">{{ personDetails.toPersonName || "-" }}</text>
                    <text class="classes ellipsis">{{ info.classesName || personDetails.orgName || "-" }}</text>
                </div>
            </div>
            <div class="total_score">
                <view class="score">{{ personDetails.totalScore || 0 }}</view>
                <view class="txt">最终得分</view>
            </div>
        </view>
        <view class="evaluation_label">
            评价类型：
            <text class="value">{{ personDetails?.evalTypeName || "-" }}</text>
        </view>
        <view class="evaluation_label">
            活动名称：
            <text class="value">{{ info.title || personDetails.title || "-" }}</text>
        </view>
        <slot name="content" :data="info"></slot>
    </view>
</template>

<script setup>
const props = defineProps({
    info: {
        type: Object,
        default: () => {
            return {}
        }
    },
    personDetails: {
        type: Object,
        default: () => {
            return {}
        }
    }
})

const personDetails = computed(() => props.personDetails)
const info = computed(() => props.info)
</script>

<style lang="scss" scoped>
.personnel_info {
    background: $uni-bg-color;
    width: calc(100% - 60rpx);
    min-height: 100rpx;
    border-radius: 20rpx;
    padding: 30rpx;
    .info_box {
        display: flex;
        margin-bottom: 22rpx;
        justify-content: space-between;
        border-bottom: 1rpx solid #d8d8d8;
        padding-bottom: 20rpx;
    }
    .info {
        display: flex;

        .avatar {
            min-width: 100rpx;
            width: 100rpx;
            height: 100rpx;
            background: var(--primary-color);
            border-radius: 50%;
            text-align: center;
            position: relative;
            font-weight: 600;
            font-size: 47rpx;
            color: $uni-bg-color;
            line-height: 100rpx;

            .avatar_img {
                width: 100rpx;
                border-radius: 50%;
                height: 100rpx;
            }
        }
        .personnel_name {
            margin-left: 30rpx;
            display: flex;
            flex-direction: column;
            font-size: 28rpx;
            justify-content: space-between;
            padding: 6rpx 0;
            color: $uni-text-color;
            line-height: 40rpx;
            .name {
                font-weight: 600;
            }
            .classes {
                font-weight: 400;
            }
        }
    }
    .total_score {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        flex: 1;
        min-width: 150rpx;
        .score {
            font-size: 36rpx;
            color: #333333;
            line-height: 50rpx;
        }
        .txt {
            font-weight: 400;
            font-size: 28rpx;
            color: #999999;
            line-height: 40rpx;
        }
    }
    .evaluation_label {
        font-weight: 400;
        font-size: 28rpx;
        color: $uni-text-color-grey;
        line-height: 40rpx;
        margin-bottom: 12rpx;
        .value {
            color: $uni-text-color;
        }
    }
}
</style>
