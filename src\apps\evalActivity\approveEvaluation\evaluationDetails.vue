<template>
    <view>
        <yd-page-view ref="page" :hideLeft="true" :hideTop="true" :refresherOnly="true" @onRefresh="init" :auto-show-back-to-top="true">
            <div class="evaluation_info">
                <!-- 背景 -->
                <div class="background_img">
                    <image class="evaluate_ban" mode="widthFix" src="@nginx/workbench/evalActivity/teacher/evaluateBan.png" alt="" />
                </div>
                <view class="evaluation_info_container">
                    <!-- 顶部 -->
                    <div class="top_nav_bar">
                        <uni-icons @click="clickLeft" class="icon_class" type="left" size="22"></uni-icons>
                        <view class="bar_title">评价详情</view>
                    </div>
                    <!-- 分数 -->
                    <view class="score_container">
                        <view class="score_title">
                            <text class="score">{{ queryParams.score || 0 }}</text
                            >分
                        </view>
                        <text class="text">最后得分</text>
                    </view>

                    <!-- 评价规则详情 -->
                    <view class="evaluation_container">
                        <view class="title_box">
                            <text class="title">
                                第{{ queryParams.frequency || 0 }}周期评价
                                <text class="score">{{ queryParams.score || 0 }}分</text>
                            </text>
                            <view class="my_record" @click="myRecord">
                                我的评价记录
                                <uni-icons type="right" size="16" color="var(--primary-color)"></uni-icons>
                            </view>
                        </view>

                        <evaluate-rule :list="dataList" :isUploadimage="false" :isShowInput="false" :isShowRemarks="false">
                            <template #info="{ data }">
                                <view class="score_label">
                                    <text>本次评分：</text>
                                    <text class="value this_score">{{ data.indicatorScore.thisIndicatorScore || 0 }}分</text>
                                </view>
                                <view class="score_label">
                                    <text>他人评分：</text>
                                    <text class="value">{{ data.indicatorScore.othersTotalScore || 0 }}分</text>
                                </view>
                                <view class="score_label">
                                    <text>最后得分：</text>
                                    <text class="value">{{ data.indicatorScore.totalIndicatorScore || 0 }}分</text>
                                </view>
                                <!-- v-if="data.indicatorScore.comment || data.indicatorScore.imgPaths || data.indicatorScore.videoPaths" -->
                                <view class="comment" >
                                    <view class="comment_title">
                                        <text>更多评分： </text>
                                        <text class="more" @click="moreComment(data.indicatorScore)">
                                            查看
                                            <uni-icons type="right" size="16" color="var(--primary-color)"></uni-icons>
                                        </text>
                                    </view>
                                </view>
                                <view class="comment" v-if="data.indicatorScore.comment">
                                    <view> 评语： </view>
                                    <text class="value"> {{ data.indicatorScore.comment }} </text>
                                </view>
                                <!-- 上传图片/视频 -->
                                <video-image-com :data="data" :isEdit="false"> </video-image-com>
                                <!-- v-if="queryParams.isApprove == 'true' && data.indicatorScore.approveScore" -->
                                <view class="approve_score" v-if="data.indicatorScore.approveScore !=null">
                                    <text>审核得分：</text>
                                    <!-- || data.indicatorScore.totalIndicatorScore  -->
                                    <text class="value">{{ data.indicatorScore.approveScore || 0 }}分</text>
                                </view>
                                <!-- v-if="queryParams.isApprove == 'true' && data.indicatorScore.approveScore" -->
                                <view class="score_label" v-if="data.indicatorScore.approveScore !=null">
                                    <text>备注：</text>
                                    <text class="value remarks">{{ data.indicatorScore.remark || "-" }}</text>
                                </view>
                            </template>
                        </evaluate-rule>
                    </view>
                </view>
            </div>
            <template #backToTop>
                <div class="back_to_top">
                    <image class="image" src="@nginx/workbench/evalActivity/backToTop.png" mode="scaleToFill" />
                </div>
            </template>
        </yd-page-view>
        <MoreComment ref="moreCommentRef" />
    </view>
</template>
<script setup>
import MoreComment from "./moreComment.vue"
import videoImageCom from "../components/videoImageCom.vue"
import EvaluateRule from "../components/evaluateRule.vue"

const moreCommentRef = ref(null)
const queryParams = ref({})
const page = ref(null)
const dataList = ref([])
function init() {
    getList()
    setTimeout(() => {
        // 1.5秒之后停止刷新动画
        page.value.paging.complete([])
    }, 500)
}

function getList() {
    http.post("/app/evalDayRulePerson/getRulePersonScoreList", { ...queryParams.value, queryThisFrom: queryParams.value.queryThisFrom == "true", isApprove: queryParams.value.isApprove == "true" }).then(({ data }) => {
        dataList.value = data?.map((i) => {
            return {
                ...i,
                secondIndicators: i.secondIndicators.map((j) => {
                    return {
                        ...j,
                        indicatorScore: {
                            ...j.indicatorScore,
                            score: j.indicatorScore.minScore,
                            imgPathsList: j.indicatorScore?.imgPaths ? j.indicatorScore?.imgPaths?.split(",") : []
                        }
                    }
                })
            }
        })
    })
}

// 返回
function clickLeft() {
    uni.navigateBack()
}

function moreComment(data) {
    moreCommentRef.value.open(data.scoreRecordList)
}

const myRecord = () => {
    navigateTo({
        url: "/apps/evalActivity/immediateEvaluation/evaluationRecord",
        query: queryParams.value
    })
}

onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })

    queryParams.value = options
    console.log(queryParams.value)
    getList()
})
</script>

<style lang="scss" scoped>
.evaluation_info {
    min-height: calc(100vh - var(--status-bar-height));
    background: $uni-bg-color-grey;
    width: 100%;
    /* #ifdef MP-WEIXIN */
    padding-top: calc(var(--status-bar-height) + 36rpx);
    /* #endif */

    .background_img {
        width: 100%;

        .evaluate_ban {
            width: 100%;
            left: 0;
            position: absolute;
            top: 0;
            height: auto;
        }
    }
    .evaluation_info_container {
        position: relative;

        .top_nav_bar {
            z-index: 99;
            width: 100vw;
            height: 88rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            position: fixed;
            top: 0;
            left: 0;
            background: url("@nginx/workbench/evalActivity/teacher/evaluateBan.png") no-repeat;
            background-size: cover;
            /* #ifdef MP-WEIXIN */
            padding-top: calc(var(--status-bar-height) + 36rpx);
            /* #endif */

            .icon_class {
                position: absolute;
                left: 30rpx;
            }

            .bar_title {
                font-weight: 500;
                font-size: 34rpx;
                color: $uni-text-color;
                line-height: 48rpx;
            }
        }
        .score_container {
            padding-top: 68rpx;
            display: flex;
            flex-direction: column;
            text-align: center;
            margin: 40rpx 0;
            .score_title {
                font-size: 48rpx;
                color: var(--primary-color);
                .score {
                    font-size: 120rpx;
                    font-weight: 600;
                }
            }
            .text {
                font-weight: 400;
                font-size: 32rpx;
                color: $uni-text-color;
                line-height: 44rpx;
            }
        }
        .evaluation_container {
            margin-top: 30rpx;
            padding: 48rpx 52rpx;
            background: #ffffff;
            border-radius: 60rpx 60rpx 0rpx 0rpx;
            .title_box {
                padding-bottom: 30rpx;
                margin-bottom: 30rpx;
                border-bottom: 1rpx solid #d8d8d8;
                display: flex;
                align-items: center;
                justify-content: space-between;
                .title {
                    font-weight: 600;
                    font-size: 30rpx;
                    color: $uni-text-color;
                    line-height: 42rpx;
                    .score {
                        color: var(--primary-color);
                    }
                }
                .my_record {
                    display: flex;
                    align-items: center;
                    font-weight: 500;
                    font-size: 24rpx;
                    color: var(--primary-color);
                    line-height: 40rpx;
                }
            }
        }
    }
    .score_label {
        font-weight: 400;
        font-size: 28rpx;
        color: $uni-text-color-grey;
        line-height: 40rpx;
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        margin-top: 20rpx;
        .value {
            font-weight: 400;
            font-size: 28rpx;
            color: $uni-text-color;
            line-height: 40rpx;
            flex: 1;
            text-align: right;
        }
        .this_score {
            color: var(--primary-color);
        }
        .remarks {
            text-align: left;
        }
    }

    .comment {
        display: flex;
        flex-direction: column;
        margin-top: 20rpx;
        font-weight: 400;
        font-size: 28rpx;
        color: $uni-text-color-grey;
        line-height: 40rpx;
        .comment_title {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .value {
            padding-top: 10rpx;
        }
        .more {
            color: var(--primary-color);
        }
    }
    .approve_score {
        font-weight: 500;
        margin-top: 24rpx;
        font-size: 28rpx;
        color: var(--primary-color);
        line-height: 40rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .value {
            font-weight: 400;
        }
    }
}
.back_to_top {
    position: fixed;
    bottom: 200rpx;
    right: 40rpx;
    .image {
        width: 140rpx;
        height: 140rpx;
    }
}
</style>
