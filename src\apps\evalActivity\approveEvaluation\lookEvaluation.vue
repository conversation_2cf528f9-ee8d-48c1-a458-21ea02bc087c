<template>
    <div class="approve_evaluation">
        <z-paging ref="paging" v-model="frequencyList" @query="queryList" :auto="false">
            <template #top>
                <uni-nav-bar statusBar fixed title="查看评价" @clickLeft="clickLeft" :border="false" left-icon="left" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
                <div class="personnel">
                    <personnel-info :info="queryParams" :personDetails="personDetails">
                        <template #content>
                            <view class="evaluation_label">
                                <text>寄语：</text>
                                <text class="value">{{ personDetails.comment || "-" }}</text>
                            </view>
                        </template>
                    </personnel-info>
                </div>
            </template>
            <view class="page_container">
                <frequencyList :list="frequencyList" :isThisPageLook="false" @handlerDetails="handlerDetails"> </frequencyList>
            </view>
            <template #bottom v-if="frequencyList && frequencyList.length && queryParams.isApprove === 'true' && personDetails.isApproveUser">
                <view class="footer_btn">
                    <button class="btn" type="primary" @click="goApprove">评价审核</button>
                </view>
            </template>
            <template #empty>
                <yd-empty text="暂无数据" />
            </template>
        </z-paging>
    </div>
</template>

<script setup>
import PersonnelInfo from "./components/personnelInfo.vue"
import FrequencyList from "./components/frequencyList.vue"

const paging = ref(null)
const queryParams = ref({})
const frequencyList = ref([])
const personDetails = ref({})

// 调用List数据
function queryList(pageNo, pageSize) {
    const { toPersonId, activityId = "" } = queryParams.value
    const parmas = {
        pageNo,
        pageSize,
        toPersonId,
        activityId
    }
    http.post("/app/evalDayRulePerson/pageDayPersonScore", parmas).then(({ data }) => {
        paging.value?.complete(data?.list || false)
    })
}

// 返回
function clickLeft() {
    uni.navigateBack()
}

function goApprove() {
    navigateTo({
        url: "/apps/evalActivity/approveEvaluation/approve",
        query: queryParams.value
    })
}

// 查看详情
function handlerDetails(params) {
    console.log(params)

    navigateTo({
        url: "/apps/evalActivity/approveEvaluation/evaluationDetails",
        query: { ...params, isApprove: personDetails.value.isApprove || false, queryThisFrom: false }
    })
}

function getPersonDetails() {
    http.post("/app/evalDayRulePerson/getEvalRulePersonDetails", { rulePersonId: queryParams.value.id }).then((res) => {
        personDetails.value = res.data
    })
}

onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    queryParams.value = options
    getPersonDetails()
    nextTick(() => {
        paging.value?.reload()
    })
})
</script>

<style lang="scss" scoped>
.approve_evaluation {
    min-height: 100vh;
    background: $uni-bg-color-grey;
    padding: 30rpx 30rpx 166rpx 30rpx;
    .personnel {
        padding: 30rpx;
    }
}
.page_container {
    padding: 0rpx 30rpx 30rpx 30rpx;
    .split_line {
        margin: 24rpx 0;
        height: 1rpx;
        width: 100%;
        background: $uni-border-color;
    }
    .score_label {
        font-weight: 400;
        font-size: 28rpx;
        color: $uni-text-color-grey;
        line-height: 40rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 20rpx;
        .value {
            font-weight: 400;
            font-size: 28rpx;
            color: $uni-text-color;
            line-height: 40rpx;
        }
        .this_score {
            color: var(--primary-color);
        }
    }
    .comment {
        display: flex;
        flex-direction: column;
        margin-top: 20rpx;
        font-weight: 400;
        font-size: 28rpx;
        color: $uni-text-color-grey;
        line-height: 40rpx;
        .comment_title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16rpx;
        }
        .more {
            color: var(--primary-color);
        }
    }
    .score_title {
        font-weight: 500;
        margin-top: 24rpx;
        font-size: 28rpx;
        color: var(--primary-color);
        line-height: 40rpx;
    }
}

.footer_btn {
    width: calc(100vw - 60rpx);
    background: $uni-bg-color;
    height: 92rpx;
    padding: 30rpx 30rpx 44rpx 30rpx;

    .btn {
        width: 100%;
        height: 100%;
        color: $uni-bg-color;
        background: var(--primary-color);
        font-weight: 400;
        font-size: 32rpx;
        color: $uni-text-color-inverse;
        line-height: 44rpx;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

.evaluation_label {
    font-weight: 400;
    font-size: 28rpx;
    color: $uni-text-color-grey;
    line-height: 40rpx;
    margin-bottom: 12rpx;
    .value {
        color: $uni-text-color;
    }
}
</style>
