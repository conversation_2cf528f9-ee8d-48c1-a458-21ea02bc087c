<template>
    <uni-popup ref="selectPopup" type="bottom" :is-mask-click="false" :safe-area="false">
        <div class="more_comment">
            <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="close" title="更多评语" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
            <view class="list" v-if="list && list.length">
                <view class="item" v-for="(item, index) in list" :key="index">
                    <view class="top_title">
                        <view class="user">
                            <div class="avatar">
                                <span>{{ item.createBy?.slice(0, 1) }}</span>
                            </div>
                            <view class="name"> {{ item.createBy || "-" }}</view>
                        </view>
                        <view class="date">{{ item.scoreTime || "-" }}</view>
                    </view>
                    <view class="content">
                        {{ item.comment }}
                    </view>
                    <!-- 上传图片/视频 -->
                    <video-image-com :data="item" :isEdit="false"> </video-image-com>
                </view>
            </view>
            <yd-empty text="暂无数据" v-else :isMargin="true" />
        </div>
    </uni-popup>
</template>

<script setup>
const selectPopup = ref(null)
import videoImageCom from "../components/videoImageCom.vue"
const list = ref([])

const open = (data = []) => {
    selectPopup.value.open()
    list.value = data?.map((i) => {
        return {
            ...i,
            indicatorScore: {
                imgPaths: i.imgPaths,
                videoPaths: i.videoPaths,
                imgPathsList: i?.imgPaths ? i.imgPaths?.split(",") : []
            }
        }
    })
}

const close = () => {
    selectPopup.value.close()
}

defineExpose({ open, close })
</script>

<style lang="scss" scoped>
.more_comment {
    background: $uni-bg-color-grey;
    height: 100vh;
    overflow-y: auto;
}
.list {
    min-height: calc(100vh - 120rpx);
    max-height: calc(100vh - 120rpx);
    overflow: auto;
    padding: 20rpx 30rpx;
    background: $uni-bg-color-grey;
    .item {
        padding: 24rpx 40rpx;
        background: $uni-bg-color;
        border-radius: 20rpx;
        margin-bottom: 30rpx;
        .top_title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-bottom: 24rpx;
            margin-bottom: 16rpx;
            border-bottom: 1rpx solid $uni-border-color;
            .user {
                display: flex;
                align-items: center;
                .avatar {
                    width: 50rpx;
                    height: 50rpx;
                    background: var(--primary-color);
                    border-radius: 50%;
                    text-align: center;
                    position: relative;
                    font-weight: 600;
                    font-size: 24rpx;
                    line-height: 48rpx;
                    color: $uni-text-color-inverse;

                    .avatar_img {
                        width: 50rpx;
                        border-radius: 50%;
                        height: 50rpx;
                    }
                }
                .name {
                    margin-left: 16rpx;
                    font-weight: 500;
                    font-size: 28rpx;
                    color: $uni-text-color;
                    line-height: 40rpx;
                }
            }
            .date {
                font-weight: 400;
                font-size: 26rpx;
                color: #666666;
                line-height: 36rpx;
            }
        }
        .content {
            font-weight: 400;
            font-size: 26rpx;
            color: #666666;
            line-height: 36rpx;
        }
    }
}
</style>
