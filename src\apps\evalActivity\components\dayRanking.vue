<template>
    <!-- 今日得分排行 -->
    <div class="day_ranking">
        <div class="ranking_title">
            <div class="left">
                <image src="@nginx/workbench/evalActivity/teacher/ranking_logo.png" class="ranking_logo" alt="" />
                <span class="text">今日得分排行</span>
            </div>
            <div class="right" @click="todayRanking">
                <span>全部</span>
                <uni-icons class="icon_class" type="right" size="14"></uni-icons>
            </div>
        </div>
        <div class="tabs_class">
            <div class="tabs student" :class="active == 'student' ? 'active' : ''" @click="active = 'student'">学生</div>
            <div class="tabs teacher" :class="active == 'teacher' ? 'active' : ''" @click="active = 'teacher'">老师</div>
        </div>
        <div class="ranking_list">
            <div class="ranking_item" v-for="(item, index) in state.rankingList" :key="index">
                <div class="ranking_img_box">
                    <img class="ranking_bg" :class="item.ranking" :src="item.rankingBg" alt="" />
                    <img class="ranking_crown" :class="item.ranking + '_crown'" :src="item.rankingCrown" alt="" />
                </div>
                <div class="title_box" v-if="item.maxScore" :class="item.ranking">
                    <span class="text num ellipsis">{{ item.maxScore }}分</span>
                    <span class="text name ellipsis">{{ item.toPersonName }}</span>
                    <span class="text from ellipsis">{{ item.classes || item.orgName }}</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { onLoad } from "@dcloudio/uni-app"
const evalTypeId = ref("")
const active = ref("student")

const sortImg = {
    "2_": {
        sort: 2,
        ranking: "two",
        rankingBg: "@nginx/workbench/evalActivity/teacher/two_bg.png",
        rankingCrown: "@nginx/workbench/evalActivity/teacher/two_crown.png"
    },
    "1_": {
        sort: 1,
        ranking: "one",
        rankingBg: "@nginx/workbench/evalActivity/teacher/one_bg.png",
        rankingCrown: "@nginx/workbench/evalActivity/teacher/one_crown.png"
    },
    "3_": {
        sort: 3,
        ranking: "three",
        rankingBg: "@nginx/workbench/evalActivity/teacher/three_bg.png",
        rankingCrown: "@nginx/workbench/evalActivity/teacher/three_crown.png"
    }
}

const state = reactive({
    rankingList: [...Object.values(sortImg)],
    pagination: {
        pageNo: 1,
        pageSize: 10
    }
})

const dayScoreMaxCountInfo = () => {
    const params = {
        evalTypeId: evalTypeId.value,
        identity: active.value == "teacher" ? 1 : 0,
        ...state.pagination
    }
    http.get("/app/evalDayRulePerson/dayScoreMaxCount", params).then(({ data }) => {
        if (data.length) {
            let _rankingList = [...Object.values(sortImg)].map((v, idx) => {
                let params = v
                if (data[v.sort - 1]) {
                    params = {
                        ...params,
                        ...data[v.sort - 1]
                    }
                }
                return params
            })
            state.rankingList = _rankingList
        } else {
            state.rankingList = Object.values(sortImg)
        }
    })
}

function todayRanking() {
    navigateTo({
        url: "/apps/evalActivity/teacher/todayRanking",
        query: { evalTypeId:
            evalTypeId.value,
            identity: active.value == "teacher" ? 1 : 0
        }
    })
}

watch(
    () => active.value,
    () => {
        dayScoreMaxCountInfo()
    }
)
onLoad((item) => {
    evalTypeId.value = item.code
    dayScoreMaxCountInfo()
})
defineExpose({ dayScoreMaxCountInfo })
</script>

<style lang="scss" scoped>
.day_ranking {
    width: calc(100% - 60rpx);
    height: 530rpx;
    padding: 30rpx;
    background: $uni-bg-color;
    border-radius: 20rpx;
    padding-bottom: 0;

    .ranking_title {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .left,
        .right {
            display: flex;
            align-items: center;
        }

        .left {
            font-weight: 600;
            font-size: 30rpx;
            color: #333333;
            line-height: 42rpx;

            .ranking_logo {
                width: 40rpx;
                height: 40rpx;
                margin-right: 20rpx;
            }
        }

        .right {
            font-weight: 400;
            font-size: 28rpx;
            color: #999999;
            line-height: 40rpx;

            .icon_class {
                color: #8c8c8c;
            }
        }
    }

    .tabs_class {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 60rpx;
        margin-top: 40rpx;

        .tabs {
            height: 56rpx;
            width: 150rpx;
            border: 1rpx solid var(--primary-color);
            text-align: center;
            line-height: 56rpx;
        }

        .student {
            border-radius: 32rpx 0rpx 0rpx 32rpx;
            color: var(--primary-color);
        }

        .teacher {
            border-radius: 0rpx 32rpx 32rpx 0rpx;
            color: var(--primary-color);
        }

        .active {
            color: $uni-bg-color;
            background: var(--primary-color);
        }
    }

    .ranking_list {
        margin-top: 120rpx;
        display: flex;
        justify-content: space-between;

        .ranking_item {
            position: relative;
            height: 260rpx;
            width: 100%;

            .ranking_img_box {
                width: 190rpx;
                height: 100%;

                .ranking_bg {
                    position: absolute;
                    left: 0;
                    width: 190rpx;
                    height: 216rpx;
                }

                .ranking_crown {
                    position: absolute;
                    left: 50rpx;
                    width: 88rpx;
                    height: 88rpx;
                }
            }
        }

        .title_box {
            position: absolute;
            left: 0;
            width: 100%;
            height: 70%;
            display: flex;
            color: #262626;
            flex-direction: column;
            justify-content: center;
            align-items: center;

            .text {
                text-align: center;
            }

            .num,
            .name {
                width: 100%;
                font-weight: 600;
                font-size: 28rpx;
                line-height: 40rpx;
            }

            .name {
                padding: 10rpx 0rpx;
            }

            .from {
                font-weight: 400;
                width: 100%;
                font-size: 24rpx;
                line-height: 34rpx;
            }
        }

        .one {
            bottom: 80rpx;
        }

        .two {
            bottom: 40rpx;
        }

        .three {
            bottom: 0rpx;
        }

        .one_crown {
            top: -80rpx;
        }

        .two_crown {
            top: -40rpx;
        }

        .three_crown {
            top: 0rpx;
        }
    }
}
</style>
