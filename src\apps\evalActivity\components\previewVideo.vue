<template>
    <uni-popup mask-background-color="#000" style="z-index: 999" ref="previewPopup" @click.stop type="bottom" :is-mask-click="false" :safe-area="false">
        <div class="preview_video" v-if="videoList && videoList.length">
            <swiper class="swiper" indicator-color="#fff" indicator-active-color="var(--primary-color)" circular :indicator-dots="true" :autoplay="false" :current="current" @change="changeSwiper">
                <swiper-item class="swiper_item" v-for="(item, index) in videoList" :key="index">
                    <view class="video_back" @click.stop="close"><uni-icons type="left" size="16" color="#fff"></uni-icons></view>
                    <view class="video_title">{{ `预览视频（${index + 1}/${videoList.length}）` }}</view>
                    <view class="item_box" v-if="item">
                        <!-- :poster="isIOS ? '@nginx/workbench/videoAlbum/bg.png' : ''" -->
                        <video :id="`video${index}`" :enable-progress-gesture="false" class="swiper_video" :src="item" :autoplay="false"></video>
                    </view>
                </swiper-item>
            </swiper>
        </div>
    </uni-popup>
</template>

<script setup>
import { computed } from "vue"

const props = defineProps({
    list: {
        type: Array,
        default: () => []
    }
})
const isIOS = computed(() => {
    // #ifdef H5 || H5-WEIXIN
    return window.webkit || /(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)
    // #endif
    // #ifdef APP-PLUS || MP-WEIXIN
    return false
    // #endif
})
const previewPopup = ref(null)
const videoList = computed(() => props.list)
const instance = getCurrentInstance()
const current = ref(0) // 当前的视频
function open(index) {
    current.value = index
    previewPopup.value.open()
}

function close() {
    previewPopup.value.close()
}
function changeSwiper(event) {
    const nowVideo = uni.createVideoContext(`video${current.value}`, instance)
    console.log(nowVideo)
    nowVideo?.pause()
    current.value = event.detail.current
}

defineExpose({ open, close })
</script>

<style lang="scss" scoped>
.preview_video {
    max-height: 100vh;
    height: 100vh;
    width: 100vw;
    .swiper {
        height: 100vh;
        width: 100vw;
        .swiper_item {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 450rpx;
            position: relative;
            .item_box {
                width: 100%;
                height: 450rpx;
                .swiper_video {
                    width: 100vw;
                    height: 100%;
                }
            }
            .video_title {
                position: absolute;
                top: calc(30rpx + var(--status-bar-height));
                //  #ifdef H5 || H5-WEIXIN
                top: 30rpx;
                //  #endif
                left: 0rpx;
                width: 100%;
                text-align: center;
                color: $uni-text-color-inverse;
            }
            .video_back {
                position: absolute;
                padding: 0rpx 30rpx 30rpx 0rpx;
                top: calc(30rpx + var(--status-bar-height));
                //  #ifdef H5 || H5-WEIXIN
                top: 30rpx;
                //  #endif
                z-index: 2;
                left: 30rpx;
            }
        }
    }
}
</style>
