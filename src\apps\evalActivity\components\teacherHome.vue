<template>
    <yd-page-view ref="page" :hideLeft="true" :hideTop="true" :refresherOnly="true" @onRefresh="init">
        <div class="evaluate_home">
            <!-- 背景 -->
            <div class="background_img">
                <image class="evaluate_ban" mode="widthFix" src="@nginx/workbench/evalActivity/teacher/evaluateBan.png" alt="" />
            </div>
            <div class="evaluate_content">
                <!-- 顶部 -->
                <div class="top_nav_bar">
                    <uni-icons @click="routerBack" class="icon_class" type="left" size="22"></uni-icons>
                    <div class="bar_title">评价</div>
                </div>
                <!-- 模块入口 -->
                <div class="module_list">
                    <div @click="handlerModular(item)" v-for="item in moduleList" :key="item.code" class="module_item">
                        <image class="img" :src="item.img" alt="" />
                        <span class="title">{{ item.title }}</span>
                        <uni-icons class="icon_class" type="right" size="18"></uni-icons>
                    </div>
                </div>
                <!-- 统计 -->
                <div class="statistics">
                    <div class="statistics_item" :class="item.code == 'medal' ? 'border' : ''" v-for="item in statisticsList" :key="item.code">
                        <span>{{ item.title }}</span>
                        <span class="num">{{ item.num }}</span>
                    </div>
                </div>
                <!-- 今日得分排行 -->
                <day-ranking ref="dayRankingRef" />
                <!-- 参与评价占比  -->
                <proportion ref="proportionRef" />
                <!-- 评价活动 -->
                <activity v-model:activityParams="state.add_evaluation" />
            </div>
        </div>
    </yd-page-view>
</template>

<script setup>
import DayRanking from "./dayRanking.vue"
import Proportion from "./proportion.vue"
import Activity from "./activity.vue"
import { onLoad } from "@dcloudio/uni-app"

const page = ref(null)
const proportionRef = ref(null)
const dayRankingRef = ref(null)
const moduleList = [
    {
        url: "/apps/evalActivity/immediateEvaluation/index",
        img: "@nginx/workbench/evalActivity/teacher/add_evaluation.png",
        title: "立即评价",
        code: "add_evaluation"
    },
    {
        url: "/apps/evalActivity/teacher/issueMedals",
        img: "@nginx/workbench/evalActivity/teacher/issue_medals.png",
        title: "发放勋章",
        code: "issue_medals"
    },
    {
        url: "/apps/evalActivity/teacher/evaluationRecord",
        img: "@nginx/workbench/evalActivity/teacher/evaluation_record.png",
        title: "评价记录",
        code: "evaluation_record"
    },
    {
        url: "/apps/evalActivity/teacher/medalRecord",
        img: "@nginx/workbench/evalActivity/teacher/medal_record.png",
        title: "勋章记录",
        code: "medal_record"
    }
]
const statisticsList = ref([
    {
        title: "兑换总次数",
        code: "exchangeCount",
        num: 0
    },
    {
        title: "勋章总数",
        code: "medalCount",
        num: 0
    },
    {
        title: "积分卡总数",
        code: "scoreCardCount",
        num: 0
    }
])
const evalTypeId = ref("")
const state = reactive({
    add_evaluation: {},
    issue_medals: null,
    evaluation_record: {},
    medal_record: {}
})

const handlerModular = (item) => {
    let params = {}
    // 立即评价
    if (item.code == "add_evaluation") {
        // 立即评价
        params = state[item.code]
        if (state.add_evaluation.operationFlag == 0) {
            uni.showToast({
                title: "暂无评价权限！",
                icon: "none"
            })
            return
        }
    } else if (item.code == "issue_medals") {
        // 发放勋章
        params = { issue_medals: evalTypeId.value }
    } else if (item.code == "medal_record") {
        // 勋章记录
        params = { medal_record: evalTypeId.value }
    } else {
        // 评价记录
        params = { evaluation_record: evalTypeId.value }
    }
    navigateTo({
        url: item.url,
        query: params
    })
}
// 获取兑换次数、勋章总数、积分卡总数
const getEmsCountInfo = () => {
    http.post("/app/evalStatistic/getEmsCount", { evalTypeId: evalTypeId.value }).then(({ data }) => {
        statisticsList.value.forEach((v) => {
            v.num = data[v.code]
        })
    })
}

// 评价活动
const getEvaluateInfo = () => {
    const params = {
        pageNo: 1,
        pageSize: 1,
        homePage: true,
        evalTypeId: evalTypeId.value
    }
    http.post("/app/evalActivity/page", params).then(({ data }) => {
        if (data.list.length) {
            state.add_evaluation = data.list[0]
        }
    })
}

function init() {
    try {
        getEmsCountInfo()
        // 评价活动
        getEvaluateInfo()
        proportionRef.value?.listEvalPersonPortionInfo()
        dayRankingRef.value?.dayScoreMaxCountInfo()
        setTimeout(() => {
            // 1.5秒之后停止刷新动画
            page.value.paging.complete([])
        }, 500)
    } catch (error) {
        setTimeout(() => {
            // 1.5秒之后停止刷新动画
            page.value.paging.complete([])
        }, 500)
    }
}

onLoad((item) => {
    evalTypeId.value = item.code
    getEmsCountInfo()
    // 评价活动
    getEvaluateInfo()
})
</script>

<style lang="scss" scoped>
.evaluate_home {
    min-height: calc(100vh - var(--status-bar-height));
    background: $uni-bg-color-grey;
    width: 100%;
    /* #ifdef MP-WEIXIN */
    padding-top: calc(var(--status-bar-height) + 36rpx);
    /* #endif */

    .background_img {
        width: 100%;

        .evaluate_ban {
            width: 100%;
            left: 0;
            position: absolute;
            top: 0;
            height: auto;
        }
    }

    .evaluate_content {
        position: relative;
        width: calc(100% - 60rpx);
        min-height: 100vh;
        padding: 0rpx 30rpx;
        padding-bottom: 40rpx;

        .top_nav_bar {
            z-index: 99;
            width: 100vw;
            height: 88rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            position: fixed;
            top: 0;
            left: 0;
            background: url("@nginx/workbench/evalActivity/teacher/evaluateBan.png") no-repeat;
            background-size: cover;
            /* #ifdef MP-WEIXIN */
            padding-top: calc(var(--status-bar-height) + 36rpx);
            /* #endif */

            .icon_class {
                position: absolute;
                left: 20rpx;
            }

            .bar_title {
                font-weight: 500;
                font-size: 34rpx;
                color: $uni-text-color;
                line-height: 48rpx;
            }
        }
    }
}

.module_list {
    padding-top: 88rpx;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    .module_item {
        padding: 20rpx;
        width: 290rpx;
        height: 100rpx;
        background: $uni-bg-color;
        border-radius: 20rpx;
        box-shadow: 10rpx 10rpx 5rpx #c9ece3;
        margin-bottom: 20rpx;
        display: flex;
        align-items: center;

        .img {
            width: 116rpx;
            height: 116rpx;
        }

        .title {
            font-weight: 600;
            font-size: 28rpx;
            color: $uni-text-color;
            line-height: 40rpx;
        }

        .icon_class {
            line-height: 40rpx;
            font-weight: 600;
            color: $uni-text-color;
        }
    }
}

.statistics {
    height: 120rpx;
    background: $uni-bg-color;
    border-radius: 20rpx;
    display: flex;
    padding: 30rpx 0rpx;
    margin-bottom: 20rpx;

    .statistics_item {
        width: 30%;
        padding: 0rpx 30rpx;
        display: flex;
        flex-direction: column;

        span {
            font-weight: 400;
            font-size: 28rpx;
            color: rgba(0, 0, 0, 0.45);
            line-height: 40rpx;
        }

        .num {
            font-family: Helvetica;
            font-size: 40rpx;
            color: $uni-text-color;
            padding-top: 12rpx;
            line-height: 48rpx;
        }
    }

    .border {
        border-left: 1rpx solid#D9D9D9;
        border-right: 1rpx solid #d9d9d9;
    }
}
</style>
