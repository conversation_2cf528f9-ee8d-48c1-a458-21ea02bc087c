<template>
    <div>
        <view class="upload_box">
            <view class="add_upload" @click="handleUpload" v-if="isEdit && (data.indicatorScore?.evalScoreTypeList?.includes('image') || data.indicatorScore?.evalScoreTypeList?.includes('video'))">
                <uni-icons type="plusempty" size="30"></uni-icons>
                <text>点击上传</text>
            </view>
            <!-- 图片列表 -->
            <view v-for="(url, urlIndex) in imageList" :key="url" class="video_image__item">
                <image @click="previewImage(urlIndex)" :src="url" mode="aspectFill" class="image_item"></image>
                <view class="delect_icon" @click="delectUrl('image', urlIndex)" v-if="isEdit">
                    <uni-icons type="clear" size="20" color="#606060"></uni-icons>
                </view>
            </view>
            <!-- 视频 -->
            <view class="video_image__item" v-if="videoPaths && videoPaths.length">
                <video :enable-progress-gesture="false" :show-center-play-btn="false" :controls="false" :src="videoPaths[0]" class="image_item"></video>
                <div class="video_mask">
                    <image src="@nginx/workbench/videoAlbum/videoPlay.png" alt="" class="video_play" @click="previewVideoFn(urlIndex)" />
                </div>
                <view class="delect_icon" @click="delectUrl('video')" v-if="isEdit">
                    <uni-icons type="clear" size="20" color="#606060"></uni-icons>
                </view>
            </view>
        </view>
        <slot></slot>
        <!-- 视频预览 -->
        <preview-video :list="videoPaths" ref="previewVideoRef" />
    </div>
</template>

<script setup>
import { computed } from "vue"
import PreviewVideo from "./previewVideo.vue"
const emit = defineEmits(["delectUrl", "upload"])

const props = defineProps({
    isEdit: {
        type: Boolean,
        default: true
    },
    data: {
        type: Object,
        default: () => {
            return {}
        }
    }
})

const isEdit = computed(() => props.isEdit)
const previewVideoRef = ref(null) // 视频预览弹框
const videoPaths = computed(() => {
    const path = props.data?.indicatorScore?.videoPaths || ""
    return path ? path?.split(",") : []
})
const imageList = computed(() => {
    return props.data?.indicatorScore?.imgPathsList || []
})

function previewImage(index) {
    uni.previewImage({
        urls: imageList.value,
        current: index
    })
}

function previewVideoFn(index) {
    previewVideoRef.value.open(index)
}

function delectUrl(type, index) {
    emit("delectUrl", type, index)
}

// 上传视频/图片
function handleUpload() {
    emit("upload")
}
</script>

<style lang="scss" scoped>
.upload_box {
    margin-top: 24rpx;
    display: grid;
    grid-template-columns: repeat(4, 1fr); /* 一行显示4列 */
    gap: 9rpx; /* 元素之间的间距 */

    .add_upload {
        width: 144rpx;
        height: 144rpx;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        background: $uni-bg-color-grey;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 400;
        font-size: 24rpx;
        color: rgba(0, 0, 0, 0.45);
        line-height: 40rpx;
    }

    .video_image__item {
        position: relative;
        width: 144rpx;
        height: 144rpx;

        .image_item {
            width: 144rpx;
            height: 144rpx;
        }
        .delect_icon {
            position: absolute;
            border-radius: 50%;
            right: -20rpx;
            top: -10rpx;
            z-index: 100;
        }
        .video_mask {
            position: absolute;
            width: 144rpx;
            height: 144rpx;
            top: 0;
            left: 0;
            background: #00000060;
            display: flex;
            align-items: center;
            justify-content: center;
            .video_play {
                width: 40rpx;
                height: 40rpx;
            }
        }
    }
}
</style>
