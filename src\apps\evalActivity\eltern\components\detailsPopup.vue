<template>
    <div>
        <uni-popup ref="detailsPopup" @click.stop type="center" :is-mask-click="false" :safe-area="false">
            <div class="popup_class">
                <div class="title">{{ details.medalName || details.scoreCardName }}</div>
                <div class="integral" v-if="detailsType == 'loyaltyCard'">{{ details.score }}积分</div>
                <div class="img_box">
                    <image mode="heightFix" class="img" :src="details.medalIconUrl || details.scoreCardIconUrl" alt="" />
                </div>
                <div class="details_item" v-for="(item, index) in detailsType === 'loyaltyCard' ? loyaltyCardList : medalList" :key="index">
                    <span class="label">{{ item.label }}</span>
                    <span class="value" v-if="item.code == 'groupNameList'">
                        {{ details[item.code]?.join("、") || "-" }}
                    </span>
                    <span class="value" v-else-if="item.code == 'issuanceMethod'">
                        {{ details[item.code] == 1 ? "按积分制" : "手动发放" }}
                    </span>
                    <span class="value" v-else-if="item.code == 'writeOffStatus'">
                        <!-- 0.未核销 1.已核销 -->
                        {{ details[item.code] == 1 ? "已核销" : "未核销" }}
                    </span>

                    <span class="value" v-else>{{ details[item.code] || "-" }}</span>
                </div>
                <div class="confirm_btn" @click="detailsPopup.close()">确定</div>
            </div>
        </uni-popup>
    </div>
</template>

<script setup>
const detailsPopup = ref(null)
const details = ref({
    medalName: "",
    medalIconUrl: ""
})
const detailsType = ref("loyaltyCard")
const loyaltyCardList = ref([
    {
        label: "学生姓名：",
        code: "personName"
    },
    {
        label: "所在班级：",
        code: "groupNameList"
    },
    {
        label: "核销状态：",
        code: "writeOffStatus"
    },
    {
        label: "发放时间：",
        code: "issuanceTime"
    },
    {
        label: "核销时间：",
        code: "writeOffTime"
    }
])
const medalList = ref([
    {
        label: "学生姓名：",
        code: "personName"
    },
    {
        label: "所在班级：",
        code: "groupNameList"
    },
    {
        label: "发放形式：",
        code: "issuanceMethod"
    },
    {
        label: "获得积分：",
        code: "medalScore"
    },
    {
        label: "获得时间：",
        code: "getTime"
    }
])
const open = (type, parameter) => {
    detailsType.value = type
    details.value = parameter
    detailsPopup.value.open()
}

defineExpose({ open })
</script>

<style lang="scss" scoped>
.popup_class {
    min-height: 300rpx;
    max-height: 934rpx;
    width: 630rpx;
    overflow-y: auto;
    background: $uni-bg-color;
    border-radius: 20rpx;
    padding-bottom: 120rpx;
    padding: 30rpx;

    .title {
        width: 100%;
        font-weight: 500;
        font-size: 34rpx;
        color: $uni-text-color;
        line-height: 48rpx;
        text-align: center;
    }

    .integral {
        width: 100%;
        text-align: center;
        padding-top: 10rpx;
        font-weight: 400;
        font-size: 28rpx;
        color: $uni-text-color;
        line-height: 40rpx;
    }

    .img_box {
        height: auto;
        background: $uni-bg-color-grey;
        border-radius: 20rpx;
        width: 100%;
        height: 400rpx;
        margin: 20rpx 0rpx;
        display: flex;
        justify-content: center;
        overflow: hidden;

        .img {
            width: auto;
            height: 100%;
        }
    }

    .details_item {
        margin-bottom: 20rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: 400;
        font-size: 26rpx;
        color: #666666;
        line-height: 36rpx;

        .value {
            color: $uni-text-color;
        }
    }

    .confirm_btn {
        height: 80rpx;
        border-top: 1rpx solid $uni-border-color;
        font-weight: 400;
        font-size: 28rpx;
        color: var(--primary-color);
        line-height: 80rpx;
        text-align: center;
    }
}
</style>
