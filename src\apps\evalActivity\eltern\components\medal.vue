<template>
    <div class="medal_com">
        <z-paging ref="paging" v-model="evalUserMedalList" @query="queryList" :auto="false" use-page-scroll>
            <template #empty>
                <yd-empty text="暂无数据" />
            </template>
            <div class="medal_type_list">
                <div class="medal_type_item" :class="activeType == item.evalTypeId ? 'type_active' : ''" v-for="item in medalTypeList" :key="item.evalTypeId" @click="changeType(item.evalTypeId)">
                    {{ item.evalTypeName }}
                </div>
            </div>
            <div class="medal_list">
                <div class="medal_item" @click="medalInfo(item.medalCode)" v-for="item in evalUserMedalList" :key="item.evalTypeId">
                    <image
                        mode="heightFix"
                        class="image"
                        :style="{
                            filter: item.owned ? 'none' : 'grayscale(100%)'
                        }"
                        :src="item.medalIconUrl"
                        alt=""
                    />
                    <span class="ellipsis medal_name">{{ item.medalName }}</span>
                </div>
            </div>
        </z-paging>
        <details-popup ref="detailsPopupRef" />
    </div>
</template>

<script setup>
import { watch } from "vue"
import DetailsPopup from "./detailsPopup.vue"
const props = defineProps({
    parame: {
        type: Object,
        default: () => {}
    }
})
const paramsIntegral = ref({})
const detailsPopupRef = shallowRef(null)
const paging = ref(null)
const evalUserMedalList = ref([])
const activeType = ref(null)
const medalTypeList = ref([])
// 调用List数据
function queryList(pageNo, pageSize) {
    const params = {
        pageNo,
        pageSize,
        ...paramsIntegral.value,
        identity: 0,
        evalTypeId: activeType.value //勋章分类(评价类型id)
    }
    http.post("/app/appEvalMedal/pageEvalUserMedal", params).then(({ data }) => {
        paging.value?.complete(data.list)
    })
}

// 勋章弹框详情
function medalInfo(medalCode) {
    const params = {
        medalCode,
        ...paramsIntegral.value,
        identity: 0
    }
    http.post("/app/appEvalMedal/getUserMedalInfo", params).then(({ data }) => {
        detailsPopupRef.value.open("medal", data)
    })
}

async function getMedalType() {
    await http.get("/app/appEvalMedal/listEvalMedalType").then((res) => {
        const defaultList = [{ evalTypeId: null, evalTypeName: "全部勋章" }]
        medalTypeList.value = res.data && res.data.length ? [...defaultList, ...res.data] : defaultList
    })
}

function changeType(evalTypeId) {
    activeType.value = evalTypeId
    paging.value?.reload()
}

watch(
    () => props.parame,
    async (value) => {
        activeType.value = value.evalTypeId
        if (value.isShowStudent == "true") {
            await getMedalType()
        }
        if (value.personId) {
            paramsIntegral.value = value
            nextTick(() => {
                setTimeout(() => {
                    paging.value?.reload()
                }, 300)
            })
        } else {
            paging.value?.complete(false)
        }
    },
    {
        immediate: true,
        deep: true
    }
)
</script>

<style lang="scss" scoped>
.medal_com {
    height: 100%;

    .medal_list {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 10px;
        padding: 30rpx;

        .medal_item {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
            height: auto;
            font-weight: 400;
            font-size: 28rpx;
            color: $uni-text-color;
            line-height: 40rpx;
            text-align: left;

            .image {
                width: auto;
                height: 224rpx;
            }

            .medal_name {
                padding-top: 20rpx;
                width: 100%;
                text-align: center;
            }
        }
    }
    .medal_type_list {
        display: flex;
        align-items: center;
        margin: 0rpx 30rpx;
        padding-bottom: 30rpx;
        overflow-x: auto;
        max-width: calc(100% - 60rpx);
        .medal_type_item {
            font-size: 28rpx;
            color: #333333;
            line-height: 40rpx;
            font-weight: 400;
            padding: 0rpx 20rpx;
            min-width: 130rpx;
        }
        .type_active {
            font-weight: 600;
        }
    }
}
</style>
