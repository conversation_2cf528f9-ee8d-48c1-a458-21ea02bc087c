<template>
    <div class="check_evaluation">
        <z-paging ref="paging" v-model="state.dataList" @query="queryList">
            <template #top>
                <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="查看评价" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
                <div class="activity_info" v-if="isShowActivity">
                    <div class="evaluation">
                        <div
                            class="label"
                            :style="{
                                background: activityStatus[queryParams.status || 1]?.color
                            }"
                        >
                            {{ activityStatus[queryParams.status || 1]?.name }}
                        </div>
                        <div class="title ellipsis">
                            {{ queryParams.title }}
                        </div>
                    </div>
                    <span class="date">{{ queryParams.activityStartDate }}~{{ queryParams.activityEndDate }}</span>
                </div>
                <div class="user_info">
                    <div class="left">
                        <div class="avatar">
                            <image mode="aspectFill" v-if="queryParams.avatar" class="avatar_img" :src="queryParams.avatar" alt="" />
                            <span v-else>{{ queryParams.toPersonName?.slice(0, 1) }}</span>
                        </div>
                        <div class="info">
                            <div class="name">{{ queryParams.toPersonName }}</div>
                            <div class="class_dept">{{ queryParams.classesName || queryParams.orgName }}</div>
                        </div>
                    </div>
                    <div class="right">
                        <div class="num">{{ queryParams.activityStastus == 2 ? queryParams.totalScore : "-" }}</div>
                        <div class="text">总得分</div>
                    </div>
                </div>
            </template>
            <template #empty>
                <yd-empty text="暂无数据" />
            </template>
            <div class="page_list">
                <view class="page_item" v-for="item in state.dataList" :key="item.id" @click="checkInfo(item)">
                    <div class="item_left">
                        <div class="text_box">
                            <span class="label">参与次数：</span>
                            <span class="value">{{ item.thisCount }}/{{ item.totalCount }}</span>
                        </div>
                        <div class="text_box">
                            <span class="label">最后得分：</span>
                            <span class="mark">{{ item.score }}</span>
                        </div>
                        <div class="text_box">
                            <span class="label">评价时间：</span>
                            <span class="value">{{ item.scoreTime }}</span>
                        </div>
                    </div>
                    <div class="item_right">
                        <span>详情</span>
                        <uni-icons color="$uni-text-color-grey" type="right" size="18"></uni-icons>
                    </div>
                </view>
            </div>
        </z-paging>
        <!-- 评价规则 -->
        <uni-popup ref="ruleInfoRef" @click.stop type="bottom" :safe-area="false">
            <div class="info_popup">
                <div class="title">
                    <span class="text">详情</span>
                    <img class="image" @click="closeRulePopup" src="https://alicdn.1d1j.cn/announcement/20230706/193b257d495e428e9cbe74ff02432f93.png" alt="" />
                </div>
                <div class="select_rules">
                    <span class="rules_title">第{{ state.evaluationsNum }}次评价</span>
                    <!-- <div class="split_line"></div> -->
                    <div class="indicator_list">
                        <div class="indicator_item" v-for="(item, index) in state.selectRules" :key="index">
                            <span class="indicator_title">{{ item.name }}</span>
                            <div class="second_indicators" v-for="(secondItem, secondIndex) in item.secondIndicators" :key="secondIndex + 'secondIndex'">
                                <span> {{ secondIndex + 1 + "." }}{{ secondItem.indicatorScore.content }} </span>
                                <!-- {{ isShowActivity }}
								  <template v-if="isShowActivity == 'true'"> -->
                                <div class="weights flexs">
                                    <span>本次评分：</span>
                                    <span> {{ secondItem.indicatorScore.thisIndicatorScore || 0 }}分 </span>
                                </div>
                                <div class="flexs">
                                    <span>他人评分：</span>
                                    <span> {{ secondItem.indicatorScore.othersIndicatorScore || 0 }}分 </span>
                                </div>
                                <!-- </template>
								  <span v-else>
									  评分范围：
									  {{ secondItem.indicatorScore.minScore + " ~ " + secondItem.indicatorScore.maxScore
									  }}
								  </span> -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </uni-popup>
    </div>
</template>

<script setup>
import { onLoad } from "@dcloudio/uni-app"

const activityStatus = {
    0: { name: "未开始", color: "#FFFFBB37" },
    1: { name: "进行中", color: "#11C685" },
    2: { name: "已结束", color: "#595959" }
}
const paging = ref(null)
const queryParams = ref({})
const ruleInfoRef = ref(false) // 活动规则弹框
const isShowActivity = ref(false)
const state = reactive({
    selectRules: {},
    dataList: [],
    evaluationsNum: 0
})

function clickLeft() {
    uni.navigateBack()
}
const checkInfo = (item) => {
    navigateTo({
        url: "/apps/evalActivity/immediateEvaluation/evaluationDetails",
        query: {
            ...queryParams.value,
            thisCount: item.thisCount
        }
    })
    // state.evaluationsNum = item.thisCount
    // ruleInfoRef.value.open()
    // const { toPersonId, activityId = "" } = queryParams.value
    // const params = { activityId, toPersonId, rulePersonId: "", queryThisFrom: false }
    // http.post("/app/evalDayRulePerson/getRulePersonScoreList", params).then(({ data }) => {
    //     state.selectRules = data
    // })
}
//关闭活动详情弹框
function closeRulePopup() {
    ruleInfoRef.value.close()
}

onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    queryParams.value = options
    isShowActivity.value = options.isShowActivity || false
    uni.setNavigationBarTitle({
        title: options.isShowActivity ? "活动详情" : "查看评价"
    })
})

// 调用List数据
function queryList(pageNo, pageSize) {
    const { toPersonId, activityId = "" } = queryParams.value
    const parmas = {
        pageNo,
        pageSize,
        toPersonId,
        activityId
    }
    uni.showLoading({
        title: "加载中..."
    })
    http.post("/app/evalDayRulePerson/pageDayPersonScore", parmas)
        .then(({ data }) => {
            paging.value.complete(data.list)
        })
        .finally(() => {
            uni.hideLoading()
        })
}
</script>

<style lang="scss" scoped>
.check_evaluation {
    min-height: calc(100vh - 88rpx);
    background: $uni-bg-color-grey;

    .activity_info {
        display: flex;
        flex-direction: column;
        min-height: 36rpx;
        background: $uni-bg-color;
        border-radius: 20rpx;
        margin: 30rpx 30rpx 0rpx 30rpx;
        padding: 30rpx;

        .evaluation {
            display: flex;
            align-items: center;
            margin-bottom: 18rpx;

            .label {
                width: 90rpx;
                min-width: 90rpx;
                padding: 0rpx 4rpx;
                height: 36rpx;
                background: var(--primary-color);
                border-radius: 6rpx;
                font-weight: 500;
                font-size: 20rpx;
                color: $uni-bg-color;
                line-height: 36rpx;
                text-align: center;
                margin-right: 12rpx;
            }

            .title {
                font-weight: 500;
                font-size: 28rpx;
                color: $uni-text-color;
                line-height: 40rpx;
                flex: 1;
            }
        }

        .date {
            font-weight: 400;
            font-size: 26rpx;
            color: #666666;
            line-height: 36rpx;
        }
    }

    .user_info {
        margin: 30rpx;
        padding: 40rpx;
        min-height: 36rpx;
        background: $uni-bg-color;
        border-radius: 20rpx;
        display: flex;
        justify-content: space-between;

        .left {
            display: flex;
            flex: 1;

            .avatar {
                width: 100rpx;
                height: 100rpx;
                background: var(--primary-color);
                border-radius: 50%;
                text-align: center;
                position: relative;
                font-weight: 600;
                font-size: 47rpx;
                color: $uni-bg-color;
                line-height: 100rpx;

                .avatar_img {
                    width: 100rpx;
                    border-radius: 50%;
                    height: 100rpx;
                }
            }

            .info {
                display: flex;
                flex: 1;
                flex-direction: column;
                justify-content: space-around;
                margin-left: 30rpx;

                .name {
                    font-weight: 600;
                    font-size: 28rpx;
                    color: $uni-text-color;
                    line-height: 40rpx;
                }

                .class_dept {
                    font-weight: 400;
                    font-size: 28rpx;
                    color: $uni-text-color;
                    line-height: 40rpx;
                }
            }
        }

        .right {
            display: flex;
            flex-direction: column;
            justify-content: space-evenly;
            align-items: flex-end;
            flex: 1;
            max-width: 150rpx;

            .num {
                font-family: HarmonyOS_Sans_SC_Medium;
                font-size: 36rpx;
                color: $uni-text-color;
                line-height: 50rpx;
            }

            .text {
                font-weight: 400;
                font-size: 28rpx;
                color: $uni-text-color-grey;
                line-height: 40rpx;
            }
        }
    }
}

.page_list {
    margin: 0rpx 30rpx;

    .page_item {
        display: flex;
        justify-content: space-between;
        height: 180rpx;
        background: $uni-bg-color;
        border-radius: 12rpx;
        padding: 30rpx;
        margin-bottom: 20rpx;

        .item_left {
            display: flex;
            flex-direction: column;

            .text_box {
                display: flex;
                font-weight: 400;
                font-size: 28rpx;
                line-height: 40rpx;
                margin: 10rpx 0rpx;

                .label {
                    color: $uni-text-color-grey;
                }

                .value {
                    color: $uni-text-color;
                }

                .mark {
                    font-weight: 500;
                    font-size: 30rpx;
                    color: var(--primary-color);
                    line-height: 42rpx;
                }
            }
        }

        .item_right {
            margin-top: 10rpx;
            display: flex;
            align-content: center;
            font-weight: 400;
            font-size: 28rpx;
            color: $uni-text-color-grey;
            line-height: 40rpx;
        }
    }
}

.info_popup {
    background: $uni-bg-color;
    border-radius: 20rpx 20rpx 0rpx 0rpx;
    padding-bottom: 120rpx;

    .title {
        height: 100rpx;
        display: flex;
        align-items: center;

        .text {
            font-size: 34rpx;
            font-weight: 600;
            color: $uni-text-color;
            line-height: 48rpx;
            width: 100vh;
            text-align: center;
            margin-left: 62rpx;
        }

        .delect_icon {
            padding: 18rpx;
        }
    }

    .content {
        min-height: 300rpx;
        max-height: 850rpx;
        overflow-y: auto;
        display: flex;
        flex-direction: column;
        align-content: center;
        padding: 0rpx 30rpx;
        width: calc(100% - 60rpx);

        .avatar_img {
            width: 100%;

            .img {
                width: 100%;
                height: auto;
            }
        }

        .content_title {
            font-weight: 600;
            font-size: 28rpx;
            color: $uni-text-color;
            line-height: 40rpx;
            margin: 20rpx 0;
        }
    }

    .select_rules {
        padding: 30rpx;
        min-height: 300rpx;
        max-height: 850rpx;
        overflow-y: auto;

        .rules_title {
            font-family:
                PingFangSC,
                PingFang SC;
            font-weight: 600;
            font-size: 30rpx;
            color: $uni-text-color;
            line-height: 42rpx;
        }

        .indicator_list {
            border-top: 1rpx solid $uni-border-color;
            margin-top: 20rpx;
            padding-top: 20rpx;

            .indicator_item {
                .indicator_title {
                    font-weight: 600;
                    font-size: 28rpx;
                    color: $uni-text-color;
                    line-height: 40rpx;
                }

                .second_indicators {
                    min-height: 50rpx;
                    background: $uni-bg-color-grey;
                    border-radius: 10rpx;
                    padding: 30rpx;
                    margin: 20rpx 0rpx;
                    font-weight: 400;
                    font-size: 28rpx;
                    color: $uni-text-color;
                    line-height: 40rpx;
                    display: flex;
                    flex-direction: column;

                    .span_class {
                        padding: 15rpx 0rpx;
                    }

                    .weights {
                        font-weight: 600;
                        font-size: 28rpx;
                        color: $uni-text-color;
                        font-style: normal;
                    }

                    .flexs {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                    }
                }
            }
        }
    }
}
</style>
