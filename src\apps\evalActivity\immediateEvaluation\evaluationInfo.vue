<template>
    <div class="evaluation_info">
        <z-paging ref="paging" v-model="state.participants" @query="queryList">
            <template #top>
                <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="评价详情" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
                <div class="activity_box">
                    <div class="activity_card">
                        <div class="activity_img">
                            <image mode="aspectFill" class="activity_img" v-if="state.detailForm?.promotionalImage" :src="state.detailForm.promotionalImage" alt="" />
                        </div>
                        <div class="right_box">
                            <div class="title_box">
                                <span class="title"
                                    ><span class="label" :style="{ background: activityStatus[state.detailForm.status]?.color }"> {{ activityStatus[state.detailForm.status]?.name }} </span>{{ state.detailForm.title }}</span
                                >
                            </div>
                            <span class="check" @click="lookActivityInfo">查看活动介绍</span>
                        </div>
                    </div>
                    <div class="activity_text">
                        <div class="item_box" v-for="(item, index) in infoList" :key="index">
                            <span class="item_lable">{{ item.lable }}</span>
                            <span class="item_value ellipsis" :class="item.lable == '评价规则：' ? 'color_class' : ''">
                                <template v-if="item.key === 'endDate'"> {{ state.detailForm[item.key2] }} - {{ state.detailForm[item.key] }} </template>
                                <template v-else-if="item.key === 'names'">
                                    {{ state.detailForm[item.key]?.join(",") || "" }}
                                </template>
                                <template v-else-if="item.key === 'cycle'">
                                    {{ item.cycle[state.detailForm[item.key]] }}
                                </template>
                                <div v-else-if="item.key === 'description'">
                                    <span v-for="(i, rIndex) in state.detailForm.rules" :key="i.id + 'rule'">
                                        <span v-if="rIndex != 0">、</span>
                                        <span @click="lookRule(i)">{{ i.name }}</span>
                                    </span>
                                </div>
                                <template v-else-if="item.key === 'managers'">
                                    {{ managersInfo(state.detailForm[item.key]) }}
                                </template>
                                <template v-else>
                                    {{ state.detailForm[item.key] }}
                                </template>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="participants">
                    <view class="participants_title">参与人员</view>
                    <view class="query_input">
                        <input type="text" class="query_input_text" v-model="state.toPersonName" placeholder="搜索姓名" @input="searchFn" />
                    </view>
                </div>
            </template>
            <div class="page_list" v-if="state.participants?.length">
                <view class="page_item" v-for="(item, index) in state.participants" :key="index">
                    <div class="item_title">
                        <div class="left_box">
                            <div class="avatar">
                                <image mode="aspectFill" v-if="item.avatar" class="avatar_img" :src="item.avatar" alt="" />
                                <span v-else>{{ item.toPersonName?.slice(0, 1) }}</span>
                            </div>
                            <span class="name ellipsis">{{ item.toPersonName }}</span>
                            <span class="ellipsis" style="flex: 1">{{ item.orgName }}</span>
                        </div>
                        <div class="right_box" @click="checkEvaluation(item)">
                            <span>查看评价</span>
                            <uni-icons color="$uni-text-color-grey" type="right" size="18"></uni-icons>
                        </div>
                    </div>
                    <div class="page_item_box">
                        <span class="item_lable">参与次数：</span>
                        <span class="item_value">{{ item.thisCount }}/{{ item.totalCount }}</span>
                    </div>
                    <div class="page_item_box">
                        <span class="item_lable">最高得分：</span>
                        <span class="item_value">{{ item.maxScore || 0 }}</span>
                    </div>
                    <div class="page_item_box">
                        <span class="item_lable">最后得分：</span>
                        <span class="item_value">{{ item.score || 0 }}</span>
                    </div>
                    <div class="page_item_box">
                        <span class="item_lable">最近评价时间：</span>
                        <span class="item_value">{{ item.scoreTime || "-" }}</span>
                    </div>
                    <div class="split_line" v-if="item.isApprove || item.operationFlag == 1"></div>
                    <div class="btn_bottom">
                        <div class="item_btn evaluation_audit" @click="evaluationApprove(item)" v-if="item.isApprove">评价审核</div>
                        <div class="item_btn" v-if="item.operationFlag == 1" @click="participantsFn(item)">立即评价</div>
                    </div>
                </view>
            </div>
            <view v-if="!state.participants?.length && pageLoading" class="loading">
                <uv-loading-icon :show="pageLoading" text="加载中..." :vertical="true"></uv-loading-icon>
            </view>
            <template #empty>
                <yd-empty text="暂无数据" />
            </template>
        </z-paging>
        <!-- 查看活动介绍 -->
        <uni-popup ref="activityInfoRef" @click.stop type="bottom" :is-mask-click="false" :safe-area="false">
            <div class="info_popup">
                <div class="title">
                    <span class="text">查看活动介绍</span>
                    <uni-icons class="delect_icon" type="closeempty" size="22" color="#333" @click="closeInfoPopup"></uni-icons>
                </div>
                <div class="content">
                    <div class="avatar_img">
                        <image mode="aspectFill" class="img" :src="state.detailForm.promotionalImage" alt="" />
                    </div>
                    <span class="content_title" pt-20>{{ state.detailForm.title || "-" }}</span>
                    <span v-html="state.detailForm.description"></span>
                </div>
            </div>
        </uni-popup>
        <!-- 评价规则 -->
        <uni-popup ref="ruleInfoRef" @click.stop type="bottom" :safe-area="false">
            <div class="info_popup">
                <div class="title">
                    <span class="text">评价规则</span>
                    <uni-icons class="delect_icon" type="closeempty" size="22" color="#333" @click="closeRulePopup"></uni-icons>
                </div>
                <div class="select_rules">
                    <span class="rules_title">{{ state.selectRules.name }}</span>
                    <div class="indicator_list">
                        <div class="indicator_item" v-for="(item, index) in state.selectRules.firstIndicators" :key="index">
                            <span class="indicator_title">{{ item.name }}</span>
                            <div class="second_indicators" v-for="(secondItem, secondIndex) in item.secondIndicators" :key="secondIndex + 'secondIndex'">
                                <span class="span_class"> {{ secondIndex + 1 + "." }}{{ secondItem.indicatorScore.content }} </span>
                                <span class="span_class">
                                    评分范围：
                                    {{ secondItem.indicatorScore.minScore + " ~ " + secondItem.indicatorScore.maxScore }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </uni-popup>
    </div>
</template>

<script setup>
import { onLoad, onShow } from "@dcloudio/uni-app"

const activityInfoRef = ref(false) // 活动详情弹框
const ruleInfoRef = ref(false) // 活动规则弹框
const pageLoading = ref(false)
const activityStatus = {
    0: { name: "未开始", color: "#FFFFBB37" },
    1: { name: "进行中", color: "var(--primary-color)" },
    2: { name: "已结束", color: "#595959" }
}
const paging = ref(null)
const infoList = ref([
    {
        key: "endDate",
        key2: "startDate",
        lable: "活动时间："
    },
    {
        key: "names",
        lable: "参与人员："
    },
    {
        // (0.日 1.周 2.月)
        key: "cycle",
        lable: "评价周期：",
        cycle: ["每天", "周", "月"]
    },
    // {
    //     key: "description",
    //     lable: "评价范围：",
    // },
    {
        key: "description",
        lable: "评价规则："
    },
    // {
    //     key: "managers",
    //     lable: "活动负责人："
    // },
    {
        key: "createBy",
        lable: "创建人："
    },
    {
        key: "createTime",
        lable: "创建时间："
    }
])

const state = reactive({
    paramsForm: {},
    ruleName: "",
    detailForm: {},
    participants: [],
    selectRules: {},
    toPersonName: ""
})

const managersInfo = computed(() => {
    return (data) => data?.map((v) => v.name).join("、") || ""
})

// 调用List数据
function queryList(pageNo, pageSize) {
    const params = {
        activityId: state.paramsForm.id,
        toPersonName: state.toPersonName,
        pageNo,
        pageSize
    }
    pageLoading.value = true
    http.post("/app/evalDayRulePerson/selectDayPersonScoreInfoPage", params)
        .then(({ data }) => {
            paging.value.complete(data.list)
        })
        .finally(() => {
            pageLoading.value = false
        })
}

function clickLeft() {
    uni.navigateBack()
}

const searchFn = () => {
    paging.value?.reload()
}
function lookRule(i) {
    ruleInfoRef.value.open()
    state.selectRules = i
}

//关闭活动详情弹框
function closeRulePopup() {
    ruleInfoRef.value.close()
}

// 打开活动详情弹框
function lookActivityInfo() {
    activityInfoRef.value.open()
}

//关闭活动详情弹框
function closeInfoPopup() {
    activityInfoRef.value.close()
}
const getEvaluateInfo = () => {
    uni.showLoading({
        title: "加载中..."
    })
    http.get("/app/evalActivity/get", { id: state.paramsForm.id })
        .then(({ data }) => {
            state.detailForm = data
        })
        .finally(() => {
            uni.hideLoading()
        })
}

function checkEvaluation(item) {
    navigateTo({
        url: "/apps/evalActivity/approveEvaluation/lookEvaluation",
        query: { ...item, isApprove: true, queryThisFrom: false }
    })
}

function evaluationApprove(item) {
    navigateTo({
        url: "/apps/evalActivity/approveEvaluation/index",
        query: { ...item, id: state.paramsForm.id }
    })
}

function participantsFn(item) {
    navigateTo({
        url: "/apps/evalActivity/immediateEvaluation/participants",
        query: item
    })
}

onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    state.paramsForm = options
    getEvaluateInfo()
})

onShow(() => {
    paging.value?.reload()
})
</script>

<style lang="scss" scoped>
.evaluation_info {
    min-height: calc(100vh - 128rpx);
    background: $uni-bg-color-grey;
    padding: 20rpx 0rpx;

    .activity_box {
        padding: 30rpx;
        min-height: 36rpx;
        background: $uni-bg-color;

        .activity_card {
            display: flex;

            .activity_img {
                width: 250rpx;
                height: 188rpx;
                border-radius: 10rpx;
            }

            .right_box {
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                flex: 1;
                margin-left: 20rpx;

                .title_box {
                    .label {
                        width: 80rpx;
                        padding: 1rpx 6rpx;
                        background: var(--primary-color);
                        border-radius: 6rpx;
                        font-weight: 500;
                        font-size: 20rpx;
                        color: $uni-bg-color;
                        text-align: center;
                        margin-right: 10rpx;
                    }

                    .title {
                        font-weight: 500;
                        font-size: 30rpx;
                        color: $uni-text-color;
                        line-height: 42rpx;
                        display: -webkit-box;
                        overflow: hidden;
                        -webkit-box-orient: vertical;
                        -webkit-line-clamp: 2;
                    }
                }

                .check {
                    font-weight: 400;
                    font-size: 28rpx;
                    color: var(--primary-color);
                    line-height: 40rpx;
                }
            }
        }

        .activity_text {
            margin-top: 30rpx;
            min-height: 200rpx;
            padding: 30rpx;
            background: $uni-bg-color-grey;
            border-radius: 10rpx;

            .item_box {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                margin: 10rpx 0rpx;

                .item_lable {
                    min-width: 140rpx;
                    font-weight: 400;
                    font-size: 28rpx;
                    color: #666666;
                    line-height: 40rpx;
                }

                .item_value {
                    font-weight: 400;
                    font-size: 28rpx;
                    color: $uni-text-color;
                    line-height: 40rpx;
                    text-align: right;
                }

                .color_class {
                    color: var(--primary-color);
                }
            }
        }
    }

    .participants {
        padding: 30rpx 30rpx 10rpx 30rpx;
        min-height: 36rpx;
        background: $uni-bg-color;
        margin-top: 30rpx;

        .query_input {
            background: $uni-bg-color;
            margin-top: 10rpx;
            .query_input_text {
                padding: 10rpx 20rpx;
                border-radius: 30rpx;
                font-size: 30rpx;
                background: $uni-bg-color-grey;
            }
        }

        .participants_title {
            font-weight: 600;
            font-size: 30rpx;
            color: $uni-text-color;
            line-height: 42rpx;
        }
    }

    .info_popup {
        background: $uni-bg-color;
        border-radius: 20rpx 20rpx 0rpx 0rpx;
        padding-bottom: 120rpx;

        .title {
            height: 100rpx;
            display: flex;
            align-items: center;

            .text {
                font-size: 34rpx;
                font-weight: 600;
                color: $uni-text-color;
                line-height: 48rpx;
                width: 100vh;
                text-align: center;
                margin-left: 62rpx;
            }

            .delect_icon {
                padding: 18rpx;
            }
        }

        .content {
            min-height: 300rpx;
            max-height: 850rpx;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            align-content: center;
            padding: 0rpx 30rpx;
            width: calc(100% - 60rpx);

            .avatar_img {
                width: 100%;

                .img {
                    width: 100%;
                    height: auto;
                }
            }

            .content_title {
                font-weight: 600;
                font-size: 28rpx;
                color: $uni-text-color;
                line-height: 40rpx;
                margin: 20rpx 0;
            }
        }

        .select_rules {
            padding: 30rpx;
            min-height: 300rpx;
            max-height: 850rpx;
            overflow-y: auto;

            .rules_title {
                font-weight: 600;
                font-size: 30rpx;
                color: $uni-text-color;
                line-height: 42rpx;
            }

            .indicator_list {
                border-top: 1rpx solid $uni-border-color;
                margin-top: 30rpx;
                padding-top: 30rpx;

                .indicator_item {
                    .indicator_title {
                        font-weight: 600;
                        font-size: 28rpx;
                        color: $uni-text-color;
                        line-height: 40rpx;
                    }

                    .second_indicators {
                        min-height: 50rpx;
                        background: $uni-bg-color-grey;
                        border-radius: 10rpx;
                        padding: 30rpx;
                        margin: 20rpx 0rpx;
                        font-weight: 400;
                        font-size: 28rpx;
                        color: $uni-text-color;
                        line-height: 40rpx;
                        display: flex;
                        flex-direction: column;

                        .span_class {
                            padding: 15rpx 0rpx;
                        }
                    }
                }
            }
        }
    }
}
.page_list {
    background: $uni-bg-color;
    padding: 30rpx;

    .page_item {
        margin-bottom: 20rpx;
        padding: 30rpx 20rpx;
        min-height: 252rpx;
        border-radius: 20rpx;
        border: 1rpx solid $uni-border-color;

        .item_title {
            padding-bottom: 10rpx;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .left_box {
                flex: 1;
                display: flex;
                align-items: center;
                font-weight: 400;
                font-size: 28rpx;
                color: #666666;
                line-height: 40rpx;

                .name {
                    max-width: 30%;
                    font-weight: 600;
                    font-size: 28rpx;
                    color: $uni-text-color;
                    line-height: 40rpx;
                    padding: 0rpx 20rpx;
                }

                .avatar {
                    min-width: 48rpx;
                    width: 48rpx;
                    height: 48rpx;
                    background: var(--primary-color);
                    border-radius: 50%;
                    font-weight: 600;
                    font-size: 22rpx;
                    color: $uni-bg-color;
                    line-height: 48rpx;
                    text-align: center;

                    .avatar_img {
                        width: 48rpx;
                        border-radius: 50%;
                        height: 48rpx;
                    }
                }
            }

            .right_box {
                display: flex;
                align-items: center;
                font-weight: 400;
                font-size: 28rpx;
                color: $uni-text-color-grey;
                line-height: 40rpx;
                min-width: 150rpx;
            }
        }

        .page_item_box {
            display: flex;
            justify-content: space-between;
            margin: 10rpx 0rpx;

            .item_lable {
                font-weight: 400;
                font-size: 28rpx;
                color: $uni-text-color-grey;
                line-height: 40rpx;
            }

            .item_value {
                flex: 1;

                font-weight: 400;
                font-size: 28rpx;
                color: $uni-text-color;
                line-height: 40rpx;
                text-align: right;
            }
        }
        .split_line {
            height: 1rpx;
            background: #d9d9d9;
            width: 100%;
            margin: 30rpx 0rpx;
        }
        .btn_bottom {
            display: flex;
            // border-top: 1rpx solid #d8d8d8;
            // padding-top: 24rpx;
            // margin-top: 30rpx;
            justify-content: flex-end;
            .item_btn {
                padding: 10rpx 20rpx;
                margin-left: 24rpx;
                border: 1rpx solid var(--primary-color);
                border-radius: 36rpx;
                font-weight: 400;
                font-size: 28rpx;
                color: var(--primary-color);
                display: flex;
                justify-content: center;
                align-items: center;
            }
            .evaluation_audit {
                color: #fdb500;
                border: 1rpx solid #fdb500;
            }
        }
    }
}
.loading {
    margin-top: 100rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>
