<template>
    <div class="info_popup">
        <div class="select_rules">
            <div class="indicator_list">
                <div class="indicator_item" v-for="(item, index) in state.selectRules" :key="index">
                    <span class="indicator_title">{{ item.name }}</span>
                    <div class="second_indicators" v-for="(secondItem, secondIndex) in item.secondIndicators" :key="secondIndex + 'secondIndex'">
                        <span> {{ secondIndex + 1 + "." }}{{ secondItem.indicatorScore.content }} </span>
                        <div class="flexs" v-for="thirdItem in secondItem.indicatorScore.scoreRecordList" :key="thirdItem.id">
                            <span>{{ thirdItem.scoreTime }}</span>
                            <span> 评分：{{ thirdItem.score || 0 }}分 </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { onLoad } from "@dcloudio/uni-app"

const queryParams = ref({})
const state = reactive({
    selectRules: {},
    dataList: [],
    evaluationsNum: 0
})

const checkInfo = () => {
    state.evaluationsNum = queryParams.value.thisCount
    const { toPersonId, activityId = "" } = queryParams.value
    const params = {
        activityId,
        toPersonId,
        rulePersonId: "",
        queryThisFrom: false
    }
    http.post("/app/evalDayRulePerson/getRulePersonScoreList", params).then(({ data }) => {
        state.selectRules = data
    })
}

const myRecord = () => {
    navigateTo({
        url: "/apps/evalActivity/immediateEvaluation/evaluationRecord",
        query: queryParams.value
    })
}

onLoad((options) => {
    queryParams.value = options
    checkInfo()
})
</script>

<style lang="scss" scoped>
.info_popup {
    background: $uni-bg-color;
    border-radius: 20rpx 20rpx 0rpx 0rpx;
    padding-bottom: 120rpx;

    .select_rules {
        padding: 30rpx;
        .rules_title {
            font-family:
                PingFangSC,
                PingFang SC;
            font-weight: 600;
            font-size: 30rpx;
            color: $uni-text-color;
            display: flex;
            justify-content: space-between;
            align-items: center;
            line-height: 42rpx;
            .my {
                color: var(--primary-color);
                font-weight: 500;
                font-size: 24rpx;
                line-height: 34rpx;
            }
        }

        .indicator_list {
            .indicator_item {
                .indicator_title {
                    font-weight: 600;
                    font-size: 28rpx;
                    color: $uni-text-color;
                    line-height: 40rpx;
                }

                .second_indicators {
                    min-height: 50rpx;
                    background: $uni-bg-color-grey;
                    border-radius: 10rpx;
                    padding: 30rpx;
                    margin: 20rpx 0rpx;
                    font-weight: 400;
                    font-size: 28rpx;
                    color: $uni-text-color;
                    line-height: 40rpx;
                    display: flex;
                    flex-direction: column;

                    span {
                        padding: 15rpx 0rpx;
                    }

                    .weights {
                        font-weight: 600;
                        font-size: 28rpx;
                        color: $uni-text-color;
                        font-style: normal;
                    }

                    .flexs {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                    }
                }
            }
        }
    }
}
</style>
