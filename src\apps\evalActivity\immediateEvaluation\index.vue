<template>
    <div class="immediate_evaluation">
        <z-paging ref="paging" v-model="state.rankingList" @query="queryList" :inside-more="true" :auto="false">
            <template #top>
                <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="立即评价" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
                <view class="search_input">
                    <input type="text" class="query_input_text" v-model="state.toPersonName" placeholder="搜索姓名" @input="handlerInput" />
                </view>
                <div class="evaluation" @click="handlerDetails" v-if="state.rankingObj.title">
                    <div class="label" v-if="state.rankingObj.status" :style="{ background: activityStatus[state.rankingObj.status]?.color }">
                        {{ activityStatus[state.rankingObj.status]?.name }}
                    </div>
                    <div class="title ellipsis">
                        {{ state.rankingObj.title || "-" }}
                    </div>
                    <uni-icons color="#8C8C8C" type="right" size="18"></uni-icons>
                </div>
            </template>
            <template #empty>
                <yd-empty text="暂无数据" />
            </template>
            <div v-if="state.rankingList?.length" class="participants_list">
                <span class="participants">参与人员</span>
                <view class="container">
                    <view class="item" @click="checkEvaluation(item)" v-for="item in state.rankingList" :key="item.item">
                        <div class="avatar">
                            <view class="status_box" v-if="item.operationFlag == 3">已审</view>
                            <image mode="aspectFill" v-if="item.avatar" class="avatar_img" :src="item.avatar" alt="" />
                            <span v-else>{{ item.toPersonName?.slice(0, 1) }}</span>
                            <div v-if="item.operationFlag == 1" class="mask_layer">
                                {{ operationFlagStatus[item.operationFlag] }}
                            </div>
                        </div>
                        <div class="name ellipsis">{{ item.toPersonName }}</div>
                        <div class="class_dept ellipsis">{{ item.classesName }}</div>
                    </view>
                </view>
            </div>
            <view v-if="!state.rankingList?.length && pageLoading" class="loading">
                <uv-loading-icon :show="pageLoading" text="加载中..." :vertical="true"></uv-loading-icon>
            </view>
        </z-paging>
    </div>
</template>

<script setup>
const paging = ref(null)
const pageLoading = ref(false)
const activityStatus = {
    0: { name: "未开始", color: "#FFFFBB37" },
    1: { name: "进行中", color: "var(--primary-color)" },
    2: { name: "已结束", color: "#595959" }
}
const operationFlagStatus = {
    0: "无权限审批",
    1: "未评",
    2: "已评",
    3: "已审核"
}
const state = reactive({
    rankingObj: {
        status: 0,
        title: ""
    },
    toPersonName: "",
    rankingList: [],
    pagination: {
        pageNo: 1,
        pageSize: 10
    }
})
// 评价详情
const handlerDetails = () => {
    navigateTo({
        url: "/apps/evalActivity/immediateEvaluation/evaluationInfo",
        query: state.rankingObj
    })
}
const checkEvaluation = (item) => {
    // 都改成跳转到参与人员页面
    navigateTo({
        url: "/apps/evalActivity/immediateEvaluation/participants",
        query: item
    })

    // 如果已经评价了的跳转查看评价
    // if (item.operationFlag != 1) {
    //     navigateTo({
    //         url: "/apps/evalActivity/approveEvaluation/lookEvaluation",
    //         query: { ...item, isApprove: false }
    //     })
    //     return
    // } else {
    //     // 跳到参与人员页面
    //     navigateTo({
    //         url: "/apps/evalActivity/immediateEvaluation/participants",
    //         query: item
    //     })
    // }
}

function clickLeft() {
    uni.navigateBack()
}

const handlerInput = () => {
    paging.value?.reload()
}
// 调用List数据
function queryList(pageNo, pageSize) {
    const params = {
        activityId: state.rankingObj.id,
        toPersonName: state.toPersonName,
        pageNo,
        pageSize
    }
    pageLoading.value = true
    http.post("/app/evalDayRulePerson/selectRulePersonPage", params)
        .then(({ data }) => {
            paging.value?.complete(data.list)
        })
        .finally(() => {
            pageLoading.value = false
        })
}

onShow(() => {
    nextTick(() => {
        if (state.rankingObj.id) {
            paging.value?.reload()
        }
    })
})

onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    state.rankingObj = options
    nextTick(() => {
        if (state.rankingObj.id) {
            paging.value?.reload()
        }
    })
})
</script>

<style lang="scss" scoped>
.immediate_evaluation {
    min-height: 100vh;
    background: $uni-bg-color-grey;

    .search_input {
        background: $uni-bg-color;
        padding: 20rpx 30rpx;
        .query_input_text {
            padding: 10rpx 20rpx;
            border-radius: 30rpx;
            font-size: 30rpx;
            background: $uni-bg-color-grey;
        }
    }

    :deep(.zp-page-top) {
        position: static;
    }

    :deep(.zp-view-super) {
        margin-top: 0 !important;
    }

    .evaluation {
        height: 36rpx;
        background: $uni-bg-color;
        border-radius: 20rpx;
        padding: 30rpx;
        display: flex;
        align-items: center;
        margin: 20rpx 30rpx 0;

        .label {
            width: 90rpx;
            min-width: 90rpx;
            padding: 0rpx 4rpx;
            height: 36rpx;
            background: var(--primary-color);
            border-radius: 6rpx;
            font-weight: 500;
            font-size: 20rpx;
            color: $uni-bg-color;
            line-height: 36rpx;
            margin-right: 12rpx;
            text-align: center;
        }

        .title {
            font-weight: 500;
            font-size: 28rpx;
            color: $uni-text-color;
            line-height: 40rpx;
            flex: 1;
        }
    }

    .participants_list {
        margin: 0rpx 30rpx;
        margin-top: 20rpx;
        padding: 30rpx;
        min-height: 100rpx;
        background: $uni-bg-color;
        border-radius: 20rpx;

        .participants {
            font-weight: 600;
            font-size: 30rpx;
            color: $uni-text-color;
            line-height: 42rpx;
        }

        .container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
            gap: 10px;
            padding-top: 10px;

            .item {
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: 400;
                flex-direction: column;

                .avatar {
                    width: 100rpx;
                    height: 100rpx;
                    background: var(--primary-color);
                    border-radius: 50%;
                    text-align: center;
                    position: relative;
                    font-weight: 600;
                    font-size: 47rpx;
                    color: $uni-bg-color;
                    line-height: 100rpx;
                    .status_box {
                        position: absolute;
                        top: -6rpx;
                        right: -6rpx;
                        font-weight: 400;
                        color: #fff;
                        font-size: 20rpx;
                        background: #ff0000bb;
                        height: 30rpx;
                        line-height: 30rpx;
                        border-radius: 10rpx;
                        padding: 0 6rpx;
                        z-index: 999;
                    }

                    .avatar_img {
                        width: 100rpx;
                        border-radius: 50%;
                        height: 100rpx;
                    }

                    .mask_layer {
                        width: 100rpx;
                        height: 100rpx;
                        position: absolute;
                        top: 0;
                        border-radius: 50%;
                        left: 0;
                        background-color: $uni-bg-color-mask;
                        font-weight: 400;
                        font-size: 26rpx;
                        color: $uni-bg-color;
                        line-height: 100rpx;
                        text-align: center;
                    }
                }

                .name {
                    font-size: 28rpx;
                    color: $uni-text-color;
                    line-height: 40rpx;
                    text-align: left;
                    width: 100%;
                    text-align: center;
                    margin-top: 20rpx;
                }

                .class_dept {
                    font-size: 26rpx;
                    color: $uni-text-color-grey;
                    line-height: 36rpx;
                    width: 100%;
                    text-align: center;
                }
            }
        }
    }
    .loading {
        margin-top: 300rpx;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
</style>
