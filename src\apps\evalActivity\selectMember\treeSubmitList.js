// 不想写了 直接丢这里 怎么方便怎么来了算了 ,这种傻b h5 拦不住刷新 放这里最合适了
// treeType: 1.学籍 2.部门 3.角色 4.外部人员

// 业务类型 businessType：

// 1学籍： 10选班级 11选学生 12选家长

// 2部门： 20选部门 21选老师

// 3角色： 30选角色 31.选老师

// 4外部人员： 40选组 41.选组成员
export const selectMember = ref({
    treeSubmitList: [],
    treeSubmitListName: "",
    subsectionList: [
        { name: "学生", businessType: 11, treeType: 1 }
        // { name: '指导老师', businessType: 21, treeType: 2 },
    ]
})
