<template>
    <view class="issue_medals">
        <z-paging ref="paging" v-model="state.medalList" @query="queryList">
            <template #top>
                <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="发放勋章" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
                <view class="handler_select">
                    <view>
                        <text class="required">*</text>
                        <text class="select_student">选择发放学生</text>
                    </view>
                    <view @click="handleTeacher" class="flex">
                        <text class="userName ellipsis">{{ state.advisorSelectionName || "请选择学生" }}</text>
                        <uni-icons type="right" size="16"></uni-icons>
                    </view>
                </view>
            </template>

            <view class="handler_content">
                <text class="content_title"> 选择勋章 </text>
                <checkbox-group class="reset_radio_group" @change="radioChange">
                    <label class="medal_col" v-for="(item, idx) in state.medalList" :key="item.id">
                        <view class="radio" :class="{ active: state.medalCodeList.includes(item.medalCode) }">
                            <checkbox :value="item.medalCode" :checked="item.medalCode === state.medalCodeList[idx]" />
                        </view>
                        <image mode="aspectFill" class="issue_medals_img" :src="item.medalIconUrl" alt="" />
                    </label>
                </checkbox-group>
            </view>
            <template #bottom>
                <view class="foote_btn">
                    <button type="primary" :loading="state.loading" @click="submitMedal">确定</button>
                </view>
            </template>
            <template #empty>
                <yd-empty text="暂无数据" />
            </template>
        </z-paging>
    </view>
</template>

<script setup>
import { onLoad } from "@dcloudio/uni-app"

const paging = ref(null)
const evalTypeId = shallowRef("")
const state = reactive({
    medalCodeList: [],
    personListDTO: [],
    advisorSelectionName: "",
    medalList: [],
    loading: false
})
const radioChange = (e) => {
    state.medalCodeList = e.detail.value
}

const handleTeacher = () => {
    navigateTo({
        url: "/apps/evalActivity/selectMember/index",
        events: {
            selectMember: (data) => {
                state.advisorSelectionName = data.treeSubmitListName
                state.personListDTO = data.treeSubmitList.map((item) => {
                    return {
                        ...item,
                        identity: 0
                    }
                })
            }
        },
        success: (res) => {
            res.eventChannel.emit("feedbackSelected", { selectedData: state.personListDTO })
        }
    })
}
// 调用List数据
function queryList(pageNo, pageSize) {
    const params = {
        evalTypeId: evalTypeId.value,
        issuanceMethod: 2,
        medalStatus: 1,
        pageNo,
        pageSize
    }
    http.post("/app/appEvalMedal/pageEvalMedal", params).then(({ data }) => {
        paging.value.complete(data?.list)
    })
}

function clickLeft() {
    uni.navigateBack()
}
// 提交发放勋章
const submitMedal = () => {
    const { personListDTO, medalCodeList } = state
    const params = { personListDTO, medalCodeList }
    state.loading = true
    http.post("/app/appEvalMedal/person/issuanceMedal", params)
        .then(({ message }) => {
            uni.navigateBack()
            uni.showToast({
                title: message
            })
        })
        .finally(() => {
            state.loading = false
        })
}

onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    evalTypeId.value = options?.issue_medals
})
</script>

<style lang="scss" scoped>
.issue_medals {
    background: $uni-bg-color-grey;
    min-height: calc(100vh - 88rpx);
}
.handler_select {
    background-color: $uni-text-color-inverse;
    display: flex;
    justify-content: space-between;
    padding: 30rpx;

    .required {
        color: $uni-color-error;
    }

    .userName,
    .select_student {
        font-weight: 400;
        font-size: 28rpx;
    }

    .select_student {
        width: 400rpx;
    }

    .flex {
        display: flex;
    }

    .userName {
        flex: 1;
        color: $uni-text-color;
    }
}

.handler_content {
    background-color: $uni-bg-color;
    margin-top: 20rpx;
    padding: 30rpx;
    overflow: hidden auto;

    .content_title {
        font-weight: 600;
        font-size: 30rpx;
        color: $uni-text-color;
    }

    .reset_radio_group {
        margin-top: 30rpx;
        overflow: hidden;

        .medal_col {
            width: 50%;
            float: left;
            box-sizing: border-box;
            text-align: center;

            .radio {
                text-align: right;
                padding-right: 40rpx;

                &.active {
                    :deep(.uni-checkbox-input) {
                        background-color: var(--primary-color) !important;
                        border-color: var(--primary-color) !important;

                        svg {
                            color: $uni-bg-color;

                            path {
                                fill: $uni-bg-color;
                            }
                        }
                    }
                }

                :deep(.uni-checkbox-input) {
                    border-radius: 50%;
                }
            }

            .issue_medals_img {
                width: 220rpx;
                height: 224rpx;
            }
        }
    }
}

.foote_btn {
    padding: 30rpx;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: $uni-bg-color;

    button {
        background-color: var(--primary-color);
    }
}
</style>
