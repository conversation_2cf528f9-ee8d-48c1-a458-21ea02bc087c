<template>
    <view>
        <!-- 勋章记录 -->
        <view class="meda_record">
            <z-paging ref="paging" v-model="state.medaList" @query="queryList">
                <template #top>
                    <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="勋章记录" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
                    <view class="search_input">
                        <input type="text" class="query_input_text" v-model="state.personName" placeholder="搜索姓名" @input="handlerInput" />
                    </view>
                    <view class="class_time">
                        <view class="reset_classes">
                            <uni-data-select class="reset_select" v-model="state.groupId" :localdata="classesList" @change="changeClasses" :clear="false" placeholder="全部班级"></uni-data-select>
                        </view>
                        <view class="reset_picker">
                            <uni-datetime-picker :border="false" :clearIcon="false" type="daterange" placeholder="选择时间" v-model="state.getStartEndTime" @change="calendarsConfirm" />
                        </view>
                    </view>
                </template>
                <template #empty>
                    <yd-empty text="暂无数据" />
                </template>
                <view class="card_list">
                    <view class="card" v-for="item in state.medaList" :key="item.id">
                        <view class="card_item">
                            <view class="badge" v-if="item.recoveryTime">已回收</view>
                            <view class="card_item_content">
                                <image class="card_item_content_img" mode="aspectFill" :src="item.medalIconUrl" alt />
                                <view class="card_item_content_right">
                                    <view class="list_item reset">
                                        <text class="user_name">{{ item.personName }} </text>
                                        <text class="user_classes">{{ item.groupNameList?.join("、") || "" }}</text>
                                    </view>
                                    <view class="list_item" v-for="it in medalRecord" :key="it.value">
                                        <text class="label">{{ it.label }}</text>
                                        <text class="value">
                                            <template v-if="it.value === 'issuanceMethod'">
                                                {{ issuanceMethod[item[it.value]] }}
                                            </template>
                                            <template v-else>{{ item[it.value] }}</template>
                                        </text>
                                    </view>
                                </view>
                            </view>
                            <view class="card_item_footer" v-if="item.issuanceMethod == 2 && !item.recoveryTime">
                                <text class="uni-body" @click="handlerOpen(item)">取消发放</text>
                            </view>
                        </view>
                    </view>
                </view>
                <template #bottom>
                    <view class="foote_btn" v-if="state.medaList.length">
                        <button type="primary" :loading="state.grantLoading" @click="submitMedal">发放</button>
                    </view>
                </template>
            </z-paging>
            <uni-popup border-radius="10px" ref="grantPopup" type="center" background-color="#fff">
                <uni-popup-dialog class="grant_box" type="info" cancelText="取消" confirmText="确定" content="确定取消发放?" @confirm="grantfirm" @close="grantClose"> </uni-popup-dialog>
            </uni-popup>
        </view>
    </view>
</template>

<script setup>
import { onLoad } from "@dcloudio/uni-app"
const issuanceMethod = ["", "自动发放", "手动方法"]
const medalRecord = [
    {
        label: "发放形式：",
        value: "issuanceMethod"
    },
    {
        label: "获得积分：",
        value: "medalScore"
    },
    {
        label: "获得时间：",
        value: "createTime"
    }
]
const paging = ref(null)
const grantPopup = ref()
const evalTypeId = ref("")
const classesList = ref([])
const state = reactive({
    getStartEndTime: [],
    personName: "",
    medaList: [],
    groupId: null,
    grantForm: {},
    grantLoading: false
})
// 弹框发放确认框
const handlerOpen = (item) => {
    state.grantForm = item
    grantPopup.value.open()
}
const grantClose = () => {
    grantPopup.value.close()
}
// 查询
const handlerInput = () => {
    paging.value.reload()
}
// 选时间
const calendarsConfirm = (val) => {
    paging.value.reload()
}
// 选择班级
const changeClasses = () => {
    paging.value.reload()
}
// 调用List数据
function queryList(pageNo, pageSize) {
    const { getStartEndTime, personName, groupId } = state
    const [getStartTime, getEndTime] = getStartEndTime || []
    const params = {
        evalTypeId: evalTypeId.value,
        personName,
        getStartTime,
        getEndTime,
        groupId,
        issuanceStatus: 1, //  1.发放 2.回收
        pageNo,
        pageSize
    }
    uni.showLoading({
        title: "加载中..."
    })
    http.post("/app/appEvalMedal/record/pageEvalMedalRecord", params)
        .then(({ data }) => {
            paging.value.complete(data.list)
        })
        .finally(() => {
            uni.hideLoading()
        })
}

function clickLeft() {
    uni.navigateBack()
}

const getClassesInfo = () => {
    http.get("/app/roll/getClassesInfo", { code: evalTypeId.value }).then(({ data }) => {
        const classes = data.map((item) => {
            return {
                text: item.classesName, // 显示的文本内容
                value: item.classesId // 选中时返回的值
            }
        })
        classesList.value = [
            {
                text: "全部班级", // 显示的文本内容
                value: null
            },
            ...classes
        ]
    })
}

const grantfirm = () => {
    const { personId, medalCode } = state.grantForm
    const params = {
        identity: 0,
        personId,
        medalCode
    }
    http.post("/app/appEvalMedal/person/cancelMedal", params).then(({ message }) => {
        uni.showToast({
            title: "取消发生成功"
        })
        grantClose()
        paging.value.reload()
    })
}
const submitMedal = () => {
    navigateTo({
        url: "/apps/evalActivity/teacher/issueMedals",
        query: { issue_medals: evalTypeId.value }
    })
}
onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    evalTypeId.value = options.medal_record
    getClassesInfo()
})
</script>

<style lang="scss" scoped>
.card_list {
    padding-bottom: 100rpx;
    background: $uni-bg-color-grey;
}
.meda_record {
    background: $uni-bg-color-grey;
    overflow: hidden auto;

    .search_input {
        background: $uni-bg-color;
        padding: 20rpx 30rpx;
        .query_input_text {
            padding: 10rpx 20rpx;
            border-radius: 30rpx;
            font-size: 30rpx;
            background: $uni-bg-color-grey;
        }
    }

    .class_time {
        display: flex;
        justify-content: space-between;
        background: $uni-bg-color;
        padding: 0 30rpx 20rpx;
        width: calc(100vw - 60rpx);

        .reset_classes {
            max-width: 46%;
            flex: 1;
            .reset_select {
                :deep(.uni-select) {
                    border: none;

                    .uni-select__input-text {
                        width: auto !important;
                        font-size: 28rpx;
                        color: #666666;
                    }

                    .uni-icons:before {
                        content: "";
                        display: block;
                        border: 10rpx solid transparent;
                        margin-left: 6rpx;
                    }

                    .uniui-bottom:before {
                        border-top: 10rpx solid var(--primary-color);
                        border-bottom-width: 1px;
                        margin-top: 6rpx;
                    }

                    .uniui-top:before {
                        border-bottom-color: var(--primary-color);
                        border-top-width: 1px;
                        margin-bottom: 6rpx;
                    }
                }

                :deep(.uni-select__input-box) {
                    max-width: 100%;
                }

                :deep(.uni-select__selector) {
                    left: -15px;
                    width: 100vw;
                    top: calc(100% + 6px) !important;
                    border: none;
                    border-radius: 0rpx 0rpx 12rpx 12rpx;
                    box-shadow: none;
                    padding: 0px 15px;
                }

                :deep(.uni-popper__arrow_bottom) {
                    display: none;
                }

                :deep(.uni-select__selector-item:last-child) {
                    border: none !important;
                }

                :deep(.uni-select__selector-item) {
                    border-bottom: 1rpx solid $uni-border-color;
                    line-height: 80rpx;
                    font-weight: 400;
                    font-size: 28rpx;
                    color: $uni-text-color;
                }
            }
        }
        .reset_picker {
            display: flex;
            align-items: center;
            width: 54%;

            &::after {
                content: "";
                display: block;
                border: 10rpx solid transparent;
                border-top: 10rpx solid var(--primary-color);
                border-bottom-width: 1px;
                margin-left: 10rpx;
            }

            :deep(.uni-icons) {
                display: none;
            }
        }
    }

    .card {
        background: $uni-bg-color-grey;
        padding: 10rpx 0rpx 0rpx 0;

        .card_item {
            padding: 40rpx;
            border-radius: 12rpx;
            background: $uni-bg-color;
            margin:0 20rpx;
            position: relative;

            .badge {
                font-weight: 400;
                font-size: 24rpx;
                background: #00000014;
                color: #bdbdbd;
                padding: 10rpx;
                border-radius: 0rpx 12rpx 0rpx 20rpx;
                position: absolute;
                top: 0;
                right: 0;
            }

            .card_item_content {
                display: flex;
                padding-bottom: 20rpx;

                .card_item_content_img {
                    width: 200rpx;
                    height: 224rpx;
                }

                .card_item_content_right {
                    flex: 1;
                    margin-left: 20rpx;

                    .list_item {
                        font-size: 26rpx;

                        &:not(:last-child) {
                            margin: 22rpx 0;
                        }

                        &.reset {
                            margin: 0;
                        }

                        .label {
                            font-weight: 400;
                            color: $uni-text-color-grey;
                        }

                        .value {
                            font-weight: 400;
                            color: $uni-text-color;
                        }

                        .user_name {
                            font-weight: 600;
                            font-size: 30rpx;
                            color: $uni-text-color;
                        }

                        .user_classes {
                            font-weight: 400;
                            font-size: 28rpx;
                            color: #666666;
                            margin-left: 10rpx;
                        }
                    }
                }
            }

            .card_item_footer {
                text-align: center;
                padding-top: 30rpx;
                color: var(--primary-color);
                border-top: 1rpx solid $uni-border-color;
            }
        }
    }

    .grant_box {
        :deep(.uni-dialog-content-text) {
            color: $uni-text-color;
        }

        :deep(.uni-dialog-button) {
            .uni-dialog-button-text {
                color: var(--primary-color);
            }
        }
    }
}

.foote_btn {
    padding: 30rpx;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: $uni-bg-color;

    button {
        background-color: var(--primary-color);
    }
}
</style>
<style lang="scss">
/* #ifdef MP-WEIXIN */
.meda_record {
    :deep(.uni-date-x) {
        justify-content: flex-end !important;
    }
}
/* #endif */
</style>
