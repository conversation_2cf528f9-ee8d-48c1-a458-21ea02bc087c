<template>
    <uni-popup ref="popup" background-color="#fff" width="100%" @maskClick="handleClick('close')">
        <view class="popup-content">
            <z-paging ref="paging" :auto="false" use-virtual-list @query="getMoreAssistants" v-model="state.historyList">
                <template #top>
                    <div class="top_list">
                        <div class="reset-list-item">
                            <uni-list-item :ellipsis="1" v-for="(item, index) in moreAssistants" :key="item.id || index" :title="item.name" :showArrow="item.showArrow" :thumb="item.avatar" :clickable="true" @click="handleClick(item.link)" />
                        </div>
                    </div>
                </template>
                <view class="history-content">
                    <template v-for="(item, idx) in _historyList" :key="idx">
                        <view class="history-group" v-if="item.children.length">
                            <view class="time">{{ item.name }}</view>
                            <view class="children_box">
                                <view class="children_item" v-for="(it, idx) in item.children" :key="idx" @click="historyClick(it)">
                                    <view class="c_name ellipsis">{{ it.name }}</view>
                                    <view class="delete" @click.stop="handlerNavigate(it)">x</view>
                                </view>
                            </view>
                        </view>
                    </template>
                </view>
                <template #empty>
                    <slot name="empty">
                        <yd-empty text="暂无数据" />
                    </slot>
                </template>
            </z-paging>
        </view>
    </uni-popup>
</template>

<script setup>
import { reactive, ref, shallowRef } from "vue"
import dayjs from "dayjs"

const moreAssistants = [
    { name: "小壹助手", avatar: "https://file.1d1j.cn/ai-icon/xiaozhi.png", showArrow: true, link: "close" },
    { name: "智能体商店", avatar: "https://file.1d1j.cn/ai-icon/shop.png", showArrow: true, link: "shop/index" },
    { name: "历史记录", avatar: "https://file.1d1j.cn/ai-icon/history.png", showArrow: false, link: "" }
]
const props = defineProps({
    assistantId: {
        type: String,
        default: ""
    },
    morePopupOpen: {
        type: Boolean,
        default: false
    },
    inUseId: {
        type: String,
        default: ""
    }
})
const emit = defineEmits(["update:morePopupOpen", "updatePrompt"])
const state = reactive({
    activeId: "",
    assistantId: "",
    form: {
        assistantId: ""
    },
    historyList: []
})

const popup = shallowRef()
const paging = ref(null)
const day = ref([]) // 今天
const yesterday = ref([]) // 昨天
const historyObj = ref({}) // 其他日期
// 更多历史对话列表
async function getMoreAssistants(pageNo = 1, pageSize = 12) {
    const params = {
        ...state.form,
        pageNo,
        pageSize
    }
    //
    await http
        .post("/ai/chat/session/page", params)
        .then(({ data }) => {
            paging.value?.complete(data.list || false)
        })
        .catch((err) => {
            paging.value.complete(false)
        })
}

const updatePrompt = (item) => {
    state.activeId = item.id || ""
    emit("updatePrompt", item)
    emit("update:morePopupOpen", false)
}
// 删除历史记录
const handlerNavigate = async (item) => {
    uni.showModal({
        title: "删除",
        content: "您确定要删除该条历史记录吗？",
        confirmColor: "var(--primary-color)",
        success(res) {
            if (res.confirm) {
                http.post("/ai/chat/session/delete", { id: item.id }).then((res) => {
                    state.activeId = ""
                    uni.showToast({
                        title: res.message
                    })
                    paging.value?.refresh()
                })
            }
        }
    })
}
const historyClick = (item) => {
    if (state.activeId !== item.id) {
        updatePrompt(item)
    } else {
        emit("update:morePopupOpen", false)
    }
}
const handleClick = (link) => {
    if (link === "close") {
        if (state.assistantId) {
            uni.reLaunch({
                url: "/apps/intelligence/index"
            })
        } else {
            updatePrompt({ assistantId: "default" })
        }
    } else if (link === "shop/index") {
        navigateTo({
            url: link
        })
    }
}

watch(
    () => props.morePopupOpen,
    (val) => {
        if (val) {
            state.form.assistantId = props.assistantId
            popup.value.open("left")
            getMoreAssistants()
        } else {
            popup.value.close()
        }
    }
)
const _historyList = computed(() => {
    // 如果是第一页，清空之前的数据 防止数据重复，否则不会清空 会继续追加.
    day.value = []
    yesterday.value = []
    historyObj.value = {}
    if (state.historyList.length === 0) return []
    // 遍历list，判断是否是今天 昨天 其他日期
    state.historyList.forEach((item) => {
        // 判断是否是今天
        const today = dayjs().format("YYYY-MM-DD")
        const createTime = dayjs(item.createTime).format("YYYY-MM-DD")
        // 判断是否是昨天 不能大于昨天
        const yesterdayTime = dayjs().subtract(1, "day").format("YYYY-MM-DD")
        if (today === createTime) {
            day.value.push(item)
        } else if (yesterdayTime == createTime) {
            // 判断是否是昨天 不能大于昨天
            yesterday.value.push(item)
        } else {
            // 否则用显示年月日
            if (!historyObj.value[createTime]) {
                historyObj.value[createTime] = [item]
            } else {
                historyObj.value[createTime].push(item)
            }
        }
    })
    let data = [
        { name: "今天", children: day.value },
        { name: "昨天", children: yesterday.value }
    ]
    return data.concat(Object.keys(historyObj.value).map((key) => ({ name: key, children: historyObj.value[key] })))
})

onLoad((item) => {
    state.assistantId = item.assistantId || ""
})
</script>

<style lang="scss" scoped>
.top_list {
    // #ifdef MP-WEIXIN
    margin-top: calc(var(--status-bar-height) + 50rpx);
    // #endif
}
.popup-content {
    width: 80vw;
    :deep(.z-paging-content) {
        width: 80vw;
    }
    .history-content {
        background: $uni-bg-color-grey;
        padding: 10rpx 0;
        .time {
            color: $uni-text-color-grey;
            font-size: 30rpx;
            margin: 10rpx 10rpx;
        }
        .children_box {
            background: $uni-bg-color;
            padding: 10rpx 0;
            .children_item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 10rpx 30rpx;

                .c_name {
                    font-size: 28rpx;
                    color: $uni-text-color;
                }
                .delete {
                    color: $uni-text-color;
                    font-size: 32rpx;
                }
            }
        }
    }
}
</style>
