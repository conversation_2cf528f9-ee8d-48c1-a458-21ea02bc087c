<template>
    <div class="y-markdown-content" v-html="formatValue()"></div>
</template>

<script setup>
import { Marked } from "marked"
import { markedHighlight } from "marked-highlight"
import katex from "./katex.mjs"

const props = defineProps({
    content: {
        type: String,
        default: ""
    },
    showLine: {
        type: Boolean,
        default: true
    },
    loading: {
        type: Boolean,
        default: true
    }
})

let copyCodeData = ref([])

const markdown = new Marked(
    markedHighlight({
        langPrefix: "ydhljs ",
        highlight(str, lang, info) {
            let preCode = ""
            console.log({ str })
            try {
                preCode = hljs.highlightAuto(str).value
            } catch (err) {
                preCode = markdown.utils.escapeHtml(str)
            }
            const lines = preCode.split(/\n/)
            // 添加自定义行号
            let html = lines
                .map((item, index) => {
                    if (item == "") {
                        return ""
                    }
                    return '<li><span class="line-num" data-line="' + (index + 1) + '"></span>' + item + "</li>"
                })
                .join("")
            if (props.showLine) {
                html = '<ol style="padding: 0px 30px;width: 20vw;">' + html + "</ol>"
            } else {
                html = '<ol style="padding: 0px 7px;list-style:none;width: 20vw;">' + html + "</ol>"
            }
            copyCodeData.value.push(str)
            let htmlCode = `<div class="markdown-wrap">`
            // #ifndef MP-WEIXIN
            // htmlCode += `<div style="color: #aaa;text-align: right;font-size: 12px;padding:8px;">`
            // htmlCode += `${lang}<a class="copy-btn" code-data-index="${copyCodeData.value.length - 1}" style="margin-left: 8px;color:#6675e9">复制代码</a>`
            // htmlCode += `</div>`
            // #endif
            htmlCode += `<pre class="hljs" style="padding:10px 8px 8px;margin-bottom:5px;overflow: auto;display: block;border-radius: 5px;"><code>${html}</code></pre>`
            htmlCode += "</div>"

            return htmlCode
        }
    }),
    {
        extensions: [
            {
                name: "math",
                level: "inline",
                start(src) {
                    return src.indexOf("$")
                },
                tokenizer(src, tokens) {
                    const match = src.match(/^\$+([^$\n]+?)\$+/)
                    if (match) {
                        return {
                            type: "math",
                            raw: match[0],
                            text: match[1].trim()
                        }
                    }
                },
                renderer(token) {
                    try {
                        return katex.renderToString(token.text, {
                            throwOnError: false,
                            displayMode: false
                        })
                    } catch (error) {
                        return `<span class="text-red-500">${error.message}</span>`
                    }
                }
            },
            {
                name: "mathBlock",
                level: "block",
                start(src) {
                    return src.indexOf("\n$$")
                },
                tokenizer(src, tokens) {
                    const match = src.match(/^\$\$+\n([^$]+?)\n\$\$+\n/)
                    if (match) {
                        return {
                            type: "mathBlock",
                            raw: match[0],
                            text: match[1].trim()
                        }
                    }
                },
                renderer(token) {
                    try {
                        return katex.renderToString(token.text, {
                            throwOnError: false,
                            displayMode: true
                        })
                    } catch (error) {
                        return `<div class="text-red-500">${error.message}</div>`
                    }
                }
            }
        ]
    }
)
const formatValue = () => {
    return markdown.parse(props.content ?? "思考中...")
}
</script>

<style lang="scss">
// #ifdef H5-WEIXIN || H5
.y-markdown-content {
    table {
        width: 100%;
        margin-bottom: 10px;
    }
    td,
    th {
        border: 1px solid #333333;
        padding: 2px 8px;
        width: 100px;
    }
}
// #endif
</style>

<style>
/* #ifdef H5-WEIXIN || H5 */
ol {
    padding-left: 44rpx !important;
}
/* #endif */
</style>
