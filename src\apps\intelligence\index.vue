<template>
    <view class="ai-intelligence">
        <z-paging ref="paging" use-chat-record-mode :auto="false" :show-scrollbar="false" :refresher-enabled="false" use-virtual-list @query="getAlbumList" v-model="state.inputReturned">
            <template #top>
                <uni-nav-bar statusBar fixed left-icon="left" @clickLeft="pageRouterBack" :border="false" :title="state.dataObj.name" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()">
                    <template #left>
                        <view class="left">
                            <uni-icons type="left" size="22" color="#333333" @click="pageRouterBack"></uni-icons>
                            <view @click.stop="handlerToggle">
                                <image style="width: 24px; height: 24px; margin-left: 3px" mode="aspectFill" :src="avatar"></image>
                                <span class="uni-badge-left-margin" @click.stop="handlerToggle">更多</span>
                            </view>
                        </view>
                    </template>
                </uni-nav-bar>
            </template>
            <view class="intelligence_list" style="transform: scaleY(-1)">
                <view class="intelligence_item" v-if="state.dataObj.id">
                    <view class="header">
                        <image class="avatar" mode="aspectFill" :src="state.dataObj.bannerUrl"></image>
                        <text>{{ state.dataObj.description }}</text>
                    </view>
                    <view class="content">
                        <text>{{ state.dataObj.properties?.welcomeMessage || "" }}</text>
                    </view>
                    <view class="footer">
                        <view class="footer_item" v-for="(it, idx) in state.samplePrompts" :key="idx" @click="handlerPrompt(it)">
                            <text>{{ it }}</text>
                            <uni-icons class="icon" type="right" size="14"></uni-icons>
                        </view>
                    </view>
                </view>

                <template v-for="(it, idx) in state.inputReturned">
                    <view class="intelligence_item_user" v-if="it.role === 'user'" :key="idx">
                        <view class="content">{{ it.content }}</view>
                    </view>
                    <view class="intelligence_item" v-if="it.role === 'assistant'" :key="idx">
                        <view class="header">
                            <image class="avatar" mode="aspectFill" :src="state.dataObj.bannerUrl"></image>
                            <text>{{ state.dataObj.name }}</text>
                        </view>
                        <view class="content">
                            <text v-if="it.loading">思考中...</text>
                            <yd-markdown :content="it.content" :showLoading="state.loadingicon"></yd-markdown>
                            <uv-loading-icon v-if="state.inputReturned.length - 1 == idx" :vertical="true" :show="state.loadingicon" color="#00b781ff" textSize="30rpx" class="reset-loading-icon"></uv-loading-icon>
                        </view>
                    </view>
                </template>
            </view>

            <template #empty>
                <slot name="empty">
                    <yd-empty text="暂无数据" />
                </slot>
            </template>
            <template #bottom>
                <view class="intelligence_footer">
                    <uni-easyinput class="intelligence_footer_input" both :adjust-position="false" :clearable="false" trim v-model="state.form.prompt" placeholder="有问题尽管问我～"></uni-easyinput>
                    <uni-icons v-if="state.loadingicon" type="circle-filled" size="34" color="#00b781ff" @click="handlerStop"></uni-icons>
                    <uni-icons v-else class="intelligence_footer_icon" :class="{ active: state.form.prompt && !state.loading }" type="paperplane" size="22" color="#fff" @click="handlerInput"></uni-icons>
                </view>
            </template>
        </z-paging>
        <MorePopup v-model:morePopupOpen="state.morePopupOpen" :inUseId="state.mathId" :assistantId="state.assistantId" @updatePrompt="updatePrompt"></MorePopup>
    </view>
</template>

<script setup>
import ydMarkdown from "./components/ydMarkdown.vue"
import MorePopup from "./components/morePopup.vue"
import { setToken } from "@/utils/storageToken.js"

const avatar = "https://file.1d1j.cn/ai-icon/xiaozhi.png"
const state = reactive({
    samplePrompts: [],
    dataObj: {},
    form: {
        sessionId: "",
        prompt: "",
        model: ""
    },
    assistantId: "default",
    inputReturned: [],
    mathId: "",
    morePopupOpen: false,
    loading: false,
    loadingicon: false,
    _prompt: "",
    _back: ""
})

const answer = ref("")
const paging = ref(null)

// 创建会话
async function createSession() {
    const params = {
        assistantId: state.assistantId,
        model: "qwen-max"
    }
    await http.post("/ai/chat/session/create", params).then(({ data }) => {
        const { id, model } = data
        state.form.sessionId = id
        state.form.model = model
    })
}

// 初始对话列表
async function getAlbumList() {
    await http.post("/ai/assistant/get", { id: state.assistantId }).then((res) => {
        state.dataObj = res.data || {}
        // 根据res.data.properties.samplePrompts的长度，然后在中间必须随机3条数据，如果少于3条，则直接返回所有数据
        const prompts = state.dataObj?.properties?.samplePrompts || []
        if (prompts.length < 4) {
            state.samplePrompts = prompts
        } else {
            // Shuffle the array
            const shuffledPrompts = [...prompts].sort(() => Math.random() - 0.5)
            // Take the first 3 items
            state.samplePrompts = shuffledPrompts.slice(0, 3)
        }
    })
}

const handlerToggle = () => {
    if (state.loadingicon) return
    state.morePopupOpen = true
    state.form.prompt = ""
}
// 发送对话
const setAnswer = (v = {}) => {
    answer.value = v
}
// 创建AbortController实例
const handlerStop = () => {
    state.loadingicon = false
    state.loading = false
    http.cancelRequest()
}
const handlerInput = async () => {
    // 只能在发消息的时候调用，并且只返一次
    if (!state.form.sessionId) await createSession()
    const { sessionId, prompt } = state.form
    if (state.loading) return
    state.inputReturned.push({
        role: "user",
        content: state._prompt || prompt
    })
    // 随机id
    const id = Math.random().toString(36).substring(2)
    // 用于输入对话时根据对应的id赋值数据
    state.mathId = id
    state.inputReturned.push({
        role: "assistant",
        id,
        content: "",
        loading: true
    })
    const params = {
        sessionId,
        prompt: state._prompt || prompt,
        fileId: "",
        imageUrl: ""
    }
    if (paging.value) {
        paging.value.scrollToBottom()
    }
    state.form.prompt = ""
    state._prompt = ""
    state.loading = true

    await http.fetchSSE("/ai/chat/completion/stream", params, setAnswer)
    state.loading = false
}
// 获取对话详细信息(包含消息)
const getChatSession = async () => {
    await http.post("/ai/chat/session/get", { id: state.mathId, includeMessages: true }).then(({ data }) => {
        data.messages.forEach((v) => {
            if (v.role === "assistant") {
                state.inputReturned.push({
                    role: "assistant",
                    id: v.id,
                    content: v.content,
                    loading: false
                })
            } else {
                state.inputReturned.push({
                    role: "user",
                    content: v.content
                })
            }
        })
        if (paging.value) {
            paging.value.scrollToBottom()
        }
    })
}

// 查看历史记录
const updatePrompt = async (item) => {
    state.assistantId = item.assistantId || "default"
    state.mathId = item.id || ""
    await getAlbumList()
    if (state.mathId) {
        await getChatSession()
    } else {
        state.form.sessionId = ""
    }
}
const handlerPrompt = (v) => {
    state._prompt = v
    handlerInput()
}
// 监听提问反馈的数据
watch(
    () => answer.value,
    (val) => {
        state.form.prompt = ""
        state.loadingicon = !!Object.keys(val).length
        state.inputReturned[state.inputReturned.length - 1].loading = false
        state.inputReturned[state.inputReturned.length - 1].content += val.text || ""
        // z-paging ref="paging" 滚动到底部
        if (paging.value) {
            paging.value.scrollToBottom()
        }
    }
)
onLoad((item) => {
    if (item.token) {
        console.log("token", item.token)
        setToken(item.token)
    }
    state.assistantId = item.assistantId || "default"
    state._back = item._back || ""
})

function pageRouterBack() {
    // #ifdef H5
    // 如果是内部应用跳转， 直接返回上一页。 state.route.noTodo  是上一页的路由
    if (state._back) {
        window.parent.postMessage({ libAppBack: true }, "*")
        return
    }
    uni.navigateBack()
    // #endif
    uni.navigateBack()
}

onMounted(async () => {
    await getAlbumList()
})
</script>

<style lang="scss" scoped>
.ai-intelligence {
    width: 100vw;
    height: 100vh;
    background-color: #f5fbf9ff;

    .left {
        width: 400rpx;
        display: flex;
        align-items: center;
        position: relative;

        .logo {
            width: 68rpx;
            height: 68rpx;
        }

        .uni-badge-left-margin {
            position: absolute;
            top: -6rpx;
            right: 4rpx;
            background-color: #ff4153ff;
            color: $uni-text-color-inverse;
            font-size: 20rpx;
            padding: 2rpx 10rpx;
            border-radius: 12rpx 12rpx 12rpx 0;
        }
    }

    :deep(.uni-navbar--fixed) {
        .uni-navbar__header {
            background-color: #f5fbf9ff !important;
        }

        .uni-navbar__header-btns-left {
            width: 150rpx !important;
        }

        .uni-nav-bar-text {
            font-weight: 600;
        }
    }

    .intelligence_list {
        .intelligence_item {
            margin: 30rpx;
            padding: 24rpx;
            background: $uni-bg-color;
            box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(226, 238, 234, 0.77);
            border-radius: 28rpx;

            .header {
                display: flex;
                font-weight: 500;
                font-size: 32rpx;

                .avatar {
                    width: 40rpx;
                    height: 40rpx;
                    min-width: 40rpx;
                    margin-right: 12rpx;
                    border-radius: 50%;
                }
            }

            .content {
                font-weight: 400;
                font-size: 28rpx;
                margin: 20rpx 0;
                .reset-loading-icon {
                    width: 16px;
                    height: 16px;
                    margin: 10px auto;
                }
            }

            .footer {
                .footer_item {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    background: #f4f6f8;
                    border-radius: 16rpx;
                    padding: 24rpx;
                    margin-top: 16rpx;

                    .icon {
                        width: 28rpx;
                        height: 28rpx;
                        background: $uni-bg-color;
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }
                }
            }
        }

        .intelligence_item_user {
            text-align: right;
            margin: 30rpx;

            .content {
                padding: 24rpx;
                border-radius: 28rpx;
                font-weight: 400;
                font-size: 28rpx;
                background: var(--primary-color);
                color: $uni-text-color-inverse;
                display: inline-flex;
                margin: 0;
                text-align: left;
                border-radius: 16rpx 16rpx 0rpx 16rpx;
            }
        }

        .answer-content {
            padding: 20rpx;
            margin: 20rpx;
            background-color: $uni-bg-color;
            border-radius: 12rpx;
            min-height: 100rpx;
            white-space: pre-wrap;
            word-break: break-all;
        }
    }

    .intelligence_footer {
        padding: 24rpx;
        background: $uni-bg-color;
        display: flex;
        align-items: center;

        &_input {
            flex: 1;
            margin: 0 24rpx;

            :deep(.is-input-border) {
                background-color: #f2f3f7 !important;
                border: none;
                border-radius: 38rpx;
            }
        }
        &_icon {
            background: #f2f3f7;
            border-radius: 38rpx;
            padding: 10rpx;
            width: 42rpx;
            height: 42rpx;
            &.active {
                background: var(--primary-color);
            }
        }
    }
}
</style>
