<template>
    <view class="invitation_class">
        <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="routerBack" title="邀请加入" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
        <lgd-tab @change="tabChangeFn" v-model="state.tabKey" :tabValue="state.tabList" underlineColor="#11C685" text-color="#11C685" />
        <div v-if="!state.url" class="noData">
            <img class="image" src="https://alicdn.1d1j.cn/announcement/20230706/312640b2818348e1af51b3a90aa6d0b4.png" alt="" />
            <span class="text">暂无权限，请联系管理员</span>
        </div>
        <div v-else>
            <!-- 筛选 -->
            <view class="select_class">
                <uni-data-picker @popupopened="showFn" @popupclosed="closeFn" v-model="state.selectDataId" :localdata="state.filterOptions" @change="onChangeClass" :map="{ text: 'name', value: 'id', children: 'children' }" v-slot:default="{ data, error }" popup-title="请选择选择班级/部门">
                    <view v-if="error" class="error">
                        <text>{{ error }}</text>
                    </view>
                    <view v-else-if="data.length" class="selected">
                        <div class="triangle_class">
                            <view v-for="(item, index) in data" :key="index" class="selected-item">
                                <text>{{ item.text }}</text>
                            </view>
                            <span class="triangle_down"></span>
                        </div>
                    </view>
                    <view v-else>
                        <text>请选择</text>
                    </view>
                </uni-data-picker>
            </view>
            <!-- 二维码 -->
            <div class="invitation_con">
                <div class="qr_code_class">
                    <div class="no_qr_code" v-if="!state.selectDataId || !state.url">
                        <img class="image" src="https://alicdn.1d1j.cn/announcement/20230706/4ad5373b2e974ca3bc468ea23d8b9b3a.png" alt="" />
                        <span class="text">暂无权限，请联系管理员</span>
                    </div>
                    <div class="qr_code_class" v-else>
                        <div class="tip">分享二维码邀请加入</div>
                        <div class="title">{{ state.className }}</div>
                        <div class="image_code" :hidden="state.isShow">
                            <uv-qrcode id="qrcode" ref="qrcodeRef" :value="state.url" @make="makeQrCodeFn" :h5SaveIsDownload="true" :auto="true"></uv-qrcode>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 邀请有效期 -->
            <hpy-form-select :dataList="rangeList" text="text" name="value" v-model="state.term" :islot="true" @change="changeTerm">
                <uni-list>
                    <uni-list-item class="mb-10" clickable showArrow title="邀请有效期">
                        <template v-slot:footer>
                            {{ state.term + "天" }}
                        </template>
                    </uni-list-item>
                </uni-list>
            </hpy-form-select>

            <div class="footer_btn">
                <button type="primary" plain="true" @click="copyLinkFn">复制链接</button>
                <!-- <button type="primary" plain="true" @click="downloadCodeFn">
          保存到相册
        </button> -->
            </div>
        </div>
    </view>
</template>

<script setup>
import lgdTab from "@/subModules/components/lgd-tab/components/lgd-tab/lgd-tab.vue"
import hpyFormSelect from "@/subModules/components/hpy-form-select/components/hpy-form-select/hpy-form-select.vue"
import { onPullDownRefresh } from "@dcloudio/uni-app"
import { copyUrl, rangeList, getListLast, getUserInfoFn } from "./invitation.js"

const qrcodeRef = shallowRef()
const state = reactive({
    codeId: "", // 链接的ID
    tabKey: 0, // tab切换key
    tabList: ["邀请家长", "邀请教师"],
    schoolType: 1, // 1-K12 2高校
    isShow: false,
    selectDataId: "",
    className: "",
    url: "", // 二维码链接
    filterOptions: [], // 选择班级部门数据
    term: 7 // 有效期默认7天
})

onPullDownRefresh(() => {
    init()
    setTimeout(function () {
        uni.stopPullDownRefresh()
    }, 1000)
})
function showFn() {
    state.isShow = true
}

function closeFn() {
    state.isShow = false
}

function copyLinkFn() {
    copyUrl(state.url)
}

function editTermFn() {
    state.term = 1
    console.log("asaa")
}

function changeTerm({ value }) {
    let data = {
        id: state.codeId,
        day: value || 7
    }
    http.post("/app/invite/cutTime", data).then((res) => {
        uni.showToast({
            title: res.message,
            duration: 2000,
            icon: "none"
        })
    })
}

function tabChangeFn() {
    state.className = ""
    state.selectDataId = ""
    state.term = 7
    state.filterOptions = []
    state.url = null
    if (state.tabKey === 0) {
        getClassListFn() // 获取班级
    } else {
        getDeptListFn() // 获取部门
    }
}

// 获取二维码接口
function getCodeUrlFn() {
    let type = 1
    if (state.tabKey != 1) {
        // 老师1,家长2，学生3
        type = state.schoolType === 1 ? 2 : 3
    }
    const data = {
        deptClassesId: state.selectDataId,
        type
    }
    http.post("/app/invite/getUrl", data).then((res) => {
        state.url = res?.data?.url
        state.codeId = res?.data?.id
    })
}

// 点击查询二维码Url
async function onChangeClass({ detail: { value } }) {
    console.log(value)
    const classNameList = []
    await value.forEach((item) => {
        classNameList.push(item.text)
    })
    state.className = classNameList.join("")
    state.selectDataId = value[value.length - 1]?.value
    getCodeUrlFn()
}

// 获取班级
function getClassListFn() {
    http.get("/app/timetable/getClassList?code=weeklyPublication").then(async (res) => {
        const { data } = res
        if (data && data.length) {
            state.filterOptions = data
            const classData = getListLast(data[0])
            state.className = classData?.showName || classData?.name
            state.selectDataId = classData?.id
            if (classData && classData.id && classData.id !== "") {
                getCodeUrlFn()
            }
        }
    })
}

// 获取部门
function getDeptListFn() {
    http.get("/app/dept/v1/list").then(async (res) => {
        const { data } = res
        if (data && data.list) {
            state.filterOptions = data?.list
            const deptData = getListLast(data?.list[0])
            state.className = deptData?.showName || deptData?.name
            state.selectDataId = deptData?.id
            if (deptData && deptData.id && deptData.id !== "") {
                getCodeUrlFn()
            }
        }
    })
}

function makeQrCodeFn(arr) {}

function downloadCodeFn() {
    // 导出临时文件路径
    qrcodeRef.value.toTempFilePath({
        success: (res) => {
            let oA = document.createElement("a")
            oA.download = "" // 设置下载的文件名，默认是'下载'
            oA.href = res.tempFilePath
            document.body.appendChild(oA)
            oA.click()
            oA.remove() // 下载之后把创建的元素删除
        },
        fail: (err) => {
            console.log(err)
        }
    })
}

async function init() {
    const data = await getUserInfoFn()
    state.schoolType = data?.schoolType // 1-K12 2高校
    let tabVal = {
        1: ["邀请家长", "邀请教师"], // k!2
        2: ["邀请学生", "邀请教师"] // 高校，大学,
    }
    state.tabList = tabVal[state.schoolType] || ["邀请家长", "邀请教师"]
}

onMounted(() => {
    init()
})
</script>

<style lang="scss" scoped>
.invitation_class {
    position: relative;
    min-height: 94vh;
    background: #f9faf9;
    overflow-y: auto;
    .noData {
        height: calc(100vh - 500rpx);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        .image {
            height: 400rpx;
            width: 400rpx;
        }
        .text {
            font-size: 28rpx;

            font-weight: 400;
            color: rgba(0, 0, 0, 0.65);
            line-height: 40rpx;
        }
    }

    .no_qr_code {
        height: 80%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        .image {
            height: 400rpx;
            width: 400rpx;
        }
        .text {
            font-size: 28rpx;

            font-weight: 400;
            color: rgba(0, 0, 0, 0.65);
            line-height: 40rpx;
        }
    }
    .filter_box {
        background: #ffffff;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        box-sizing: border-box;
        padding: 0 30rpx;
        margin-top: 20rpx;
        :deep(.uni-collapse-item__title) {
            .uni-collapse-item__title-box {
                padding: 0;
            }
            .uni-collapse-item__title-arrow {
                margin-right: 0;
                .uni-icons {
                    color: var(--primary-color) !important;
                }
            }
        }
    }
    .select_class {
        background: #fff;
        // height: 100rpx;
        // line-height: 100rpx;
        padding: 20rpx 30rpx;
        :deep(.selected) {
            display: flex;
            // height: 100rpx;
            // line-height: 100rpx;
        }
    }
    .invitation_con {
        padding: 20rpx 30rpx;
        .qr_code_class {
            height: 710rpx;
            background: #ffffff;
            border-radius: 20rpx;
            display: flex;
            flex-direction: column;
            margin-bottom: 20rpx;
            align-items: center;

            .tip {
                font-size: 28rpx;
                font-weight: 400;
                color: #999999;
                line-height: 40rpx;
                margin-top: 50rpx;
            }
            .title {
                margin: 20rpx 0rpx 64rpx 0rpx;
                font-size: 40rpx;
                font-weight: 500;
                color: #333333;
                line-height: 56rpx;
                text-align: center;
            }
            .image_code {
                width: 400rpx;
                height: 400rpx;
                image {
                    width: 100%;
                    height: 100%;
                }
            }
        }
    }
    .footer_btn {
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
        height: 162rpx;
        background: #ffffff;
        opacity: 0.9;
        display: flex;
        align-items: center;
        button {
            width: 96%;
            height: 92rpx;
            color: var(--primary-color);
            border-color: var(--primary-color);
            font-size: 32rpx;

            font-weight: 400;
            color: var(--primary-color);
            line-height: 92rpx;
        }
    }
}
.code_img {
    width: 400rpx !important;
    height: 400rpx !important;
    margin: 10px auto !important;
}
.triangle_class {
    display: flex;
    align-items: center;

    .triangle_down {
        width: 0;
        height: 0;
        overflow: hidden;
        font-size: 0;
        line-height: 0;
        border-width: 10rpx;
        margin-top: 10rpx;
        margin-left: 10rpx;
        border-style: solid dashed dashed dashed;
        border-color: var(--primary-color) transparent transparent transparent;
    }
}
</style>
