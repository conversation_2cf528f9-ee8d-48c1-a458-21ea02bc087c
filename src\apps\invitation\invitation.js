/**
 * @description: 复制链接
 * @return {string} "00"
 */
export function copyUrl(url) {
    uni.setClipboardData({
        data: url,
        success: function () {
            uni.showToast({
                title: "复制成功",
                duration: 2000,
                icon: "none"
            })
        },
        fail: function (err) {
            uni.showToast({
                title: "复制失败",
                duration: 2000,
                icon: "none"
            })
        }
    })
}

/**
 * @description: 递归拿到name
 * @return {string}
 */
export function getListLast(obj) {
    if (obj?.children?.length) {
        return getListLast(obj.children[0])
    }
    return obj
}

// 有效期数据
export const rangeList = [
    { value: 3, text: "3天" },
    { value: 7, text: "7天" },
    { value: 30, text: "30天" },
    { value: 90, text: "90天" }
]

// 获取用户信息
export function getUserInfoFn() {
    return http.get("/app/token").then((res) => {
        return res.data || {}
    })
}
