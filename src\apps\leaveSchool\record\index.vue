<template>
    <z-paging ref="paging" class="container_box" v-model="state.dataList" @query="queryList">
        <template #top>
            <view class="search_box">
                <uni-search-bar radius="100" placeholder="搜索班级" v-model="state.classesName" @confirm="confirm" bgColor="$uni-bg-color-grey" clearButton="none" cancelButton="none" />
            </view>
        </template>
        <view class="main_box">
            <view class="item_box" v-for="item in state.dataList" :key="item.classesId">
                <view class="title">{{ item.classesName }}</view>
                <view>放学时间：{{ item.afterTime }}</view>
                <view>操作人：{{ item.userName }}</view>
            </view>
        </view>
        <template #empty>
            <yd-empty text="暂无数据" />
        </template>
    </z-paging>
</template>
<script setup>
const paging = ref()
const state = reactive({
    dataList: []
})

const queryList = async (pageNo, pageSize) => {
    try {
        const params = {
            pageNo,
            pageSize,
            classesName: state.classesName
        }
        const { data } = await http.post("/app/after/school/log/page", params)
        paging.value.complete(data.list)
    } catch (err) {
        paging.value.complete(false)
    }
}

const confirm = () => {
    paging.value.reload()
}
</script>
<style lang="scss" scoped>
.container_box {
    .search_box {
        background: #fff;
        :deep(.uni-searchbar__box) {
            justify-content: flex-start;
            padding-left: 10rpx;
        }
    }
    .empty_box {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: #ccc;
        .img {
            width: 540rpx;
            height: 400rpx;
        }
    }
    .main_box {
        padding: 40rpx 30rpx;
        background: #f9faf9;
        .item_box {
            box-sizing: border-box;
            border-radius: 20rpx;
            min-height: 216rpx;
            padding: 32rpx 30rpx;
            font-size: 24rpx;
            color: #666666;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            margin-bottom: 30rpx;
            box-shadow: 0rpx 16rpx 16rpx 0rpx rgba(220, 245, 238, 0.5);
            background: #fff;
            .title {
                font-size: 30rpx;
                color: #333;
                font-weight: 600;
            }
        }
    }
}
</style>
