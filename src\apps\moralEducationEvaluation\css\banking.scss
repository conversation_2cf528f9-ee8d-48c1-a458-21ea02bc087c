// 德育评价排行
.moral_education {
    min-height: 100vh;
    position: relative;

    // 底部按钮
    .footer_box {
        width: 92%;
        height: 106rpx;
        background: $uni-bg-color;
        position: absolute;
        bottom: 0rpx;
        left: 0rpx;
        padding: 30rpx;

        button {
            background: var(--primary-color);
            border-radius: 10rpx;
            font-size: 32rpx;
            height: 92rpx;
            font-weight: 400;
            color: $uni-text-color-inverse;
            line-height: 92rpx;
        }
    }
}

// 头部
.head {
    .title_box {
        width: 100%;
        text-align: center;
        line-height: 88rpx;
        font-size: 34rpx;
        font-weight: 500;
        color: $uni-text-color;
    }
}

.right_class {
    display: flex;
    justify-content: flex-end;
    height: 100%;
    align-items: center;

    .text {
        font-size: 28rpx;
        font-weight: 400;
        color: $uni-text-color;
        line-height: 40rpx;
    }

    .image {
        width: 44rpx;
        height: 44rpx;
    }
}

// 排行列表
.banking_list {
    min-height: calc(100vh - 880rpx - var(--status-bar-height));
    max-height: calc(100vh - 880rpx - var(--status-bar-height));
    //  #ifdef H5 || H5-WEIXIN
    max-height: calc(100vh - 880rpx);
    min-height: calc(100vh - 880rpx);
    //  #endif
    //  #ifdef APP-PLUS
    max-height: calc(100vh - 880rpx);
    min-height: calc(100vh - 880rpx);
    //  #endif
    overflow-y: auto;
    padding: 30rpx;
    background: $uni-bg-color;
    border-radius: 40rpx 40rpx 0rpx 0rpx;

    .list_item {
        height: 100rpx;
        background: $uni-bg-color-grey;
        border-radius: 50rpx;
        margin-bottom: 30rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0rpx 30rpx;

        .left {
            display: flex;
            align-items: center;

            .text {
                line-height: 36rpx;
                font-size: 28rpx;
                font-weight: 400;
                color: #ff7b46;
            }

            .num {
                font-size: 44rpx;
                color: #ff7b46;
            }
        }

        .middle {
            display: flex;
            align-items: center;
            max-width: 300rpx;

            .num {
                font-size: 44rpx;
                color: #ff7b46;
                margin-right: 38rpx;
            }

            .image {
                width: 50rpx;
                height: 50rpx;
                border-radius: 50%;
                margin-right: 20rpx;
            }

            .gradeclass {
                font-size: 27rpx;
                font-weight: 400;
                line-height: 36rpx;
                color: #ff7841;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
                word-break: break-all;
            }
        }

        .right {
            display: flex;
            align-items: center;

            .fraction {
                font-size: 30rpx;
                font-weight: 400;
                line-height: 36rpx;
                color: #ff7b46;
            }

            .image {
                width: 36rpx;
                height: 36rpx;
            }
        }

        .no_myclass {
            max-width: 500rpx;

            .gradeclass {
                color: $uni-text-color;
            }

            .fraction {
                color: $uni-text-color-grey;
            }
        }
    }
}
