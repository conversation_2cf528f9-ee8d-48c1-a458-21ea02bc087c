<template>
    <div class="scoreInfo">
        <!-- 头部自定义导航栏 -->
        <view class="head">
            <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" :title="pageParams?.title || '积分列表'" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
        </view>
        <score-list :list="state.list" :myClass="pageParams" @clickItem="clickItem" />
    </div>
</template>

<script setup>
import ScoreList from "../components/scoreList.vue"
import { onLoad } from "@dcloudio/uni-app"

const state = reactive({
    list: []
})

const pageParams = ref({})
onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    pageParams.value = options
})

function clickLeft() {
    uni.navigateBack()
}
function clickItem(item) {
    navigateTo({
        url: "/apps/moralEducationEvaluation/scoreInfo/day",
        query: {
            ...item,
            activityId: pageParams.value.activityId
        }
    })
}

function getInfoFn() {
    const { activityId, cycleId, targetId } = pageParams.value
    const params = {
        activityId, // 活动标识
        cycleId, // 周期标识
        targetId // 我的班级
    }
    http.post("/app/moralEducationActivityMobile/identityDetail", params).then((res) => {
        state.list = res.data
    })
}

onMounted(() => {
    getInfoFn()
})
</script>

<style lang="scss" scoped>
// 头部
.head {
    .title_box {
        width: 100%;
        text-align: center;
        line-height: 88rpx;
        font-size: 34rpx;
        font-weight: 500;
        color: $uni-text-color;
    }
}
</style>
