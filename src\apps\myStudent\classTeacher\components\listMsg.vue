<template>
    <view class="list_msg_container">
        <view class="item" v-for="(item, index) in list" :key="index">
            <text class="left">{{ item.name }}</text>
            <text class="right">{{ item.value }}</text>
        </view>
    </view>
</template>
<script setup>
defineProps({
    list: {
        type: Array,
        default: () => []
    }
})
</script>
<style lang="scss">
.list_msg_container {
    .item {
        display: flex;
        justify-content: space-between;
        padding: 24rpx 40rpx;
        border-bottom: 1rpx solid $uni-border-color;
        &:last-of-type {
            border-bottom: none;
        }
        .left {
            color: $uni-text-color-grey;
        }
        .right {
            color: $uni-text-color;
        }
    }
}
</style>
