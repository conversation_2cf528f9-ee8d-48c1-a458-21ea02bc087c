<template>
    <view class="my_student_container">
        <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="routerBack" title="我的学生" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
        <view class="top_box">
            <cover-image class="yd_img" :src="classData.classesIcon || 'https://alicdn.1d1j.cn/announcement/20230708/3513f624fcaf4631b50126ebd1eea1cc.png'"></cover-image>
            <view class="r_box">
                <view class="top_text">{{ classData.classesName }}</view>
                <view class="btm_text"
                    >学生人数：<text class="text">{{ classData.studentNums }}人</text></view
                >
            </view>
        </view>
        <view class="list_box">
            <view class="title">老师 ({{ classData.teacherNums }})</view>
            <list-table :list="classData.employeeSubjectVOList" @handleClick="(item) => handleClick(item, 'teacher')">
                <template #default="{ item }">
                    <text class="yd_teacher" v-if="classData.masterId.includes(item.employeeId)">班主任</text>
                    <text class="yd_subject">{{ item.subject }}</text>
                </template>
            </list-table>
        </view>
        <view class="list_box">
            <view class="title">学生 ({{ classData.studentNums }})</view>
            <list-table :list="classData.classStudentList" @handleClick="(item) => handleClick(item, 'student')"></list-table>
        </view>
    </view>
</template>
<script setup>
import listTable from "../components/listCom.vue"

const classData = reactive({
    employeeSubjectVOList: [],
    classStudentList: [],
    classesId: ""
})

async function getList() {
    const params = {
        classesId: classData.classesId
    }
    const { data = {} } = await http.get("/app/class/classStudentList", params)
    Object.keys(data).forEach((i) => (classData[i] = data[i]))
    if (classData.employeeSubjectVOList.length) {
        classData.employeeSubjectVOList.forEach((i) => {
            i.subject = i.name
            i.name = i.employeeName
        })
    }
    if (classData.masterId) {
        classData.masterId = classData.masterId.split(",")
    }
}

const handleClick = (item, flag) => {
    let params = {}
    if (flag === "teacher") {
        params = item
        params.isDirector = classData.masterId.includes(item.employeeId)
        navigateTo({ url: "/apps/myStudent/classTeacher/teacherDetail", query: params })
    } else {
        params = item
        params.elterns = typeof item.elterns === "string" ? item.elterns : JSON.stringify(item.elterns) || "[]"
        navigateTo({ url: "/apps/myStudent/classTeacher/studentDetail", query: params })
    }
}

onLoad((options) => {
    classData.classesId = options.classesId
    getList()
})
</script>
<style lang="scss">
.my_student_container {
    background: $uni-bg-color-grey;
    min-height: 100vh;

    :deep(.uni-navbar__content) {
        border-bottom-color: transparent !important;
    }
    .top_box {
        display: flex;
        padding: 40rpx 28rpx;
        background-color: $uni-bg-color;
        .yd_img {
            width: 120rpx;
            height: 120rpx;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 24rpx;
        }
        .r_box {
            display: flex;
            flex-direction: column;
            justify-content: space-evenly;
            .top_text {
                font-size: 32rpx;
                color: $uni-text-color;
                font-weight: 600;
            }
            .btm_text {
                font-size: 28rpx;
                color: $uni-text-color-grey;
                .text {
                    color: $uni-text-color;
                }
            }
        }
    }
    .list_box {
        padding: 0 28rpx;
        margin-top: 20rpx;
        background-color: $uni-bg-color;
        .title {
            color: $uni-text-color-grey;
            border-bottom: 1rpx solid $uni-border-color;
            padding: 40rpx 0;
        }
        .yd_subject {
            font-size: 24rpx;
            padding: 2rpx 16rpx 4rpx 16rpx;
            color: var(--primary-color);
            background: #ebfaf5;
            border: 1rpx solid var(--primary-color);
            border-radius: 8rpx;
            margin-left: 8rpx;
        }
        .yd_teacher {
            font-size: 24rpx;
            padding: 2rpx 16rpx 4rpx 16rpx;
            color: $uni-color-warning;
            background: #fff3db;
            border: 1rpx solid $uni-color-warning;
            border-radius: 8rpx;
            margin-left: 8rpx;
        }
    }
}
</style>
