<template>
    <view class="list_container">
        <view v-for="(item, index) in list" :key="index" class="yd_item" @click="handleClick(item)">
            <view class="left_text"
                ><text class="head">{{ item.name.charAt(0) }}</text
                >{{ item.name }}</view
            >
            <view class="main_box"><slot :item="item"></slot></view>
            <view class="right"><uni-icons type="right" size="15" color="#8c8c8c"></uni-icons></view>
        </view>
    </view>
</template>
<script setup>
defineProps({
    list: {
        type: Array,
        default: () => []
    }
})

const emit = defineEmits(["handleClick"])

const handleClick = (item) => {
    emit("handleClick", item)
}
</script>
<style lang="scss">
.list_container {
    .yd_item {
        display: flex;
        padding: 30rpx 0;
        border-bottom: 1rpx solid $uni-border-color;
        .left_text {
            color: $uni-text-color;
            .head {
                display: inline-block;
                color: $uni-text-color-inverse;
                width: 64rpx;
                height: 64rpx;
                text-align: center;
                line-height: 64rpx;
                background-color: var(--primary-color);
                border-radius: 50%;
                margin-right: 15rpx;
            }
        }
        .main_box {
            flex: 1;
            margin: auto;
        }
        .right {
            margin: auto;
        }
    }
}
</style>
