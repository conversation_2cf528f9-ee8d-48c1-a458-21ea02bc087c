<template>
    <view class="teacher_detail_container">
        <view class="top_box">
            <view class="left_text">{{ teaData.name.charAt(0) }}</view>
            <view class="right_text">{{ teaData.name }}老师 <text v-if="teaData.isDirector === 'true'" class="yd_teacher">班主任</text></view>
        </view>
        <view class="main_box"> <list-msg :list="list"></list-msg></view>
    </view>
</template>
<script setup>
import listMsg from "./components/listMsg.vue"

let teaData = reactive({})

const list = ref([
    { name: "手机号", value: "", type: "phone" },
    { name: "任教科目", value: "", type: "subject" }
])
onLoad((params) => {
    Object.keys(params).forEach((i) => (teaData[i] = params[i]))
    list.value.forEach((i) => (i.value = teaData[i.type]))
})
</script>
<style lang="scss">
.teacher_detail_container {
    .top_box {
        padding: 40rpx 28rpx;
        display: flex;
        .left_text {
            display: inline-block;
            width: 120rpx;
            height: 120rpx;
            color: $uni-text-color-inverse;
            font-size: 32rpx;
            border-radius: 50%;
            background-color: var(--primary-color);
            font-weight: 600;
            margin-right: 24rpx;
            text-align: center;
            line-height: 120rpx;
        }
        .right_text {
            font-size: 32rpx;
            color: $uni-text-color;
            font-weight: 600;
            margin: auto 0;
            .yd_teacher {
                font-size: 24rpx;
                padding: 2rpx 16rpx 4rpx 16rpx;
                color: $uni-color-warning;
                background: #fff3db;
                border: 1rpx solid $uni-color-warning;
                border-radius: 8rpx;
                margin-left: 8rpx;
            }
        }
    }
    .main_box {
        padding: 0 28rpx;
    }
}
</style>
