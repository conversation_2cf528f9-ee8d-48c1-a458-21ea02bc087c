<template>
    <view class="create-footer">
        <slot name="footer">
            <!-- 审批 -->
            <view class="btns" v-if="options.status">
                <button class="btn plain" plain @click="handerGgreeRefuse(0)">拒绝</button>
                <button class="btn" type="primary" @click="handerGgreeRefuse(2)">同意</button>
            </view>
            <button class="siteSave" v-else type="primary" @click="handerSubmit">发布</button>
        </slot>
    </view>
</template>

<script setup>
const props = defineProps({
    forms: { type: Object, default: () => ({}) },
    options: {
        type: Object,
        default: () => {}
    }
})

const options = ref({})

const state = reactive({
    isTemplate: false,
    form: {
        id: "",
        approveStatus: ""
    }
})
const params = computed(() => props.forms)

watch(
    () => props.options,
    (val) => {
        options.value = val
        options.value.status = val.status == "true" || false
        state.form.id = val.id || null
    },
    { immediate: true }
)
const handerSubmit = () => {
    let URL = "/app/mobile/mess/publish/create"
    if (params.value.id) {
        URL = "/app/mobile/mess/publish/update"
    }
    http.post(URL, params.value).then(({ message }) => {
        uni.showToast({ title: message, icon: "none" })
        uni.redirectTo({
            url: "/apps/notice/index"
        })
    })
}
// 同意 拒绝
const handerGgreeRefuse = (status) => {
    state.form.approveStatus = status
    http.post("/app/mobile/mess/publish/updateApproveStatus", state.form).then(({ message }) => {
        uni.showToast({ title: message, icon: "none" })
        uni.navigateBack()
    })
}
</script>

<style lang="scss" scoped>
.create-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: $uni-text-color-inverse;
    padding: 20rpx;

    .btn {
        &[type="primary"] {
            background: var(--primary-color);
        }
    }

    .btns {
        display: flex;
        justify-content: space-between;

        .btn {
            flex: 1;
            margin: 0 10rpx;
        }

        .plain {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }
    }

    .siteSave {
        background-color: $uni-color-primary;
    }
}
</style>
