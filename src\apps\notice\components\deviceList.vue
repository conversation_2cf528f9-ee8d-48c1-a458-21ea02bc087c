<template>
    <view class="notification-scope">
        <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="接收设备" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()" />
        <view class="reset-list">
            <uni-section :title="items.deviceName" v-for="items in state.deviceList" :key="items.brandId">
                <uni-list>
                    <uni-list-item v-for="item in items.children" :key="item.brandId" :title="item.deviceName" />
                </uni-list>
                <yd-empty class="yd-empty" v-if="state.deviceList.length == 0" text="暂无数据" />
            </uni-section>
        </view>
    </view>
</template>

<script setup>
import { reactive } from "vue"
const state = reactive({
    deviceList: [],
    propsForm: {
        id: ""
    }
})

const getNotifyInfo = () => {
    http.post("/cloud/mobile/mess/publish/device", state.propsForm).then(({ data }) => {
        let bookMachineArr = [{ deviceName: "借还书机", brandId: 6, children: [] }]
        let classCardArr = [{ deviceName: "班牌", brandId: 1, children: [] }]
        data?.forEach((v) => {
            if (v.deviceType === 6) {
                bookMachineArr[0].children.push(v)
            } else if (v.deviceType === null) {
                classCardArr[0].children.push(v)
            }
        })
        state.deviceList = classCardArr.concat(bookMachineArr)
    })
}

const clickLeft = () => {
    uni.navigateBack()
}

onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    state.propsForm = options
    getNotifyInfo()
})
</script>

<style lang="scss" scoped>
.yd-empty {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
      /* #ifdef MP-WEIXIN */
    transform: translate(-50%, 100%);
    /* #endif */
}

.notification-scope {
    height: 100vh;
    background: $uni-bg-color-grey;

    .reset-list {
        margin: 10rpx 0;
        overflow: hidden auto;
        height: calc(100vh - 100rpx);

        :deep(.uni-section-header) {
            .distraction {
                font-size: 28rpx;
                font-weight: 600;
            }
        }
    }
}
</style>
