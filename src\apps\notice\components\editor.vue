<template>
    <view class="editor-content">
        <!-- #ifdef H5 || H5-WEIXIN -->
        <Toolbar class="reset-toolbar" :editor="editorRef" :defaultConfig="state.toolbarConfig" :mode="state.mode" />
        <Editor class="reset-editor" style="min-height: 500rpx" :defaultConfig="editorConfig" :mode="state.mode"
            v-model="valueHtml" @onCreated="handleCreated" @onChange="handleChange" @onDestroyed="handleDestroyed"
            @onFocus="handleFocus" @onBlur="handleBlur" @customAlert="customAlert" @customPaste="customPaste" />
        <!-- #endif -->
        <!-- #ifdef MP-WEIXIN -->
        <!-- #endif -->
        <!-- <uv-parse :content="cosntnte"></uv-parse> -->

    </view>
</template>

<script setup>
// #ifdef H5 || H5-WEIXIN
// 文档: https://www.wangeditor.com/v5/API.html#getallmenukeys
import "@wangeditor/editor/dist/css/style.css"
import { Editor, Toolbar } from "@wangeditor/editor-for-vue"
// const cosntnte = `
// 					<p>露从今夜白，月是故乡明</p>
// 					<p>露从今夜白，月是故乡明</p>
// 					<p>露从今夜白，月是故乡明</p>
// 					<p>露从今夜白，月是故乡明</p>
// 					<p>露从今夜白，月是故乡明</p>
// 				`
const props = defineProps({
    valueHtml: {
        type: String,
        default: ""
    }
})
const emit = defineEmits(["update:valueHtml"])
const valueHtml = computed(() => props.valueHtml)
const editorConfig = computed(() => {
    return { placeholder: !props.valueHtml || props.valueHtml == "<p><br></p>" ? "请输入内容..." : "" }
})
const state = reactive({
    editor: null,
    toolbarConfig: {
        toolbarKeys: ["headerSelect", "fontSize", "fontFamily", "bold", "italic", "underline", "bulletedList", "numberedList", "insertImage", "undo", "redo"]
    },

    mode: "default" // or 'simple' 'default'
})

// 编辑器实例，必须用 shallowRef，重要！
const editorRef = shallowRef()
const handleCreated = (editor) => {
    // 打印所有可用的工具栏按钮
    //  state.editor = Object.seal(editor) // 一定要用 Object.seal() ，否则会报错
    // console.log("所有可用的工具栏按钮：", editor.getAllMenuKeys())
    editorRef.value = Object.seal(editor)
}
// 编辑器值改变时触发
const handleChange = (editor) => {
    console.log("change:", editor.children)
    console.log("获取非格式化的 html", editor.getHtml())
    emit("update:valueHtml", editor.getHtml())
}
// 编辑器销毁时触发
const handleDestroyed = (editor) => {
    console.log("destroyed", editor)
}
// 编辑器聚焦时触发
const handleFocus = (editor) => {
    console.log("focus", editor)
}
// 编辑器失焦时触发
const handleBlur = (editor) => {
    emit("update:valueHtml", editor.getHtml())
}
// 编辑器点击时触发
const customAlert = (info, type) => {
    alert(`【自定义提示】${type} - ${info}`)
}
// 编辑器粘贴时触发
const customPaste = (editor, event, callback) => {
    console.log("ClipboardEvent 粘贴事件对象", event)
    // const html = event.clipboardData.getData('text/html') // 获取粘贴的 html
    // const text = event.clipboardData.getData('text/plain') // 获取粘贴的纯文本
    // const rtf = event.clipboardData.getData('text/rtf') // 获取 rtf 数据（如从 word wsp 复制粘贴）

    // 自定义插入内容
    editor.insertText("xxx")

    // 返回 false ，阻止默认粘贴行为
    event.preventDefault()
    callback(false) // 返回值（注意，vue 事件的返回值，不能用 return）

    // 返回 true ，继续默认的粘贴行为
    // callback(true)
}
// #endif
</script>

<style lang="scss" scoped>
// #ifdef H5 || H5-WEIXIN
.editor-content {
    border: 1px solid $uni-border-color;

    .reset-toolbar {
        border-bottom: 1px solid $uni-border-color;
    }

    .reset-editor {
        -webkit-user-select: text;
        outline: none;

        :deep(.w-e-text-container) {
            min-height: 500rpx;
        }

        :deep(.w-e-toolbar) {
            flex-wrap: wrap;
        }
    }
}

// #endif</style>
