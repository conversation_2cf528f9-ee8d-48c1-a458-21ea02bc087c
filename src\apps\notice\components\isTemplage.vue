<template>
    <view class="isTemplage" v-if="state.options.isTemplate == 'true'">
        <uni-data-select v-model="state.myType" :localdata="tabsList" @change="changeSelect"
            :clear="false"></uni-data-select>
    </view>
</template>

<script setup>
import { reactive } from "vue"
const tabsList = [
    {
        text: "通知公告",
        value: "announcement"
    },
    {
        text: "新闻资讯",
        value: "news"
    },
    {
        text: "校园风采",
        value: "campusStyle"
    },
    {
        text: "文章鉴赏",
        value: "article"
    },
    {
        text: "失物招领",
        value: "receive"
    },
    {
        text: "海报",
        value: "poster"
    },
    {
        text: "公文传阅",
        value: "officialDoc"
    },
    {
        text: "图片/视频",
        value: "picturesVideo"
    }
]
const state = reactive({
    myType: "announcement",
    options: {
        isTemplate: "" // 是否是模版
    }
})
// 下拉查询 我收到的  周期切换
const changeSelect = (item) => {
    navigateTo({ url: `/apps/notice/${item}/create`, query: { ...state.options, identifier: item } })
}
onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    state.options = options
    state.myType = options.identifier || "announcement"
})
</script>

<style lang="scss" scoped>
.isTemplage {
    background-color: $uni-bg-color;

    :deep(.uni-select__input-text) {
        font-size: 28rpx;
        color: #666666;
    }

    :deep(.uni-stat__select) {
        height: 80rpx;

        .uni-select {
            border: none;
            height: 20rpx;

            .uni-select__input-text {
                width: auto;
            }

            .uni-icons:before {
                content: "";
                display: block;
                border: 10rpx solid transparent;
                margin-left: 6rpx;
            }

            .uniui-bottom:before {
                border-top: 10rpx solid var(--primary-color);
                border-bottom-width: 1px;
                margin-top: 6rpx;
            }

            .uniui-top:before {
                border-bottom-color: var(--primary-color);
                border-top-width: 1px;
                margin-bottom: 6rpx;
            }
        }
    }
}
</style>
