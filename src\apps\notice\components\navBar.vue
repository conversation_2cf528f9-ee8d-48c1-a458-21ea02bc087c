<template>
    <view class="nav-bar">
        <uni-nav-bar statusBar fixed :border="false" left-icon="left" @clickLeft="back" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()">
            <view class="box-bg">
                {{ props.id ? props.title : `${props.title}` }}
                <!-- #ifdef MP-WEIXIN -->
                <view class="box-bg-item" v-if="state.status === 'all'" @click="popuptabs.open()">
                    <view class="box-bg-icon"></view>
                </view>

                <!-- #endif -->
            </view>
            <template #right>
                <!-- #ifdef H5 || H5-WEIXIN-->
                <view v-if="state.status == 'true'" :style="{ color: statusObj[state.approveStatus]?.color }">{{ statusObj[state.approveStatus].text }} </view>
                <view class="bar_right" v-if="state.status === 'false'" @click="handlerTemplate">
                    <img style="width: 20px; height: 18px" src="https://file.1d1j.cn/cloud-mobile/notice/templt.png" alt="" />
                    <text>模版</text>
                </view>
                <div class="bar_right" v-if="state.status === 'all'" @click="handlerAllReleases">所有发布</div>
                <uni-icons v-if="state.status === 'publish'" type="trash" size="18" @click="handlerDelete"></uni-icons>
                <!-- #endif -->
            </template>
        </uni-nav-bar>
        <!-- #ifdef MP-WEIXIN -->
        <view class="bar_rights" v-if="state.status === 'false'" @click="handlerTemplate"> <img style="width: 20px; height: 18px" src="https://file.1d1j.cn/cloud-mobile/notice/templt.png" alt="" />模版 </view>
        <uni-icons style="justify-content: flex-end; display: flex; margin-right: 10rpx" v-if="state.status === 'publish'" type="trash" size="18" @click="handlerDelete"></uni-icons>
        <!-- #endif -->

        <uni-popup ref="popuptabs" type="top" background-color="#fff" borderRadius="20rpx 20rpx 0 0">
            <view class="notice-popup-content">
                <radio-group @change="radioChange($event)">
                    <view class="content" v-for="it in questionList" :key="it.value">
                        <label class="uni-list-cell">
                            <view>{{ it.title }}</view>
                            <view>
                                <radio :value="JSON.stringify(it)" color="var(--primary-color)" :checked="it.value == groupActive" />
                            </view>
                        </label>
                    </view>
                </radio-group>
            </view>
        </uni-popup>
    </view>
</template>

<script setup>
const questionList = [
    { title: "所有发布", value: "allReleases" },
    { title: "信息发布", value: "notice" }
]
const props = defineProps({
    // 返回事件
    clickLeft: Function,
    title: {
        type: String,
        default: ""
    },
    id: {
        type: String,
        default: ""
    },
    status: {
        type: String,
        default: ""
    }
})
const groupActive = ref("")
const popuptabs = ref(false)
const statusObj = {
    0: { text: "拒绝", color: "#fd4f45" },
    1: { text: "审核中", color: "#f0ad4e" },
    2: { text: "已审核", color: "var(--primary-color)" },
    4: { text: "已撤销", color: "var(--primary-color)" }
}
const state = reactive({
    id: "",
    title: "",
    status: "false",
    approveStatus: null
})

const back = () => {
    if (props.clickLeft) {
        return props.clickLeft()
    }
    uni.navigateBack({
        delta: 1
    })
}
// 模版
const handlerTemplate = () => {
    navigateTo({
        url: "/apps/notice/components/libraryTemplate",
        success: (res) => {
            res.eventChannel.emit("libraryTemplate", { title: props.title })
        }
    })
}
// 删除我发布的
const handlerDelete = () => {
    uni.showModal({
        title: "提示",
        content: "您确定要删除该条信息吗？",
        confirmColor: "var(--primary-color)",
        success(res) {
            if (res.confirm) {
                const { id } = state
                http.post("/app/mobile/mess/publish/delete", { id }).then(({ message }) => {
                    uni.showToast({ title: message, icon: "none" })
                    uni.navigateBack()
                })
            }
        }
    })
}
// 所有发布
const handlerAllReleases = () => {
    navigateTo({
        url: "/apps/notice/allReleases/index",
        query: { dataSelectType: "allReleases" }
    })
}

const radioChange = (evt) => {
    const item = JSON.parse(evt.detail.value)
    groupActive.value = item.value
    if (item.value === "allReleases") {
        handlerAllReleases()
    } else {
        navigateTo({
            url: "/apps/notice/index"
        })
    }
    popuptabs.value.close()
}
watch(
    () => props.status,
    (v) => {
        state.status = v
    }
)
onLoad((options) => {
    Object.keys(options).forEach((key) => {
        state[key] = decodeURIComponent(options[key])
    })
    groupActive.value = options.dataSelectType || "notice"
    state.id = options.id || props.id
    state.status = options.status || options.type || props.status || "false"
    state.approveStatus = options.approveStatus || null
})
</script>

<style lang="scss" scoped>
.nav-bar {
    background-color: $uni-bg-color;

    .bar_right {
        color: var(--primary-color);
        display: flex;
        align-items: center;
    }

    :deep(.uni-navbar__header-btns-right) {
        width: 100rpx !important;
    }

    .box-bg {
        flex: 1;
        display: flex;
        text-align: center;
        justify-content: center;
        align-items: center;
        font-size: 34rpx;

        .box-bg-item {
            display: inline-block;
            display: flex;
            height: 100%;
            align-items: center;

            .box-bg-icon {
                display: inline-block;
                margin-left: 10rpx;
                border-top: 10rpx solid $uni-color-primary;
                border-left: 10rpx solid transparent;
                border-right: 10rpx solid transparent;
            }
        }
    }

    .notice-popup-content {
        padding-top: 168rpx;

        .handle {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20rpx;

            .handle-title {
                font-size: 36rpx;
                font-weight: 500;
                text-align: center;
                flex: 1;
            }

            .handle-icon {
                width: 40rpx;
            }
        }

        .content {
            .uni-list-cell {
                display: flex;
                align-items: center;
                padding: 20rpx;
                justify-content: space-between;
                border-bottom: 1rpx solid #eee;
            }
        }
    }
}

.bar_rights {
    color: $uni-color-primary;
    background-color: #f9faf9;
    text-align: right;
    padding: 10rpx 20rpx 0;
    font-size: 28rpx;
    display: flex;
    align-items: center;
}
</style>
