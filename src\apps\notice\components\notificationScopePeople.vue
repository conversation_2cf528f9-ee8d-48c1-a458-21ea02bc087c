<template>
    <view class="notification-scope">
        <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="通知范围人员"
            :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()" />

        <uni-list class="reset-list">
            <uni-list-item v-for="item in state.scopePeopleList" :key="item.id" :title="item.name" />
            <yd-empty class="yd-empty" v-if="state.scopePeopleList.length == 0" text="暂无数据" />
        </uni-list>
    </view>
</template>

<script setup>
import { reactive } from "vue"
const state = reactive({
    scopePeopleList: [],
    propsForm: {
        id: "",
        deptId: ""
    }
})

const getNotifyInfo = () => {
    let url = "/cloud/mobile/mess/publish/notifyEmployees"
    // 家长
    if (state.propsForm.classesId) {
        url = "/cloud/mobile/mess/publish/notifyElterns"
    }
    // 浏览人员： 字符 转 boolean
    if (state.propsForm.isView) {
        state.propsForm.isView = state.propsForm.isView == "true"
    }
    // 确认人员： 字符 转 boolean
    if (state.propsForm.isConfirm) {
        state.propsForm.isConfirm = state.propsForm.isConfirm == "true"
    }
    http.post(url, state.propsForm).then(({ data }) => {
        state.scopePeopleList = data
    })
}

const clickLeft = () => {
    uni.navigateBack()
}

onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    state.propsForm = options
    getNotifyInfo()
})
</script>

<style lang="scss" scoped>
.yd-empty {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    /* #ifdef MP-WEIXIN */
    transform: translate(-50%, 100%);
    /* #endif */
}

.notification-scope {
    height: 100vh;
    background: $uni-bg-color-grey;

    .reset-list {
        margin: 10rpx 0;
        overflow: hidden auto;
        height: calc(100vh - 164rpx);
    }
}
</style>
