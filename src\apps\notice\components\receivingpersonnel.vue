<template>
    <view class="notification-scope">
        <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="接收人员" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()">
            <template v-slot:right>
                <!-- #ifdef H5 || H5-WEIXIN-->
                <div class="bar_right" @click="handlerSubimt">确定</div>
                <!-- #endif -->
            </template>
        </uni-nav-bar>
        <view class="reset-list">
            <checkbox-group @change="checkboxChange">
                <view class="uni-list-cell" v-for="item in state.propsForm.deviceList" :key="item.key">
                    <checkbox :value="item.key" :checked="state.personActive.includes(item.key)" activeBackgroundColor="#00b781" icon-color="#fff">
                        <text class="name">{{ item.name }}</text>
                    </checkbox>
                </view>
            </checkbox-group>
            <yd-empty class="yd-empty" v-if="!state.propsForm.deviceList.length" text="暂无数据" />
        </view>
        <!-- #ifdef MP-WEIXIN  -->
        <button class="siteSave" type="primary" @click="handlerSubimt">确定</button>
        <!-- #endif  -->
    </view>
</template>

<script setup>
const eventChannel = getCurrentInstance().proxy.getOpenerEventChannel()
const state = reactive({
    propsForm: {
        deviceList: [
            {
                name: "全部家长",
                key: "1",
                checked: false
            },
            {
                name: "全部教职工",
                key: "2",
                checked: false
            }
        ]
    },
    personActive: []
})
const setMap = (devices) => {
    state.personActive = devices || []
    state.propsForm.deviceList.forEach((item, i) => {
        if (devices.includes(item.key)) {
            state.propsForm.deviceList[i].checked = true
        } else {
            state.propsForm.deviceList[i].checked = false
        }
    })
}

const clickLeft = () => {
    uni.navigateBack()
}

const checkboxChange = (evt) => {
    setMap(evt.detail.value)
}
const handlerSubimt = () => {
    try {
        eventChannel.emit("selectMember", state.propsForm.deviceList)
        uni.navigateBack()
    } catch (error) {
        console.log(error)
        uni.navigateBack()
    }
}
onLoad((items) => {
    Object.keys(items).forEach((key) => {
        items[key] = decodeURIComponent(items[key])
    })
    const selectedRecipient = items.selectedRecipient ? items.selectedRecipient.split(",") : []
    setMap(selectedRecipient)
})
</script>

<style lang="scss" scoped>
.yd-empty {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    /* #ifdef MP-WEIXIN */
    transform: translate(-50%, 100%);
    /* #endif */
}

.notification-scope {
    height: 100vh;

    .bar_right {
        color: var(--primary-color);
    }

    .reset-list {
        margin: 10rpx 0;
        overflow: hidden auto;
        height: calc(100vh - 222rpx);
        // #ifdef MP-WEIXIN
        height: calc(100vh - 316rpx);

        // #endif
        .uni-list-cell {
            display: flex;
            text-align: center;
            justify-content: flex-start;
            border-bottom: 1px solid $uni-border-color;
            padding: 20rpx;

            .name {
                font-size: 28rpx;
            }
        }
    }

    .siteSave {
        margin: 0 30rpx;
        background-color: $uni-color-primary;
    }
}
</style>
