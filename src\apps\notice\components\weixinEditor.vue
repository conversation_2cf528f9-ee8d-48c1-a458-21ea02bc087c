<template>
    <view class="weixin-editor">
        <!-- 操作按钮 -->
        <view class="toolbar">
            <!-- 字号选择 -->
            <picker mode="selector" :range="fontSizes" range-key="label" @change="onFontSizeChange">
                <button>字号({{ currentFontSize.label }})</button>
            </picker>

            <!-- 字体选择 -->
            <picker mode="selector" :range="fontFamilies" range-key="label" @change="onFontFamilyChange">
                <button>字体({{ currentFontFamily.label }})</button>
            </picker>
            <image class="icons" :src="'https://file.1d1j.cn/cloud-mobile/notice/image.png'" @click="insertImage" />
            <image class="icons" :src="'https://file.1d1j.cn/cloud-mobile/notice/bold.png'" @click="setBold" />
            <image class="icons" :src="'https://file.1d1j.cn/cloud-mobile/notice/italic.png'" @click="setItalic" />
            <image class="icons" :src="'https://file.1d1j.cn/cloud-mobile/notice/underline.png'"
                @click="setUnderline" />
            <image class="icons" :src="'https://file.1d1j.cn/cloud-mobile/notice/unordered_list.png'"
                @click="insertUnorderedList" />
            <image class="icons" :src="'https://file.1d1j.cn/cloud-mobile/notice/ordered_list.png'"
                @click="insertOrderedList" />
            <!-- <image class="icons" :src="'https://file.1d1j.cn/cloud-mobile/notice/left.png'" @click="undo" />
            <image class="icons" :src="'https://file.1d1j.cn/cloud-mobile/notice/right.png'" @click="redo" /> -->

            <!-- <button @click="setHeader">标题</button> -->
            <uni-icons type="trash" size="20" @click="clear"></uni-icons>

        </view>
        <!-- <button @click="getContents">获取内容</button> -->
        <!-- 富文本编辑器 -->
        <editor id="weixinEditor" class="weixinEditor" :placeholder="placeholder" @ready="onEditorReady"
            @input="onInput" @statuschange="onStatusChange"></editor>
        <!-- 显示HTML内容 -->
        <!-- <view class="preview">
            <text>预览内容：</text>
            <rich-text :nodes="htmlContent"></rich-text>
        </view> -->
    </view>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, watchEffect } from 'vue'
// 响应式数据
const props = defineProps({
    valueHtml: {
        type: String,
        default: ''
    }
})
const emit = defineEmits(['valueHtml', 'editValueHtml']);

// 字号选项
const fontSizes = ref([
    { label: '小', value: '12px' },
    { label: '标准', value: '16px' },
    { label: '中', value: '18px' },
    { label: '大', value: '20px' },
    { label: '特大', value: '24px' }
])

// 字体选项
const fontFamilies = ref([
    { label: '默认', value: '' },
    { label: '宋体', value: 'SimSun, 宋体' },
    { label: '黑体', value: 'SimHei, 黑体' },
    { label: '微软雅黑', value: 'Microsoft YaHei' },
    { label: 'Arial', value: 'Arial, sans-serif' }
])
// 当前选择的字号和字体
const currentFontSize = ref(fontSizes.value[1]) // 默认选中"标准"
const currentFontFamily = ref(fontFamilies.value[0]) // 默认选中"默认"
const placeholder = ref('请输入内容...')
const editorCtx = ref(null)
const htmlContent = ref('')
const formats = ref({})
const canUndo = ref(false)
const canRedo = ref(false)
// 获取组件实例
const instance = getCurrentInstance()
const initEditor = () => {
    uni.createSelectorQuery()
        .in(instance.proxy) // 在小程序中确保作用域正确
        .select('#weixinEditor')
        .fields({
            node: true,
            context: true
        }, (res) => {
            if (res && res.context) {
                editorCtx.value = res.context
                console.log('编辑器初始化成功')
            } else {
                console.error('编辑器初始化失败', res)
                retryInitEditor()
            }
        }).exec()
}
// 重试机制
const retryInitEditor = (retryCount = 0) => {
    if (retryCount >= 3) {
        uni.showToast({
            title: '编辑器初始化失败，请刷新页面',
            icon: 'none'
        })
        return
    }

    setTimeout(() => {
        console.log(`第${retryCount + 1}次重试初始化编辑器`)
        initEditor()
    }, 500 * (retryCount + 1))
}

const onEditorReady = () => {
    initEditor()
    // // 初始化完成后设置内容
    // if (props.valueHtml) {
    //     setTimeout(() => {
    //         if (editorCtx.value ) {
    //             debugger
    //             editorCtx.value.setContents({
    //                 html: props.valueHtml
    //             })
    //             // htmlContent.value = props.valueHtml
    //         }
    //     }, 100) // 延迟确保编辑器完全初始化
    // }
}


// 输入内容变化
const onInput = (e) => {
    emit("editValueHtml", e.detail.html)
    console.log('输入内容变化', e)
    // checkUndoRedoStatus()
}
// 安全检查编辑器上下文
const ensureEditorContext = () => {
    if (!editorCtx.value || typeof editorCtx.value.format !== 'function') {
        console.warn('编辑器上下文未正确初始化')
        return false
    }
    return true
}
// 检查撤回和重做状态
const checkUndoRedoStatus = () => {

    if (!ensureEditorContext()) return
    // 使用安全调用方式
    const ctx = editorCtx.value
    try {
        ctx.execCommand('getUndoStatus', {}, (res) => {
            canUndo.value = res.result?.undoable ?? false
            canRedo.value = res.result?.redoable ?? false
        })
        return
    } catch (e) {
        console.error('微信特有API调用失败', e)
    }
}

// 格式变化
const onStatusChange = (e) => {
    formats.value = e.detail
    console.log('格式变化', formats.value)
}

// 插入图片
const insertImage = () => {
    uni.chooseImage({
        count: 1,
        success: (res) => {
            const tempFilePaths = res.tempFilePaths
            editorCtx.value.insertImage({
                src: tempFilePaths[0],
                alt: '图片',
                width: '80%',
                success: () => {
                    console.log('插入图片成功')
                    checkUndoRedoStatus()
                }
            })
        }
    })
}

// 设置加粗
const setBold = () => {
    // editorCtx.value.format('bold')
    if (ensureEditorContext()) {
        editorCtx.value.format('bold')
    }
}

// 设置斜体
const setItalic = () => {
    if (ensureEditorContext()) {
        editorCtx.value.format('italic')
    }
}

// 设置下划线
const setUnderline = () => {
    if (ensureEditorContext()) {
        editorCtx.value.format('underline')
    }
}

// 设置标题
const setHeader = () => {
    if (ensureEditorContext()) {
        editorCtx.value.format('header', 2)
    }
}

// 撤回
const undo = () => {
    if (!canUndo.value) {
        uni.showToast({
            title: '无法撤回',
            icon: 'none'
        })
        return
    }

    editorCtx.value.undo({
        success: () => {
            console.log('撤回成功')
            checkUndoRedoStatus()
        },
        fail: (err) => {
            console.error('撤回失败', err)
        }
    })
}

// 重做
const redo = () => {
    if (!canRedo.value) {
        uni.showToast({
            title: '无法重做',
            icon: 'none'
        })
        return
    }

    editorCtx.value.redo({
        success: () => {
            console.log('重做成功')
            // checkUndoRedoStatus()
        },
        fail: (err) => {
            console.error('重做失败', err)
        }
    })
}

// 清空
const clear = () => {
    editorCtx.value.clear({
        success: () => {
            uni.showToast({
                title: '已清空',
                icon: 'success'
            })
            // checkUndoRedoStatus()
        }
    })
}

// 获取内容
const getContents = () => {
    editorCtx.value.getContents({
        success: (res) => {
            emit('valueHtml', res.html)
            htmlContent.value = res.html
            console.log('获取内容成功', res)
            uni.showToast({
                title: '获取内容成功',
                icon: 'success'
            })
        }
    })
}

// 字号选择变化
const onFontSizeChange = (e) => {
    const index = e.detail.value
    currentFontSize.value = fontSizes.value[index]
    if (ensureEditorContext()) {
        editorCtx.value.format('fontSize', currentFontSize.value.value)
    }
}

// 字体选择变化
const onFontFamilyChange = (e) => {
    const index = e.detail.value
    currentFontFamily.value = fontFamilies.value[index]
    if (ensureEditorContext()) {
        editorCtx.value.format('fontFamily', currentFontFamily.value.value)
    }
}


// 插入无序列表
const insertUnorderedList = () => {
    if (ensureEditorContext()) {
        editorCtx.value.format('list', 'bullet')
    }
}

// 插入有序列表
const insertOrderedList = () => {
    if (ensureEditorContext()) {
        editorCtx.value.format('list', 'ordered')
    }
}
watch(() => props.valueHtml, (newVal) => {
    debugger

    if (editorCtx.value && newVal && !htmlContent.value) {
        // 初始化完成后设置内容
        setTimeout(() => {
            if (editorCtx.value) {
                debugger
                editorCtx.value.setContents({
                    html: props.valueHtml
                })
                // htmlContent.value = props.valueHtml
            }
        }, 100) // 延迟确保编辑器完全初始化
        // }
        // editorCtx.value.setContents({
        //     html: newVal
        // })
    }
})
// 使用onMounted确保DOM已渲染
onMounted(() => {
    nextTick(() => {
        initEditor()
    }) // 增加延迟确保组件完全渲染
})

</script>

<style lang="scss" scoped>
.weixin-editor {
    .weixinEditor {
        height: 300rpx;
        border-top: 1rpx solid #ddd;
        border-bottom: 1rpx solid #ddd;
        padding: 10rpx 0;
        text-indent: 20rpx;
        margin-bottom: 20rpx;
    }

    .toolbar {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 20rpx;
        background-color: #fff;

        .icons {
            width: 60rpx;
            height: 60rpx;
        }

        button {
            font-size: 24rpx;
            background-color: #f8f8f8;

            &:active {
                background-color: #e8e8e8;
            }
        }

    }


    .preview {
        border: 1rpx solid #eee;
        padding: 20rpx;
        margin-top: 20rpx;
        min-height: 200rpx;
        background-color: #f9f9f9;
    }

    /* 针对picker的特殊样式 */
    .toolbar picker {
        display: inline-block;
        margin-right: 10rpx;
        margin-bottom: 10rpx;
    }

    .toolbar picker button {
        width: 100%;
        text-align: center;
    }
}
</style>