<!-- 嵌套在原生上 -->
<template>
    <view class="create_container" :class="{ IosActive: checkPlatform() === 'Ios' }">
        <!-- <NavBar :title="state.navBarTitle" /> -->
        <view :class="{ scrollTop: state.isScrollTop }" :style="{ height: state.isScrollTop ? state.scrollTopH : '' }">
            <uni-easyinput class="title_input" v-model="state.form.title" :inputBorder="false" trim="all" maxlength="50"
                placeholder="请输入标题（0/50）" />

            <uni-easyinput class="title_input" v-if="isArticle" v-model="state.form.author" :inputBorder="false"
                trim="all" maxlength="50" placeholder="请填写作者（0/50）" />

            <uni-easyinput class="title_input" v-if="isArticle || isNews" v-model="state.form.source"
                :inputBorder="false" trim="all" maxlength="50" placeholder="请填写出处（0/50）" />

            <!-- 失物招领 receive -->
            <template v-if="isReceive">
                <uni-easyinput class="title_input" v-model="state.form.lossItems" :inputBorder="false" trim="all"
                    maxlength="20" placeholder="请输入招领物品（0/20）" />

                <uni-easyinput class="title_input" v-model="state.form.lossLocation" :inputBorder="false" trim="all"
                    placeholder="请输入丢失/拾取地点" />

                <uni-datetime-picker class="title_input" :border="false" type="datetime" @click="state.show = true"
                    v-model="state.form.lossTime" placeholder="请选择丢失/拾取时间" />

                <uni-easyinput class="title_input" v-model="state.form.contactPerson" :inputBorder="false" trim="all"
                    placeholder="请输入联系人" />
                <uni-easyinput class="title_input" v-model="state.form.contactPhone" :inputBorder="false" trim="all"
                    maxlength="11" placeholder="请输入联系电话" />
            </template>


            <view class="edit_container">
                <template v-if="state.form.contentType">
                    <!-- 海报图片 -->
                    <template v-if="isEditF(state.form)">
                        <MobileEdit v-if="state.form.details[0].parseJson" :createTempId="state.tempId"
                            :createData="state.createData" @EmitMobileEdit="emitMobileEdit" />
                        <img v-else :src='state.form.details[0].contentImg' alt=""
                            style="display: block;width:100%;height:100%" />
                    </template>
                    <!-- 可编辑图库海报 -->
                    <view class="body" v-else>
                        <MobileEdit v-if="state.isEdit" :createTempId="state.tempId" :createData="state.createData"
                            @EmitMobileEdit="emitMobileEdit" />
                        <img @click="state.isEdit = true" v-else :src='state.createData[0].contentImg' alt=""
                            style="display: block;width:100%;height:100%" />
                    </view>
                </template>
                <!-- 正文 -->
                <template v-else>
                    <img v-if="!state.form.contentType && state.form.identifier == 'poster'"
                        :src='state.form.contentImg' alt="" style="display: block;width:100%;height:100%" />
                    <template v-if="state.form.identifier !== 'poster'">
                        <!-- #ifndef MP-WEIXIN  -->
                        <Editor v-model:valueHtml="state.form.content" />
                        <!-- #endif -->

                        <!-- #ifdef MP-WEIXIN -->
                        <weixin-editor v-model:valueHtml="valueHtml" @editValueHtml="editValueHtml" />
                        <!-- #endif -->
                    </template>

                </template>
            </view>
        </view>
        <view class="IosBtn">
            <button v-if="!state.isEdit" type="primary" class="btn_save" :class="{ acitve: checkPlatform() == 'Ios' }"
                @click="submit">保 存</button>
        </view>
    </view>
</template>

<script setup>
import { sendAppEvent, checkPlatform } from "@/utils/sendAppEvent.js"
import Editor from "../components/editor.vue"
import weixinEditor from "@/apps/notice/components/weixinEditor.vue"
// import MobileEdit from "./index.jsx";
const valueHtml = ref('')
const state = reactive({
    isScrollTop: false,
    scrollTopH: '',
    paddingBottom: { paddingBottom: '30px' },
    tempId: null,
    createData: [],
    isEdit: false,
    show: false,
    lossTime: new Date(),
    Uedit: false,
    form: {
        identifier: "",
        contentImg: "",
        content: "",
        coverImg: "",
        title: "",
        author: "",
        source: "",
        lossItems: "",
        lossLocation: "",
        lossTime: "",
        contactPerson: "",
        contactPhone: "",
        contentType: 0,
        brandIds: [],
        details: []
    },
    type: "",
    _title: "",
    navBarTitle: ''
});

const isReceive = computed(() => {
    return state.type === "receive";
});

const isArticle = computed(() => {
    return state.type === "article";
});
const isNews = computed(() => {
    return state.type === "news";
});
const setDefaultContent = () => {
    const item = {
        announcement: {
            title: `通知`,
            content: ``,
        },
        receive: {
            title: `失物招领`,
            content: ``,
        },
        campusStyle: {
            title: `校园风采`,
            content: ``,
        },
        article: {
            title: `文章`,
            content: ``,
        },
        news: {
            title: `新闻`,
            content: ``,
        },
        officialDoc: {
            title: `公文`,
            content: ``,
        },
    };
    const { title, content } = item[state.type] || {};
    window.document.title = state._title || `发布` + title;
    state.navBarTitle = state._title || title
    return content || state.form.content;
};
const emitMobileEdit = (item) => {
    state.form.details = item
    state.createData = item
    state.isEdit = false
}
const chooseMonth = (time) => {
    state.form.lossTime = moment(time).format('YYYY-MM-DD HH:mm:ss')
    state.show = false
}
const initInstance = () => {
    return setDefaultContent();
};

const verifyField = cb => {
    const { title, author, source, content, lossItems, lossTime, lossLocation, contactPerson, contactPhone } = state.form
    if (!title) {
        return uni.showToast({
            icon: "none",
            title: "请输入标题！"
        })
    }
    if (state.type === 'receive') {
        if (!lossItems) {
            return uni.showToast({
                icon: "none",
                title: "请输入招领物品！"
            })
        }
        if (!lossLocation) {
            return uni.showToast({
                icon: "none",
                title: "请输入丢失/拾取地点！"
            })
        }
        if (!lossTime) {
            return uni.showToast({
                icon: "none",
                title: "拾取时间！"
            })
        }

        if (!contactPerson) {
            return uni.showToast({
                icon: "none",
                title: "请输入联系人！"
            })
        }
        if (!contactPhone) {
            return uni.showToast({
                icon: "none",
                title: "请输入联系电话！"
            })
        } else {
            // if (!/^((12[0-9])|(13[0-9])|(14[5])|(15([0-3]|[5-9]))|(18[0,5-9]))\d{8}$/.test(val)) return false;
            if (!/^[1][3-9][0-9]{9}$/.test(contactPhone)) return uni.showToast({
                icon: "none",
                title: "请输入正确的联系电话！"
            })
        }

    }
    if (isArticle.value && !author) {
        return uni.showToast({
            icon: "none",
            title: "请填写作者"
        })
    }
    if ((isArticle.value || isNews.value) && !source) {
        return uni.showToast({
            icon: "none",
            title: "请填写出处"
        })
    }
    if (!content && !state.tempId && state.form.identifier !== 'poster') {
        return uni.showToast({
            icon: "none",
            title: "请输入内容！"
        })
    }
    cb && cb();
};

const submit = () => {
    verifyField(() => {
        sendAppEvent("saveText", {
            subs: state.form.details,
            ...toRaw(state.form),
            tempTitle: state.form.title,
            type: state.type,
        });
    });
};

const getLocationParams = (keyWords) => {
    // 提取路由值（字符串）
    let href = window.location.href;
    // 从占位符开始截取路由（不包括占位符）
    let query = href.substring(href.indexOf("?") + 1);
    // 根据 & 切割字符串
    let vars = query.split("&");
    for (let i = 0; i < vars.length; i++) {
        let pair = vars[i].split("=");
        // 根据指定的参数名去筛选参数值
        if (pair[0] == keyWords) {
            return pair[1];
        }
    }
    return "";
}
// 编辑
const eidtContent = () => {
    const copyId = getLocationParams('copyId')
    const editId = getLocationParams('editId')
    const contentType = getLocationParams('contentType')
    const messType = getLocationParams('messType')
    if (copyId || editId) {
        // 详情
        const params = { id: copyId || editId, contentType, messType };
        http.post("/cloud/mobile/mess/publish/getInfo", params).then((res) => {
            const { data } = res
            const { title, content, details, contentType, contentImg, identifier } = data;
            if (copyId) {
                state.form.title = title
                state.form.content = content
                valueHtml.value = content
            } else {
                for (let i in state.form) {
                    if (!i && (!data[i] || !data[i].length)) return
                    state.form[i] = data[i]
                    if (i === 'lossTime' && data[i]) {
                        state.lossTime = data[i]
                    }
                }
            }
            if (contentType == 1 && details.length) {
                state.tempId = copyId || editId
                state.createData = details
                state.form.contentType = contentType
                state.form.details = details
            }
            nextTick(() => {
                // 如果contentType =0 contentImg 不为空 identifier == "poster"
                if (identifier == "poster" && !contentType && contentImg) {
                    state.Uedit = true
                }
            })

        })
    }
}

const isEditF = computed(() => {
    return (item) => {
        const { contentType, details } = item
        if (contentType == 1 && details.length && details[0]?.parseJson) {
            return false
        }

        return true
    }
})

const editValueHtml = (item) => {
    state.form.content = item
}
onUnmounted(() => {
    window.removeEventListener("resize");
})
onMounted(() => {
    const originalHeight = window.innerHeight;
    eidtContent()
    setDefaultContent();
    window.addEventListener("resize", () => {
        const resizeHeight = window.innerHeight;
        if (originalHeight - resizeHeight > 50) {
            state.scrollTopH = `${originalHeight - resizeHeight}px`
            console.log('我打开了键盘', originalHeight - resizeHeight);

        } else {
            console.log('我关闭了键盘', originalHeight - resizeHeight);
        }
        document.activeElement.scrollIntoView(true);
    });
});
onLoad((item) => {
    Object.keys(item).forEach((key) => {
        item[key] = decodeURIComponent(item[key])
    })
    state.type = item.type || ""
    state._title = item.title || ""
})
</script>

<style scoped lang="scss">
.title_input {
    border-bottom: 1rpx solid #d8d8d8;
    background: #f9faf9;
    font-size: 28rpx;

    :deep(.uni-date__x-input) {
        font-size: 24rpx;
        color: #333;
        text-indent: 10rpx;
    }

    :deep(.uni-icons) {
        display: none;
    }

    :deep(.van-cell__value) {
        position: relative;

        .van-field__word-limit {
            position: absolute;
            top: 0;
            right: 0;
        }
    }

    &::after {
        border-bottom: none !important;
    }
}

// 富文本
:deep(.tox-tinymce) {
    border: none;
    height: 100%;

    &:not(.tox-tinymce-inline) .tox-editor-header:not(:first-child) .tox-toolbar:first-child,
    .tox-tinymce:not(.tox-tinymce-inline) .tox-editor-header:not(:first-child) .tox-toolbar-overlord:first-child .tox-toolbar__primary {
        border-top: none;
        background: none !important;
    }

    :deep(img) {
        width: 100% !important;
    }
}

.create_container {
    min-height: 100vh;
    position: relative;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    background: #f9faf9;
    overflow: hidden auto;

    &.IosActive {
        padding-bottom: 200rpx !important;
    }

    .scrollTop {
        overflow: hidden auto;
    }

    .edit_container {
        flex: 1;
        padding-bottom: 120rpx;
        border-top: none !important;

        img {
            width: 100% !important;
            height: auto;
        }
    }

    :deep(.tox .tox-toolbar, .tox .tox-toolbar__primary, .tox .tox-toolbar__overflow) {
        background: none !important;
    }

    .IosBtn {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        display: block;
        width: auto;
        background: #ffffff;
        padding: 0 30rpx;

        .btn_save {
            width: 100%;
            margin-top: 30rpx;
            margin: 30rpx 0 30rpx;
            background-color: #11c685;
            border-color: #11c685;

            &.acitve {
                margin-bottom: 100rpx;
            }
        }
    }
}

@supports (bottom: constant(safe-area-inset-bottom)) or (bottom: env(safe-area-inset-bottom)) {
    .IosBtn {
        padding-bottom: calc(10rpx + constant(safe-area-inset-bottom));
        padding-bottom: calc(10rpx + env(safe-area-inset-bottom));
    }
}
</style>

<style lang="scss">
.tox .tox-dialog__body-nav-item:focus {
    background: none;
}

.tox .tox-dialog__body-nav-item--active {
    border-bottom: 2rpx solid #11c685;
    color: #11c685;
}

.tox-dialog {
    margin-top: 50%;
    transform: translateY(20%);
}

.tox .tox-dialog-wrap__backdrop {
    background: rgba(0, 0, 0, 0.3);
}

.tox-dialog__footer-end {
    .tox-button {
        background-color: #11c685;
        border-color: #11c685;
    }

    .tox-button--secondary {
        background: #f0f0f0 !important;
        border-color: #f0f0f0 !important;
    }
}

//https://alicdn.1d1j.cn/announcement/20220420/ec295462706c44c782fce3b7e97504de.jpg

#tinymce {
    img {
        width: 100% !important;
        height: auto !important;
        display: inline-block;
    }
}

.edit_container p img {
    max-width: 80%;
    width: auto !important;
    height: auto !important;
}

.tox .tox-edit-area__iframe,
.tox .tox-toolbar-overlord {
    background: #f9faf9;

}
</style>
