<template>
    <div class="conten" :style="{ width: '100%', height: '100%', backgroundColor: data.document?.backgroundColor||'' }">
        <div v-for="(item, index) in data.elements" :key="item.id">
            <div v-if="item.type == 'image'">
                <div :style="{ ...hasStyleAttributeRem(item) }">
                    <img :src="item.imageSrc" alt="" style="display: block;width:100%;height:100%" />
                </div>
            </div>
            <div v-if="item.type == 'text'">
                <div v-html="replaceBr(item.value)" :style="{ ...hasStyleAttributeRem(item) }"
                    @click="changeShowOverlay('open', true, item.value, index, null, item.id)">
                </div>
            </div>
            <div v-if="item.type == 'group'">
                <div :style="{ ...hasStyleAttributeRem(item) }">
                    <div v-for="citem in item.children" :key="citem.id">
                        <div v-if="citem.type == 'image'" :style="{
                            ...hasStyleAttributeRem(
                                citem
                            )
                        }">
                            <img :src="citem.imageSrc" alt="" style="display: block;width:100%;height:100%" />
                        </div>
                        <div v-else v-html='replaceBr(citem.value)' :style="{ ...hasStyleAttributeRem(citem) }" @click='changeShowOverlay("open", true, citem.value, index, cindex,
                            citem.id)'></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { reactive, onMounted } from 'vue'
import { hasStyleAttributeRem, replaceBr } from "./utils/format.js";
const props = defineProps({
    data: {
        type: Object,
        default: () => {
            return {
                document: {
                    width: 750,
                    height: 1334,
                    backgroundColor: "#fff",
                },
                elements: [],
            };
        },
    },
})
const emit = defineEmits(['TextClick']);
const state = reactive({

})


const changeShowOverlay = (type, boole, value, index, cindex, id) => {
    emit("TextClick", {
        type,
        boole,
        value,
        index,
        cindex: cindex,
        id: id,
    });
};
</script>

<style lang="scss" scoped >
@import "./index.scss";
</style>