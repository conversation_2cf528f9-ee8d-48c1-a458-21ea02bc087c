@keyframes fadeinB {
    0% {
        opacity: 0;
        transform: translateY(-100px);
        -webkit-transform: translateY(-100px);
        -moz-transform: translateY(-100px);
        -ms-transform: translateY(-100px);
        -o-transform: translateY(-100px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.notice {
    background-color: #000;
    transition: all 0.25s;
    height: 100%;
    width: 100%;
    font-size: 28rpx;
    display: flex;
    flex-direction: column;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    z-index: 999999;
    // 顶部 按钮区域
    .header {
        padding: 20rpx 20rpx;
        z-index: -1;
        display: flex;
        justify-content: space-between;
        .back_warp {
            display: flex;
            align-items: center;
            .back_prev {
                margin-right: 32px;
            }
        }
    }
    .show_header {
        animation: fadeinB ease 0.5s;
        z-index: 1;
    }

    // 内部编辑区域
    .body {
        flex: 1;
        // padding: 30px 30px 100px 30px;
        position: relative;
        overflow: hidden;

        .conten {
            transition: all 0.2s;
            // background-color: #fff;
            // width: 750px;
            // height: 1334px;

            transform: scale(0.8);
            position: fixed;
            left: 0;
            right: 0;
            bottom: 0;
            top: 60rpx;
            min-height: 100%;

            // position: relative;
        }
        .conten_bg {
            position: absolute;
            left: 1000px;
            top: 1000px;
        }
    }

    // 底部区域
    .footer {
        color: #fff;
        z-index: 1;
        // background: green;

        .footer_handle_list {
            display: flex;
            .footer_handle_item {
                flex: 1;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                padding: 24px 0;
                cursor: pointer;
                span {
                    padding-top: 10px;
                }
            }
        }
    }

    // 文字替换
    .popup {
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        z-index: 99999;
        background-color: #121212cc;

        .overlay {
            padding: 20px 40px;
            box-sizing: border-box;
            // min-height: 100%;
            display: flex;
            flex-direction: column;
            height: 100%;

            .handle {
                display: flex;
                justify-content: space-between;
                .handle_icon {
                    // padding: 20px;
                    color: #fff;
                    .van-icon {
                        font-size: 48px;
                    }
                }
            }
            .text_warp {
                padding-top: 70px;
                box-sizing: border-box;
                overflow: scroll;
                flex: 1;
                // display: flex;

                .input {
                    box-sizing: border-box;
                    // min-height: 90%;
                    width: 100%;

                    background: none;
                    font-size: 32px;
                    border: none;
                    // resize: none;
                    cursor: pointer;
                    overflow: scroll;
                    :deep(.is-textarea) {
                        background: transparent !important;
                        color: #fff;
                        // height: calc(100vh - 202px);
                    }
                }
            }
        }

        // #ifdef MP-WEIXIN
        .overlay {
            padding-top: 180rpx !important;
        }

        :deep(.uni-easyinput__content) {
            background-color: transparent !important;

            .uni-easyinput__content-textarea {
                color: #fff !important;
            }
        }

        //   #endif
    }

    // 页面加载进度
    .pageLoad {
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        background: #000;
        color: #fff;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 999;
        .pageLoad_animation {
            display: flex;
            flex-direction: column;
            align-items: center;
            .van-circle {
                width: 100px;
                height: 100px;
                .van-circle__text {
                    color: #fff;
                }
            }
            span {
                padding-top: 28px;
            }
        }
    }
}
