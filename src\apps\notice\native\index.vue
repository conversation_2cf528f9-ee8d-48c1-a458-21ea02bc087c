<template>
    <view class="notice">
        <NavBar style="z-index: 99999;" title="海报" />

        <view class="pageLoad" v-if="state.loading">
            <view class="pageLoad_animation">
                <span>正在载入...</span>
            </view>
        </view>
        <view class="header">
            <view class="back_warp">
                <view class="back back_prev" @click="handleRevoke" :style='{
                    color:
                        state.recordList.length ? "#fff" : "rgba(255, 255, 255, 0.4)"
                }'>
                    <uni-icons type="undo" size="20" :color="colorF"></uni-icons>
                    <span class=" back_text">撤销</span>
                </view>
                <view class="back" @click="handleRedo" :style='{
                    color: colorF
                }'>
                    <uni-icons type="redo" size="20" :color="colorF"></uni-icons>
                    <span class="back_text">重做</span>
                </view>
            </view>
            <view class="save_warp">
                <button type="primary" size="mini" round style="background-color:#04B578" @click="handleSave"
                    :loading="state.btnloading">
                    保 存
                </button>
            </view>
        </view>
        <view class="body">
            <MobileEdit :data="state.template.mobile" @TextClick="onTextClick" />
            <RenderPc :data="state.template.pc" id="imagePc" ref="imagePcRef" />
            <RenderMobile :data="state.template.mobile" id="imageMobile" ref="imageMobileRef" />
        </view>
        <view class="popup" v-if="state.showOverlay">
            <view class="overlay" id="overlay">
                <view class="handle">
                    <uni-icons @click='changeShowOverlay("close", false)' type="closeempty" color="#fff"
                        size="20"></uni-icons>
                    <uni-icons @click='changeShowOverlay("success", false)' type="checkmarkempty" color="#fff"
                        size="20"></uni-icons>
                </view>
                <view class="text_warp">
                    <uni-easyinput type="textarea" class="input" :maxlength="-1" :inputBorder="false" autoHeight
                        v-model="state.textareaValue" :trim="true" placeholder="请输入内容"
                        @input="changTextareaValue"></uni-easyinput>
                </view>
            </view>
        </view>
        <!-- 确认删除框 -->
        <yd-popup ref="confirmRef" :titleflag="false" @confirm="dialogConfirm">
            <view style="padding: 33px 0px 10px 0px"> 重新编辑 </view>
            <view style="padding: 33px 0px 10px 0px"> 此操作将会覆盖当前编辑的所有内容 </view>
        </yd-popup>

        <uni-popup ref="saveLoadingRef" type="center" background-color="#000000a3">
            <view class="saveLoading" style="color: #fff;padding: 40rpx;text-align: center;font-size: 12px;">
                <uni-icons type="spinner-cycle" size="30" color="#fff"></uni-icons>
                <view> <text>保存中...</text></view>
            </view>
        </uni-popup>
    </view>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, nextTick } from 'vue'
import NavBar from "../components/navBar.vue"
import MobileEdit from "./edit.vue"
import RenderPc from "./renderPc.vue"
import RenderMobile from "./renderMobile.vue"
import { generatorImage } from "./utils/format.js"
import { sendAppEvent, checkPlatform } from "@/utils/sendAppEvent.js"
const saveLoadingRef = ref(null)
const imagePcRef = ref(null)
const imageMobileRef = ref(null)

const colorF = computed(() => {
    return state.recordList.length ? '#fff' : 'rgba(255, 255, 255, 0.4)'
})
const emit = defineEmits(['EmitMobileEdit']);
const state = reactive({
    currentRate: 10,
    pageLoadNumber: computed(() => state.currentRate.toFixed(0) + "%"),
    showOverlay: false,
    showTemp: false,
    textareaValue: "",
    // 记录操作日志
    _recordList: [],
    recordList: [],
    loading: true,
    template: {},
    details: [],
    btnloading: false,
    tempId: '',
    isTemplate: false
})
const confirmRef = ref(null)
const props = defineProps({
    createTempId: {
        type: String,
        default: ""
    },
    createData: {
        type: Array,
        default: () => []
    }
})


let template_cope = {}
const analysis = (details) => {
    const [mobile, pc] = details.sort((a, b) => a.width - b.width)

    state.details = details?.map((item) => {
        return {
            width: item.width,
            height: item.height,
            coverImg: item.coverImg,
            contentImg: ""
        }
    })
    const mobileJson = mobile.parseJson ? JSON.parse(mobile.parseJson) : {}
    const pcJson = pc.parseJson ? JSON.parse(pc.parseJson) : {}
    const newpc = { ...pcJson, id: pc.id }
    const newmobile = { ...mobileJson, id: mobile.id }
    state.template = {
        pc: newpc,
        mobile: newmobile
    }
    template_cope = JSON.parse(
        JSON.stringify({
            pc: newpc,
            mobile: newmobile
        })
    )
    nextTick(() => {
        state.currentRate += 100
        state.loading = false
    })
}

// 文字修改后value
let newTextareaValue = ref(null)
// 当前选择文字节点index
let currentIndex = {
    parent: 0,
    child: 0,
    id: null
}


// 撤销
const handleRevoke = () => {
    const len = state.recordList.length
    if (len) {
        const { parent, child, value, id } = state.recordList[len - 1]
        newTextareaValue.value = value
        setNewTextareaValue({ parent, child, id }, () => {
            state.recordList.splice(len - 1, 1)
            state._recordList.splice(len - 1, 1)
        }, 'revoke', len - 1)
    }
}
// 重做
const handleRedo = () => {
    if (state.recordList.length) {
        confirmRef.value.open()
    }
}
const dialogConfirm = () => {
    state.template = JSON.parse(JSON.stringify(template_cope))
    confirmRef.value.close()
}
// 文字赋值
const setNewTextareaValue = ({ parent, child, id }, cb, type, idx) => {
    if (newTextareaValue.value != null) {
        state.template.mobile.elements.forEach((i) => {
            if (i.id === id) {
                return (i.value = newTextareaValue.value)
            }
            if (i.children && i.children.length) {
                i.children.forEach((j) => {
                    if (j.id === id) {
                        // 撤销找回之前的
                        if (type == 'revoke') {
                            return (j.value = state._recordList[idx][id])
                        }
                        return (j.value = newTextareaValue.value)
                    }
                })
            }
        })
        state.template.pc.elements.forEach((i) => {
            if (i.id === id) {
                return (i.value = newTextareaValue.value)
            }
            if (i.children && i.children.length) {
                i.children.forEach((j) => {
                    if (j.id === id) {
                        // 撤销找回之前的
                        if (type == 'revoke') {
                            return (j.value = state._recordList[idx][id])
                        }
                        return (j.value = newTextareaValue.value)
                    }
                })
            }
        })
        newTextareaValue.value = null
    }
    cb && cb()
}
const changeShowOverlay = (type, boole, value, index, cindex, id) => {
    state.showOverlay = boole
    if (type == "open") {
        currentIndex = {
            parent: index,
            child: typeof cindex == "number" ? cindex : null,
            id: id
        }
        state._recordList.push({ [id]: value })
        state.textareaValue = value
    }
    if (type == "success") {
        setNewTextareaValue(currentIndex, () => {
            // 记录操作
            state.recordList.push({
                ...currentIndex,
                value: state.textareaValue
            })
        })
    }
}
// 文字点击
const onTextClick = ({ type, boole, value, index, cindex, id }) => {
    changeShowOverlay(type, boole, value, index, cindex, id)
}
// 文字改变
const changTextareaValue = (evt) => {
    newTextareaValue.value = evt
};

const textareaFocus = () => { };

const getTempTitle = async () => {
    let title = null
    await state.template.mobile.elements.forEach((i) => {
        if (i.isTitle) {
            return (title = i.value)
        }
        if (i.children && i.children.length) {
            i.children.forEach((j) => {
                if (j.isTitle) {
                    return (title = j.value)
                }
            })
        }
    })
    return title
}

// 保存
const handleSave = async () => {
    uni.showLoading({
        title: "保存中..."
    })
    //   #ifndef MP-WEIXIN
    saveLoadingRef.value.open()
    //   #endif
    state.btnloading = true
    try {
        let pcFile = null
        let mobileFile = null
        // 处理 H5 环境
        // #ifdef H5
        const domImagePc = document.getElementById("imagePc")
        const domImageMobile = document.getElementById("imageMobile")
        pcFile = await generatorImage(domImagePc, `pc`)
        mobileFile = await generatorImage(domImageMobile, `mobile`)
        // #endif
        // 处理小程序环境
        // #ifdef MP-WEIXIN
        try {
            const _imagePcRef = await imagePcRef.value.weixinMobileCanvas()
            const _imageMobilecRef = await imageMobileRef.value.weixinMobileCanvas()
            pcFile = _imagePcRef
            mobileFile = _imageMobilecRef
            debugger
        } catch (canvasError) {
            console.error('Canvas获取失败:', canvasError)
            uni.showToast({
                icon: "none",
                title: "画布加载失败，请重试"
            })
            state.btnloading = false
            return
        }
        // #endif


        let formData = new FormData()
        formData.append(`file`, pcFile)
        formData.append(`file`, mobileFile)
        state.btnloading = false
        let _data = []
        let _code = 0
        let _message = ''

        const _pcFile = await http._uploadFile("/file/common/upload", null, {
            folderType: "app"
        }, pcFile)

        const _mobileFile = await http._uploadFile("/file/common/upload", null, {
            folderType: "app"
        }, mobileFile)
        _code = _mobileFile.code
        _message = _mobileFile.message
        _data = [..._data, ..._pcFile.data, ..._mobileFile.data]
        let pcParseJson = ''
        let mobileParseJson = ''
        state.details.forEach((item) => {
            let type = "pc"
            if (item.width === 750) {
                type = "mobile"
            }
            item.contentImg = _data.find((item) => item.fileName === `${type}.png`)?.url
            let { document, elements } = state.template[type]
            item.parseJson = JSON.stringify({
                document,
                elements
            })
            item.coverImg = item.contentImg
            if (type === "mobile") {
                mobileParseJson = item.parseJson
            } else {
                pcParseJson = item.parseJson
            }
        })
        saveLoadingRef.value.close()

        if (_code == 0) {
            uni.showToast({
                icon: "none",
                title: "保存成功"
            })
            try {
                const _title = await getTempTitle()
                // 从模版进入的 创建海报
                if (state.isTemplate === "true") {
                    navigateTo({
                        url: `/apps/notice/poster/create`,
                        query: {
                            title: _title,
                            coverImg: state.details[0]?.coverImg || '',
                            contentImg: state.details[0].contentImg || "",
                            pcParseJson,
                            mobileParseJson,
                            isTemplate: true,
                            tempId: state.tempId,
                            identifier: "poster"
                        }
                    })
                    return
                }
                if (props.createTempId) {
                    emit("EmitMobileEdit", state.details)
                } else {
                    sendAppEvent("save", {
                        subs: state.details || [],
                        tempTitle: _title
                    })
                }

            } catch (error) {
                console.error(error)
            }
        } else {
            uni.showToast({
                icon: "none",
                title: _message || "上传失败"
            })
        }
    } catch (error) {
        console.error(error)
        saveLoadingRef.value.close()
        uni.showToast({
            icon: "none",
            title: "保存失败"
        })
        state.btnloading = false
    }
}
onMounted(() => {
    if (props.createTempId) {
        analysis(props.createData)
    } else {
        // 获取模板详情
        http.get(`/cloud/mobile/mess/template/${state.tempId || props.createTempId}`, {}).then((res) => {
            const { details } = res.data
            analysis(details)
        })
            .finally(() => {
                state.loading = false
                state.currentRate = 0
            })
    }

})
onLoad((item) => {
    Object.keys(item).forEach((key) => {
        item[key] = decodeURIComponent(item[key])
    })
    state.tempId = item.tempId || ''
    state.isTemplate = item.isTemplate || false
})
</script>
<style lang="scss" scoped>
@import "./index.scss";

.saveLoading {
    padding: 20rpx;
    color: #fff;
}
</style>