<template>
    <view :style="{
        width: `${data.document?.width}px`,
        height: `${data.document?.height}px`,
        backgroundColor: data.document?.backgroundColor,
        position: 'absolute',
        left: '5000px',
        top: '5000px'
    }" canvas-id="imageMobile">
        <view v-for="item in data.elements" :key="item.id">
            <view v-if="item.type === 'image'" :style="{ ...hasStyleAttributePX(item) }">
                <img :src="item.imageSrc" alt="" style="display: block; width: 100%; height: 100%" />
            </view>
            <view v-else-if="item.type === 'text'" :style="{ ...hasStyleAttributePX(item) }"
                v-html="replaceBr(item.value)"> </view>
            <view v-else-if="item.type === 'group'" :style="{ ...hasStyleAttributePX(item) }">
                <view v-for="citem in item.children" :key="citem.id">
                    <view v-if="citem.type === 'image'" :style="{ ...hasStyleAttributePX(citem) }">
                        <img :src="citem.imageSrc" alt="" style="display: block; width: 100%; height: 100%" />
                    </view>
                    <view v-else :style="{ ...hasStyleAttributePX(citem) }" v-html="replaceBr(citem.value)"> </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script setup>
import { replaceBr, hasStyleAttributePX } from "./utils/format.js"
const props = defineProps({
    data: {
        type: Object,
        default: () => {
            return {
                document: {
                    width: 1024,
                    height: 576,
                    backgroundColor: "#fff"
                },
                elements: []
            }
        }
    }
})

// 改进的canvas查询方式
const getCanvasNode = () => {
    return new Promise((resolve, reject) => {
        const query = uni.createSelectorQuery()
        query.select(`#imageMobile`).fields({
            node: true,
            size: true
        }, (res) => {
            // if (!res || !res.node) {
            //     reject(new Error(`无法获取imageMobile  的canvas节点`))
            //     return
            // }
            resolve(res)
        }).exec()
    })
}
const getDome = async () => {
    await new Promise(resolve => setTimeout(resolve, 800))
    // 确保组件已挂载
    await nextTick()
    try {
        const canvasNode = await getCanvasNode()
        return canvasNode
    } catch (err) {
        console.error('获取canvas节点失败:', err)
        // throw new Error('获取画布失败，请确保画布已正确渲染')
        // 如果第一次失败，再试一次
        await new Promise(resolve => setTimeout(resolve, 1000))
        const canvasNode = await getCanvasNode()
        return canvasNode
    }

}
// 处理小程序环境
const weixinMobileCanvas = async () => {
      try {
    // 确保组件已渲染
    await nextTick()
    
    // 获取canvas节点
    const mobileCanvas = await getCanvasNode()
    if (!mobileCanvas) {
      throw new Error('Canvas节点不存在')
    }
    
    // 设置canvas实际尺寸
    const dpr = uni.getSystemInfoSync().pixelRatio
    mobileCanvas.width = props.data.document?.width * dpr
    mobileCanvas.height = props.data.document?.height * dpr
    
    // 截图
    const tempFilePath = await new Promise((resolve, reject) => {
      uni.canvasToTempFilePath({
        canvas: mobileCanvas,
        canvasId: 'imageMobile', // 明确指定canvasId
        destWidth: mobileCanvas.width,
        destHeight: mobileCanvas.height,
        quality: 1,
        fileType: 'png',
        success: (res) => {
          resolve(res.tempFilePath)
        },
        fail: (err) => {
          reject(err)
        }
      }, instance.proxy)
    })
    
    return await http.tempFilePathToFile("/file/common/upload", tempFilePath, 'mobile.png')
  } catch (err) {
    console.error('截图失败:', err)
    throw err
  }
}

defineExpose({
    weixinMobileCanvas
})
</script>

<style lang="scss" scoped>
@import "./index.scss";
</style>
