// import html2canvas from "html2canvas"
// import html2canvas from "https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"
;(async () => {
    const { default: html2canvas } = await import("https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js")
})()
import { cssArr } from "./cssStyle"

export const replaceBr = (value) => {
    return value ? value.replace(/\n|\r\n/g, "<div></div>") : null
}

export const getTagStyleAttribute = (tab = "div") => {
    let cssArr = []
    // #ifdef MP-WEIXIN
    const divTag = document.createElement(tab)
    const inline = divTag.style
    cssArr = Object.keys(inline)
    // #endif
    cssArr = ["top", "left", "right", "bottom", "width", "height", "fontSize", "paddingTop", "paddingLeft", "paddingRight", "textIndent", "color", "backgroundColor", "border", "borderRadius", "textShadow"]
    return cssArr
    // 返回小程序支持的样式属性列表
}

export const hasStyleAttributePX = (obj) => {
    const suffixArr = ["top", "left", "right", "bottom", "width", "height", "x", "y", "fontSize", "paddingTop", "paddingLeft", "paddingRight", "textIndent"]
    let result = {}
    for (let k in obj) {
        if (suffixArr.includes(k)) {
            if (typeof obj[k] == "number") {
                result[k] = obj[k] + "px"
                continue
            } else {
                result[k] = obj[k]
            }
        }
        if (k == "textShadow") {
            let textShadow = []
            obj.textShadow.forEach((num) => {
                if (typeof num == "number") {
                    textShadow.push(num / 2 + "px")
                } else {
                    textShadow.push(num)
                }
            })
            result.textShadow = textShadow.join(" ")
            continue
        }
        if (k == "borderRadius") {
            let borderRadius = []
            obj.borderRadius.forEach((num) => {
                if (typeof num == "number") {
                    borderRadius.push(num / 2 + "px")
                } else {
                    borderRadius.push(num)
                }
            })
            result.borderRadius = borderRadius.join(" ")
            continue
        }
        if (k == "border") {
            let border = []
            obj.border.forEach((num) => {
                if (typeof num == "number") {
                    border.push(num / 2 + "px")
                } else {
                    border.push(num)
                }
            })
            result.border = border.join(" ")
            continue
        }
        if (cssArr.indexOf(k) !== -1) {
            result[k] = obj[k]
        }
    }
    return result
}

export const hasStyleAttributeRem = (obj) => {
    const suffixArr = ["top", "left", "right", "bottom", "width", "height", "x", "y", "fontSize", "paddingTop", "paddingLeft", "paddingRight", "textIndent"]
    // 小程序环境下使用默认的基准字体大小
    const DEFAULT_FONT_SIZE = 16
    const htmlSize = typeof document !== "undefined" && document.querySelector ? Number.parseFloat(document.querySelector("html").style.fontSize) || DEFAULT_FONT_SIZE : DEFAULT_FONT_SIZE
    let result = {}
    for (let k in obj) {
        if (suffixArr.includes(k)) {
            if (typeof obj[k] == "number") {
                result[k] = obj[k] / htmlSize / 2 + "rem"
                continue
            } else {
                result[k] = obj[k]
            }
        }
        if (k == "textShadow") {
            let textShadow = []
            obj.textShadow.forEach((num) => {
                if (typeof num == "number") {
                    textShadow.push(num / htmlSize / 2 + "rem")
                } else {
                    textShadow.push(num)
                }
            })
            result.textShadow = textShadow.join(" ")
            continue
        }
        if (k == "borderRadius") {
            let borderRadius = []
            obj.borderRadius.forEach((num) => {
                if (typeof num == "number") {
                    borderRadius.push(num / htmlSize / 2 + "rem")
                } else {
                    borderRadius.push(num)
                }
            })
            result.borderRadius = borderRadius.join(" ")
            continue
        }
        if (k == "border") {
            let border = []
            obj.border.forEach((num) => {
                if (typeof num == "number") {
                    border.push(num / htmlSize / 2 + "rem")
                } else {
                    border.push(num)
                }
            })
            result.border = border.join(" ")
            continue
        }
        if (cssArr.indexOf(k) !== -1) {
            result[k] = obj[k]
        }
    }

    // console.log(result);
    return result
}

export const dataURLtoFile = (dataurl, filename) => {
    var arr = dataurl.split(","),
        mime = arr[0].match(/:(.*?);/)[1],
        bstr = atob(arr[1]),
        n = bstr.length,
        u8arr = new Uint8Array(n)

    const suffix = mime.split("/")[1]
    while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
    }
    return new File([u8arr], `${filename}.${suffix}`, { type: mime })
}
function blobToFile(blob, fileName, mimeType) {
    const fileType = mimeType || blob.type || "application/octet-stream"

    return new File([blob], fileName, {
        type: fileType,
        lastModified: Date.now()
    })
}
function dataURLtoBlob(dataurl) {
    let arr = dataurl.split(","),
        mime = arr[0].match(/:(.*?);/)[1],
        bstr = atob(arr[1]),
        n = bstr.length,
        u8arr = new Uint8Array(n)
    while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
    }
    return new Blob([u8arr], { type: mime })
}
export const generatorImage = async (targetDom, name, fileType = "file") => {
    // 小程序环境下直接返回null或抛出错误
    if (typeof html2canvas === "undefined" || typeof document === "undefined") {
        return fileType === "file" ? null : ""
    }
    return new Promise((resolve, reject) => {
        window.scrollTo(0, 0)
        html2canvas(targetDom, {
            logging: true,
            scale: 1,
            allowTaint: false,
            useCORS: true,
            height: targetDom.offsetHeight,
            width: targetDom.offsetWidth,
            windowWidth: document.body.scrollWidth,
            windowHeight: document.body.scrollHeight,
            dpi: window.devicePixelRatio,
            backgroundColor: null
        })
            .then((canvas) => {
                let base64 = canvas.toDataURL("image/png", 1)
                let file = dataURLtoFile(base64, name)
                resolve(fileType == "file" ? file : base64)
            })
            .catch((err) => {
                reject(err)
            })
    })
}
