<template>
    <view class="container">
        <NavBar :title="state.details.title" :id="state.details.id" />
        <view class="handler">
            <view class="handler-text">
                {{ state.details.identityUserName }}发布于
                {{ state.details.timerDate }}
            </view>
            <view class="handler-text" v-if="state.propsForm.type === 'publish'"> <uni-icons type="eye" size="14"></uni-icons> 浏览 {{ state.details.viewUsers }} </view>
        </view>
        <view class="constnt">
            <view class="constnt-box">
                <view class="content-html">
                    <view>来源于：{{ state.details.source }}</view>
                </view>
                <view class="content-html" v-if="state.details.contentImg">
                    <image class="reset-image" :class="{ active: state.details.contentImg }" fit="cover" :src="state.details.contentImg" />
                </view>
                <view class="content-html" v-if="state.urls.length">
                    <template v-for="it in state.urls">
                        <image class="reset-image" :src="it" />
                    </template>
                </view>
                <view class="content-html" v-else v-html="state.details.content"></view>
            </view>
            <uni-list v-if="state.propsForm.type === 'publish'">
                <uni-list-item title="接收人员" showArrow :rightText="state.details.notifyUsersInfo" link />
                <!--  @click="handerNotifyScopes"  -->
                <uni-list-item title="接收设备" :rightText="'班牌'" />
                <uni-list-item title="选择设备" showArrow :rightText="state.details.notifyDevicesInfo || '无'" link @click="onClickDevice" />
                <uni-list-item title="班牌霸屏显示" :rightText="state.details.isDominateScreen ? '开启' : '关闭'" />
                <uni-datetime-picker v-if="!state.details.id" disabled v-model="state.details.datetimerange" type="datetimerange" rangeSeparator="至" />
            </uni-list>
        </view>
        <CreateFooter :options="state.propsForm" v-if="state.propsForm.status"> </CreateFooter>
        <view v-if="!state.propsForm.status && state.propsForm.isApprove != 'approve'" class="create-footer">
            <view class="footer" v-if="state.propsForm.type === 'publish'">
                <!-- <button plain type="primary" :disabled="state.details.isRetract" @click="handerWithdraw">
                            {{ state.details.isRetract ? "已撤回" : "撤回" }}
                        </button> -->
                <button class="btn" plain type="primary" :disabled="state.details.isRetract" @click="editNew">编辑</button>
            </view>
        </view>
    </view>
</template>

<script setup>
import CreateFooter from "../components/createFooter.vue"
import NavBar from "../components/navBar.vue"

// const showPopover = shallowRef(false);
const state = reactive({
    urls: [],
    propsForm: {
        id: "",
        type: "",
        receiveUsers: 0
    },
    details: {
        id: "",
        identityUserName: "",
        timerDate: "",
        viewUsers: 0,
        contentImg: "",
        content: "",
        notifyUsersInfo: "",
        viewUsers: 0,
        status: 0,
        isDominateScreen: false,
        datetimerange: []
    }
})
// const isDisabled = shallowRef(false);
// 新闻详情
const getDetails = () => {
    const { id, type, contentType, messType } = state.propsForm
    const params = { id }
    // 收到
    let url = "/app/mobile/mess/receive/info"
    // 发布
    if (type === "publish") {
        url = "/app/mobile/mess/publish/getInfo"
        params.contentType = contentType
        params.messType = messType
    }
    http.post(url, params).then(({ data }) => {
        state.details = data
        state.details.datetimerange = [data.dominateStartTime, data.dominateEndTime]
    })
}
// 撤销
const handerWithdraw = () => {
    const { id } = state.details
    http.post("/app/mobile/mess/publish/updateRetract", { id }).then(({ message }) => {
        uni.showToast({ title: message, icon: "none" })
        getDetails()
    })
}

// 编辑新闻
const editNew = () => {
    const { id, contentType, messType } = state.details
    navigateTo({
        url: `/apps/notice/news/create`,
        query: { id, contentType, messType }
    })
}
// // 通知范围
// const handerNotifyScopes = () => {
//     const { id } = state.details;
//     navigateTo({
//         url: '/apps/notice/components/notificationScope',
//         query: {
//             id
//         }
//     })
// }
// 班牌
const onClickDevice = () => {
    const { id } = state.details
    navigateTo({
        url: "/apps/notice/components/deviceList",
        query: {
            id
        }
    })
}

// 确认
const getReceivedConfirm = () => {
    const { receiveId } = state.details
    http.post("/cloud/mobile/mess/receive/updateIncrementconfirms", { receiveId }).then(({ message }) => {
        uni.showToast({ title: message, icon: "none" })
        getDetails()
    })
}
// 删除我发布的
// const handlerDelete = () => {
//     uni.showModal({
//         title: "提示",
//         content: "您确定要删除该条信息吗？",
//         confirmColor: "var(--primary-color)",
//         success(res) {
//             if (res.confirm) {
//                 const { id } = state.details;
//                 http.post("/app/mobile/mess/publish/delete", { id }).then(({ message }) => {
//                     uni.showToast({ title: message, icon: "none" })
//                     routerBack()
//                 });
//             }
//         }
//     })
// }
onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    state.propsForm = options
    if (options.status) {
        // status 是从审批列表过来的
        state.propsForm.type = "publish"
    }
    getDetails()
})
</script>

<style lang="scss" scoped>
.container {
    height: 100vh;
    background: $uni-bg-color-grey;

    .handler {
        display: flex;
        justify-content: space-between;
        padding: 0 30rpx;

        .handler-text {
            font-size: 24rpx;
            color: #666666;
            padding: 10rpx 0;
        }
    }

    .constnt {
        height: calc(100vh - 280rpx);
        overflow: hidden auto;

        .constnt-box {
            background-color: var(--primary-bg-color);
            margin: 20rpx 0 30rpx;
            box-shadow: 0rpx 8rpx 8rpx 0rpx var(--primary-bg-color);
            border-radius: 20rpx;
            padding: 20rpx;

            .content-html {
                font-size: 26rpx;
                margin-bottom: 30rpx;
                line-height: 40rpx;
                padding: 4rpx 0;

                .reset-image,
                :deep(img) {
                    width: 100% !important;
                    height: auto;

                    &.active {
                        height: 1334rpx;
                    }
                }
            }
        }
    }

    .footer {
        display: flex;
        justify-content: space-between;
        padding: 20rpx;
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: $uni-text-color-inverse;

        .btn {
            flex: 1;
            font-size: 30rpx;

            &[type="primary"] {
                background: var(--primary-color);
                color: $uni-text-color-inverse;
                border-color: var(--primary-color);
            }

            &[disabled][type="primary"] {
                background: #00b78185;
                border-color: #d5cdcd;
                color: #d5cdcd;
            }
        }

        .butn-primary {
            background: var(--primary-color);
            color: $uni-text-color-inverse;
            border-color: var(--primary-color);
        }
    }
}
.create-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: $uni-text-color-inverse;
    padding: 20rpx;
    .btn {
        flex: 1;
        margin: 0 10rpx;
    }
}
</style>
