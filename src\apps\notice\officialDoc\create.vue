<template>
    <view class="container">
        <NavBar title="公文传阅" />
        <IsTemplage />
        <view class="create">
            <input class="uni-input" v-model.trim="state.form.title" maxlength="50" placeholder="请输入标题（0/50）" />
            <!-- <view v-if="state.form.details.length" :style="{ height: state.form.details[0].height + 'rpx', padding: '0 20rpx' }">
                <image :style="{ width: '100%', height: '100%' }" :src="state.form.details[0].contentImg"></image>
            </view> -->
            <view v-if="state.form.details.length" :style="{ height: state.form.details[0].height + 'rpx', padding: '0 20rpx' }">
                <MobileEdit v-if="state.isEdit" :createData="state.form.details" :createTempId="state.tempId" @EmitMobileEdit="emitMobileEdit" />
                <image @click="state.isEdit = true" :style="{ width: '100%', height: '100%' }" :src="state.form.details[0].contentImg" />
            </view>
            <template v-else>
                <!-- #ifndef MP-WEIXIN  -->
                <Editor v-model:valueHtml="state.form.content" />
                <!-- #endif -->

                <!-- #ifdef MP-WEIXIN -->
                <weixin-editor v-model:valueHtml="valueHtml" @editValueHtml="editValueHtml" />
                <!-- #endif -->
            </template>
            <uni-list-item title="附件">
                <template v-slot:footer>
                    <view class="file-picker files-pickers">
                        <!-- #ifdef MP-WEIXIN -->
                        <view v-show="state.form.attachments.length < 3" @click="chooseFileForMP">
                            <image style="width: 20px; height: 20px" src="@nginx/workbench/notice/fileIcon.png"></image>
                        </view>
                        <!-- #endif -->

                        <!-- #ifndef MP-WEIXIN -->
                        <uni-file-picker v-show="state.form.attachments.length < 3" ref="files" :limit="3" file-mediatype="all" :auto-upload="false" @select="fileSelect" @delete="fileDelete" v-model="state.form.attachments">
                            <image style="width: 20px; height: 20px" src="@nginx/workbench/notice/fileIcon.png"></image>
                        </uni-file-picker>
                        <!-- #endif -->
                    </view>
                </template>
            </uni-list-item>

            <uni-list-item v-for="(item, index) in state.form.attachments" :key="index" :title="item.name || ''">
                <template v-slot:footer>
                    <uni-icons type="trash" size="20" style="color: red" @click="fileDelete(item)"></uni-icons>
                </template>
            </uni-list-item>
            <uni-list-item title="传阅人" showArrow :rightText="notifyUsers" link @click="handerNotifyScopes" />

            <view class="uni-list-cell">
                <view class="uni-list-cell-db">是否置顶</view>
                <switch color="#00b781" @change="switchTopChange" :checked="state.form.isTop" style="transform: scale(0.8)" />
            </view>
            <uni-list-item title="定时发布">
                <template v-slot:footer>
                    <view class="datetime-picker">
                        <uni-datetime-picker :border="false" type="datetime" v-model="state.form.timerDate" />
                    </view>
                </template>
            </uni-list-item>
        </view>
        <CreateFooter :forms="state.form" :options="state.propsForm" />
        <yd-selector ref="selectorRef" @confirm="confirmFn" />
        <!-- <yd-tinymce /> -->
    </view>
</template>

<script setup>
import NavBar from "../components/navBar.vue"
import IsTemplage from "../components/isTemplage.vue"
import CreateFooter from "../components/createFooter.vue"
import Editor from "../components/editor.vue"
import weixinEditor from "@/apps/notice/components/weixinEditor.vue"
const valueHtml = ref("")
const selectorRef = ref(null)
const state = reactive({
    propsForm: {},
    form: {
        title: "",
        content: "",
        identifier: "officialDoc",
        contentType: 0,
        attachments: [],
        notifyUsersInfo: "",
        officicalDocTeachers: [],
        isTop: false, // 是否置顶
        timerDate: "", // 定时发布
        officicalDocElterns: [],
        details: []
    },
    isEdit: false,
    tempId: ""
})

// 编辑模版后
const emitMobileEdit = (item) => {
    state.form.details = item
    state.isEdit = false
}
const notifyUsers = computed(() => {
    if (state.form.officicalDocTeachers.length || state.form.officicalDocElterns.length) {
        return `教职工${state.form.officicalDocTeachers.length}人、家长${state.form.officicalDocElterns.length}人`
    }
    return ""
})

// 是否置顶
const switchTopChange = (e) => {
    state.form.isTop = e.detail.value
}

// 通知范围
function handerNotifyScopes() {
    console.log("nihao")

    const typeList = [
        {
            type: "people_dept",
            name: "老师",
            selectLevel: "people_dept"
        },
        {
            type: "parent",
            name: "家长",
            selectLevel: "parent"
        }
    ]
    selectorRef.value?.open(typeList, true)
}
// 上传附件
const fileSelect = (file) => {
    if (file.tempFiles && file.tempFiles.length) {
        file.tempFiles.forEach((i) => {
            const { path, name, uuid, size } = i
            http.uploadFile("/file/common/upload", path, { folderType: "app" }).then((url) => {
                const params = {
                    attachment: url,
                    name: name || path.substring(path.lastIndexOf("/") + 1),
                    uuid: uuid + size
                }
                state.form.attachments.push(params)
            })
        })
    }
}

// 小程序文件选择方法
const chooseFileForMP = () => {
    // 使用uni.chooseMessageFile兼容小程序 从聊天记录中选择文件。
    uni.showActionSheet({
        itemList: ["从相册选择图片", "从聊天记录选择文件"],
        success: (res) => {
            if (res.tapIndex === 0) {
                // 选择图片
                uni.chooseImage({
                    count: 3 - state.form.attachments.length,
                    success: (res) => {
                        const tempFiles = res.tempFiles.map((file) => ({
                            path: file.path,
                            name: `image_${Date.now()}.jpg`,
                            size: file.size,
                            uuid: file.path + file.size
                        }))
                        fileSelect({ tempFiles })
                    }
                })
            } else if (res.tapIndex === 1) {
                // 从聊天记录选择
                uni.chooseMessageFile({
                    count: 3 - state.form.attachments.length,
                    success: (res) => {
                        const tempFiles = res.tempFiles.map((file) => ({
                            path: file.path,
                            name: file.name,
                            size: file.size,
                            uuid: file.name + file.size
                        }))
                        fileSelect({ tempFiles })
                    }
                })
            }
        }
    })
}
watch(
    () => state.form.timerDate,
    (val) => {
        state.form.isTimer = !!val.length
    }
)

// 删除附件
const fileDelete = (file) => {
    const uuid = file.uuid || file.tempFile.uuid
    state.form.attachments = state.form.attachments.filter((item) => item.uuid !== uuid)
}

function confirmFn(ids, selected) {
    state.form.notifyUsersInfo = selected.map((i) => i.name)?.join("、")
    state.form.officicalDocElterns = [] // 班级
    state.form.officicalDocTeachers = [] // 部门
    selected.forEach((item) => {
        if (item.typeValue === "people_dept") {
            state.form.officicalDocTeachers.push({
                id: item.id,
                userId: item.id,
                name: item.name,
                pid: item.pid,
                deptVOList: [
                    {
                        deptId: item.pid || "",
                        name: item.pName || ""
                    }
                ]
            })
        } else {
            const obj = { ...item, student: [] }
            delete obj["student"]
            item.student.elterns = [obj]
            state.form.officicalDocElterns.push(item.student)
        }
    })
    console.log(ids, selected, state.form, "选择的id和id对象")
}

const editValueHtml = (item) => {
    state.form.content = item
}
onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    state.propsForm = options
    const { title = "", coverImg = "", contentImg = "", id = "", pcParseJson, mobileParseJson, tempId } = options
    // 这是通过模版进入的
    state.form.details = []
    if (coverImg || contentImg) {
        state.tempId = tempId
        state.form.contentType = 1
        state.form.title = title
        state.form.details = [
            {
                contentImg: coverImg || contentImg,
                coverImg: coverImg || contentImg,
                height: 1334,
                parseJson: mobileParseJson,
                width: 750
            },
            {
                contentImg: coverImg || contentImg,
                coverImg: coverImg || contentImg,
                height: 576,
                parseJson: pcParseJson,
                width: 1024
            }
        ]
    }
})
</script>

<style lang="scss" scoped>
.container {
    height: calc(100vh - 130rpx);
    background-color: #f9faf9;

    .create {
        margin: 10rpx 0;
        padding-bottom: 174rpx;

        .uni-list-cell,
        .uni-input {
            padding: 15rpx 0;
            // #ifdef MP-WEIXIN
            padding: 15rpx;
            // #endif
            background-color: $uni-text-color-inverse;
            margin: 1rpx 0;
            text-indent: 20rpx;
            color: #3b4144;
        }

        .uni-list-cell {
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-top: 1rpx solid #eee;
            padding-left: 15rpx;

            .uni-list-cell-db {
                text-indent: 14rpx;
                font-size: 28rpx;
            }
        }

        .uni-input {
            padding: 15rpx 0;
            background-color: $uni-text-color-inverse;
            margin: 1rpx 0;
            text-indent: 20rpx;
        }

        // 附件
        .file-picker {
            text-align: right;

            :deep(.uni-icons) {
                display: none;
            }
        }

        .datetime-picker {
            flex: 1;
            text-align: right;

            :deep(.icon-calendar) {
                display: none;
            }
            :deep(.uni-date__x-input) {
                height: 48rpx;
                line-height: 48rpx;
            }

            :deep(.uni-date-x) {
                justify-content: flex-end !important;
            }
        }

        .files-pickers {
            flex: 1;

            :deep(.uni-file-picker__lists) {
                display: none;
            }
        }
    }

    :deep(.uni-list-item__extra) {
        width: 251rpx;
    }
}
</style>

<style lang="scss">
.container {
    .uni-navbar--fixed {
        z-index: 89 !important;
    }
}
</style>
