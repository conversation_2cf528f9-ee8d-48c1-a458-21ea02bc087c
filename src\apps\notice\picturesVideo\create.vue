<template>
    <view class="container">
        <NavBar title="图片/视频" />
        <IsTemplage />
        <view class="create">
            <input class="uni-input" v-model.trim="state.form.title" maxlength="50" placeholder="请输入标题（0/50）" />
            <!-- <view v-if="state.form.details.length" :style="{ height: state.form.details[0].height + 'rpx', padding: '0 20rpx' }">
                <image :style="{ width: '100%', height: '100%' }" :src="state.form.details[0].contentImg"></image>
            </view> -->
            <view v-if="state.form.details.length" :style="{ height: state.form.details[0].height + 'rpx', padding: '0 20rpx' }">
                <MobileEdit v-if="state.isEdit" :createData="state.form.details" :createTempId="state.tempId" @EmitMobileEdit="emitMobileEdit" />
                <image @click="state.isEdit = true" :style="{ width: '100%', height: '100%' }" :src="state.form.details[0].contentImg" />
            </view>
            <view v-else class="create-img">
                <view class="video-img">
                    <view class="cover-img" v-for="(item, idx) in state.form.aioMachineContent.urls" :key="idx">
                        <uni-icons class="cover-img-delete" type="clear" size="20" color="#00000080" @click="selectDeleteImage(idx)"></uni-icons>
                        <image class="reset-img" v-if="getFileType(item) == 'image'" :src="item" alt="" mode="scaleToFill" />
                        <video class="reset-img" :src="item" v-else></video>
                    </view>
                    <view class="cover-img">
                        <view class="file-picker-image" @click="selectUploadImage">
                            <view class="picker-btn">
                                <view class="icons">
                                    <uni-icons type="plusempty" size="30" color="#999"></uni-icons>
                                </view>
                                <view class="icons-text">点击上传</view>
                            </view>
                        </view>
                    </view>
                    <view class="cover-img" v-if="state.form.aioMachineContent.urls.length && state.form.aioMachineContent.urls.length % 2 === 0" />
                </view>
            </view>
            <uni-list-item title="显示设置" showArrow :rightText="state.showTypeName" link @click="showSetRef.open('bottom')" />
            <uni-list-item title="图片轮播时间" showArrow :rightText="`${state.form.aioMachineContent.showSecond ? state.form.aioMachineContent.showSecond + '秒' : ''} `" link @click="rotationTimeRef.open('center')" />

            <uni-list-item title="发布方式" showArrow :rightText="state.modeName" link @click="releaseModeRef.open('bottom')" />

            <uni-datetime-picker v-if="state.form.aioMachineContent.mode == 3" v-model="state.datetimerange" type="datetimerange" rangeSeparator="至" @change="changeTime" />

            <uni-list-item title="选择画屏" showArrow :rightText="state.paintingScreen" link @click="handerJeHaiji('paintingScreen')">
                <template v-slot:footer>
                    <text class="item_right_text ellipsis">
                        {{ state.paintingScreen }}
                    </text>
                </template>
            </uni-list-item>

            <uni-list-item title="选择一体机" showArrow :rightText="state.integratedMachine" link @click="handerJeHaiji('integratedMachine')">
                <template v-slot:footer>
                    <text class="item_right_text ellipsis">
                        {{ state.integratedMachine }}
                    </text>
                </template>
            </uni-list-item>
        </view>
        <!-- <view class="footer">
            <button type="primary" @click="handerSubmit">
                发布
            </button>
        </view> -->
        <CreateFooter :forms="state.form" :options="state.propsForm" />
        <!-- <yd-tinymce /> -->

        <uni-popup ref="showSetRef" background-color="#fff" borderRadius="10px 10px 0 0">
            <view class="show-set-popup-content">
                <view class="handler">
                    <i />
                    <view class="t_left"> 显示设置 </view>
                    <uni-icons type="closeempty" size="20" color="#9D9D9D" @click="showSetRef.close()"></uni-icons>
                </view>
                <view class="content">
                    <radio-group @change="showSetChange">
                        <label class="uni-list-cell" v-for="item in showSetList" :key="item.key">
                            <view>{{ item.text }}</view>
                            <view>
                                <radio color="#00b781" :value="String(item.key)" :checked="item.key == state.form.aioMachineContent.showType" />
                            </view>
                        </label>
                    </radio-group>
                </view>
            </view>
        </uni-popup>

        <uni-popup ref="rotationTimeRef" type="dialog">
            <uni-popup-dialog class="rotationTimedialog" mode="input" title="图片轮播时间" placeholder="请输入" confirmText="提交" :maxlength="3" @confirm="dialogInputConfirm" @close="rotationTimeRef.close()"> </uni-popup-dialog>
        </uni-popup>

        <uni-popup ref="releaseModeRef" background-color="#fff" borderRadius="10px 10px 0 0">
            <view class="show-set-popup-content">
                <view class="handler">
                    <i />
                    <view class="t_left"> 发布方式 </view>
                    <uni-icons type="closeempty" size="20" color="#9D9D9D"></uni-icons>
                </view>
                <view class="content">
                    <radio-group @change="releaseModeChange">
                        <label class="uni-list-cell" v-for="item in releaseMode" :key="item.key">
                            <view>{{ item.text }}</view>
                            <view>
                                <radio color="#00b781" :value="String(item.key)" :checked="item.key == state.form.aioMachineContent.mode" />
                            </view>
                        </label>
                    </radio-group>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script setup>
import NavBar from "../components/navBar.vue"
import IsTemplage from "../components/isTemplage.vue"
import CreateFooter from "../components/createFooter.vue"

const showSetRef = ref(false)
const releaseModeRef = ref(false)
const rotationTimeRef = ref(false)
const showSetList = [
    { text: "适应边框", key: 1 },
    { text: "占满屏幕（拉伸比例）", key: 2 },
    { text: "占满屏幕（裁剪）", key: 3 }
]
const releaseMode = [
    { text: "插入发布", key: 1 },
    { text: "覆盖发布", key: 2 },
    { text: "临时发布", key: 3 }
]

const state = reactive({
    swiperDotIndex: 0,
    showTypeName: "",
    modeName: "",
    paintingScreen: "",
    integratedMachine: "",
    datetimerange: [],
    form: {
        title: "",
        deviceList: [], // 画屏
        aioMachineContent: {
            showType: 0, //  1: 适应边框 2: 占满屏幕（拉伸比例） 3: 占满屏幕（裁剪）
            showSecond: 0, // 轮播时间
            mode: 0, // 1:
            dominateEndTime: "", // 临时发布开始时间
            dominateStartTime: "", // 临时发布结束时间
            urls: []
        }, // 上传的视频图片
        contentType: 0,
        identifier: "aio_machine",
        details: []
    },
    propsForm: {
        id: "",
        contentType: 0,
        messType: 8
    },
    isEdit: false,
    tempId: ""
})

// 编辑模版后
const emitMobileEdit = (item) => {
    state.form.details = item
    state.isEdit = false
}
function getFileType(url) {
    // 使用正则表达式获取文件扩展名
    const extension = url.split(/[#?]/)[0].split(".").pop().toLowerCase()

    const imageTypes = ["jpg", "jpeg", "png", "gif", "bmp", "svg"]
    const videoTypes = ["mp4", "avi", "mov", "mkv", "flv", "webm"]

    if (imageTypes.includes(extension)) {
        return "image"
    } else if (videoTypes.includes(extension)) {
        return "video"
    } else {
        return "unknown"
    }
}

// 上传封面
const selectUploadImage = () => {
    uni.chooseFile({
        count: 1,
        success: (res) => {
            // 上传图片
            res.tempFiles?.forEach((v, index) => {
                const isFlag = getFileType(v.name)
                if (["image", "video"].includes(isFlag)) {
                    http.uploadFile("/file/common/upload", v.path, { folderType: "app" })
                        .then((url) => {
                            state.form.aioMachineContent.urls.push(url)
                        })
                        .catch(() => {
                            uni.hideLoading()
                        })
                        .finally(() => {
                            uni.hideLoading()
                        })
                } else {
                    uni.showToast({
                        title: "请选择图片或视频！",
                        icon: "none"
                    })
                }
            })
        }
    })
}

// 删除封面
const selectDeleteImage = (idx) => {
    state.form.aioMachineContent.urls.splice(idx, 1)
}

// 递归函数 获取最后一级后返回
const getLast = (arr, last) => {
    arr.forEach((item) => {
        if (item.children.length) {
            getLast(item.children, last)
        } else {
            last.push(item)
        }
    })
}

// 选择班牌时间
const changeTime = (e) => {
    state.form.aioMachineContent.dominateStartTime = e[0] || ""
    state.form.aioMachineContent.dominateEndTime = e[1] || ""
}
const handerJeHaiji = (name) => {
    navigateTo({
        url: "/apps/notice/components/selectMember/banPai",
        query: {
            treeType: {
                paintingScreen: 11, // 画屏
                integratedMachine: 5 // 一体机
            }[name]
        },
        events: {
            selectMember: (data) => {
                state[name] = data.treeSubmitListName
                let deviceList = []
                getLast(data.treeSubmitList, deviceList)
                if (name === "integratedMachine") {
                    state.form.brandIds = deviceList.map((item) => item.id)
                } else {
                    state.form.deviceList = deviceList.map((item) => {
                        return {
                            brandId: item.id,
                            deviceType: 11
                        }
                    })
                }
            }
        },
        success: (res) => {
            res.eventChannel.emit("feedbackSelected", { selectedData: [] })
        }
    })
}

const dialogInputConfirm = (e) => {
    state.form.aioMachineContent.showSecond = e
}

const showSetChange = (e) => {
    state.form.aioMachineContent.showType = e.detail.value
    state.showTypeName =
        showSetList.filter((item) => {
            return item.key == e.detail.value
        })[0].text || ""
    showSetRef.value.close()
}
const releaseModeChange = (e) => {
    state.form.aioMachineContent.mode = e.detail.value
    state.modeName =
        releaseMode.filter((item) => {
            return item.key == e.detail.value
        })[0].text || ""
    releaseModeRef.value.close()
}
const getDetails = () => {
    http.post("/app/mobile/mess/publish/getInfo", state.propsForm).then(({ data }) => {
        const { showType, mode, dominateStartTime, dominateEndTime } = data.aioMachineContent
        state.form = data
        state.form.brandIds = []
        state.form.id = ""
        state.form.isRetract = false

        state.form.deviceList = []
        state.form.aioMachineContent.showType = showType
        state.showTypeName =
            showSetList.filter((item) => {
                return item.key == showType
            })[0].text || ""
        state.form.aioMachineContent.mode = mode
        state.modeName =
            releaseMode.filter((item) => {
                return item.key == mode
            })[0].text || ""
        state.datetimerange = [data.dominateStartTime, data.dominateEndTime]
        state.form.aioMachineContent.dominateStartTime = data.dominateStartTime
        state.form.aioMachineContent.dominateEndTime = data.dominateEndTime
    })
}

onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    state.propsForm = options
    const { title = "", coverImg = "", contentImg = "", id = "", pcParseJson, mobileParseJson, tempId } = options
    id && getDetails()
    // 这是通过模版进入的
    state.form.details = []
    if (coverImg || contentImg) {
        state.tempId = tempId
        state.form.contentType = 1
        state.form.title = title
        state.form.details = [
            {
                contentImg: coverImg || contentImg,
                coverImg: coverImg || contentImg,
                height: 1334,
                parseJson: mobileParseJson,
                width: 750
            },
            {
                contentImg: coverImg || contentImg,
                coverImg: coverImg || contentImg,
                height: 576,
                parseJson: pcParseJson,
                width: 1024
            }
        ]
    }
})
</script>

<style lang="scss" scoped>
.container {
    height: calc(100vh - 130rpx);
    background-color: #f9faf9;

    .create {
        padding: 10rpx 0;
        background-color: $uni-text-color-inverse;
        // padding-bottom: 100rpx;

        .uni-input {
            padding: 15rpx 0;
            background-color: $uni-text-color-inverse;
            margin: 1rpx 0;
            text-indent: 20rpx;
        }

        .create-img {
            border: 1px dashed $uni-border-color;
            margin: 25rpx;
            border-radius: 12rpx;
            background-color: #f9faf9;

            .video-img {
                display: flex;
                justify-content: space-around;
                flex-wrap: wrap;

                .cover-img {
                    width: 300rpx;
                    height: 160rpx;
                    overflow: hidden;
                    border-radius: 20rpx;
                    margin: 20rpx 0;
                    position: relative;

                    .reset-img {
                        width: 100%;
                        height: 100%;
                    }

                    .cover-img-delete {
                        position: absolute;
                        z-index: 9999;
                        top: 0;
                        right: 0;
                    }
                }

                .file-picker-image {
                    .picker-btn {
                        text-align: center;
                        width: 290rpx;
                        height: 150rpx;
                        border: 1px dashed $uni-border-color;

                        .icons {
                            margin-top: 20rpx;
                        }

                        .icons-text {
                            color: $uni-text-color-grey;
                        }
                    }

                    :deep(.uni-file-picker__lists.is-text-box) {
                        display: none;
                    }
                }
            }
        }
    }

    .show-set-popup-content {
        .handler {
            padding: 20rpx;
            display: flex;
            justify-content: space-between;
        }

        .content {
            .uni-list-cell {
                padding: 25rpx;
                font-size: 28rpx;
                display: flex;
                justify-content: space-between;
                border-bottom: 1rpx solid $uni-border-color;
            }
        }
    }

    .rotationTimedialog {
        :deep(.uni-dialog-title-text) {
            color: $uni-text-color;
        }

        :deep(.uni-input-wrapper) {
            position: relative;

            &::after {
                content: "秒";
                position: absolute;
                right: 0;
                top: 22rpx;
            }
        }

        :deep(.uni-button-color) {
            color: var(--primary-color) !important;
        }
    }
}

:deep(.uni-list-item__content) {
    flex: none;
	 max-width: 80%;
    word-break: break-all;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

:deep(.uni-list-item__extra) {
    flex: 1;
}

.item_right_text {
    font-weight: 400;
    font-size: 28rpx;
    color: #999999;
    line-height: 40rpx;
    text-align: right;
    flex: 1;
}

.create-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: $uni-text-color-inverse;
    padding: 20rpx;
}
</style>
