<template>
    <view class="container">
        <NavBar title="海报" />
        <IsTemplage />
        <view class="create">
            <input class="uni-input" v-model.trim="state.form.title" maxlength="50" placeholder="请输入标题（0/50）" />
            <view v-if="state.form.details.length" :style="{ height: state.form.details[0].height + 'rpx', padding: '0 20rpx' }">
                <MobileEdit v-if="state.isEdit" :createData="state.form.details" :createTempId="state.tempId" @EmitMobileEdit="emitMobileEdit" />
                <image @click="state.isEdit = true" :style="{ width: '100%', height: '100%' }" :src="state.form.details[0].contentImg" />
            </view>

            <view class="create-img" v-else>
                <view class="cover-img" v-if="state.form.contentImg">
                    <uni-icons class="cover-img-delete" type="clear" size="20" color="#00000080" @click="state.form.contentImg = ''"></uni-icons>
                    <image class="reset-img" :src="state.form.contentImg" alt="" />
                </view>

                <uni-file-picker v-else class="file-picker-image" limit="1" :imageStyles="imageStyles" @select="selectUploadImage"></uni-file-picker>
            </view>

            <uni-list-item title="接收人员" showArrow :rightText="state.form.notifyUsersInfo" link @click="onClickPersonnel" />

            <uni-list-item title="定时发布">
                <template v-slot:footer>
                    <view class="datetime-picker">
                        <uni-datetime-picker :border="false" type="datetime" v-model="state.form.timerDate" />
                    </view>
                </template>
            </uni-list-item>
            <uni-list-item title="接收设备" showArrow :rightText="state.form.receiver" link @click="onClickDevice" />
            <uni-list-item title="选择班牌" v-if="state.form.receiver" showArrow :rightText="state.form.notifyDevicesInfo" link @click="handerBanPai" :ellipsis="1">
                <template v-slot:footer>
                    <text class="item_right_text ellipsis">
                        {{ state.form.notifyDevicesInfo }}
                    </text>
                </template>
            </uni-list-item>

            <uni-list-item title="选择借还机" showArrow :rightText="state.deviceInfo" link @click="handerJeHaiji" :ellipsis="1">
                <template v-slot:footer>
                    <text class="item_right_text ellipsis">
                        {{ state.deviceInfo }}
                    </text>
                </template>
            </uni-list-item>

            <view class="uni-list-cell" v-if="state.form.notifyDevicesInfo">
                <view class="uni-list-cell-db">班牌霸屏显示</view>
                <switch color="var(--primary-color)" @change="switchScreenChange" :checked="state.form.isDominateScreen" style="transform: scale(0.8)" />
            </view>

            <uni-datetime-picker v-if="state.form.isDominateScreen" v-model="state.datetimerange" type="datetimerange" rangeSeparator="至" @change="changeTime" />
        </view>
        <CreateFooter :forms="state.form" :options="state.propsForm" />
        <!-- <yd-tinymce /> -->
    </view>
</template>

<script setup>
import { reactive } from "vue"
import NavBar from "../components/navBar.vue"
import IsTemplage from "../components/isTemplage.vue"
import CreateFooter from "../components/createFooter.vue"
// 编辑模版
// import MobileEdit from "../native/index.vue";
const isEditF = computed(() => {
    return (item) => {
        const { contentType, details } = item
        if (contentType == 1 && details.length && details[0]?.parseJson) {
            return false
        }

        return true
    }
})
// 编辑模版
const imageStyles = {
    // width: 80,
    // height: 80,
}
const state = reactive({
    selectedRecipient: [],
    form: {
        title: "",
        identifier: "poster",
        contentType: 0,
        contentImg: "", // 封面图
        depts: [], // 部门id
        notifyUsersInfo: "", // 接收人员name
        isTop: false, // 是否置顶
        isNeedConfirm: false, // 是否需要确认
        isDominateScreen: false, // 是否霸屏
        receiver: "",
        notifyDevicesInfo: "", // 班牌name
        brandIds: [], // 班牌id
        dominateEndTime: "", // 结束时间
        dominateStartTime: "", // 开始时间
        deviceList: [], // 设备id
        attachments: [], // 附件
        deviceTypes: [], // 设备类型
        timerDate: "", // 定时发布
        details: []
    },
    isEdit: false,
    tempId: ""
})

// 编辑模版后
const emitMobileEdit = (item) => {
    state.form.details = item
    state.isEdit = false
}
// 上传封面
const selectUploadImage = (e) => {
    // 上传图片
    e.tempFiles?.forEach(async (v, index) => {
        await http
            .uploadFile("/file/common/upload", v.path, { folderType: "app" })
            .then((url) => {
                state.form.contentImg = url
            })
            .catch(() => {
                uni.hideLoading()
            })
            .finally(() => {
                uni.hideLoading()
            })
    })
}
// 接收人员
const onClickPersonnel = () => {
    navigateTo({
        url: "/apps/notice/components/receivingpersonnel",
        query: { selectedRecipient: state.selectedRecipient },
        events: {
            selectMember: (data) => {
                state.form.notifyUsersInfo = ""
                state.selectedRecipient = []
                if (data.length) {
                    const names = []
                    data.forEach((v) => {
                        if (v.key == "1") {
                            state.form.isCheckedEltern = v.checked
                        }
                        if (v.key == "2") {
                            state.form.isCheckedEmployee = v.checked
                        }
                        if (v.checked) {
                            names.push(v.name)
                            state.selectedRecipient.push(v.key)
                        }
                    })
                    state.form.notifyUsersInfo = names.join("、")
                }
            }
        }
    })
}
// 接收设备
const onClickDevice = () => {
    navigateTo({
        url: "/apps/notice/components/optionDevices",
        events: {
            selectMember: (data) => {
                state.form.receiver = data.name
            }
        },
        success: (res) => {
            res.eventChannel.emit("feedbackSelected", { selectedData: [] })
        }
    })
}
// 递归函数 获取最后一级后返回
const getLast = (arr, last) => {
    arr.forEach((item) => {
        if (item.children.length) {
            getLast(item.children, last)
        } else {
            last.push(item)
        }
    })
}
// 选择班牌
const handerBanPai = () => {
    navigateTo({
        url: "/apps/notice/components/selectMember/banPai",
        query: {
            treeType: 2
        },
        events: {
            selectMember: (data) => {
                state.form.notifyDevicesInfo = data.treeSubmitListName
                let brandIds = []
                getLast(data.treeSubmitList, brandIds)
                state.form.brandIds = brandIds.map((item) => item.id)
            }
        },
        success: (res) => {
            res.eventChannel.emit("feedbackSelected", { selectedData: [] })
        }
    })
}

// 选择借还机
const handerJeHaiji = () => {
    navigateTo({
        url: "/apps/notice/components/selectMember/banPai",
        query: {
            treeType: 6
        },
        events: {
            selectMember: (data) => {
                state.deviceInfo = data.treeSubmitListName
                let deviceList = []
                getLast(data.treeSubmitList, deviceList)
                state.form.deviceList = deviceList.map((item) => {
                    return {
                        brandId: item.id,
                        deviceType: 6
                    }
                })
            }
        },
        success: (res) => {
            res.eventChannel.emit("feedbackSelected", { selectedData: [] })
        }
    })
}

// 是否霸屏
const switchScreenChange = (e) => {
    state.form.isDominateScreen = e.detail.value
}
// 选择班牌时间
const changeTime = (e) => {
    state.form.dominateStartTime = e[0] || ""
    state.form.dominateEndTime = e[1] || ""
}

const getDetails = () => {
    http.post("/app/mobile/mess/publish/getInfo", state.propsForm).then(({ data }) => {
        const { coverImg, dominateStartTime, dominateEndTime, notifyDevicesInfo, notifyUsersInfo, deviceList } = data
        state.form = data
        state.form.coverImg = coverImg
        state.form.receiver = notifyDevicesInfo ? "班牌" : ""
        state.form.notifyDevicesInfo = notifyDevicesInfo
        state.form.deviceList = deviceList.map((v) => v.brandId)
        state.datetimerange = [dominateStartTime, dominateEndTime]
        state.form.notifyUsersInfo = notifyUsersInfo
        state.tempId = data.id
        state.form.timerDate = data.isTimer ? data.timerDate : null
        console.log("state.form.timerDate", state.form.timerDate)
    })
}
watch(
    () => state.form.timerDate,
    (val) => {
        state.form.isTimer = val && !!val.length
    }
)

onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    state.propsForm = options
    const { title = "", coverImg = "", contentImg = "", id = "", pcParseJson, mobileParseJson, tempId } = options
    id && getDetails()
    // 这是通过模版进入的
    state.form.details = []
    if (coverImg || contentImg) {
        state.tempId = tempId
        state.form.contentType = 1
        state.form.title = title
        state.form.details = [
            {
                contentImg: coverImg || contentImg,
                coverImg: coverImg || contentImg,
                parseJson: mobileParseJson,
                height: 1334,
                width: 750
            },
            {
                contentImg: coverImg || contentImg,
                coverImg: coverImg || contentImg,
                parseJson: pcParseJson,
                height: 576,
                width: 1024
            }
        ]
    }
})
</script>

<style lang="scss" scoped>
.container {
    height: calc(100vh - 130rpx);
    background-color: #f9faf9;

    .create {
        margin: 10rpx 0;
        padding-bottom: 174rpx;

        .create-img {
            width: 100vw;
            height: 660rpx;
            position: relative;

            .file-picker-image {
                position: absolute;
                top: 50%;
                left: 50%;
                margin-top: -132rpx;
                margin-left: -100rpx;
            }

            .cover-img {
                padding: 20rpx;
                position: relative;

                .reset-img {
                    width: 690rpx;
                    margin: 0 auto;
                }

                .cover-img-delete {
                    position: absolute;
                    z-index: 9999;
                    top: 0;
                    right: 0;
                }
            }
        }

        .uni-input {
            padding: 15rpx 0;
            background-color: $uni-text-color-inverse;
            margin: 1rpx 0;
            text-indent: 20rpx;
        }

        .uni-list-cell {
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-top: 1rpx solid #eee;
            padding-left: 15rpx;

            .uni-list-cell-db {
                text-indent: 14rpx;
                font-size: 28rpx;
            }
        }

        // 附件
        .file-picker {
            text-align: right;

            :deep(.uni-icons) {
                display: none;
            }
        }

        .datetime-picker {
            flex: 1;
            text-align: right;

            :deep(.icon-calendar) {
                display: none;
            }
            :deep(.uni-date__x-input) {
                height: 48rpx;
                line-height: 48rpx;
            }

            :deep(.uni-date-x) {
                justify-content: flex-end !important;
            }
        }
    }
}

:deep(.uni-list-item__content) {
    flex: none;
    max-width: 80%;
    word-break: break-all;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

:deep(.uni-list-item__extra) {
    flex: 1;
}

.item_right_text {
    font-weight: 400;
    font-size: 28rpx;
    color: #999999;
    line-height: 40rpx;
    text-align: right;
    flex: 1;
}
</style>
