<template>
    <div>
        <!-- #ifdef H5-WEIXIN || H5 -->
        <iframe class="webview" :src="src" frameborder="0"></iframe>
        <!-- #endif -->
        <!-- #ifdef MP-WEIXIN || APP-PLUS-->
        <web-view class="webview" :src="src"></web-view>
        <!-- #endif -->
    </div>
</template>

<script setup>
import { getToken } from "@/utils/storageToken.js"
import { checkPlatform } from "@/utils/sendAppEvent.js"

const platform = ["yide-ios-app", "yide-android-app", "yide-Harmony-app"].includes(checkPlatform())
const token = getToken()?.replace("Bearer ", "")
const src = ref(`${import.meta.env.VITE_BASE_OAH5}/#/?token=${token}&skipDetail=${platform}`)
onLoad((options) => {
    if (options.code != "oaApprove") {
        src.value = `${import.meta.env.VITE_BASE_OAH5}/#/leave?token=${token}&fromKey=${options.code}&skipDetail=${platform}`
    }
})
</script>

<style lang="scss" scoped>
.webview {
    height: 100vh;
    width: 100vw;
}
</style>
