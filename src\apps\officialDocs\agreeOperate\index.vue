<template>
    <view class="agree_operate">
        <template v-if="isSelectMember">
            <NavBar title="办理操作" :clickLeft="clickLeft" />
            <view class="content">
                <uni-forms ref="baseForm" :modelValue="state.baseForm">
                    <view class="forms_item">
                        <uni-forms-item>
                            <template #label>
                                <view class="label">办理结果</view>
                            </template>
                            <uni-easyinput class="input" :inputBorder="false" v-model="state.baseForm.resultMap"
                                disabled placeholder="请输入" />
                        </uni-forms-item>
                    </view>
                    <view class="forms_item" v-if="state.operate == 'back'">
                        <uni-forms-item>
                            <template #label>
                                <view class="label">回退至</view>
                            </template>

                            <uni-easyinput class="input" :inputBorder="false" v-model="state.baseForm.nodeName" disabled
                                suffixIcon="right" @iconClick="iconClick('back')" placeholder="请选择" />
                        </uni-forms-item>
                    </view>
                    <view class="forms_item" v-if="state.operate !== 'back' && isTaoHong">
                        <uni-forms-item>
                            <template #label>
                                <view class="label">
                                    <text class="required">*</text>套红模版
                                </view>
                            </template>
                            <uni-easyinput class="input" :inputBorder="false" v-model="state.baseForm.coverRed" disabled
                                suffixIcon="right" @iconClick="iconClick('template')" placeholder="请选择" />
                        </uni-forms-item>
                    </view>
                    <view class="forms_item" v-if="state.operate !== 'back' && isQianZhang">
                        <uni-forms-item>
                            <template #label>
                                <view class="label">添加签章</view>
                            </template>

                            <uni-easyinput class="input" :inputBorder="false" v-model="state.baseForm.sealName" disabled
                                suffixIcon="right" @iconClick="iconClickSign" placeholder="请添加签章" />
                        </uni-forms-item>
                    </view>
                    <view class="forms_item" v-if="state.operate !== 'back' && !isGuiDang">
                        <uni-forms-item>
                            <template #label>
                                <view class="label">下一步操作</view>
                            </template>
                            <uni-easyinput class="input" :inputBorder="false" v-model="state.baseForm.nextFlowTitle"
                                disabled placeholder="请输入" />
                        </uni-forms-item>
                    </view>
                    <view class="forms_item" v-if="state.operate !== 'back' && !isGuiDang">
                        <uni-forms-item required>
                            <template #label>
                                <view class="label">
                                    <text class="required">*</text>办理人员
                                </view>
                            </template>
                            <view v-if="state.nextUserList.length" class="rest_placeholder active">
                                <text>{{ state.baseForm.treeSubmitListName }}</text>
                            </view>
                            <view v-else @click="isSelectMember = false" class="rest_placeholder">
                                <text class="text" v-if="!state.baseForm.treeSubmitListName">请输入</text>
                                <text v-else>{{ state.baseForm.treeSubmitListName }}</text>
                            </view>
                        </uni-forms-item>
                    </view>
                    <view class="forms_item opinion">
                        <uni-forms-item>
                            <template #label>
                                <view class="label">意见输入</view>
                            </template>
                            <uni-easyinput class="input" autoHeight :inputBorder="false" type="textarea"
                                :maxlength="maxlength" v-model="state.baseForm.comment" placeholder="请输入" />
                            <view class="label textarea_count">{{ state.baseForm.comment.length }}/{{ maxlength }}
                            </view>
                        </uni-forms-item>
                    </view>
                </uni-forms>
            </view>
            <view class="footer">
                <button class="mini-btn" type="default" size="mini" @click="handleCancel">取 消</button>
                <button class="mini-btn" type="primary" size="mini" :loading="state.submitLoading"
                    @click="handleSubmit">
                    确 定
                </button>
            </view>

            <uni-popup ref="childPopup" type="bottom" background-color="#fff" borderRadius="20rpx 20rpx 0 0">
                <view class="child-switch">
                    <view class="handle">
                        <text class="title">{{ state.popupTitle }}</text>
                        <uni-icons class="close" type="closeempty" size="16" @click="childPopup.close()"></uni-icons>
                    </view>
                    <view class="child-list">
                        <radio-group @change="radioChange">
                            <label class="uni-list-cell" v-for="item in childList" :key="item.id">
                                <view>{{ item.name }}</view>
                                <view>
                                    <radio :value="item.id" :checked="item.id === state.personId" color="#00b781" />
                                </view>
                            </label>
                        </radio-group>
                    </view>
                </view>
            </uni-popup>
            <!-- 归档二次确定 -->
            <uni-popup ref="deletePopup" border-radius="10px" background-color="#fff" :is-mask-click="false">
                <view class="delete-popup-content">
                    <view class="header">
                        <text class="title">归档</text>
                        <uni-icons class="close" type="closeempty" size="20" @click="deletePopup.close()"></uni-icons>
                    </view>
                    <view class="body">
                        确定要归档么？
                    </view>
                    <view class="footer-btn">
                        <button class="mini-btn" @click="deletePopup.close()" tyle="default" size="mini">取 消</button>
                        <button class="mini-btn" type="primary" @click="handleSaveDeleteOrder" tyle="primary"
                            size="mini">
                            确 定
                        </button>
                    </view>
                </view>
            </uni-popup>
        </template>

        <selectMember v-else @selectMember="handerSelectMember" v-model:isSelectMember="isSelectMember"
            :treeSubmitList="treeSubmitList" />

    </view>
</template>

<script setup>
import NavBar from "../components/navBar.vue"
import selectMember from "../selectMember/index.vue"
import useStore from "@/store"
const { officialDocs } = useStore()
const _formListData = computed(() => officialDocs.submitFormData)
const deletePopup = ref(null)
const childList = ref([
    {
        name: '类型名称',
        id: '1'
    }
])
const maxlength = 200
const isSelectMember = ref(true)
const treeSubmitList = ref([])

const childPopup = ref(null)

const state = reactive({
    nextUserList: [],
    formListData: {},
    baseForm: {
        resultMap: '',
        nextFlowTitle: "",
        treeSubmitListName: '',
        comment: "",
        // 同意操作
        assigneeIds: [],
        nextNodeId: '',
        // fileId: '',
        outcome: 2,
        coverRed: '',
        coverRedId: '',
        // fileUrl: '',
        // 回退操作
        nodeId: '',
        nodeName: '',
        sealId: '',
        sealName: '',
        // 已添加
    },
    popupTitle: "",
    personId: "",
    submitLoading: false,
    selectType: '',
    fallbackList: [],// 回退节点
    coverRedList: [],
    // 路由参数
    id: '',
    operate: "",
    type: '',
    showType: ''
})
const iconClickSign = () => {
    const { type, operate, showType, id } = state
    navigateTo({
        url: "/apps/officialDocs/agreeOperate/signature",
        query: {
            id,
            type,
            operate,
            showType,
        }
    })
}
const iconClick = async (item) => {
    state.selectType = item
    if (item == 'back') {
        state.popupTitle = '回退至'
        childList.value = state.fallbackList
    } else if (item == 'template') {
        // 套红模版
        state.popupTitle = '套红模版'
        childList.value = state.coverRedList
    } else {
        state.popupTitle = '选择模版'
    }
    childPopup.value.open()
}
// 获取套红模板 跟新正文
const getApplyRedHeader = async () => {
    const params = {
        templateId: state.baseForm.coverRedId,
        documentId: _formListData.value?.id || ''
    }
    uni.showToast({
        title: '套红中...',
        icon: "loading"
    })
    state.submitLoading = true
    const { data } = await http.post("/cloud/official-doc/document/apply-red-header", params)
    state.submitLoading = false
    officialDocs.setSubmitFormListKey('fileId', data.fileId)
    officialDocs.setSubmitFormListKey('fileUrl', data.fileUrl)
    uni.showToast({
        title: '套红成功',
        icon: "success"
    })
}
const radioChange = async (evt) => {
    const id = evt.detail.value
    const name = childList.value.find(v => v.id == id)?.name || ''
    if (state.selectType == 'back') {
        state.baseForm.nodeName = name
        state.baseForm.nodeId = id
    }
    // 套红模版
    else if (state.selectType == 'template') {
        state.baseForm.coverRed = name
        state.baseForm.coverRedId = id
        officialDocs.setSubmitFormListKey('coverRed', name)
        officialDocs.setSubmitFormListKey('coverRedId', id)
        getApplyRedHeader()
    }
    childPopup.value.close()
}
// 取消
const handleCancel = () => {
    uni.navigateBack()
}

const submitInfo = async (API) => {
    const { type, showType, id, baseForm, formListData } = state
    const params = {
        ...baseForm,
        ..._formListData.value,
    }
    if (formListData.status == 'GUI_DANG') {
        params.fileId = ''
    }
    let res = await http.post(API, params).finally(() => {
        state.submitLoading = false
    }).catch(err => {
        state.submitLoading = false
    })
    uni.showToast({
        title: res.message,
    })

    navigateTo({
        url: "/apps/officialDocs/manage/index",
        query: {
            id,
            type,
            showType,
        },
    })
}
// 确定归档弹框
const handleSaveDeleteOrder = () => {
    submitInfo("/cloud/official-doc/workflow/completeTask")
}
// 确定
const handleSubmit = () => {
    if (state.submitLoading) return
    state.submitLoading = true
    let API = ""
    if (["agre", "submit"].includes(state.operate)) {
        if (isTaoHong.value && !state.baseForm.coverRedId) {
            uni.showToast({
                title: '套红模板不能为空！',
                icon: "none"
            })
            state.submitLoading = false
            return
        }
        if (!state.baseForm.assigneeIds.length && !isGuiDang.value) {
            uni.showToast({
                title: '办理人员不能为空！',
                icon: "none"
            })
            state.submitLoading = false

            return
        }

        API = "/cloud/official-doc/workflow/completeTask"
        // } else {
        //     res = await http.get("/cloud/official-doc/document/get", params).finally(() => {
        //         state.submitLoading = false
        //     }).catch(err => {
        //         state.submitLoading = false
        //     })
        // }
        if (isGuiDang.value) {
            deletePopup.value.open()
            return
        }

    } else {
        // 回退
        API = "/cloud/official-doc/workflow/changeActivityState"
        if (!state.baseForm.nodeId) {
            uni.showToast({
                title: '回退选项不能为空！',
                icon: "none"
            })
            state.submitLoading = false
            return
        }
    }
    submitInfo(API)
}

const handerSelectMember = (data) => {
    treeSubmitList.value = data.treeSubmitList
    state.baseForm.treeSubmitListName = data.treeSubmitListName
    state.baseForm.assigneeIds = data.treeSubmitList.map(v => v.id)
    isSelectMember.value = true
}
state.baseForm.resultMap = computed(() => {
    const obj = {
        "CHUAN_YUE": "传阅",
        "YUE_DU": "阅读",
        "BAN_JIE": "办结",
        "GUI_DANG": "归档",
        "CHENG_BAN": "承办",
        "QIAN_ZHANG": "签章",
        "FEN_FA": "分发",
        "BAN_JIE": "办结",
    }
    return obj[state.formListData.status] || '同意'
})
// 是否套红
const isTaoHong = computed(() => {
    return ["TAO_HONG"].includes(state.formListData.status || '')
})
// 归档
const isGuiDang = computed(() => {
    return ["GUI_DANG"].includes(state.formListData.status || '')
})
// 是否签章
const isQianZhang = computed(() => {
    return ["QIAN_ZHANG"].includes(state.formListData.status)
})

// 回退节点
const openFallback = async (documentId) => {
    // 加载回退节点
    const { data } = await http.post(`/cloud/official-doc/workflow/getStepFlowElementList`, {
        documentId
    })
    state.fallbackList = data
}

async function getNextFlowElement(documentId) {
    const { data } = await http.post("/cloud/official-doc/workflow/getNextFlowElement", { documentId })
    state.baseForm.nextFlowTitle = data.name || ''
    state.baseForm.nextNodeId = data.id || ''
    state.nextUserList = data.userInfoList
    state.baseForm.assigneeIds = []
    state.baseForm.treeSubmitListName = ''
    if (data.userInfoList.length) {
        let _treeSubmitListName = []
        data.userInfoList.forEach(item => {
            _treeSubmitListName.push(item.name)
            state.baseForm.assigneeIds.push(item.id)
        })
        state.baseForm.treeSubmitListName = _treeSubmitListName.join("、")
    }
}
// 套红模板选项
const getCoverRedList = async () => {
    const { data } = await http.post("/cloud/official-doc/red-letterhead/enabledList")
    state.coverRedList = data.map(v => ({ id: v.id, name: v.templateName }))
}

watch(() => _formListData.value, async (val) => {
    if (val.id) {
        state.formListData = val
        state.baseForm.coverRed = val.coverRed || ''
        state.baseForm.coverRedId = val.coverRedId || ''
        state.baseForm.sealId = val.sealId || ''
        state.baseForm.sealName = val.sealId ? '已添加' : ''
        state.baseForm.comment = ''
        await getNextFlowElement(val.id)
        await openFallback(val.id)
        val.status == 'TAO_HONG' && await getCoverRedList()
    }
}, { immediate: true, deep: true })

function clickLeft() {
    navigateTo({
        url: "/apps/officialDocs/manage/reviewDraft/index",
        query: {
            type: state.type,
            showType: state.showType,
            id: state.id
        },
    })
}
onLoad(async (item) => {
    Object.keys(item).forEach((key) => {
        item[key] = decodeURIComponent(item[key])
    })
    state.operate = item.operate || ''
    state.id = item.id
    state.type = item.type
    state.showType = item.showType
})

</script>

<style lang='scss' scoped>
$bag: #F7F7F7;

.agree_operate {
    background: $bag;
    height: 100vh;

    .content {
        background: $uni-text-color-inverse;

        /* #ifdef MP-WEIXIN */
        .uni-forms-item {
            display: block;
        }

        .label {
            font-weight: 400;
            font-size: 24rpx;
            color: #666666;
            margin-bottom: 6rpx;
        }

        /* #endif */

        .forms_item {
            :deep(.uni-forms-item) {
                display: block;
                padding: 0 30rpx;
            }

            &.opinion {
                border-top: 20rpx solid $bag;
                padding: 30rpx;

                :deep(.uni-forms-item) {
                    padding: 0;
                }

                .label {
                    font-size: 28rpx;
                    color: $uni-text-color;
                }

                .input {
                    text-indent: 20rpx;
                }

                .textarea_count {
                    text-align: right;
                    background-color: $bag !important;
                    padding: 0 10rpx 10rpx;

                    font-weight: 400;
                    font-size: 28rpx;
                    color: $uni-text-color-grey;
                }

                :deep(.uni-easyinput__content) {
                    background-color: #F7F7F7 !important;
                    text-indent: 20rpx;
                }
            }

            .label {
                margin-bottom: 10rpx;
                font-weight: 400;
                font-size: 24rpx;
                color: #666666;

                .required {
                    color: red;
                }
            }

            .rest_placeholder {
                background-color: $bag !important;
                padding: 20rpx;

                .text {
                    color: #d5d1d1;
                    font-size: 24rpx;
                }
            }

            .input {

                :deep(.uni-easyinput__content-input),
                :deep(.uni-easyinput__content) {
                    background-color: $bag !important;

                }
            }
        }
    }


    .footer {
        position: fixed;
        left: 0;
        bottom: 0;
        box-sizing: border-box;
        background: $uni-text-color-inverse;
        width: 100vw;
        padding: 30rpx 30rpx 68rpx;
        text-align: right;

        &.download {
            display: flex;
            justify-content: space-evenly;

            .btn {
                text-align: center;
                font-weight: 400;
                font-size: 24rpx;
                color: #333333;
            }
        }

        .mini-btn {
            margin: 0 10rpx;
            padding-top: 6rpx;
            padding-bottom: 6rpx;
            border: 1px solid $uni-text-color-grey;
            background-color: $uni-text-color-inverse;

            &[type="primary"] {
                background-color: $uni-color-primary;
                border: 1px solid $uni-color-primary
            }

        }
    }

    .child-switch {
        min-height: 400rpx;

        .handle {
            text-align: center;
            position: relative;
            height: 80rpx;
            line-height: 80rpx;

            .close {
                position: absolute;
                right: 20rpx;
                top: 0;
            }
        }

        .child-list {
            .uni-list-cell {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 20rpx;
                border-bottom: 1rpx solid #f2f2f2;

                :deep(.uni-radio-input) {
                    border: none;
                    background-color: transparent;
                }
            }
        }
    }

    .delete-popup-content {
        padding: 20rpx 30rpx;
        width: 80vw;

        .header {
            position: relative;
            text-align: center;

            .title {
                font-weight: 500;
                font-size: 34rpx;
                color: #333333;
            }

            .close {
                position: absolute;
                right: 0;
                top: 0;
            }
        }

        .body {
            text-align: center;
            margin: 60rpx 0;

            :deep(.swiper-box) {
                height: 848rpx !important;

                .uni-list-item__extra-text {
                    font-size: 28rpx;
                }

                .uni-list-item__extra {
                    flex: 2;
                }
            }

            :deep(.uni-swiper__dots-box) {
                height: 45rpx;
            }

            :deep(.uni-list-item__extra-text) {
                max-height: 300rpx;
                overflow: hidden auto;
            }
        }

        .footer-btn {
            display: flex;

            .mini-btn {

                &[type="primary"] {
                    background-color: $uni-color-primary;
                }
            }
        }


    }
}
</style>
