<template>
    <qiun-data-charts type="line" :opts="state.opts" :chartData="props.chartData" />
</template>

<script setup>
import qiunDataCharts from "@/subModules/components/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue"

const props = defineProps({
    chartData: {
        type: Object,
        default: () => { }
    }
})

const state = reactive({
    opts: {
        color: ["#11C685", "#5289FB", "#FFB50A"],
        padding: [15, 10, 0, 15],
        dataLabel: false,
        dataPointShape: false,
        legend: {
            show: false,
            position: 'top',
        },
        xAxis: {
            disableGrid: true
        },
        yAxis: {
            gridType: "dash",
            dashLength: 2,
            data: []
        },
        extra: {
            tooltip: {
                showBox: false
            },
            line: {
                type: "curve",
                width: 1,
                activeType: "hollow",
            }
        }
    }
})
</script>

<style lang="scss" scoped></style>

<script>
export default {
    componentPlaceholder: {
        "qiun-data-charts": "view"
    }
}
</script>
