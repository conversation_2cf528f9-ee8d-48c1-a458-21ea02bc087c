<template>
    <qiun-data-charts type="pie" :opts="state.opts" :chartData="props.chartData" />
</template>
<script setup>
import qiunDataCharts from "@/subModules/components/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue"
const props = defineProps({
    chartData: {
        type: Object,
        default: () => { }
    }
})

const state = reactive({
    opts: {
        color: ["#11C685", "#5289FB", "#FFB50A", "#F98A43"],
        padding: [5, 5, 5, 5],
        enableScroll: false,
        legend: {
            show: false,
            position: 'right',
        },
        extra: {
            tooltip: {
                showBox: true,
            },
            markLine: {
                dashLength: 2
            },
            pie: {
                activeOpacity: 0.5,
                activeRadius: 10,
                offsetAngle: 0,
                labelWidth: 15,
                border: true,
                borderWidth: 3,
                borderColor: "#FFFFFF"
            }
        }
    }
})
</script>
<style lang="scss" scoped></style>
