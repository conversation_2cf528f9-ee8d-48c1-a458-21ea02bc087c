<template>
    <view class="tabs" :class="_class">
        <view class="tabs-itme" v-for="item in list" :key="item.key" :class="{ active: item.key == current }"
            @click="handerTabs(item)">
            {{ item.name }}
        </view>
    </view>
</template>

<script setup>
const props = defineProps({
    list: {
        type: Array,
        default: () => []
    },
    current: {
        type: String,
        default: ''
    },
    _class: {
        type: String,
        default: ''
    }
})
const emit = defineEmits(['change', 'update:current'])

const handerTabs = (item) => {
    console.log(item, 'tabs');
    
    emit('update:current', item.key)
    emit('change', item)
}

</script>

<style lang='scss' scoped>
.tabs {
    display: flex;
    justify-content: space-around;
    align-items: center;
    background-color: $uni-text-color-inverse;
    margin-top: 18rpx !important;
    &._tabs {
        justify-content: flex-start !important;
        margin: 0 !important;

        .tabs-itme {
            font-weight: 400;
            font-size: 30rpx;
            color: #666666;

            &.active {
                color: $uni-color-primary;
                font-weight: 600;
                font-size: 36rpx;

                &::after {
                    background-color: $uni-color-primary;
                }
            }
        }
    }

    .tabs-itme {
        font-weight: 500;
        font-size: 30rpx;
        color: $uni-text-color-grey;
        padding: 22rpx;

        &.active {
            color: $uni-text-color;
            position: relative;

            &::after {
                content: '';
                width: 40rpx;
                height: 6rpx;
                display: block;
                position: absolute;
                left: 50%;
                bottom: 0;
                transform: translateX(-50%);
                background-color: $uni-color-primary;
            }
        }
    }
}
</style>