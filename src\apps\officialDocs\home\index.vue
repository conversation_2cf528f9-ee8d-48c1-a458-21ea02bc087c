<template>
    <view class='official_docs_home'>
        <view class="bannar">
            <view class="bannar_title">
                <uni-icons type="left" size="20" @click="handleBack"></uni-icons>
                <view class="bannar_title_text">公文管理</view>
            </view>
        </view>
        <view class="content">
            <view class="content_item grid">
                <view class="content_item_grid" v-for="(item, idx) in baseList" :key="idx">
                    <image class="content_item_grid_icon" mode="aspectFill" :src="item.icon" />
                    <view class="content_item_grid_content">
                        <view class="content_item_grid_num">{{ state.count[item.key] }}</view>
                        <view class="content_item_grid_title">{{ item.title }}</view>
                    </view>
                </view>
            </view>

            <view class="content_item">
                <view class="line_box">
                    <view class="top_box">
                        <view>公文数量统计</view>
                        <view class="line_box_legend">
                            <template v-for="(item, index) in linData" :key="index">
                                <view class="line_box_point" :style="{ background: lineColor[index] }"></view>
                                <text>{{ item.name }}</text>
                            </template>
                        </view>
                    </view>
                    <LineCharts class="main_box" :chartData="state.lineData" />
                </view>
            </view>

            <view class="content_item">
                <view class="pie_box">
                    <view class="top_box">公文紧急程度占比</view>
                    <view class="pie_main_box" v-if="state.pieData.series[0].data.length">
                        <PieCharts class="left_box" type="pie" :chartData="state.pieData" />
                        <view class="right_box">
                            <view class="item_box" v-for="(item, index) in state.pieData.series[0].data" :key="index">
                                <view class="point_box">
                                    <view class="point" :style="{ background: pieColor[index] }" />
                                    <view>{{ item.name }}</view>
                                </view>
                                <view>
                                    数量：{{ item.value }} 占比：{{ item.percentage }}
                                </view>
                            </view>
                        </view>
                    </view>
                    <view v-else class="empty_box">暂无数据</view>
                </view>
            </view>
        </view>

        <view class="tabbar">
            <view class="tabbar_item active">
                <image class="tabbar_item_icon" mode="aspectFill"
                    src="https://file.1d1j.cn/cloud-mobile/officialDocs/home_active.png" />
                <view class="tabbar_item_text">首页</view>
            </view>
            <view class="tabbar_item" @click="changeTabbar">
                <image class="tabbar_item_icon" mode="aspectFill"
                    src="https://file.1d1j.cn/cloud-mobile/officialDocs/official.png" />
                <view class="tabbar_item_text">公文管理</view>
            </view>
        </view>
    </view>
</template>

<script setup>
import { sendAppEvent, checkPlatform } from "@/utils/sendAppEvent.js"
import LineCharts from "../components/LineCharts.vue"
import PieCharts from "../components/PieCharts.vue"

const lineColor = ["#11C685", "#5289FB", "#FFB50A"]
const pieColor = ["#11C685", "#5289FB", "#FFB50A", "#F98A43"]
const linData = [
    {
        name: "发文",
    },
    {
        name: "收文",
    },
    {
        name: "签报",
    }
]
const total = ref(0)

const baseList = [{
    icon: 'https://file.1d1j.cn/cloud-mobile/officialDocs/dispatch.png',
    title: '发文总数',
    key: 'docDraftsNum',
}, {
    icon: 'https://file.1d1j.cn/cloud-mobile/officialDocs/receiving.png',
    title: '收文总数',
    key: 'docReceiptsNum',
}, {
    icon: 'https://file.1d1j.cn/cloud-mobile/officialDocs/signoff.png',
    title: '签报总数',
    key: 'memoDraftsNum',
}]
const state = reactive({
    count: {
        docDraftsNum: 0,
        docReceiptsNum: 0,
        memoDraftsNum: 0
    },
    lineData: {
        categories: [],
        series: [
            {
                data: []
            }
        ]
    },
    pieData: {
        series: [
            {
                data: []
            }
        ]
    },

})
const changeTabbar = () => {
    navigateTo({
        url: '/apps/officialDocs/manage/index',
    })
}

const getCountInfo = () => {
    http.get("/cloud/official-doc/document/count").then(({ data }) => {
        state.count = data || {
            docDraftsNum: 0,
            docReceiptsNum: 0,
            memoDraftsNum: 0
        }
    })
}

const adjustPercentages = (items, total) => {
    if (total === 0) return items; // 避免除以0

    // 1. 计算每个项目的百分比（保留2位小数）
    const itemsWithPercent = items.map(item => ({
        ...item,
        rawPercent: (item.value / total) * 100,
    }));

    // 2. 计算每个项目的整数部分和余数部分
    const itemsWithFloor = itemsWithPercent.map(item => ({
        ...item,
        floor: Math.floor(item.rawPercent),
        remainder: item.rawPercent - Math.floor(item.rawPercent),
    }));

    // 3. 计算总和和差值
    const sumFloor = itemsWithFloor.reduce((sum, item) => sum + item.floor, 0);
    let remainder = 100 - sumFloor;

    // 4. 按余数从大到小排序，将剩余的1%分配给余数最大的项目
    const sorted = [...itemsWithFloor].sort((a, b) => b.remainder - a.remainder);

    // 5. 分配剩余的百分比
    for (let i = 0; i < remainder; i++) {
        sorted[i].floor += 1;
    }

    // 6. 返回最终结果
    return sorted.map(item => ({
        name: item.name,
        value: item.value,
        percentage: item.floor + '%',
        labelText: item.floor + '%'
    }));
};
// 各类型告警数量占比
const countTypes = async () => {
    // const { data } = await http.get("/cloud/official-doc/document/urgency-level-count")
    http.get("/cloud/official-doc/document/urgency-level-count").then(({ data }) => {
        setTimeout(() => {
            const obj = {}
            const _obj = {}
            // 将series的数据按名称（  "其他","平件","急件","特急"）分组 
            data.data.forEach((v, idx) => {
                obj[v] = []
                data.series.forEach((k, index) => {
                    obj[v].push(k.data[idx])
                })
            })
            // 计算（"其他","平件","急件","特急"）每组的单个之和
            // 其他：1(发文) + 1(收文) + 0(签报) = 2
            // 平件：0 + 0 + 2 = 2
            // 急件：0 + 1 + 1 = 2
            // 特急：5 + 0 + 0 = 5
            Object.keys(obj).forEach((k, index) => {
                _obj[k] = Object.values(obj)[index].reduce((a, b) => a + b, 0)
            })
            // 计算总量（"其他","平件","急件","特急"）之和
            // total = 2 + 2 + 2 + 5 = 11
            total.value = Object.values(_obj).reduce((a, b) => a + b, 0)
            const adjustedData = adjustPercentages(
                data.data.map((v, idx) => ({
                    name: v,
                    value: _obj[v] || 0
                })),
                total.value
            );
            state.pieData = {
                series: [{ data: adjustedData }]
            };
        }, 600);
    })

}
// 公文数量统计
const trendingLastDays = async () => {
    http.get("/cloud/official-doc/document/month-count").then(({ data }) => {
        setTimeout(() => {
            let lineData = {
                categories: data.data,
                series: data.series.length ? data.series : [{
                    "data": [0, 0, 0, 0, 0, 0],
                }]
            };
            state.lineData = lineData
        }, 600);
    })
}


function handleBack() {
    // #ifdef H5
    const roleArr = ["yide-ios-app", "yide-android-app", "yide-Harmony-app"]
    if (roleArr?.includes(checkPlatform())) {
        sendAppEvent("backApp", {}) ||
            uni.switchTab({
                url: "/pages/workbench/index"
            })
        return
    }
    // #endif
    uni.switchTab({
        url: "/pages/workbench/index"
    })
}
onMounted(() => {
    getCountInfo()
    trendingLastDays()
    countTypes()
})
</script>

<style lang='scss' scoped>
.official_docs_home {
    box-sizing: border-box;
    width: 100vw;
    height: 100vh;
    padding-bottom: 120rpx;
    background: #F6F6F6;

    .bannar {
        width: 100vw;
        height: 160rpx;
        font-weight: 600;
        font-size: 36rpx;
        color: #000000;
        background: linear-gradient(180deg, #37D365 0%, #98E6B0 77%, #FDFFFE 100%);
        position: relative;
        // #ifdef MP-WEIXIN
        padding-top: 96rpx;

        // #endif
        .bannar_title {
            padding: 15rpx 30rpx;
            display: flex;
            flex: 1;
            align-items: center;

            .bannar_title_text {
                width: calc(100vw - 20rpx);
                text-align: center;

            }
        }
    }

    .content {
        width: 100vw;
        padding: 0 30rpx 30rpx;
        box-sizing: border-box;
        position: absolute;
        top: 90rpx;
        // #ifdef MP-WEIXIN
        top: 180rpx;

        // #endif
        .content_item {
            padding: 14rpx 0;
            margin: 0 auto 24rpx;
            background: $uni-text-color-inverse;
            box-shadow: 0rpx 8rpx 16rpx 0rpx rgba(241, 241, 241, 0.5);
            border-radius: 16rpx;
            display: flex;
            justify-content: space-around;
            align-items: center;

            &.grid {
                padding: 46rpx 0;
            }

            .content_item_grid {
                display: flex;
                align-items: center;

                .content_item_grid_icon {
                    width: 72rpx;
                    height: 72rpx;
                    border-radius: 50%;
                }

                .content_item_grid_content {
                    text-align: center;
                    margin-left: 14rpx;

                    .content_item_grid_num {
                        font-weight: 600;
                        font-size: 28rpx;
                        color: #000000;
                    }

                    .content_item_grid_title {
                        font-weight: 400;
                        font-size: 22rpx;
                        color: #666666;
                    }
                }
            }

            .line_box {
                width: 100%;
                height: 480rpx;
                background: $uni-bg-color;
                border-radius: 16rpx;
                box-sizing: border-box;

                .top_box {
                    display: flex;
                    justify-content: space-between;
                    font-size: 28rpx;
                    font-weight: bold;
                    color: #000;
                    padding: 0 30rpx;

                    .line_box_legend {
                        display: flex;
                        align-items: center;
                        font-weight: 400;
                        font-size: 24rpx;
                        color: #666666;

                        .line_box_point {
                            width: 20rpx;
                            height: 4rpx;
                            margin: 0 18rpx;
                        }
                    }
                }

                .main_box {
                    height: calc(100% - 70rpx);
                }
            }

            .pie_box {
                width: 100%;
                height: 392rpx;
                box-sizing: border-box;
                background: $uni-bg-color;
                border-radius: 16rpx;
                padding-bottom: 40rpx;

                .top_box {
                    font-size: 28rpx;
                    font-weight: bold;
                    color: #000;
                    padding: 0 30rpx;
                }

                .pie_main_box {
                    height: 100%;
                    display: flex;

                    .left_box {
                        width: calc(100vw - 300rpx);
                        height: 100%;
                    }

                    .right_box {
                        flex: 1;
                        flex-direction: column;
                        justify-content: center;

                        .item_box {
                            color: #8b8b8b;
                            font-size: 20rpx;
                            margin: 14rpx 0;

                            .point_box {
                                display: flex;
                                align-items: center;

                                .point {
                                    width: 16rpx;
                                    height: 16rpx;
                                    border-radius: 50%;
                                    margin-right: 6rpx;
                                    background-color: red;
                                }
                            }
                        }
                    }

                }

                .empty_box {
                    color: #ccc;
                    text-align: center;
                    margin-top: 120rpx;
                }
            }
        }

    }

    .tabbar {
        position: fixed;
        bottom: 0;
        width: 100vw;
        height: 120rpx;
        background-color: $uni-text-color-inverse;
        display: flex;
        justify-content: space-evenly;
        align-items: center;

        .tabbar_item {
            text-align: center;

            &.active {
                .tabbar_item_text {
                    color: $uni-color-primary;
                }
            }

            .tabbar_item_icon {
                width: 40rpx;
                height: 40rpx;
            }

            .tabbar_item_text {
                font-weight: 500;
                font-size: 20rpx;
                color: $uni-text-color;
            }
        }

    }
}
</style>