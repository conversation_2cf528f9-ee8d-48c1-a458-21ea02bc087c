<template>
    <view class='official_docs'>
        <Manage v-if="state.tabbarValue == 1" />
        <Home v-else />
<!-- 
        <view class="tabbar">
            <view class="tabbar_item" v-for="item in tabbars" :key="item.key"
                :class="{ active: item.key == state.tabbarValue }" @click="changeTabbar(item.key)">
                <image class="tabbar_item_icon" mode="aspectFill" :src="iconComput(item)" />
                <view class="tabbar_item_text"> {{ item.text }}</view>
            </view>
        </view> -->
    </view>
</template>

<script setup>
import { computed, reactive } from 'vue'
import Home from "./home/<USER>"
import Manage from "./manage/index.vue"

const tabbars = [
    { text: "首页", key: 0, icon: 'home' },
    { text: "公文管理", key: 1, icon: 'official' },
]
const iconComput = computed(() => {
    return (item) => {
        if (item.key === state.tabbarValue) {
            return `https://file.1d1j.cn/cloud-mobile/officialDocs/${item.icon}_active.png`
        }
        return `https://file.1d1j.cn/cloud-mobile/officialDocs/${item.icon}.png`
    }
})
const state = reactive({
    tabbarValue: 0,
})

const changeTabbar = (index) => {
    state.tabbarValue = index
}

onLoad((item) => {
    Object.keys(item).forEach((key) => {
        item[key] = decodeURIComponent(item[key])
    })
    state.tabbarValue = item.tabbarValue || 0
})
</script>

<style lang='scss' scoped>
.official_docs {
    width: 100vw;
    height: 100vh;
    background: #F6F6F6;

    .tabbar {
        position: fixed;
        bottom: 0;
        width: 100vw;
        height: 120rpx;
        background-color: $uni-text-color-inverse;
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        .tabbar_item {
            text-align: center;

            &.active {
                .tabbar_item_text {
                    color: $uni-color-primary;
                }
            }

            .tabbar_item_icon {
                width: 40rpx;
                height: 40rpx;
            }

            .tabbar_item_text {
                font-weight: 500;
                font-size: 20rpx;
                color: $uni-text-color;
            }
        }

    }
}
</style>