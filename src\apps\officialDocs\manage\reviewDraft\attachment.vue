<template>
    <view class="attachment" :class="{ active: !_formListData.taskId }">
        <view v-for="item in state.dataList" :key="item.id" class="list-item">
            <uni-list-item :title="item.name" clickable>
                <template v-slot:body>
                    <view class="slot-box">
                        <view @click="handlerViewAttach(item)">{{ item.name }}</view>
                        <!-- #ifdef MP-WEIXIN -->
                        <uni-icons v-if="isDelete" type="more-filled" size="20"
                            @click="handlerShowDownload(item)"></uni-icons>
                        <!-- #endif -->
                        <!-- #ifndef MP-WEIXIN -->
                        <uni-icons v-if="!isDownload" type="more-filled" size="20"
                            @click="handlerShowDownload(item)"></uni-icons>
                        <!-- #endif -->

                    </view>
                </template>
            </uni-list-item>
        </view>
        <yd-empty v-if="!state.loading && !state.dataList.length" text="暂无数据" />
        <uni-popup ref="deletePopup" border-radius="10px" background-color="#fff" :is-mask-click="false">
            <view class="delete-popup-content">
                <view class="header">
                    <text class="title"></text>
                    <uni-icons class="close" type="closeempty" size="20" @click="deletePopup.close()"></uni-icons>
                </view>
                <view class="body">
                    确定删除订单？删除后无法恢复
                </view>
                <view class="footer-btn">
                    <button class="mini-btn" @click="deletePopup.close()" tyle="default" size="mini">取消</button>
                    <button class="mini-btn" type="primary" @click="handleSaveDeleteOrder" tyle="primary"
                        size="mini">确定</button>
                </view>
            </view>
        </uni-popup>
        <view class="footer" v-if="state.isShowdownload" @click.stop="state.isShowdownload = false">
            <view class="download">
                <view class="btn" @click.stop="handleDownload">
                    <uni-icons type="download" size="20"></uni-icons>
                    <view>
                        下载
                    </view>
                </view>
                <view class="btn" v-if="isDelete" @click.stop="deletePopup.open()">
                    <uni-icons type="trash" size="20"></uni-icons>
                    <view>
                        删除
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script setup>
import useStore from "@/store"
import { Base64 } from "js-base64"
import { previewFileByUrl } from "@/utils/index"

const { officialDocs } = useStore()
const _formListData = computed(() => officialDocs.getFormListData)
// 只有拟稿 登记可以删附件
const isDelete = computed(() => {
    return ["NI_GAO", 'DENG_JI'].includes(officialDocs.getFormListData?.status || '')
})
// 除归档都可以下载
const isDownload = computed(() => {
    return ["GUI_DANG"].includes(officialDocs.getFormListData?.status || '')
})
const deletePopup = ref(null)
const _isDownload = ref(false)

const state = reactive({
    dataList: [],
    attachmentId: '',
    isShowdownload: false,
    officialDocumentId: '',
    fileParams: {},
    // 路由参数
    id: '',
    operate: "",
    type: '',
    showType: '',
    loading: false,
    finished: false,
    pageNo: 1, pageSize: 10,
    total: 0
})


const initPage = async () => {
    const params = {
        officialDocumentId: state.officialDocumentId,
    }
    // #ifndef MP-WEIXIN
    uni.showToast({
        title: '加载中...',
        icon: "loading"
    })
    // #endif
    // #ifdef MP-WEIXIN
    wx.showToast({
        title: '加载中...',
        icon: "loading"
    })
    // #endif

    state.loading = true
    try {
        const { data } = await http.post("/cloud/official-doc/document-file/list", params)
        // #ifndef MP-WEIXIN
        uni.hideToast()
        // #endif
        // #ifdef MP-WEIXIN
        wx.hideToast()
        // #endif
        state.dataList = data
        state.loading = false
    } catch (error) {
        console.error('加载附件列表失败:', error)
        state.loading = false
    } finally {
        state.loading = false
    }
}
// 删除附件 下载
const handlerShowDownload = (item) => {
    state.isShowdownload = true
    state.attachmentId = item.id
    state.fileParams = item
}

// 查看附件
const handlerViewAttach = (item) => {
    previewFileByUrl(item.url)
    // #ifndef MP-WEIXIN
    // window.open(`${import.meta.env.VITE_BASE_PREVIEW}?url=${encodeURIComponent(Base64.encode(item.url))}`)
    // #endif
    // #ifdef MP-WEIXIN
    return
    _isDownload.value = true
    const fileName = item.name;
    wx.showLoading({
        title: "加载中...",
    });
    wx.downloadFile({
        url: item.url,
        success: (res) => {
            // 下载成功
            if (res.statusCode === 200) {
                // 直接预览临时文件（不保存）
                uni.openDocument({
                    filePath: res.tempFilePath,
                    fileType: getFileType(fileName),
                    success: () => {
                        _isDownload.value = false
                        console.log('预览成功')
                        wx.hideLoading();
                    },
                    fail: (err) => {
                        wx.hideLoading();
                        wx.showToast({
                            title: '不支持预览此文件类型',
                            icon: 'none'
                        });
                        _isDownload.value = false
                    }
                });
            }
        },
        fail: (err) => {
            wx.hideLoading();
            wx.showToast({
                title: '预览失败',
                icon: 'none'
            });
            console.error('预览失败:', err);
            _isDownload.value = false
        }
    })
    // #endif
}

// 根据文件名获取文件类型
function getFileType(fileName) {
    const extension = fileName.split('.').pop().toLowerCase();
    switch (extension) {
        case 'pdf': return 'pdf';
        case 'doc': case 'docx': return 'doc';
        case 'xls': case 'xlsx': return 'xls';
        case 'ppt': case 'pptx': return 'ppt';
        case 'txt': return 'txt';
        default: return '';
    }
}

// 下载附件
const handleDownload = () => {
    const item = state.fileParams
    const fileName = item.name;
    const link = document.createElement("a");
    link.style.display = "none";
    link.href = item.url;
    link.setAttribute("download", fileName);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    state.isShowdownload = false
}

// 确定删除
const handleSaveDeleteOrder = () => {
    http.get("/cloud//official-doc/document-file/delete", { id: state.attachmentId }).then(({ data }) => {
        deletePopup.value.close()
        state.isShowdownload = false
        initPage()
    })
}


watch(() => _formListData.value, val => {
    if (val.id) {
        state.officialDocumentId = val.id
        state.fileUrl = val.nodeFileUrl || val.fileUrl
        // 重置分页状态
        state.pageNo = 1
        state.finished = false
        initPage()
    }
}, { immediate: true, deep: true })

onLoad(async (item) => {
    Object.keys(item).forEach((key) => {
        item[key] = decodeURIComponent(item[key])
    })
    state.operate = item.operate || ''
    state.id = item.id
    state.type = item.type
    state.showType = item.showType

})

</script>

<style lang='scss' scoped>
.attachment {
    padding: 22rpx 23rpx 0;
    height: calc(100vh - 360rpx);
    /* #ifdef MP-WEIXIN */
    height: calc(100vh - 425rpx);
    /* #endif */

    overflow: hidden auto;

    &.active {
        height: calc(100vh - 210rpx);
    }

    .list-item {
        border-bottom: 20rpx solid #F6F6F6;

        :deep(.uni-list-item) {
            border-radius: 16rpx;
        }

        .slot-box {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex: 1;
        }
    }


    .footer {
        position: fixed;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        box-sizing: border-box;
        background: #7575756e;
        z-index: 9;

        .download {
            display: flex;
            justify-content: space-evenly;
            position: absolute;
            left: 0;
            bottom: 0;
            box-sizing: border-box;
            background: $uni-text-color-inverse;
            width: 100vw;
            padding: 30rpx 30rpx 50rpx;
            text-align: right;
            z-index: 9999;

            .btn {
                text-align: center;
                font-weight: 400;
                font-size: 24rpx;
                color: #333333;
            }

            .mini-btn {
                margin: 0 10rpx;
                border: 1px solid $uni-text-color-grey;
                background-color: $uni-text-color-inverse;

                &[type="primary"] {
                    background-color: $uni-color-primary;
                    border: 1px solid $uni-color-primary
                }

            }
        }
    }

    .delete-popup-content {
        padding: 20rpx 30rpx;
        width: 80vw;

        .header {
            position: relative;
            text-align: center;

            .title {
                font-weight: 500;
                font-size: 34rpx;
                color: #333333;
            }

            .close {
                position: absolute;
                right: 0;
                top: 0;
            }
        }

        .body {
            text-align: center;
            margin: 60rpx 0;

            :deep(.swiper-box) {
                height: 848rpx !important;

                .uni-list-item__extra-text {
                    font-size: 28rpx;
                }

                .uni-list-item__extra {
                    flex: 2;
                }
            }

            :deep(.uni-swiper__dots-box) {
                height: 45rpx;
            }

            :deep(.uni-list-item__extra-text) {
                max-height: 300rpx;
                overflow: hidden auto;
            }
        }

        .footer-btn {
            display: flex;

            .mini-btn {

                &[type="primary"] {
                    background-color: $uni-color-primary;
                }
            }
        }


    }
}
</style>