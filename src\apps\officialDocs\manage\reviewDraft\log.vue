<template>
    <view class='log' :class="{ active: !_formListData.taskId }">
        <uv-steps :current="currentCompt" direction="column" dot active-color="#00b781">
            <uv-steps-item v-for="item in state.process" :key="item.id">
                <template v-slot:title>
                    <view class="log_item">
                        <view class="title_time">
                            <view class="title">
                                <text>{{ item.name }}</text>
                                <text style="margin: 0 20rpx;">{{ item.outcomeCode }}</text>
                            </view>
                            <view class="time">{{ item.approvalTime }}</view>
                        </view>
                        <view class="user_status">
                            <view class="user_list">
                                <view class="user_item" v-for="(user, index) in item.userInfoList" :key="index">
                                    <view class="user">
                                        <image class="usr_icons" :src="user.avatar" />
                                        <view>{{ user.name }}</view>
                                        <image class="status_icons"
                                            :src="item.id == 'root' || user.outcome == 2 ? iconsStatus[0] : ''" />
                                    </view>
                                    <text class="comment">{{ user.comment }}</text>
                                </view>
                            </view>
                        </view>
                    </view>
                </template>
            </uv-steps-item>
        </uv-steps>
        <yd-empty v-if="!state.loading && !state.process.length" text="暂无数据" />

    </view>
</template>

<script setup>
import useStore from "@/store"
const { officialDocs } = useStore()
const _formListData = computed(() => officialDocs.getFormListData)
const iconsStatus = ['https://file.1d1j.cn/cloud-mobile/officialDocs/succesStatus.png', 'https://file.1d1j.cn/cloud-mobile/officialDocs/failedStatus.png']

const state = reactive({
    active: 0,
    procInstId: '',
    process: [],
    loading: false
})

async function initPage() {
    uni.showToast({
        title: '加载中...',
        icon: "loading"
    })
    state.loading = true
    try {
        const { data } = await http.get("/cloud/official-doc/workflow/getProcFlowElementList", { procInstId: state.procInstId })
        state.process = data
        state.loading = false
        uni.hideToast()
    } catch (error) {
        state.loading = false
    } finally {
        state.loading = false
    }
}

// 归档
const isGuiDang = computed(() => {
    return ["GUI_DANG"].includes(_formListData.value.status || '')
})
const currentCompt = computed(() => {
    return isGuiDang.value ? state.process.length : state.process.length - 2
})
watch(() => _formListData.value, val => {
    state.procInstId = val.procInstId
    state.procInstId && initPage()
}, { immediate: true, deep: true })
</script>

<style lang='scss' scoped>
.log {
    margin-top: 20rpx;
    background-color: $uni-text-color-inverse;
    padding: 30rpx;
    max-height: calc(100vh - 420rpx);
    /* #ifdef MP-WEIXIN */
    max-height: calc(100vh - 484rpx);
    /* #endif */
    overflow: hidden auto;

    &.active {
        max-height: calc(100vh - 270rpx);
    }

    .log_item {
        .title_time {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .title {
                font-weight: 400;
                font-size: 28rpx;
                color: $uni-text-color;
            }

            .time {
                font-weight: 400;
                font-size: 24rpx;
                color: $uni-text-color-grey;
            }
        }

        .user_status {
            font-weight: 400;
            font-size: 24rpx;
            color: #666666;

            .user_list {
                display: flex;
                flex-wrap: wrap;

                .user_item {
                    margin: 10rpx 10rpx 10rpx 0;
                    display: flex;

                    .user {
                        position: relative;
                        width: 80rpx;
                        text-align: center;

                        .people_icons {
                            width: 76rpx;
                            height: 76rpx;
                        }

                        .usr_icons {
                            width: 60rpx;
                            height: 60rpx;
                            border-radius: 50%;
                            background-color: #999;
                        }

                        .status_icons {
                            width: 30rpx;
                            height: 30rpx;
                            border-radius: 50%;
                            position: absolute;
                            top: 32rpx;
                            right: 0;
                        }
                    }

                    .comment {
                        margin-left: 10rpx;
                        flex: 1;
                    }
                }
            }
        }
    }

    :deep(.uv-steps-item) {
        .uv-steps-item__content {
            margin: 0 0 0 10rpx !important;
        }
    }


}
</style>