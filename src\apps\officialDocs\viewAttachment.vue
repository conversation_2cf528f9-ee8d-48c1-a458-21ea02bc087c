<template>
    <view class='view_attachment'>
        <NavBar title="查看附件" :clickLeft="clickLeft" />
        <view class="content">
            <view class="qz-item" v-for="(item, idx) in state.previewUrls" :key="idx" :id="`page-${idx}`">
                <img :src="item" alt="" class="a4-page">
            </view>
            <yd-empty class="empty" v-if="!state.previewUrls.length">暂无数据</yd-empty>
        </view>
    </view>
</template>

<script setup>
import NavBar from "./components/navBar.vue"
const state = reactive({
    id: '',
    operate: "",
    type: '',
    showType: '',
    previewUrls: [],
})
function clickLeft() {
    uni.navigateBack()
}
const initPage = async () => {
    uni.showToast({
        title: '加载中...',
        icon: "loading"
    })
    const { data } = await http.post("/cloud/official-doc/document/preview", { documentId: state.id })
    state.previewUrls = data.previewUrls
    uni.hideToast()
}


onLoad(async (item) => {
    Object.keys(item).forEach((key) => {
        item[key] = decodeURIComponent(item[key])
    })
    state.operate = item.operate || ''
    state.id = item.id
    state.type = item.type
    state.showType = item.showType
    initPage()
})
</script>

<style lang='scss' scoped>
.view_attachment {
    .content {
        margin: 20rpx 0 140rpx;
        background-color: $uni-text-color-inverse;

        .a4-page,
        .qz-item {
            width: 100vw;
        }

        .empty {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
    }
}
</style>