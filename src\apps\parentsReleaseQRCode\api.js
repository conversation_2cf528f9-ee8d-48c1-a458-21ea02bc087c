import axios from "@/utils/http.js";

// 校验放行二维码是否过期
export function getPassSettingQrcodeCheck(params) {
  return axios.get("/cloud/quick/passSetting/qrcodeCheck", params);
}

// 获取孩子信息
export function getFaceList(params) {
  //
  return axios.get(
    "/attweb/attendanceSignTask/app/yourChildren" ||
      "/app/work/selectParentStudent",
    params
  );
}

// 场地 获取放行设置
export function getSiteList(data) {
  return axios.post(
    "/cloud/quick/passSetting/getSchoolQuickPassSettingByType",
    data
  );
}

// 家长放行
export function getQuickPassEltern(data) {
  return axios.post("/cloud/quick/passSetting/quickPassEltern", data);
}
