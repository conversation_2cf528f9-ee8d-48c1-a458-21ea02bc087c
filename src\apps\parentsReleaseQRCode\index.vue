<template>
    <view class="releaseForm"></view>
</template>
<script setup>
import { onMounted } from "vue"
import { getPassSettingQrcodeCheck } from "./api"
const params = { isWx: null, random: "" }
onLoad((item) => {
    params.isWx = item.isWx
    params.random = item.random
})
const init = () => {
    uni.showLoading({
        title: "二维码校验中......"
    })
    getPassSettingQrcodeCheck({ random: params.random })
        .then(({ data }) => {
            // releaseError 放行失败  releaseForm 放行成功
            navigateTo({
                url: `/apps/parentsReleaseQRCode/${data.status == 1 ? "releaseError" : "releaseForm"}/index`,
                query: params
            })
        })
        .catch((err) => {
            console.log(err)
        })
        .finally(() => {
            uni.hideLoading()
        })
}
onMounted(() => {
    window.parent.postMessage({ isLoading: true }, "*")
    init()
})

window.addEventListener("message", function (event) {
    // 检查消息来源是否可信
    if (event.data.name === "updateInit") {
        // 调用iframe内的函数
        params.random = event.data.random
        init()
    }
})
</script>
<style lang="scss" scoped>
:deep(.uni-checkbox-input),
:deep(.uni-radio-input) {
    border-radius: 50%;
    overflow: hidden;

    &:hover {
        border-color: var(--primary-color);
    }

    svg {
        color: $uni-bg-color;
        background: var(--primary-color);
        transform: translate(-50%, -50%) scale(1);

        &:focus {
            outline-color: var(--primary-color);
            outline-style: auto;
            outline-width: 5px;
        }

        path {
            fill: $uni-bg-color;
        }
    }
}

.releaseForm {
    padding: 30rpx;
    overflow: hidden;

    .form-item {
        background: #ffffff;
        border-radius: 20rpx;
        padding: 30rpx;

        .reset-input {
            border: none;
            height: 88rpx;
            background-color: #f6f6f6 !important;
            border-radius: 46rpx;

            &.placeholder {
                text-indent: 40rpx;
                align-items: center;
                color: #999;
                line-height: 88rpx;
            }
        }
    }

    .uni-list-cell {
        display: flex;
        justify-content: space-between;
        margin: 20rpx 0;
        padding: 22rpx 48rpx;
        background-color: #f6f6f6;
        border-radius: 46rpx;
        border: 2rpx solid transparent;

        &.active {
            border-color: var(--primary-color);
            background-color: #e8fff8;

            :deep(.uni-radio-input) {
                background-color: var(--primary-color) !important;
                border-color: var(--primary-color) !important;
            }
        }
    }

    .res-popup {
        background-color: #ffffff !important;
        border-radius: 20rpx 20rpx 0 0;

        .handler {
            display: flex;
            justify-content: space-between;
            padding: 20rpx 30rpx;
            align-items: center;

            .title {
                flex: 1;
                text-align: center;
            }
        }

        .uni-list {
            display: flex;
            justify-content: space-between;
            margin: 20rpx 0;
            padding: 22rpx 48rpx;
            border-bottom: 1rpx solid #d8d8d8;

            :deep(.uni-checkbox-input) {
                border: none;

                svg {
                    transform: translate(-50%, -50%) scale(1);
                    background-color: #ffffff;

                    path {
                        fill: var(--primary-color);
                    }
                }
            }
        }
    }

    .footer {
        // 页脚定位
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 30rpx;
        background-color: #ffffff;

        .text {
            font-weight: 400;
            font-size: 24rpx;
            color: #666666;
            line-height: 34rpx;
            text-align: left;
            font-style: normal;
        }

        .reset-checkbox {
            font-weight: 400;
            font-size: 24rpx;
            color: #333333;
            text-align: left;
            font-style: normal;
            margin: 24rpx 0;
        }

        .releaseSave {
            background: linear-gradient(90deg, #3cdc6b 0%, var(--primary-color) 100%);
            border-radius: 46rpx;
        }
    }
}
</style>
