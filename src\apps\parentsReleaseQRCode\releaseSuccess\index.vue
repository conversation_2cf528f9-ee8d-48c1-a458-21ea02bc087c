<template>
    <uni-nav-bar :border="false" @clickLeft="routerBack" left-icon="left" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()">
        <view class="title_box">放行成功</view>
    </uni-nav-bar>
    <view class="content">
        <img src="/static/<EMAIL>" alt="" />
        <view class="success-txt"> 放行成功！ </view>
        <view class="tips"> 请在校门口有序等待孩子出校门，确保安全出校！ </view>
    </view>
</template>
<script setup>
const routerBack = () => {
    window.history.back()
}
</script>
<style lang="scss" scoped>
.title_box {
    width: 100%;
    text-align: center;
    line-height: 88rpx;
    font-size: 34rpx;
    font-weight: 500;
    color: #333333;
}

.content {
    // 居中
    position: absolute;
    left: 0;
    right: 0;
    top: 50%;
    margin-top: -150rpx;
    text-align: center;

    img {
        width: 116rpx;
        height: 148rpx;
    }

    .success-txt {
        font-weight: 500;
        font-size: 34rpx;
        color: #333333;
        font-style: normal;
        text-align: center;
        padding: 24rpx 0;
    }

    .tips {
        text-align: center;
        font-weight: 400;
        font-size: 28rpx;
        color: #666666;
        font-style: normal;
    }
}
</style>
