<template>
    <view class="status-badge" :class="statusDictionaries[$props.status].class_">
        <text class="ellipsis">{{ statusDictionaries[$props.status].text }}</text>
    </view>
</template>

<script setup>
const $props = defineProps(["status"])
const statusDictionaries = reactive({
    3: {
        text: "未到巡查时间",
        class_: "not-yet"
    },
    4: {
        text: "已逾期",
        class_: "not-yet"
    },
    0: {
        text: "待巡查",
        class_: ""
    },
    1: {
        text: "已完成",
        class_: "completed"
    },
    2: {
        text: "已失效",
        class_: "invalid"
    }
})
</script>

<style lang="scss" scoped>
.status-badge {
    background: #ffc327;
    border-radius: 10rpx;
    font-weight: 600;
    color: $uni-text-color-inverse;
    text-align: center;
    box-sizing: border-box;
    padding: 4rpx 10rpx;
    margin-left: 30rpx;
    line-height: 40rpx;
    // vertical-align: center;
}
text {
    font-size: 20rpx;
}
.not-yet {
    background: #595959;
}
.completed {
    background: var(--primary-color);
}
.invalid {
    background: #bfbfbf;
}
</style>
