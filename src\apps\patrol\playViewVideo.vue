<template>
    <yd-page-view ref="page" title="监控点名称">
        <video class="video_box" :enable-progress-gesture="false" :src="state.url"></video>
    </yd-page-view>
</template>

<script setup>
let state = reactive({ cameraIndexCode: "", url: "" })

onLoad((params) => {
    const { cameraIndexCode } = params
    state.cameraIndexCode = cameraIndexCode
})

onReady(() => {
    uni.showLoading({
        title: "加载中..."
    })
    http.post("/cloud/patrol/hkMonitorPoint/getHkMonitorPointCamerasTalk", { cameraIndexCode: state.cameraIndexCode })
        .then(({ data }) => {
            state.url = data.url
        })
        .catch((err) => {
            console.log(err)
        })
        .finally(() => {
            uni.hideLoading()
        })
})
</script>

<style lang="scss" scoped>
.video_box {
    width: 100vw;
    height: calc(100vh - 100rpx);
}
</style>
