<template>
    <view class="result" v-if="flag">
        <view class="place">
            <view class="title">
                <text>巡查地点</text>
            </view>
            <view class="place-cell">
                <view class="left">
                    <image class="image" src="@nginx/workbench/patrol/icon-place.svg" alt="" />
                    <text class="place-desc">{{ item.businessName }}</text>
                </view>
                <view class="right">
                    <image class="image" src="@nginx/workbench/patrol/icon-completed.svg" alt="" />
                </view>
            </view>
        </view>
        <div style="height: 10px; background-color: #f9faf9"></div>
        <view class="feedback">
            <view class="title">
                <text>巡查人员</text>
            </view>
            <view class="cell">
                <text class="name">执行人</text>
                <text class="result">{{ item.executeNames }}</text>
            </view>
            <view class="cell">
                <text class="name">完成人</text>
                <text class="result">{{ item.finishUserName }}</text>
            </view>
        </view>
        <div style="height: 10px; background-color: #f9faf9"></div>
        <view class="feedback">
            <view class="title">
                <text>巡查反馈</text>
            </view>
            <view class="cell" v-for="(i, index) in formList" :key="index">
                <text class="name" style="width: auto">{{ i.title }}</text>
                <view class="result active" v-if="i.name == 'ImageUpload'">
                    <image class="reset-img" @click="lookTeacherImage(i.value, idx)" v-for="(item, idx) in i.value" :key="item" mode="scaleToFill" :src="item" />
                </view>
                <text class="result" v-else>{{ i.value || "暂无反馈" }}</text>
            </view>
        </view>
    </view>
</template>

<script setup>
// clockIn 打卡状态：0：已打卡、1:未打卡、2：无需打卡
const pageParams = reactive({})
const item = ref({})
const formList = ref([])
const flag = ref(false)
// 预览图片
function lookTeacherImage(urls, idx) {
    uni.previewImage({
        current: urls[idx], // 当前显示图片的http链接
        urls: urls, // 需要预览的图片http链接列表
        indicator: "number", // 底部工具条的指示器样式，默认为 'number'
        loop: true // 是否可循环预览，默认为 false
    })
}

onUnload(() => {
    if (pageParams.app) {
        routerBack()
    }
})

onLoad((params) => {
    Object.assign(pageParams, params)
    get(pageParams.id)
})
const get = (id) => {
    http.get("/app/patrolDayTaskSite/get", { id: id })
        .then((res) => {
            flag.value = true
            item.value = res.data
            let list = JSON.parse(res.data.formJson)
            list.forEach((i) => {
                if (i.valueType == "Array") {
                    try {
                        if (typeof i.value == "string") {
                            let _value = JSON.parse(i.value)
                            // 如果不是上传图片，就不拼接
                            if (i.name === "ImageUpload") {
                                i.value = _value
                            } else {
                                i.value = _value.join()
                            }
                        } else {
                            i.value = i.value.join()
                        }
                    } catch (error) {
                        console.log(error)
                    }
                }
            })
            formList.value = list
        })
        .catch((err) => {
            // setTimeout(
            //     () => {
            //         pageParams.app ? sendAppEvent("backApp", {}) : uni.navigateBack()
            //     },
            //     pageParams.app ? 2000 : 0
            // )
        })
}
</script>

<style lang="scss" scoped>
.result {
    .reset-img {
        width: 152rpx;
        height: 152rpx;
        margin: 18rpx;
        margin-right: 0;
        border-radius: 8rpx;
        background-color: #eeeeee;
    }

    .place {
        height: 262rpx;
        background: $uni-bg-color;
        box-sizing: border-box;
        padding: 0 30rpx 30rpx;

        .title {
            height: 100rpx;
            font-size: 30rpx;
            font-weight: 600;
            color: #333333;
            border-bottom: 1px solid #d8d8d8;
            display: flex;
            align-items: center;
        }

        .place-cell {
            // height: 102rpx;
            border-radius: 20rpx;
            background: #e7fff6;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-sizing: border-box;
            padding: 16rpx 30rpx;
            margin-top: 30rpx;

            .left {
                display: flex;
                .image {
                    width: 40rpx;
                    height: 48rpx;
                }

                .place-desc {
                    margin: 0 0 0 10rpx;
                    font-size: 30rpx;
                    font-weight: 600;
                    // overflow: hidden;
                    // text-overflow: ellipsis;
                    // white-space: nowrap;
                    width: 450rpx;
                }
            }

            .right {
                display: flex;
                align-items: center;
                .image {
                    width: 100rpx;
                    height: 100rpx;
                }
            }
        }
    }

    .feedback {
        background: $uni-bg-color;
        box-sizing: border-box;
        padding: 0 30rpx 30rpx;
        // min-height: 556rpx;
        margin-top: 20rpx;

        .title {
            height: 100rpx;
            font-size: 30rpx;
            font-weight: 600;
            color: #333333;
            display: flex;
            align-items: center;
        }

        .cell {
            min-height: 96rpx;
            border-top: 1px solid #d8d8d8;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-sizing: border-box;
            padding: 28rpx 0;
            font-size: 26rpx;

            .name {
                width: 200rpx;
                color: #666666;
                font-size: 26rpx;
                font-weight: 400;
            }

            .result {
                color: #333333;
                flex: 1;
                text-align: right;
                font-size: 26rpx;
                font-weight: 400;

                &.active {
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: end;
                }
            }
        }
    }
}
</style>
