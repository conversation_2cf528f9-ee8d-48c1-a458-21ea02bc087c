<template>
    <view class="submit" v-if="show">
        <view class="content">
            <view class="title">
                <text>巡查地点</text>
            </view>
            <view class="place-cell">
                <view class="left">
                    <image class="icon_img" src="@nginx/workbench/patrol/icon-place.svg" alt="" />
                    <view>
                        <text class="place-desc">{{ item.businessName }}</text>
                    </view>
                </view>
                <view class="right">
                    <view class="icon">
                        <status-badge :status="0"></status-badge>
                    </view>
                    <image class="icon_img" src="@nginx/workbench/patrol/icon-place.svg" />
                </view>
            </view>
            <!-- <view class="recently-records" @click="jumpToRecentlyRecords">
        <text>查看最近记录</text>
      </view> -->
        </view>
        <div v-if="item.clockIn == 1" class="clockInTips">
            <p class="status">现场打卡状态：<span class="text">已打卡</span></p>
            <p class="status">现在你在此页面也可以完成巡查任务</p>
        </div>
        <div class="interval"></div>
        <view class="feedback-form" v-if="item.clockIn != 2 && formList.length > 0">
            <view class="title">
                <text>请填写巡查反馈</text>
            </view>
            <view class="form-cell" v-for="(i, index) in formList" :key="index">
                <view class="label_">
                    <span v-if="i.props.required" style="color: red">*</span>
                    <text>{{ i.title }}</text>
                </view>
                <y-checkbox v-if="i.name == 'MultipleSelect'" v-model="i.value" :options="i.props.options" :_props="i.props"></y-checkbox>
                <y-radio v-else-if="i.name == 'SelectInput'" v-model="i.value" :options="i.props.options" :_props="i.props"></y-radio>
                <y-textarea v-else-if="i.name == 'TextareaInput'" v-model="i.value" :placeholder="i.props.placeholder" :_props="i.props"></y-textarea>
                <y-input v-else-if="i.name == 'TextInput'" v-model="i.value" :placeholder="i.props.placeholder" :_props="i.props"></y-input>
                <YImageUpload v-else-if="i.name == 'ImageUpload'" v-model="i.value" :options="i.props.options" :_props="i.props" v-model:saveLoading="saveLoading"></YImageUpload>
            </view>
        </view>
        <view class="btn-wrap" v-if="item.clockIn != 2">
            <button class="btn-submit" type="primary" @click="handleSubmit">提交，完成巡查</button>
        </view>
        <div class="tips" v-if="item.clockIn == 2">
            <p class="p">此任务需要现场打卡，请选择合适的巡查方式</p>
            <p class="p">1、前往巡查场地直接使用班牌的巡查应用完成巡查任务；</p>
            <p class="p">2、前往巡查场地，点击下方扫码按钮扫描现场二维码，完成扫码打卡后方可在此页面完成巡查任务。</p>
            <p class="p">现场打卡状态：<span style="color: #ffc327">未打卡</span></p>
            <view class="btn-tips" @click="Scan">开始扫码</view>
        </div>
    </view>
</template>

<script setup>
import statusBadge from "./components/status-badge.vue"
import YImageUpload from "./components/y-ImageUpload.vue"
import yCheckbox from "./components/y-checkbox.vue"
import yRadio from "./components/y-radio.vue"
import yInput from "./components/y-input.vue"
import yTextarea from "./components/y-textarea.vue"

import { sendAppEvent, checkPlatform } from "@/utils/sendAppEvent.js"
onUnload(() => {
    if (pageParams.app) {
        routerBack()
    }
})

// 打卡状态：1：已打卡、2:未打卡、3：无需打卡
const pageParams = reactive({})
const saveLoading = ref(false)
const show = ref(false)
const item = ref({})
const formList = ref([])
onLoad((params) => {
    Object.assign(pageParams, params)
    get(pageParams.id)
})
const get = (id) => {
    uni.showLoading({
        title: "加载中"
    })
    http.get("/app/patrolDayTaskSite/get", { id: id })
        .then((res) => {
            item.value = res.data
            if (res.data.formJson) {
                let list = JSON.parse(res.data.formJson)
                list.forEach((i) => {
                    if (i.valueType == "Array") {
                        try {
                            i.value = JSON.parse(i.value)
                        } catch (error) {
                            i.value = []
                        }
                    }
                })
                formList.value = list
                console.log(formList.value)
            }
            show.value = true
            uni.hideLoading()
        })
        .catch((err) => {
            uni.hideLoading()
            setTimeout(
                () => {
                    routerBack()
                },
                pageParams.app ? 2000 : 0
            )
        })
}
const scanRes = async (siteId) => {
    console.log(item.value, siteId, item.value.businessId == siteId)
    if (siteId != item.value.businessId) {
        uni.showToast({
            title: "打开场地与该任务场地不是同一个",
            icon: "none",
            duration: 2000
        })
        return false
    }
    http.post("/app/patrolQrCode/siteClockIn", {
        siteId: siteId,
        taskSiteId: pageParams.id
    }).then((res) => {
        setTimeout(() => {
            get(pageParams.id)
        }, 1000)
    })
}
const Scan = () => {
    if (checkPlatform() == "yide-ios-app" || checkPlatform() == "yide-android-app") {
        sendAppEvent("getQrResult", {})
    } else {
        uni.scanCode({
            onlyFromCamera: true,
            success: function (res) {
                console.log(res, "nihao")
                scanRes(res.result)
            }
        })
    }
}

const handleSubmit = () => {
    if (saveLoading.value) {
        return
    }
    let arr = JSON.parse(JSON.stringify(formList.value))
    let flag = false
    arr.forEach((i) => {
        if (i.props.required) {
            if (i.valueType == "Array") {
                if (!(i.value && i.value.length > 0)) {
                    flag = true
                }
            } else {
                if (!i.value) {
                    flag = true
                }
            }
        }
    })
    if (flag) {
        uni.showToast({
            title: "请检查必填项!",
            icon: "none",
            duration: 2000
        })
        return false
    }
    arr.forEach((i) => {
        if (i.valueType == "Array") {
            i.value = JSON.stringify(i.value)
        }
    })
    http.post("/app/patrolDayTaskSite/finishSiteTask", {
        id: pageParams.id,
        formJson: JSON.stringify(arr)
    }).then((res) => {
        uni.showToast({
            title: res.message,
            icon: "none",
            duration: 2000,
            success: () => {
                routerBack()
            }
        })
    })
}
</script>

<style lang="scss" scoped>
page {
    padding-bottom: 100rpx;
}

.interval {
    background-color: #f9faf9;
    height: 40rpx;
}

#reader {
    width: 500rpx;
    height: 600rpx;
}

.clockInTips {
    .status {
        font-size: 30rpx;
        font-weight: 400;
        color: #333333;
        line-height: 50rpx;
        padding: 0 30rpx;
    }

    .text {
        color: var(--primary-color);
    }
}

.tips {
    padding: 0 30rpx;

    .p:nth-child(1) {
        font-size: 30rpx;
        font-weight: 600;
        color: #333333;
        line-height: 42rpx;
    }

    .p:nth-child(2),
    .p:nth-child(3),
    .p:nth-child(4) {
        font-size: 30rpx;
        font-weight: 400;
        color: #333333;
        margin-top: 30rpx;
    }

    .btn-tips {
        width: 690rpx;
        height: 92rpx;
        background: var(--primary-color);
        border-radius: 10rpx;
        margin: 0 auto;
        font-size: 32rpx;
        font-weight: 400;
        color: $uni-text-color-inverse;
        line-height: 92rpx;
        text-align: center;
        margin-top: 78rpx;
    }
}

.submit {
    .title {
        height: 100rpx;
        font-size: 30rpx;
        font-weight: 600;
        color: #333333;
        display: flex;
        align-items: center;
        border-bottom: 1px solid #d8d8d8;
    }

    .content {
        background: $uni-bg-color;
        box-sizing: border-box;
        padding: 0 30rpx 30rpx;

        .place-cell {
            border-radius: 20rpx;
            background: #fcf3d9;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-sizing: border-box;
            padding: 16rpx 30rpx;
            margin-top: 30rpx;

            .left {
                display: flex;
                flex: 1;
                .icon_img {
                    width: 40rpx;
                    height: 48rpx;
                }

                .place-desc {
                    margin: 0 0 0 10rpx;
                    font-size: 30rpx;
                    font-weight: 600;
                    width: 450rpx;
                }
            }

            .right {
                display: flex;
                align-items: center;

                .icon_img {
                    width: 100rpx;
                    height: 100rpx;
                }
            }
        }

        .recently-records {
            text-align: right;
            font-size: 26rpx;
            font-weight: 400;
            color: var(--primary-color);
            margin-top: 30rpx;
        }
    }

    .feedback-form {
        background: $uni-bg-color;
        box-sizing: border-box;
        padding: 0 30rpx 30rpx;

        .form-cell {
            .label_ {
                font-size: 28rpx;
                font-weight: 400;
                color: #333333;
                margin: 20rpx 0 20rpx;
            }
        }
    }

    .btn-wrap {
        box-sizing: border-box;
        padding: 50rpx 30rpx 0;

        .btn-submit {
            height: 46px;
            background: var(--primary-color);
            border-radius: 10rpx;
            font-size: 32rpx;
            font-weight: 400;
            color: $uni-text-color-inverse;
            text-align: center;
            line-height: 46px;
        }
    }
}
</style>
