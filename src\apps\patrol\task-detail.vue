<template>
    <view class="task-detail">
        <view class="task-desc">
            <text class="title">{{ item.name }}</text>
            <text class="time">开始时间：{{ item.taskStartTime }}</text>
            <text class="time">截止时间：{{ item.taskEndTime }}</text>
            <text class="member">执行人：{{ item.users }}</text>
            <!-- <text class="task-content"
        >任务描述：一年级所有班级上午课程巡查，请根据课程上课情况如实填写反馈表单。</text
      > -->
            <view class="status">
                <text>任务状态: </text>
                <status-badge :status="item.status || 0"></status-badge>
            </view>
        </view>
        <div style="background-color: #f9faf9; height: 10px"></div>
        <view class="place-list">
            <view class="title">
                <text>巡查地点</text>
            </view>
            <view class="place-cell" :class="{ completed: j.status == 1, nottime: item.status == 3 }" v-for="(j, index) in list" :key="index" @click="handleJumpTo(j)">
                <view class="left">
                    <image class="icon_place" src="@nginx/workbench/patrol/icon-place.svg" />
                    <view>
                        <text class="place-desc">{{ j.siteName }}</text>
                        <p class="p">执行人：{{ j.userName }}</p>
                        <p class="p">
                            监控点：
                            <!-- 点击触摸事件 -->
                            <text style="width: 150rpx; display: inline-block" v-if="j.monitorPointNum" @click.stop="handlerViewVideo(j)">{{ j.monitorPointNum }} <text class="icons" /></text>
                            <text v-else>-</text>
                        </p>
                    </view>
                </view>
                <view class="right">
                    <image class="icon_completed" src="@nginx/workbench/patrol/icon-completed.svg" alt="" v-if="j.status == 1" />
                    <status-badge :status="j.status" v-else></status-badge>
                </view>
            </view>
        </view>

        <uni-popup ref="notYetPopupRef" type="center" :safe-area="false">
            <view class="not-yet-pop">
                <view class="message-content">
                    <text>当前任务未到巡查时间，无法完成任务，请等待</text>
                </view>
                <view class="btn-confirm" @click="handlePopClose">
                    <text>知道了</text>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script setup>
import statusBadge from "./components/status-badge.vue"

import { onPullDownRefresh } from "@dcloudio/uni-app"

const notYetPopupRef = ref()
const pageParams = reactive({})
const item = ref({})
const list = ref([])

onPullDownRefresh(() => {
    get(pageParams.id)
    getList(pageParams.id)
    setTimeout(() => {
        uni.stopPullDownRefresh()
    }, 1000)
})
onLoad((params) => {
    Object.assign(pageParams, params)
})
onShow(() => {
    get(pageParams.id)
    getList(pageParams.id)
})
const get = (id) => {
    http.get("/app/patrol/task/getTaskInfo", { taskId: id }).then((res) => {
        item.value = res.data
    })
}
const getList = (id) => {
    uni.showLoading({
        title: "加载中"
    })
    http.post("/app/patrol/task/record/page", { id, pageNo: 1, pageSize: 100 }).then((res) => {
        uni.hideLoading()
        list.value = res.data.list
    })
}
const handleJumpTo = (j) => {
    if (item.value.status == 3) {
        notYetPopupRef.value.open()
    } else if (j.status == 1) {
        navigateTo({
            url: "/apps/patrol/result",
            query: { id: j.taskSiteId }
        })
    } else {
        navigateTo({
            url: "/apps/patrol/submit",
            query: { id: j.taskSiteId }
        })
    }
}

const handlePopClose = () => {
    notYetPopupRef.value.close()
}
// 点击触摸事件
const handlerViewVideo = (j) => {
    navigateTo({
        url: "/apps/patrol/viewVideo",
        query: { id: j.taskSiteId, siteId: j.siteId }
    })
}
</script>

<style lang="scss" scoped>
.task-detail {
    .task-desc {
        height: 448rpx;
        background: $uni-bg-color;
        box-sizing: border-box;
        padding: 30rpx;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        text {
            font-size: 26rpx;
            font-weight: 400;
            color: #333333;
            margin: 0;
        }

        .title {
            font-size: 30rpx;
            font-weight: 600;
        }

        .status {
            display: flex;
            align-items: center;
        }
    }

    .place-list {
        min-height: 100rpx;
        background: $uni-bg-color;
        box-sizing: border-box;
        padding: 0 30rpx 30rpx;
        margin-top: 10px;

        .title {
            height: 100rpx;
            font-size: 30rpx;
            font-weight: 600;
            color: #333333;
            border-bottom: 1px solid #d8d8d8;
            display: flex;
            align-items: center;
        }

        .place-cell {
            border-radius: 20rpx;
            background: #fcf3d9;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-sizing: border-box;
            padding: 16rpx 30rpx;
            margin-top: 30rpx;

            .left {
                display: flex;
                align-items: center;
                flex: 1;

                .icon_place {
                    width: 60rpx;
                    height: 60rpx;
                    margin-right: 10rpx;
                }

                .p {
                    font-weight: 400;
                    margin-top: 10rpx;
                    color: #333333;
                    font-size: 26rpx;
                    margin-top: 16rpx;

                    .icons {
                        display: inline-block;
                        border: 10rpx solid transparent;
                        border-left: 10rpx solid var(--primary-color);
                        margin-left: 10rpx;
                    }
                }

                .place-desc {
                    margin: 0;
                    font-size: 30rpx;
                    font-weight: 600;
                    width: 450rpx;
                }
            }

            .right {
                .icon_completed {
                    width: 40rpx;
                    height: 40rpx;
                }
            }
        }

        .completed {
            background: #e7fff6;
        }

        .nottime {
            background: #f8f8f8 !important;
        }
    }

    .not-yet-pop {
        width: 690rpx;
        height: 328rpx;
        background: $uni-bg-color;
        border-radius: 20rpx;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .message-content {
            font-size: 34rpx;
            font-weight: 400;
            color: #333333;
            text-align: center;
            padding: 68rpx 40rpx 0;

            text {
                display: inline-block;
            }
        }

        .btn-confirm {
            height: 98rpx;
            border-top: 1px solid #d8d8d8;
            margin-top: 64rpx;
            font-size: 28rpx;
            font-weight: 400;
            color: var(--primary-color);
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 0 0 20rpx 20rpx;

            &:active {
                background: #d8d8d8;
            }
        }
    }
}
</style>
