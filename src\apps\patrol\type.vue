<template></template>
<script setup>
import { onLoad } from "@dcloudio/uni-app"
onLoad((params) => {
    //   type 1待完成 2 已完成 3 已逾期
    if (params.type == 2) {
        navigateTo({
            url: "/apps/patrol/result",
            query: { id: params.id, app: 1 }
        })
    } else {
        navigateTo({
            url: "/apps/patrol/result",
            query: { id: params.id, app: 1 }
        })
    }
})
</script>
