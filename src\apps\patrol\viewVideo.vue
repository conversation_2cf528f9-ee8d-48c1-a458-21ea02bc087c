<template>
    <view class="view-video">
        <view class="hander">
            <view class="title">
                <text>巡查地点</text>
            </view>
            <view class="architecture">
                <view class="left">
                    <image class="icon" src="@nginx/workbench/patrol/icon-place.svg" />
                    <text class="place-desc">{{ state.viewVideo.businessName }}</text>
                </view>
                <image class="icon" src="@nginx/workbench/patrol/icon-completed.svg" alt="" v-if="state.viewVideo.status == 1" />
                <status-badge :status="state.viewVideo.status" v-else></status-badge>
            </view>
        </view>
        <view class="monitor-point" v-for="item in state.monitorPoint" :key="item">
            <view class="left">
                <view class="name">
                    <text>监控点</text>
                </view>
                <view class="result">
                    <text>{{ item.monitorPointRegion }}{{ item.monitorPointName ? "/" + item.monitorPointName : "" }}</text>
                </view>
            </view>
            <view class="right" @click="handlerOpenVideo(item)">
                <image class="icon" src="@nginx/workbench/patrol/<EMAIL>" />
            </view>
        </view>
        <uni-popup ref="popupRrrorRef" type="dialog">
            <view class="popup-error">
                <view class="tips"> 当前视频流不支持在移动端播放，请在PC端查看视频 </view>
                <view class="success-txt" @click="popupRrrorRef.close()"> 知道了 </view>
            </view>
        </uni-popup>
    </view>
</template>
<script setup>
import statusBadge from "./components/status-badge.vue"

const popupRrrorRef = shallowRef()
const state = reactive({
    viewVideo: {
        businessName: "",
        status: 0
    },
    monitorPoint: []
})
// 打开视频
const handlerOpenVideo = (item) => {
    navigateTo({
        url: "/apps/patrol/playViewVideo",
        query: { cameraIndexCode: item.cameraIndexCode }
    })
}

const getPatrolDayTaskSite = (id) => {
    http.get("/app/patrolDayTaskSite/get", { id })
        .then(({ data }) => {
            state.viewVideo = data
        })
        .catch((err) => {
            console.log(err)
        })
}
const getListSchoolHkMonitorPoint = (siteId) => {
    http.post("/cloud/patrol/hkMonitorPoint/listSchoolHkMonitorPoint", { siteId })
        .then(({ data }) => {
            state.monitorPoint = data
        })
        .catch((err) => {
            console.log(err)
        })
}

onLoad((params) => {
    getPatrolDayTaskSite(params.id)
    getListSchoolHkMonitorPoint(params.siteId)
})
</script>
<style lang="scss" scoped>
.view-video {
    .hander {
        background-color: $uni-bg-color;
        padding: 30rpx;

        .title {
            font-weight: 600;
            font-size: 30rpx;
            padding-bottom: 30rpx;
            margin-bottom: 30rpx;
            border-bottom: 1rpx solid #d8d8d8;
        }

        .architecture {
            display: flex;
            align-items: center;
            padding: 30rpx;
            box-sizing: border-box;
            justify-content: space-between;

            background: #fcf3d9;
            border-radius: 20rpx;

            .left {
                display: flex;
            }

            .icon {
                width: 50rpx;
                height: 50rpx;
                vertical-align: bottom;
            }

            .place-desc {
                font-weight: 600;
                font-size: 30rpx;
                margin: 0 10rpx;
            }
        }
    }

    .monitor-point {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 30rpx;
        box-sizing: border-box;
        background-color: $uni-bg-color;
        margin: 30rpx;
        border-radius: 20rpx;

        .left {
            .name {
                font-size: 30rpx;
                font-weight: 600;
                margin-bottom: 30rpx;
            }

            .result {
                font-size: 24rpx;
                font-weight: 400;
            }
        }

        .right {
            .icon {
                width: 72rpx;
                height: 72rpx;
            }
        }
    }
}

.popup-error {
    width: 70vw;
    background: $uni-bg-color;
    border-radius: 20rpx;
    text-align: center;
    margin: 60rpx;

    .tips {
        text-align: center;
        padding: 40rpx;
        border-bottom: 1rpx solid #d8d8d8;
        font-weight: 400;
        font-size: 32rpx;
        color: #333333;
    }

    .success-txt {
        color: var(--primary-color);
        line-height: 40rpx;
        font-weight: 400;
        font-size: 14px;
        padding: 20rpx 0;
    }
}
</style>
