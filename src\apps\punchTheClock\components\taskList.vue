<template>
    <div class="my_task">
        <z-paging ref="paging" :auto="false" v-model="pageList" @query="queryList">
            <template #top>
                <uni-nav-bar statusBar fixed :border="false" left-icon="left" @clickLeft="routerBack" title="我的任务" :leftWidth="navBarLeftWidth(80)" :rightWidth="navBarRightWidth(80)">
                    <template v-slot:right>
                        <!-- #ifdef H5 || H5-WEIXIN-->
                        <div class="bar_right" v-if="identityType == 'teacher'" @click="myTask">我发布的</div>
                        <!-- #endif -->
                        <!-- #ifdef APP-PLUS-->
                        <div class="bar_right" v-if="identityType == 'teacher'" @click="myTask">我发布的</div>
                        <!-- #endif -->
                    </template>
                </uni-nav-bar>
                <!-- #ifdef MP-WEIXIN -->
                <div class="bar_right" v-if="identityType == 'teacher'" @click="myTask">我发布的</div>
                <!-- #endif -->
                <uv-tabs :current="1" :scrollable="false" lineColor="var(--primary-color)" :activeStyle="{ color: 'var(--primary-color)' }" :inactiveStyle="{ color: '#999999' }" :list="list" @click="tabsClick"></uv-tabs>
            </template>
            <template #empty>
                <yd-empty text="暂无数据" />
            </template>
            <div class="task_page" v-if="pageList && pageList.length > 0">
                <div class="task_item" @click="taskDetails(item)" v-for="item in pageList" :key="item.id">
                    <div class="left">
                        <div class="title two_ellipsis">
                            <div v-if="item.subjectIdList[0] != -1" class="tag ellipsis">
                                {{ item.subjectList.join() }}
                            </div>
                            <span class="t"> {{ item.taskName }}</span>
                        </div>
                        <span class="date">截止时间 {{ item.publishEnd }}</span>
                    </div>
                    <div class="right" v-if="query.status != 'not_start'">
                        <div
                            class="circle"
                            :style="{
                                height: query.status == 'end' ? '140rpx' : '110rpx',
                                width: query.status == 'end' ? '140rpx' : '110rpx',
                                border: query.status == 'end' ? 'none' : '16rpx solid',
                                borderColor: item.todayHaveSign ? '#88e2c2FF' : '#11c68533',
                                background: query.status == 'in_progress' ? '#fff' : query.status !== 'end' ? 'var(--primary-color)' : 'var(--primary-bg-color)'
                            }"
                        >
                            <span v-if="query.status == 'in_progress'">{{ item.todayHaveSign ? "已打卡" : "未打卡" }}</span>
                            <span v-else>打卡次数</span>
                            <span> {{ item.alreadySignCount }}/{{ item.totalSignCount }} </span>
                        </div>
                    </div>
                </div>
            </div>
        </z-paging>
        <div v-if="identityType == 'teacher'" class="add" @click="addTask">
            <uni-icons type="plusempty" size="30" color="#fff"></uni-icons>
        </div>
    </div>
</template>

<script setup>
const paging = ref(null)
const props = defineProps({
    identityType: {
        type: String,
        default: "teacher"
    }
})

const identityType = computed(() => props.identityType)
const list = ref([
    {
        name: "未开始",
        code: "not_start"
    },
    {
        name: "进行中",
        code: "in_progress"
    },
    {
        name: "已结束",
        code: "end"
    }
])
const pageList = ref([])

const query = ref({
    status: "in_progress"
})

// 调用List数据
function queryList(pageNo, pageSize) {
    const params = {
        ...query.value,
        pageNo,
        pageSize
    }
    http.post("/attweb/attendanceSignTask/app/myTask", params).then(({ data }) => {
        paging.value?.complete(data.list)
    })
}

function myTask() {
    navigateTo({
        url: "/apps/punchTheClock/myTaskList"
    })
}

function taskDetails(item) {
    navigateTo({
        url: "/apps/punchTheClock/taskDetails",
        query: {
            parameter: JSON.stringify(item)
        }
    })
}

function tabsClick(item) {
    query.value.status = item.code
    paging.value?.reload()
}

function addTask() {
    navigateTo({
        url: "/apps/punchTheClock/addTask/index"
    })
}

onShow(() => {
    nextTick(() => {
        paging.value?.reload()
    })
})
</script>

<style lang="scss" scoped>
.my_task {
    background: $uni-bg-color-grey;
    .bar_right {
        text-align: right;
        // #ifdef MP-WEIXIN
        background: $uni-bg-color-grey;
        padding: 10rpx 30rpx;
        // #endif
        font-weight: 400;
        font-size: 28rpx;
        color: var(--primary-color);
        line-height: 40rpx;
    }
    .task_page {
        padding: 20rpx 30rpx;
        min-height: calc(100vh - 128rpx);
        background: $uni-bg-color-grey;
        .task_item {
            padding: 30rpx;
            min-height: 106rpx;
            background: $uni-bg-color;
            box-shadow: 0rpx 16rpx 16rpx 0rpx #dcf5ee80;
            border-radius: 20rpx;
            display: flex;
            justify-content: space-between;
            margin-bottom: 18rpx;
            .left {
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                .title {
                    overflow-wrap: break-word;
                    word-break: break-all;
                    white-space: normal;
                    overflow: hidden;
                    .tag {
                        display: inline-block;
                        max-width: 82rpx;
                        border-radius: 4rpx;
                        border: 1rpx solid var(--primary-color);
                        font-weight: 400;
                        font-size: 20rpx;
                        color: var(--primary-color);
                        line-height: 30rpx;
                        text-align: center;
                        margin-bottom: -4rpx;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        padding: 0 10rpx;
                    }
                    .t {
                        padding-left: 10rpx;
                        line-height: 30rpx;
                        font-weight: 600;
                        font-size: 30rpx;
                        color: $uni-text-color;
                    }
                }
                .date {
                    font-weight: 400;
                    font-size: 24rpx;
                    color: #666666;
                    line-height: 34rpx;
                }
            }
            .right {
                margin-left: 20rpx;
                .circle {
                    width: 110rpx;
                    height: 110rpx;
                    background: $uni-bg-color;
                    border: 16rpx solid #11c68533; /* 圆环的宽度和颜色 */
                    border-radius: 50%;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-direction: column;
                    font-weight: 500;
                    font-size: 26rpx;
                    color: var(--primary-color);
                    line-height: 34rpx;
                }
            }
        }
    }
    .add {
        position: fixed;
        bottom: 226rpx;
        right: 30rpx;
        width: 112rpx;
        height: 112rpx;
        background: var(--primary-color);
        box-shadow: 0rpx 8rpx 8rpx 0rpx #11c68533;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50%;
    }
}
</style>
