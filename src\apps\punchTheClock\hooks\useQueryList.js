export default function (api, other) {
    let query = reactive({
        pageNo: 1,
        pageSize: 10
    })

    let page = reactive({
        total: 1,
        list: [],
        status: "more",
        finished: false
    })
    const getList = () => {
        return new Promise((resolve, reject) => {
            const params = {
                ...query,
                ...other
            }
            if (page.list.length >= page.total) {
                resolve(true)
                return
            } else {
                page.status = "loading"
                http.post(api, params)
                    .then((res) => {
                        page.total = res.data.total || 1
                        query.pageNo = res.data.pageNo + 1
                        page.list.push(...res.data.list)
                        resolve(res)
                    })
                    .catch((err) => {
                        query.pageNo = 1
                        query.pageSize = 10
                        page.total = 1
                        page.list = []
                        page.status = "noMore"
                        reject(false)
                    })
                    .finally(() => {
                        page.status = "noMore"
                    })
            }
        })
    }

    const reset = () => {
        page.list = []
        query.pageNo = 1
        query.pageSize = 10
        getList()
    }

    return {
        query,
        page,
        getList,
        reset
    }
}
