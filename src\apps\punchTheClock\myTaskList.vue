<template>
    <div class="my_task">
        <z-paging ref="paging" :auto="false" v-model="pageList" @query="queryList">
            <template #top>
                <uni-nav-bar statusBar fixed :border="false" left-icon="left" @clickLeft="clickLeft" title="我发布的" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
            </template>
            <div class="task_list">
                <div class="task_item" @click="taskDetails(item)" v-for="(item, index) in pageList" :key="index">
                    <div class="task_title_box">
                        <div class="tip ellipsis" v-if="item.subjectList && item.subjectList[0] != '不限'">
                            <span v-for="(sub, sIndex) in item.subjectList" :key="sIndex">
                                {{ sub }}
                            </span>
                        </div>
                        <div class="title ellipsis">
                            {{ item.taskName }}
                        </div>
                        <div @click.stop="openEdit(item)">
                            <uni-icons type="more-filled" size="20" color="#999999"></uni-icons>
                        </div>
                    </div>
                    <div class="task_content">
                        <div class="describe two_ellipsis">
                            {{ item.taskDescription }}
                        </div>
                        <div class="content_item">
                            <img class="label_img" src="@nginx/workbench/punchTheClock/cycle.png" alt="" />
                            <span class="label">周期：</span>
                            <span class="value"
                                >第<span style="color: var(--primary-color)">{{ item.cycleNowDay }}</span
                                >/{{ item.cycleTotalDay }}天</span
                            >
                        </div>
                        <div class="content_item">
                            <img class="label_img" src="@nginx/workbench/punchTheClock/frequency.png" alt="" />
                            <span class="label">频次：</span>
                            <span class="value">{{ item.frequency }}</span>
                        </div>
                        <div class="content_item">
                            <img class="label_img" src="@nginx/workbench/punchTheClock/target.png" alt="" />
                            <span class="label">布置对象：</span>
                            <span class="value">{{ item.publishScope }}</span>
                        </div>
                        <div class="content_item">
                            <img class="label_img" src="@nginx/workbench/punchTheClock/range.png" alt="" />
                            <span class="label release_scope">发布范围：</span>
                            <span class="value">
                                <span v-for="(s, sindex) in item.publishScopeObjList" :key="sindex"> <span v-if="sindex != 0">、</span>{{ s }} </span>
                            </span>
                        </div>
                        <div class="content_item">
                            <img class="label_img" src="@nginx/workbench/punchTheClock/date.png" alt="" />
                            <span class="label">截止日期：</span>
                            <span class="value">{{ item.publishEnd }}</span>
                        </div>
                        <div class="content_item">
                            <img class="label_img" src="@nginx/workbench/punchTheClock/punchedInNum.png" alt="" />
                            <span class="label">今日已打卡人数：</span>
                            <span class="value">{{ item.todaySignCount }}</span>
                        </div>
                        <div class="content_item">
                            <span class="label">创建时间：</span>
                            <span class="value">{{ item.createTime }}</span>
                        </div>
                        <div
                            class="status"
                            :style="{
                                color: statusColor[item.status]
                            }"
                        >
                            {{ statusText[item.status] }}
                        </div>
                    </div>
                </div>
            </div>
            <template #empty>
                <yd-empty text="暂无数据" />
            </template>
        </z-paging>

        <uni-popup type="bottom" ref="editPopup" background-color="#fff" :borderRadius="'10rpx 10rpx 0rpx 0rpx'">
            <div class="edit_popup">
                <div class="title">
                    <span>打卡任务</span>
                    <uni-icons @click="editPopup.close()" class="icon" type="closeempty" size="20"></uni-icons>
                </div>
                <uni-list :border="false">
                    <uni-list-item title="编辑" clickable @click="editFn" />
                    <uni-list-item title="删除" clickable @click="delectFn" />
                </uni-list>
            </div>
        </uni-popup>
        <div class="add" @click="addTask">
            <uni-icons type="plusempty" size="30" color="#fff"></uni-icons>
        </div>
    </div>
</template>

<script setup>
const paging = ref(null)
const editPopup = ref()
const statusText = ref({
    not_start: "未开始",
    in_progress: "进行中",
    end: "已结束"
})
const statusColor = {
    not_start: "#f0ad4e",
    in_progress: "var(--primary-color)",
    end: "#717171"
}
const pageList = ref([])
const editItem = ref({})

function clickLeft() {
    uni.navigateBack()
}

function addTask() {
    navigateTo({
        url: "/apps/punchTheClock/addTask/index"
    })
}

function editFn() {
    const { status, id } = editItem.value
    console.log(id)
    if (status != "in_progress") {
        navigateTo({
            url: "/apps/punchTheClock/addTask/index",
            query: {
                id
            }
        })
    } else {
        uni.showToast({
            title: `任务${statusText.value[status]}无法编辑`,
            icon: "none",
            duration: 2000
        })
    }
    editPopup.value.close()
}

function delectFn() {
    http.post("/attweb/attendanceSignTask/app/delete", [editItem.value.id]).then((res) => {
        editPopup.value.close()
        uni.showToast({
            title: res.message,
            icon: "none",
            duration: 2000
        })
        paging.value?.reload()
    })
}

function openEdit(item) {
    editItem.value = item
    editPopup.value.open()
}

function taskDetails(item) {
    console.log(item)
    const { id, cycleNowDay, cycleTotalDay } = item
    navigateTo({
        url: "/apps/punchTheClock/myTaskDetails",
        query: {
            id,
            totalSignCount: cycleTotalDay,
            alreadySignCount: cycleNowDay
        }
    })
}

function queryList(pageNo, pageSize) {
    http.post("/attweb/attendanceSignTask/app/list", { pageNo, pageSize }).then((res) => {
        paging.value?.complete(res.data.list)
    })
}

onShow(() => {
    nextTick(() => {
        paging.value?.reload()
    })
})
</script>

<style lang="scss" scoped>
.my_task {
    min-height: calc(100vh - 88rpx);
    width: calc(100% - 60rpx);
    background: $uni-bg-color-grey;
    .task_list {
        padding: 20rpx 30rpx;
        .task_item {
            margin-bottom: 20rpx;
            min-height: 404rpx;
            background: $uni-bg-color;
            box-shadow: 0rpx 8rpx 8rpx 0rpx #dcf5ee80;
            border-radius: 12rpx;
            padding: 30rpx;
            .task_title_box {
                border-bottom: 1rpx solid $uni-border-color;
                height: 42rpx;
                display: flex;
                padding-bottom: 20rpx;
                align-items: center;
                .tip {
                    min-width: 64rpx;
                    max-width: 64rpx;
                    height: 32rpx;
                    border-radius: 4rpx;
                    border: 1rpx solid var(--primary-color);
                    color: var(--primary-color);
                    font-weight: 400;
                    font-size: 20rpx;
                    line-height: 32rpx;
                    text-align: center;
                    flex: 1;
                    margin-top: 4rpx;
                }
                .title {
                    flex: 1;
                    font-weight: 500;
                    font-size: 30rpx;
                    color: $uni-text-color;
                    margin: 0rpx 10rpx;
                    width: calc(100% - 64rpx - 40rpx);
                }
            }
            .task_content {
                margin-top: 20rpx;
                .describe {
                    font-family:
                        PingFangSC,
                        PingFang SC;
                    font-weight: 400;
                    font-size: 28rpx;
                    color: $uni-text-color;
                    line-height: 40rpx;
                }
                .content_item {
                    margin-top: 20rpx;
                    display: flex;
                    align-items: flex-start;
                    .label_img {
                        width: 30rpx;
                        height: 30rpx;
                        margin: 6rpx 6rpx 0rpx 0rpx;
                    }
                    .label,
                    .value {
                        margin-left: 6rpx;
                        font-weight: 400;
                        font-size: 28rpx;
                        color: $uni-text-color;
                        line-height: 40rpx;
                    }
                    .release_scope {
                        min-width: 150rpx;
                    }
                }
                .status {
                    text-align: right;
                    font-weight: 400;
                    font-size: 28rpx;
                    color: $uni-color-warning;
                    line-height: 40rpx;
                }
            }
        }
    }
    .edit_popup {
        padding: 30rpx;
        border-radius: 10rpx 10rpx 0rpx 0rpx;
        padding-bottom: 60rpx;
        .title {
            width: 100%;
            font-size: 34rpx;
            font-weight: 600;
            color: $uni-text-color;
            text-align: center;
            margin-bottom: 20rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            .icon {
                position: absolute;
                right: 20rpx;
            }
        }
    }
    :deep(.uni-list-item__content) {
        text-align: center;
    }
    .add {
        position: fixed;
        bottom: 226rpx;
        right: 30rpx;
        width: 112rpx;
        height: 112rpx;
        background: var(--primary-color);
        box-shadow: 0rpx 8rpx 8rpx 0rpx #11c68533;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50%;
    }
}
</style>
