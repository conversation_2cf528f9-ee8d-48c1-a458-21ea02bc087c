<template>
    <view class="detailsPage">
        <!-- 头部 -->
        <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="活动详情" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
        <view class="detailsinfoBox">
            <view class="optionList">
                <view class="titleInfoBox">
                    <view>
                        <image class="coverImg" mode="aspectFill" :src="detailsObj.coverImg || '@nginx/workbench/registration/coverImg.png'"></image>
                    </view>
                    <view class="infoBoxRight">
                        <view class="rightTitle">
                            <view class="rightTitle_status">
                                <text class="rightTitle_status_name">{{ detailsObj.statusName }}</text>
                            </view>
                            <text class="rightTitle_name">{{ detailsObj.activityName }}</text>
                        </view>

                        <view class="rightTime">
                            <view class="rightTime_name">{{ detailsObj.createBy }}创建于{{ detailsObj.createTime }}</view>
                            <view class="lookTable" @click="intoTable">查看报名表</view>
                        </view>
                    </view>
                </view>
            </view>
            <view class="textBox">
                <view class="textBoxDec">
                    <sc-ellipsis v-if="detailsObj.activityIntroduction" :text="detailsObj.activityIntroduction" fontSize="28rpx" :maxWidth="maxWidth" :maxLine="6" suffixOpen="展开全部" suffixClose="收起" suffixColor="var(--primary-color)"></sc-ellipsis>
                </view>
            </view>

            <view class="setItem">
                <view class="setItem_Box">
                    <view class="setItem_title">活动时间</view>
                    <view>
                        <text>{{ detailsObj.activityStartTime }}</text>
                        <text class="time_tip">至</text>
                        <text>{{ detailsObj.activityEndTime }}</text>
                    </view>
                </view>
            </view>
            <view class="setItem">
                <view class="setItem_Box">
                    <view class="setItem_title">报名时间</view>
                    <view>
                        <text>{{ detailsObj.registerStartTime }}</text>
                        <text class="time_tip">至</text>
                        <text>{{ detailsObj.registerEndTime }}</text>
                    </view>
                </view>
            </view>
            <view class="setItem">
                <view class="setItem_Box">
                    <view class="setItem_title">报名对象范围</view>
                    <view style="text-align: right">
                        {{ comRangeList }}
                    </view>
                </view>
            </view>
            <view class="setItem">
                <view class="setItem_Box">
                    <view class="setItem_title">活动备注</view>
                    <view style="text-align: right">
                        {{ detailsObj.activityRemarks }}
                    </view>
                </view>
            </view>
            <view class="setItem">
                <view class="setItem_Box">
                    <view class="setItem_title">子项设置</view>
                    <view>
                        {{ detailsObj.subType === 1 ? "各子项不单独报名" : "各子项单独报名" }}
                    </view>
                </view>
            </view>

            <view class="select_status" @click="upThisList">
                <view class="select_status_name"> 报名状态 </view>
                <view class="packup">
                    <view
                        class="packupName"
                        :style="{
                            color: getuserEnrollStatus(detailsObj.userEnrollStatus).stateColor
                        }"
                        v-if="detailsObj.subType === 1"
                        >{{ getuserEnrollStatus(detailsObj.userEnrollStatus).text }}</view
                    >
                    <uv-icon v-if="state.upOrdownward" name="arrow-down" size="14"></uv-icon>
                    <uv-icon v-if="!state.upOrdownward" name="arrow-right" size="14"></uv-icon>
                </view>
            </view>
            <view class="cardListBoxList">
                <view class="cardListBox" v-for="item in detailsObj.enrollSubList" :key="item.id" @click="danduClickItem(item)">
                    <view class="cardListItem">
                        <view class="radioItem" v-if="detailsObj.subType === 2 && state.belong !== '2'">
                            <image class="radioItemImg" src="@nginx/workbench/registration/noRadio.png" v-if="item.subUserEnrollStatus === 1 && !isSelected(item)"></image>
                            <image class="radioItemImg" src="@nginx/workbench/registration/disRadio.png" v-if="item.subUserEnrollStatus === 2 || item.subUserEnrollStatus === 3 || item.subUserEnrollStatus === 4"></image>
                            <image class="radioItemImg" src="@nginx/workbench/registration/yesEadio.png" v-if="item.subUserEnrollStatus === 1 && isSelected(item)"></image>
                        </view>
                        <view class="cardListItem_dec">
                            <view style="width: 85%" class="decTitle" v-if="detailsObj.subType === 2">
                                {{ item.subName }}
                            </view>
                            <view class="decTitle" v-if="detailsObj.subType === 1">
                                {{ item.subName }}
                            </view>

                            <!-- <view class="rangeBox">
                                <text class="titleName">报名对象范围</text>
                                <text class="titleName_result"> {{ item.rangeList }}</text>
                            </view> -->
                            <view class="restrictBox">
                                <text class="titleName">报名数量限制</text>
                                <text class="titleName_result">{{ item.numQuantity || "无限制" }}</text>
                            </view>
                            <view class="numBox">
                                <text class="titleName">已报名数量</text>
                                <text class="titleName_result">{{ item.subEnrollCount }}</text>
                            </view>
                        </view>

                        <view class="status_icon_box" v-if="detailsObj.subType === 2">
                            <view
                                class="status_icon"
                                :style="{
                                    backgroundColor: getuserEnrollStatus(item.subUserEnrollStatus).color
                                }"
                            >
                                <text
                                    class="status_icon_name"
                                    :style="{
                                        color: getuserEnrollStatus(item.subUserEnrollStatus).fontColor
                                    }"
                                >
                                    {{ getuserEnrollStatus(item.subUserEnrollStatus).text }}</text
                                >
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <view class="footBoxOut">
            <view class="footBox">
                <view v-if="state.belong === '2'" class="operationBtn">
                    <view class="delBox" @click="delBoxBtn">
                        <text class="delBox_name">删除</text>
                    </view>
                    <view class="closeBox" @click="closeBoxBtn">
                        <text class="closeBox_name">关闭</text>
                    </view>
                    <view class="editBox" @click="editBoxBtn">
                        <text class="editBox_name">编辑</text>
                    </view>
                </view>
                <button class="publish" @click="promptly" v-else :loading="confirmLoading">
                    <text class="publishName">立即报名</text>
                </button>
            </view>
        </view>
    </view>
</template>
<script setup>
import ScEllipsis from "./components/sc-ellipsis.vue"
const instance = getCurrentInstance()
const state = ref({
    belong: "all",
    upOrdownward: true,
    activitySubIds: [],
    socleParams: {}
})
const confirmLoading = ref(false)
const maxWidth = ref(0)

const detailsObj = ref({
    rangeList: []
})

const upThisList = () => {
    state.value.upOrdownward = !state.value.upOrdownward
}
const statusObj = {
    1: {
        text: "未开始",
        color: "#FFAC08"
    },
    2: {
        text: "报名中",
        color: "var(--primary-color)"
    },
    3: {
        text: "已结束",
        color: "#ADADAD"
    },
    4: {
        text: "已关闭",
        color: "#ADADAD"
    },
    default: {
        text: "-",
        color: ""
    } // 默认值存储在 default 属性中
}

function getStatusObj(key) {
    return statusObj[key] || statusObj.default
}

const userEnrollStatus = {
    0: {
        text: "未开始",
        color: "#E4E4E4",
        fontColor: "#B3B3B3",
        stateColor: "#B3B3B3"
    },
    1: {
        text: "可报名",
        color: "var(--primary-color)",
        fontColor: "#FFFFFF",
        stateColor: "var(--primary-color)"
    },
    2: {
        text: "无资格",
        color: "#E4E4E4",
        fontColor: "#B3B3B3",
        stateColor: "#B3B3B3"
    },
    3: {
        text: "名额已满",
        color: "#E4E4E4",
        fontColor: "#B3B3B3",
        stateColor: "#B3B3B3"
    },
    4: {
        text: "已报名",
        color: "#5EA9F9",
        fontColor: "#FFFFFF",
        stateColor: "#5EA9F9"
    },
    5: {
        text: "已结束",
        color: "#E4E4E4",
        fontColor: "#B3B3B3",
        stateColor: "#B3B3B3"
    },
    6: {
        text: "已关闭",
        color: "#E4E4E4",
        fontColor: "#B3B3B3",
        stateColor: "#B3B3B3"
    },
    7: {
        text: "报名失败",
        color: "#E4E4E4",
        fontColor: "#B3B3B3",
        stateColor: "#B3B3B3"
    },
    default: {
        text: "-",
        color: ""
    } // 默认值存储在 default 属性中
}

function getuserEnrollStatus(key) {
    return userEnrollStatus[key] || userEnrollStatus.default
}

// 计算颜色
const comColor = computed(() => {
    return getStatusObj(detailsObj.value.status).color
})

//计算是不是要显示
const comShowList = computed(() => {
    return state.value.upOrdownward ? "unset" : "none"
})

const comRangeList = computed(() => {
    return detailsObj.value.rangeList.map((item) => item.rangeName).join("、")
})

const handlerDetailsInfo = (data) => {
    const params = {
        activityId: data.activityId
    }
    if (state.value.saveParam.studentId) {
        params.studentId = state.value.saveParam.studentId
    }
    http.post("/app/activity/enroll/info", params).then((res) => {
        // 查到详情回显一下
        detailsObj.value = res.data
    })
}

// 选中这个item
const selectItemThis = (item) => {
    const index = state.value.activitySubIds.findIndex((i) => i.id === item.id)
    if (~index) {
        state.value.activitySubIds.splice(index, 1)
    } else {
        state.value.activitySubIds.push(item)
    }
}

const isSelected = computed(() => {
    return (item) => !!~state.value.activitySubIds.findIndex((i) => i.id === item.id)
})

// 单独报名点了才有用
const danduClickItem = (data) => {
    if (detailsObj.value.subType === 2) {
        selectItemThis(data)
    }
}

const promptly = () => {
    if (detailsObj.value.subType === 1) {
        // 这里直接报名成功
        confirmLoading.value = true
        http.post("/app/activity/enroll/userEnroll", {
            activityId: detailsObj.value.id,
            studentId: state.value.saveParam.studentId ? state.value.saveParam.studentId : undefined
        })
            .then((res) => {
                handlerDetailsInfo({ activityId: detailsObj.value.id })
                uni.showToast({
                    title: "您已报名此活动",
                    icon: "none"
                })
            })
            .finally(() => {
                confirmLoading.value = false
            })
    } else {
        // 没有子项数组activitySubIds不给提交
        if (state.value.activitySubIds.length) {
            confirmLoading.value = true
            http.post("/app/activity/enroll/userEnroll", {
                activityId: detailsObj.value.id,
                activitySubIds: state.value.activitySubIds.map((item) => item.id),
                studentId: state.value.saveParam.studentId ? state.value.saveParam.studentId : undefined
            })
                .then((res) => {
                    handlerDetailsInfo({ activityId: detailsObj.value.id })
                    state.value.activitySubIds = []
                    uni.showToast({
                        title: "您已报名此活动",
                        icon: "none"
                    })
                })
                .finally(() => {
                    confirmLoading.value = false
                })
        }
    }
}

const delBoxBtn = () => {
    http.post("/app/activity/enroll/delete", {
        activityId: detailsObj.value.id
    }).then((res) => {
        uni.showToast({
            title: "删除成功",
            icon: "none"
        })
        uni.reLaunch({
            url: "/apps/registration/index"
        })
    })
}

const closeBoxBtn = () => {
    if (detailsObj.value.statusName === "报名中") {
        http.post("/app/activity/enroll/close", {
            activityId: detailsObj.value.id
        }).then((res) => {
            uni.showToast({
                title: "关闭成功",
                icon: "none"
            })
            uni.reLaunch({
                url: "/apps/registration/index"
            })
        })
    } else {
        uni.showToast({
            title: "报名中的活动才可关闭",
            icon: "none"
        })
    }
}

// 未开始的才能编辑
const editBoxBtn = () => {
    if (detailsObj.value.statusName === "未开始") {
        navigateTo({
            url: "/apps/registration/creation",
            query: state.value.saveParam
        })
    } else {
        uni.showToast({
            title: "未开始的活动报名才可编辑",
            icon: "none"
        })
    }
}

const intoTable = () => {
    // 进入报名表
    navigateTo({
        url: "/apps/registration/iRForm",
        query: state.value.saveParam
    })
}

onLoad(async (options) => {
    state.value.saveParam = options
    state.value.belong = options.belong
    handlerDetailsInfo(options)
    nextTick(() => {
        uni.createSelectorQuery()
            .in(instance)
            .select(".textBoxDec")
            .boundingClientRect((data) => {
                let screenWidth = uni.getSystemInfoSync().screenWidth // 获取屏幕宽度
                let rpxWidth = (data.width / screenWidth) * 750 // 将px转换为rpx
                console.log("荣容器宽度（rpx）：", rpxWidth)

                maxWidth.value = rpxWidth
            })
            .exec()
    })
})

function clickLeft() {
    uni.navigateBack()
}
</script>
<style lang="scss" scoped>
.detailsPage {
    font-size: 28rpx;
    display: flex;
    flex-direction: column;
}

.detailsinfoBox {
    background-color: $uni-bg-color-grey;
    .optionList {
        background: $uni-bg-color;
        padding: 24rpx 32rpx;
        border-bottom: 1rpx solid $uni-border-color;
    }
    .textBox {
        background: $uni-bg-color;
        padding: 24rpx 32rpx;
        border-bottom: 1rpx solid $uni-border-color;
        .textBoxDec {
            font-weight: 400;
            font-size: 28rpx;
            color: $uni-text-color;
        }
        .textBoxDecAll {
            font-weight: 400;
            font-size: 28rpx;
            color: var(--primary-color);
            line-height: 40rpx;
            text-align: left;
            font-style: normal;
        }
    }
}

.setItem {
    background: $uni-bg-color;
    padding: 0rpx 32rpx;
    .setItem_Box {
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1rpx solid $uni-border-color;
        padding: 36rpx 0rpx;
        .setItem_title {
            padding-right: 24rpx;
            flex-shrink: 0;
        }
    }
}

.titleInfoBox {
    display: flex;
    .infoBoxRight {
        padding: 20rpx 0rpx 20rpx 20rpx;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        flex: 1;
        .rightTitle {
            .rightTitle_status {
                display: inline-flex;
                padding: 4rpx 10rpx;
                background: v-bind(comColor);
                text-align: center;
                border-radius: 9rpx;
                margin-right: 16rpx;
                .rightTitle_status_name {
                    font-weight: 500;
                    font-size: 20rpx;
                    color: $uni-text-color-inverse;
                }
            }
            .rightTitle_name {
                font-weight: 600;
                font-size: 30rpx;
                line-height: 42rpx;
                color: $uni-text-color;
            }
        }
    }
}

.footBoxOut {
    height: 176rpx;
    background-color: $uni-bg-color-grey;
    .footBox {
        position: fixed;
        width: 100%;
        height: 176rpx;
        background: $uni-bg-color;
        bottom: 0px;
        .publish {
            margin: 22rpx 30rpx;
            text-align: center;
            height: 92rpx;
            line-height: 92rpx;
            background: var(--primary-color);
            border-radius: 10rpx;
            .publishName {
                font-weight: 400;
                font-size: 32rpx;
                color: $uni-text-color-inverse;
                line-height: 44rpx;
                text-align: center;
                font-style: normal;
            }
        }
        .operationBtn {
            padding-top: 20rpx;
            display: flex;
            align-items: center;
            justify-content: space-around;
            .delBox {
                width: 212rpx;
                height: 92rpx;
                background: $uni-bg-color;
                border-radius: 10rpx;
                border: 2rpx solid $uni-border-color;
                display: flex;
                align-items: center;
                justify-content: center;
                .delBox_name {
                    font-weight: 400;
                    font-size: 32rpx;
                    color: #666666;
                }
            }
            .closeBox {
                width: 212rpx;
                height: 92rpx;
                background: $uni-bg-color;
                border-radius: 10rpx;
                border: 2rpx solid var(--primary-color);
                display: flex;
                align-items: center;
                justify-content: center;
                .closeBox_name {
                    font-weight: 400;
                    font-size: 32rpx;
                    color: var(--primary-color);
                }
            }
            .editBox {
                width: 212rpx;
                height: 92rpx;
                background: var(--primary-color);
                border-radius: 10rpx;
                display: flex;
                align-items: center;
                justify-content: center;
                .editBox_name {
                    font-weight: 400;
                    font-size: 32rpx;
                    color: $uni-text-color-inverse;
                }
            }
        }
    }
}

.coverImg {
    border-radius: 16rpx;
    width: 90rpx;
    height: 160rpx;
    object-fit: contain; //保持图片的原始宽高比，缩放图片以适应容器的宽度或高度
}

.rightTime {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .rightTime_name {
        font-weight: 400;
        font-size: 24rpx;
        color: #8c8c8c;
        line-height: 34rpx;
    }
    .lookTable {
        font-weight: 400;
        font-size: 24rpx;
        color: var(--primary-color);
        flex-shrink: 0;
        line-height: 34rpx;
    }
}

.select_status {
    padding: 24rpx 32rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .select_status_name {
        font-weight: 600;
        font-size: 30rpx;
        color: $uni-text-color;
        line-height: 44rpx;
    }
    .packup {
        display: flex;
        align-items: center;
        .packupName {
            font-weight: 400;
            font-size: 28rpx;
            text-align: left;
        }
    }
}

.cardListBox {
    padding: 0rpx 24rpx 32rpx;
    .cardListItem {
        position: relative;
        background: $uni-bg-color;
        border-radius: 16rpx;
        padding: 24rpx 32rpx;
        display: flex;
        align-items: center;
        .radioItem {
            flex-shrink: 0;
            .radioItemImg {
                width: 36rpx;
                height: 36rpx;
            }
        }
        .cardListItem_dec {
            padding-left: 10rpx;
            flex: 1;
            .decTitle {
                font-weight: 500;
                font-size: 28rpx;
                color: $uni-text-color;
                line-height: 44rpx;
                padding-bottom: 24rpx;
            }
            .rangeBox {
                padding-bottom: 24rpx;
            }
            .restrictBox {
                padding-bottom: 24rpx;
            }
        }
        .status_icon_box {
            position: absolute;
            top: 0;
            right: 0;
            .status_icon {
                width: 120rpx;
                height: 48rpx;
                text-align: center;
                line-height: 48rpx;
                border-radius: 0rpx 20rpx 0rpx 20rpx;
                .status_icon_name {
                    font-weight: 600;
                    font-size: 24rpx;
                }
            }
        }
    }
}

.titleName {
    display: inline-block;
    width: 268rpx;
    font-weight: 400;
    font-size: 26rpx;
    color: $uni-text-color-grey;
    line-height: 26rpx;
    text-align: left;
}
.titleName_result {
    font-weight: 400;
    font-size: 26rpx;
    color: $uni-text-color;
    line-height: 26rpx;
}

.cardListBoxList {
    display: v-bind(comShowList);
}

.time_tip {
    padding-left: 24rpx;
    padding-right: 24rpx;
}
</style>
