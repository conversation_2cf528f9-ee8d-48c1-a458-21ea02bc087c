<template>
    <view class="punishment">
        <z-paging ref="paging" v-model="state.dataList" @query="getVideoList" :auto="false">
            <template #top>
                <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="back" title="处分档案管理" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()">
                    <!-- #ifdef H5 || APP-PLUS  -->
                    <template v-slot:right>
                        <div class="bar_right" @click="changeStudent" v-if="state.selectList.length > 1">
                            <span class="student_name">{{ state.studentName }}</span>
                            <image class="select_img" src="@nginx/components/select_child.png" />
                        </div>
                    </template>
                    <!-- #endif -->
                </uni-nav-bar>
            </template>
            <!-- #ifdef MP-WEIXIN -->
            <div class="bar_right" @click="changeStudent" v-if="state.selectList.length > 1">
                <span class="student_name">{{ state.studentName }}</span>
                <image class="select_img" src="@nginx/components/select_child.png" />
            </div>
            <!-- #endif -->
            <view class="content">
                <view class="list-itme" v-for="item in state.dataList" :key="item.id">
                    <view class="list-itme-hande">
                        <img class="img" src="@nginx/workHall/student/sanctions.png" alt="" />
                        <text>{{ item.eventTypeName }}</text>
                        -
                        <text>{{ item.actionTypeName }}</text>
                    </view>
                    <view class="list-itme-content">
                        <view class="content-time">{{ item.createTime }}</view>
                        <view class="content-btn">
                            <view class="content-text" @click="onConfirm(item)">{{ "处分申诉" }} </view></view
                        >
                    </view>
                </view>
            </view>
            <template #empty>
                <yd-empty text="暂无数据" />
            </template>
        </z-paging>
        <yd-select-popup :fieldNames="{ value: 'studentId', label: 'studentName' }" ref="selectPopupRef" title="请选择学生" :list="state.selectList" @closePopup="closePopup" :selectId="[state.studentId]"></yd-select-popup>
    </view>
</template>

<script setup>
import useStore from "@/store"
import { onLoad } from "@dcloudio/uni-app"
const props = defineProps({
    identityType: {
        type: String,
        default: ""
    }
})
const { user } = useStore()
const selectPopupRef = ref(null)
const paging = ref(null)
const state = reactive({
    dataList: [],
    selectList: [],
    studentId: ""
})
// 老师
async function getVideoList(pageNo, pageSize) {
    const params = {
        studentId: state.studentId,
        pageNo,
        pageSize
    }
    await http
        .post("/app/disciplinary/action/studentPage", params)
        .then(({ data }) => {
            paging.value.complete(data.list)
        })
        .catch(() => {
            paging.value.complete(false)
        })
}
function onConfirm(item) {
    // elternDisciplinary_schoolId
    // studentDisciplinary_schoolId
    let code = `elternDisciplinary_${user.schoolInfo.id}`
    if (props.identityType == "student") {
        code = `studentDisciplinary_${user.schoolInfo.id}`
    }
    uni.navigateTo({
        url: `/apps/oaApprove/index?id=${item.id}&code=${code}`
    })
}
function back() {
    uni.navigateBack({
        delta: 1
    })
}

// 选择学生
function changeStudent() {
    selectPopupRef.value.open()
}
const closePopup = (val) => {
    if (!val) return
    state.studentName = val.studentName
    state.studentId = val.studentId
}

watch(
    () => state.studentName,
    (val, oldVal) => {
        if (val !== oldVal) {
            paging.value.reload()
        }
    }
)
onLoad(() => {
    // 大学学生
    if (props.identityType == "student") {
        state.studentId = user.userInfo.identityUserId
    } else {
        state.selectList = user.studentInfo
        state.studentId = user.studentInfo[0]?.studentId || ""
        state.studentName = user.studentInfo[0]?.studentName
    }
    if (paging.value) {
        paging.value?.reload()
    } else {
        getVideoList(1, 10)
    }
})
</script>

<style lang="scss" scoped>
.bar_right {
    // #ifdef MP-WEIXIN
    margin-top: 10rpx;
    // #endif
    padding: 10rpx 0rpx;
    font-weight: 400;
    font-size: 28rpx;
    color: var(--primary-color);
    line-height: 40rpx;
    display: flex;
    justify-content: flex-end;

    .student_name {
        text-align: right;
    }

    .select_img {
        width: 44rpx;
        height: 44rpx;
        flex-shrink: 0;
    }
}
.punishment {
    background: #f9faf9;
    height: 100vh;
    :deep(.uni-navbar__header-btns) {
        width: 107px !important;
    }
    .content {
        background: #fff;
        margin: 20rpx 30rpx;
        border-radius: 10px;
        .list-itme {
            border-bottom: 1px solid #d9d9d9;
            margin: 10px 15px;
            padding: 17px 0;
            .list-itme-hande {
                display: flex;
                align-items: center;
                font-weight: 600;
                font-size: 14px;
                .img {
                    width: 22px;
                    height: 22px;
                    border-radius: 50%;
                    margin-right: 10px;
                }
            }
            .content-time {
                font-weight: 400;
                font-size: 14px;
                color: #999999;
                margin: 10px 22px 10px 34px;
            }
            .content-btn {
                display: flex;
                justify-content: flex-end;
                .content-text {
                    min-width: 86rpx;
                    padding: 4rpx 10rpx;
                    height: 32rpx;
                    line-height: 32rpx;
                    text-align: center;
                    background: #ffffff;
                    border-radius: 18rpx;
                    border: 1rpx solid var(--primary-color);
                    color: var(--primary-color);
                    &.active {
                        background: #d7d7d7;
                        color: #ffffff;
                        border: 1rpx solid #d7d7d7;
                    }
                }
            }
        }
    }
}
</style>
