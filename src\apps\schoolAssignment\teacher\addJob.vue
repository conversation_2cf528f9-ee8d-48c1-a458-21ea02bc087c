<template>
    <z-paging>
        <view class="container_box">
            <view class="header_box">
                <view class="top_input">
                    <uni-easyinput :inputBorder="false" v-model="state.title" :clearable="false"></uni-easyinput>
                    <view class="change_box" @click="handleOpen('selectPopupRef')">
                        {{ state.select.name }}
                        <img class="icon" src="@nginx//workbench/schoolAssignment/change.png" />
                    </view>
                </view>
                <view class="textarea_box">
                    <uni-easyinput type="textarea" v-model.trim="state.content" :inputBorder="false" placeholder="请输入作业内容"></uni-easyinput>
                </view>
                <view class="img_list_box">
                    <view class="img_item" v-for="(item, index) in state.imgList" :key="index">
                        <img class="img_item_img" :src="item" />
                        <uni-icons class="close_icon" type="clear" size="22" color="#404040" @click="handleDelectImg(index)"></uni-icons>
                    </view>
                </view>
            </view>
            <view class="upload_box">
                <view class="upload" @click="handleOpen('imgRef')">
                    <img class="upload_img" src="@nginx//workbench/schoolAssignment/img.png" />
                    <view>图片</view>
                </view>
            </view>
            <!-- 反馈时间选择 -->
            <view class="time_box">
                <uni-list>
                    <uni-list-item title="提交反馈截止时间">
                        <template v-slot:footer>
                            <view class="time_text">
                                <template v-if="state.feedbackEndTime">{{ changeTime(state.feedbackEndTime) }}<uni-icons type="clear" style="margin-left: 10rpx" size="18" color="#999" @click="handleClear('feedbackEndTime')"></uni-icons></template>
                                <template v-else>无<uni-icons type="right" size="18" style="margin-left: 10rpx" color="#999" @click="handleOpen('feedbackEndTime')"></uni-icons></template>
                            </view>
                        </template>
                    </uni-list-item>
                    <uni-list-item title="定时发布">
                        <template v-slot:footer>
                            <view class="time_text">
                                <template v-if="state.releaseTime">{{ changeTime(state.releaseTime) }}<uni-icons type="clear" style="margin-left: 10rpx" size="18" color="#999" @click="handleClear('releaseTime')"></uni-icons></template>
                                <template v-else>无<uni-icons type="right" size="18" style="margin-left: 10rpx" color="#999" @click="handleOpen('releaseTime')"></uni-icons></template>
                            </view>
                        </template>
                    </uni-list-item>
                </uni-list>
            </view>
            <view class="add_subject_box">
                <text>接受班级</text>
                <uni-icons type="plusempty" size="24" color="var(--primary-color)" @click="handleAdd"></uni-icons>
            </view>
            <view class="add_subject_list" v-for="(item, index) in state.classesList" :key="index">
                <uni-list>
                    <uni-list-item note="接受班级">
                        <template v-slot:footer>
                            <view><uni-icons type="trash-filled" size="18" color="#999" @click="handleDelect(index)"></uni-icons> </view>
                        </template>
                    </uni-list-item>
                    <uni-list-item title="接受班级">
                        <template v-slot:footer>
                            <view class="select_list">
                                <uni-data-picker popup-title="请选择" :localdata="state.classList" v-model="item.classesId" :map="{ text: 'name', value: 'id', children: 'children' }" v-slot:default="{ data, options }">
                                    <view v-if="data.length">
                                        <text v-for="item in data">{{ item.text }}</text>
                                    </view>
                                    <view v-else-if="options">请选择</view>
                                </uni-data-picker>
                                <uni-icons type="right" size="18" color="#999"></uni-icons>
                            </view>
                        </template>
                    </uni-list-item>
                    <!-- TODO: 暂时不做 -->
                    <!-- <uni-list-item title="关联课表" clickable>
                        <template v-slot:footer>
                            <view class="select_list">
                                <text>{{ item.timetableId ? "选择了" : "请选择" }}</text>
                                <uni-icons type="right" size="18" color="#999"></uni-icons>
                            </view>
                        </template>
                    </uni-list-item> -->
                </uni-list>
            </view>
        </view>
        <template #bottom>
            <view class="footer">
                <button class="btn" @click="submit" :disabled="disabled">发布</button>
            </view>
        </template>
    </z-paging>
    <yd-select-popup ref="selectPopupRef" title="请选择" :list="state.subjectList" :fieldNames="{ value: 'id', label: 'name' }" @closePopup="closePopup"></yd-select-popup>
    <yd-select-popup ref="imgRef" title="上传图片" :list="imgSeletList" @closePopup="closeImg"></yd-select-popup>
    <uv-datetime-picker ref="feedbackEndTime" v-model="state.feedbackEndTime" mode="datetime" confirmColor="#11C685"> </uv-datetime-picker>
    <uv-datetime-picker ref="releaseTime" v-model="state.releaseTime" mode="datetime" confirmColor="#11C685"> </uv-datetime-picker>
</template>
<script setup>
import dayjs from "dayjs"
const instance = getCurrentInstance().proxy
const eventChannel = instance.getOpenerEventChannel()

const imgSeletList = ref([
    { label: "拍摄", value: "camera" },
    { label: "从手机相册选择", value: "album" }
])

const selectPopupRef = ref(null)
const feedbackEndTime = ref(null)
const imgRef = ref(null)
const releaseTime = ref(null)
const disabled = ref(false)

const state = reactive({
    title: "",
    content: "",
    feedbackEndTime: "",
    releaseTime: "",
    select: {},
    subjectList: [],
    classList: [],
    classesList: [],
    imgList: []
})

const type = {
    selectPopupRef: selectPopupRef,
    feedbackEndTime: feedbackEndTime,
    releaseTime: releaseTime,
    imgRef: imgRef
}

const handleOpen = (val) => {
    if (val !== "selectPopupRef") {
        state[val] = Number(new Date())
    }
    type[val].value.open()
}

// 添加班级
const handleAdd = () => {
    state.classesList.push({
        classesId: "",
        timetableId: "",
        timetableTime: ""
    })
}

// 删除图片
const handleDelectImg = (index) => {
    state.imgList.splice(index, 1)
}

// 删除班级
const handleDelect = (index) => {
    showModal({
        content: "确定要删除接受班级吗？",
        success: () => {
            state.classesList.splice(index, 1)
        }
    })
}

// 清除时间
const handleClear = (val) => {
    state[val] = ""
}

const date = dayjs().format("MM/DD")
const closePopup = (val) => {
    if (!val) return
    state.value = `${date}${val.name}作业`
}

// 选择图片上传关闭事件
const closeImg = (val) => {
    imgSeletList.value.forEach((i) => (i.isCheck = false))
    if (!val) return
    uni.chooseImage({
        sourceType: val,
        sizeType: ["original", "compressed"], //可以指定是原图还是压缩图，默认二者都有
        success: (res) => {
            if (res.tempFilePaths.length) {
                res.tempFilePaths.forEach((path) => {
                    http.uploadFile("/app/file/upload", path).then((res) => {
                        state.imgList.push(res)
                    })
                })
            }
        }
    })
}

// 时间转换
const changeTime = (val) => {
    if (!val) return ""
    return dayjs(val).format("YYYY-MM-DD HH:mm")
}

const submit = async () => {
    const params = {
        classesList: state.classesList,
        content: state.content,
        feedbackEndTime: changeTime(state.feedbackEndTime),
        imgPaths: state.imgList.join(","),
        releaseTime: changeTime(state.releaseTime),
        subjectId: state.select.id,
        title: state.title
    }
    if (vaild()) {
        try {
            disabled.value = true
            await http.post("/app/work/create", params)
            uni.showToast({
                title: "发布成功",
                icon: "none"
            })
            uni.navigateBack({
                delta: 1,
                success: () => {
                    eventChannel.emit("backEvent")
                }
            })
        } catch (e) {
            disabled.value = false
        }
    }
}

function vaild() {
    if (!state.title) {
        uni.showToast({
            title: "请输入作业标题",
            icon: "none"
        })
        return false
    }
    if (!state.content) {
        uni.showToast({
            title: "请输入作业内容",
            icon: "none"
        })
        return false
    }
    if (state.classesList.length == 0) {
        uni.showToast({
            title: "请选择班级",
            icon: "none"
        })
        return false
    }
    const item = state.classesList.find((i) => i.classesId == "")
    if (item) {
        uni.showToast({
            title: "请选择班级",
            icon: "none"
        })
        return false
    }
    return true
}

/**获取科目*/
const getSubject = async () => {
    const { data = [] } = await http.get("/app/work/selectSubjectByUserId")
    if (data.length == 0) return
    state.select = data[0]
    state.title = `${date}${data[0].name}作业`
    state.subjectList = data
}

/**获取班级 */
const getClassList = async () => {
    const { data = [] } = await http.get("/app/timetable/getClassList", { code: "schoolAssignment" })
    console.log("data:", data)
    state.classList = data
}

onMounted(() => {
    getSubject()
    getClassList()
})
</script>
<style lang="scss" scoped>
.container_box {
    background: #f9faf9;
    padding-top: 20rpx;
    .header_box {
        padding: 0 30rpx;
        background: #fff;
        .top_input {
            display: flex;
            border-bottom: 2rpx solid #d8d8d8;
            :deep(.uni-easyinput__content-input) {
                padding-left: 0 !important;
            }
            .change_box {
                color: #999999;
                display: flex;
                align-items: center;
                .icon {
                    width: 22rpx;
                    height: 22rpx;
                    margin-left: 10rpx;
                }
            }
        }
        .textarea_box {
            border-bottom: 2rpx solid #d8d8d8;
        }
        .img_list_box {
            display: flex;
            flex-wrap: wrap;

            .img_item {
                position: relative;
                width: 150rpx;
                height: 150rpx;
                margin-right: 30rpx;
                margin-bottom: 30rpx;
                &:nth-of-type(4n) {
                    margin-right: 0;
                }
                .img_item_img {
                    width: 100%;
                    height: 100%;
                    border-radius: 8rpx;
                    overflow: hidden;
                }
                .close_icon {
                    position: absolute;
                    right: -18rpx;
                    top: -16rpx;
                }
            }
        }
    }
    .upload_box {
        height: 158rpx;
        background: #fff;
        margin-top: 20rpx;
        margin-bottom: 20rpx;
        padding-left: 30rpx;
        .upload {
            font-size: 28rpx;
            color: #999;
            width: 150rpx;
            height: 150rpx;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            .upload_img {
                width: 43rpx;
                height: 38rpx;
                margin-bottom: 15rpx;
            }
        }
    }
    .time_box {
        background: #fff;
        padding: 0 30rpx;
        .time_text {
            font-size: 28rpx;
            color: #999;
            display: flex;
            align-items: center;
        }
    }
    .add_subject_box {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 40rpx 30rpx 20rpx 30rpx;
    }
    .add_subject_list {
        margin-bottom: 20rpx;
        .select_list {
            display: flex;
            color: #999;
            flex: 1;
            text-align: right;
            font-size: 28rpx;
            justify-content: flex-end;
        }
    }
}
.footer {
    padding: 0 30rpx;
    background: #fff;
    font-size: 32rpx;
    .btn {
        color: $uni-text-color-inverse;
        height: 92rpx;
        line-height: 92rpx;
        border-radius: 10rpx;
        background: var(--primary-color);
        margin-left: 0;
        margin-top: 30rpx;
        margin-bottom: 66rpx;
        font-size: 32rpx;
        display: flex;
        justify-content: center;
        align-items: center;
    }
}
</style>
