<template>
    <z-paging :auto="false" ref="paging" v-model="state.dataList" @query="queryList">
        <template #top>
            <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="routerBack" title="作业" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"></uni-nav-bar>
            <view class="header_container">
                <view class="top_select">
                    <view class="box" @click="handleWhole">
                        <text>{{ state.whole.label }}</text>
                        <text class="triangle"></text>
                    </view>
                    <view class="box">
                        <uni-data-picker popup-title="请选择" :localdata="state.subjectList" v-model="state.subjectId" :map="{ text: 'name', value: 'id', children: 'children' }" v-slot:default="{ data, error, options }" @change="onchange">
                            <text v-if="error">{{ error }}</text>
                            <text v-if="options"></text>
                            <text v-if="data && data.length > 0">
                                <text v-for="(item, index) in data" :key="item.value">{{ index > 0 ? item.text : "" }}</text>
                            </text>
                            <text v-else>全部</text>
                        </uni-data-picker>
                        <text class="triangle"></text>
                    </view>
                </view>
                <view class="select_box">
                    <view class="left">请选择</view>
                    <view class="right">
                        <view :class="['item_btn', active == index ? 'active' : '']" v-for="(item, index) in dateList" :key="index" @click="handleClick(index)">{{ item.name }}</view>
                    </view>
                </view>
                <view class="date_box">
                    <view class="date_item">
                        <picker mode="date" :value="state.dataPicker.startTime" :start="state.minDate" :end="state.maxDate" @change="(e) => bindTimeChange(e, 'startTime')">
                            <text class="date_text">{{ state.dataPicker.startTime }}</text>
                        </picker>
                    </view>
                    <text class="center_text">至</text>
                    <view class="date_item">
                        <picker mode="date" :value="state.dataPicker.endTime" :start="state.minDate" :end="state.maxDate" @change="(e) => bindTimeChange(e, 'endTime')">
                            <text class="date_text">{{ state.dataPicker.endTime }}</text>
                        </picker>
                    </view>
                </view>
            </view>
        </template>
        <view class="main_container">
            <view class="item_container" v-for="item in state.dataList" :key="item.id" @click="itemClick(item)">
                <view class="header">
                    <view class="l">
                        <img class="img" src="@nginx/workbench/schoolAssignment/kebiao.png" />
                        {{ item.subjectName }}
                    </view>
                    <view class="r"> {{ item.releaseTime }}发布 </view>
                </view>
                <view class="info">{{ item.title }}</view>
                <view class="lab_box">
                    <view class="lab_box" v-if="!item.isScheduled">
                        <view class="icon_box">
                            <uni-icons type="eye-filled" :size="20" color="#999"></uni-icons>
                            <text class="text"
                                >已读 <text style="color: var(--primary-color)">{{ item.read }}</text
                                >/{{ item.total }}</text
                            >
                        </view>
                        <view class="icon_box">
                            <uni-icons type="calendar-filled" :size="20" color="#999"></uni-icons>
                            <text class="text"
                                >已完成 <text style="color: var(--primary-color)">{{ item.read }}</text
                                >/{{ item.total }}</text
                            >
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <view class="add_btn" @click="handleAdd">
            <uni-icons type="plusempty" size="30" color="#fff"></uni-icons>
        </view>
        <template #empty>
            <yd-empty text="暂无数据" />
        </template>
    </z-paging>
    <yd-select-popup ref="selectPopupRef" title="请选择" :list="list" @closePopup="closePopup" :selectId="[state.whole.value]" />
</template>
<script setup>
import dayjs from "dayjs"
const list = [
    { label: "我发布的", value: 2 },
    { label: "待发布", value: 1 },
    { label: "全部作业", value: 0 }
]

const dateList = [
    {
        name: "今日",
        startTime: dayjs().startOf("day").format("YYYY-MM-DD"),
        endTime: dayjs().endOf("day").format("YYYY-MM-DD")
    },
    {
        name: "本周",
        startTime: dayjs().startOf("week").add(1, "day").format("YYYY-MM-DD"),
        endTime: dayjs().endOf("week").add(1, "day").format("YYYY-MM-DD")
    },
    {
        name: "上周",
        startTime: dayjs().add(-1, "week").startOf("week").add(1, "day").format("YYYY-MM-DD"),
        endTime: dayjs().add(-1, "week").endOf("week").add(1, "day").format("YYYY-MM-DD")
    },
    {
        name: "本月",
        startTime: dayjs().startOf("month").format("YYYY-MM-DD"),
        endTime: dayjs().endOf("month").format("YYYY-MM-DD")
    },
    {
        name: "上月",
        startTime: dayjs().add(-1, "month").startOf("month").format("YYYY-MM-DD"),
        endTime: dayjs().add(-1, "month").endOf("month").format("YYYY-MM-DD")
    }
]

const active = ref(0)
const paging = ref(null)

const state = reactive({
    whole: {
        label: "我发布的",
        value: 2
    },
    subjectList: [],
    subjectId: null,
    classesId: null,
    dataPicker: dateList[active.value],
    minDate: dayjs().subtract(6, "month").format("YYYY-MM-DD"),
    maxDate: dayjs().add(6, "month").format("YYYY-MM-DD"),
    dataList: []
})

const queryList = async (pageNo, pageSize) => {
    try {
        const params = {
            pageNo,
            pageSize,
            isWhole: state.whole.value,
            subjectId: state.subjectId.includes("_all_") ? 0 : state.subjectId,
            classesId: state.classesId,
            startTime: state.dataPicker.startTime,
            endTime: state.dataPicker.endTime
        }
        const { data } = await http.post("/app/work/page", params)
        paging.value.complete(data.list)
    } catch (err) {
        paging.value.complete(false)
    }
}

const selectPopupRef = ref(null)
const handleWhole = () => {
    selectPopupRef.value.open()
}

const closePopup = async (val) => {
    if (!val || state.whole.value == val.value) return
    state.whole = val
    await getClassSubjectList()
    paging.value.reload()
}

// 切换课程
const onchange = (e) => {
    const value = e.detail.value
    state.classesId = value[1].value
    paging.value.reload()
}

// 切换查询周期
const handleClick = (index) => {
    active.value = index
    state.dataPicker = dateList[active.value]
    paging.value.reload()
}

// 时间选择器切换
const bindTimeChange = (e, val) => {
    state.dataPicker[val] = e.detail.value
    paging.value.reload()
}

// 列表点击
const itemClick = (item) => {
    navigateTo({
        url: "/apps/schoolAssignment/teacher/detail",
        query: {
            id: item.id
        },
        events: {
            backEvent: () => {
                paging.value.reload()
            }
        }
    })
}

// 新增作业
const handleAdd = () => {
    navigateTo({
        url: "/apps/schoolAssignment/teacher/addJob",
        events: {
            backEvent: () => {
                paging.value.reload()
            }
        }
    })
}

// 获取所有课程,避免ID冲突重设全部选项id
function setUniqueIdsForAll(data) {
    if (!Array.isArray(data)) return []
    let uniqueCounter = 0 // 用于生成唯一的ID

    function traverseAndSetId(nodes, parentId) {
        nodes.forEach((node) => {
            if (node.name === "全部") {
                // 如果节点名为“全部”，则设置一个新的唯一ID
                node.id = `${parentId}_all_${uniqueCounter++}`
            }
            // 继续遍历子节点（如果存在）
            if (node.children && node.children.length > 0) {
                traverseAndSetId(node.children, node.id)
            }
        })
    }

    // 遍历根节点，假设根节点没有父节点，因此可以使用一个特殊的标识符如'root'
    traverseAndSetId(data, "root")

    return data
}

// 获取所有课程
const getClassSubjectList = async () => {
    const { data } = await http.get("/app/work/getClassSubjectList", { isWhole: state.whole.value })
    state.subjectList = setUniqueIdsForAll(data) || []
    state.classesId = state.subjectList[0].children[0].id
    state.subjectId = state.subjectList[0].children[0].children[0].id
}

onLoad(async (options) => {
    await getClassSubjectList()
    if (options.isHome) {
        state.classesId = null
        state.subjectId = "_all_"
    }
    paging.value?.reload()
})
</script>
<style lang="scss" scoped>
.header_container {
    padding: 30rpx;
    .triangle {
        width: 0;
        height: 0;
        display: inline-block;
        border-left: 15rpx solid transparent; /* 左边框 */
        border-right: 15rpx solid transparent; /* 右边框 */
        border-top: 15rpx solid var(--primary-color); /* 顶边框 */
        margin-left: 10rpx;
    }
    .top_select {
        display: flex;
        justify-content: space-between;
        .box {
            display: flex;
            align-items: center;
            font-size: 28rpx;
        }
    }
    .select_box {
        font-size: 26rpx;
        color: $uni-text-color-grey;
        display: flex;
        align-items: center;
        margin-top: 30rpx;
        .left {
            width: 92rpx;
            flex-shrink: 0;
        }
        .right {
            flex: 1;
            display: flex;
            justify-content: space-between;
            .item_btn {
                width: 100rpx;
                height: 50rpx;
                border-radius: 25rpx;
                border: 1rpx solid #d8d8d8;
                background: #fff;
                text-align: center;
                line-height: 50rpx;
            }
            .active {
                color: var(--primary-color);
                border: 1rpx solid var(--primary-color);
            }
        }
    }
    .date_box {
        display: flex;
        align-items: center;
        color: #666666;
        margin: 20rpx;
        .center_text {
            margin: 0 20rpx;
        }
        .date_item {
            display: inline-block;
            flex: 1;
            .date_text {
                color: $uni-text-color-grey;
                font-size: 28rpx;
                text-align: center;
                display: inline-block;
                width: 100%;
                padding: 20rpx 0;
                background: #f9faf9;
            }
        }
    }
}
.main_container {
    padding: 20rpx 30rpx;
    background: #f9faf9;
    .item_container {
        background: #ffffff;
        box-shadow: 0rpx 8rpx 8rpx 0rpx rgba(220, 245, 238, 0.5);
        border-radius: 12rpx;
        padding: 34rpx 30rpx;
        margin-bottom: 20rpx;
        .header {
            display: flex;
            align-items: center;
            padding-bottom: 28rpx;
            border-bottom: 2rpx solid #d8d8d8;
            .l {
                font-size: 30rpx;
                font-weight: 600;
                flex: 1;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                .img {
                    width: 36rpx;
                    height: 36rpx;
                    margin-right: 14rpx;
                    vertical-align: bottom;
                }
            }
            .r {
                font-size: 28rpx;
                color: var(--primary-color);
            }
        }
        .info {
            padding-top: 30rpx;
            padding-bottom: 20rpx;
            font-size: 28rpx;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .lab_box {
            display: flex;
            .icon_box {
                display: flex;
                align-items: center;
                font-size: 24rpx;
                margin-right: 40rpx;
                color: $uni-text-color-grey;
                .text {
                    margin-left: 16rpx;
                }
            }
        }
    }
}
.add_btn {
    position: fixed;
    right: 30rpx;
    bottom: 60rpx;
    width: 112rpx;
    height: 112rpx;
    border-radius: 50%;
    background: var(--primary-color);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0rpx 8rpx 8rpx 0rpx rgba(17, 198, 133, 0.2);
}
</style>
