<template>
    <div class="my_tab">
        <view v-show="isLoading" class="loading_box" :style="isLoading ? { height: '80vh' } : { height: 0 }">
            <uv-loading-icon color="var(--primary-color)" textColor="var(--primary-color)" :vertical="true" :show="isLoading" text="课表加载中..." textSize="30rpx"></uv-loading-icon>
        </view>
        <view v-show="!isLoading">
            <!-- 选择天 -->
            <view class="sift_box">
                <view class="select_day" @click="selectDayRef.open()">
                    <text>{{ nowDayName || "请选择" }}</text>
                    <text class="triangle_down"></text>
                </view>
            </view>
            <z-paging ref="paging" :auto="false" use-page-scroll @query="getList" v-model="mytableList" @onRefresh="getList(thisWeek)" :reload-when-refresh="false">
                <view class="table_list">
                    <view class="item" v-for="item in mytableList" :key="item.id">
                        <view class="title_box">
                            <text class="title">{{ item.showName }}</text>
                            <text class="date">{{ item.startTime }}-{{ item.endTime }}</text>
                        </view>
                        <view class="course_box">
                            <view class="course_status">
                                <image mode="widthFix" class="image_icon" :src="`@nginx/workbench/schoolTable/table_${item.isExpire ? 'fail' : 'success'}.png`" alt="" />
                            </view>
                            <text class="course_name">{{ item.subjectName }}</text>
                        </view>
                        <view class="remind_box">
                            <view class="status" :class="!item.isExpire ? 'success_status' : ''"></view>
                            <text class="remind_text">课前提醒：{{ item.beforeClassRemind || "-" }}</text>
                        </view>
                        <view class="edit_remind" @click="editRemind(item)" v-if="!item.isExpire">修改提醒</view>
                    </view>
                </view>
                <template #empty>
                    <slot name="empty">
                        <yd-empty text="暂无数据" />
                    </slot>
                </template>
            </z-paging>
            <view class="back_today" v-if="isShowBackToday || thisWeek != selectWeek" @click="backToday">
                <image mode="widthFix" class="back_icon" src="@nginx/workbench/schoolTable/back_today.png" alt="" />
            </view>
        </view>
        <yd-select-popup ref="selectDayRef" title="请选择" :list="weekDayList" @closePopup="closePopup" :selectId="[nowDayId]"></yd-select-popup>

        <!-- 输入框 -->
        <uni-popup ref="inputPopupRef" type="dialog">
            <uni-popup-dialog mode="input" title="请输入课前提醒" v-model="selectItem.beforeClassRemind" placeholder="请输入" @confirm="dialogInputConfirm"></uni-popup-dialog>
        </uni-popup>
    </div>
</template>

<script setup>
import dayjs from "dayjs"
import { nextTick } from "vue"

const { system } = store()
const emit = defineEmits(["backWeek"])
const props = defineProps({
    thisWeek: {
        type: Number,
        default: null
    }
})
const inputPopupRef = ref(null)
const selectDayRef = ref(null)
const selectItem = ref({})
const paging = ref(null)
const weekDayList = ref([])
const nowDayId = ref(null)
const nowDayName = ref(null)
const totalList = ref([])
const mytableList = ref([])
const isShowBackToday = ref(false)
const inputValue = ref("")
const isLoading = ref(false)

const thisWeek = computed(() => props.thisWeek)
const selectWeek = computed(() => system.apps.schoolTable.tableSelectWeek)

function setPageData(data) {
    const { list, weekMap } = data
    totalList.value = list
    weekDayList.value = weekMap.map((i, index) => {
        return {
            value: index + 1,
            label: i
        }
    })
    nowDayId.value = dayjs().day() // 当前周
    nowDayName.value = weekMap[nowDayId.value - 1]
    nextTick(() => {
        paging.value?.setLocalPaging(list[nowDayId.value - 1]?.list)
        isShowBackToday.value = nowDayId.value != dayjs().day()
    })
}

// 调用接口
async function getMyTable() {
    isLoading.value = true
    await http
        .get("/app/timetable/v1/getTeacherTimetable", { weekTime: selectWeek.value })
        .then((res) => {
            setPageData(res.data)
            system.setAppData({ sys: "schoolTable", data: { myTableData: res.data } })
        })
        .catch(() => {
            nextTick(() => {
                paging.value?.setLocalPaging([])
            })
        })
        .finally(() => {
            isLoading.value = false
        })
}

// 查询列表数据
async function getList() {
    // const storeData = system.apps.schoolTable
    // if (storeData && storeData.myTableData && storeData.myTableData.thisWeek == storeData.tableSelectWeek) {
    //     setPageData(storeData.myTableData)
    // } else {
    //
    getMyTable()
    // }
    // nextTick(() => {
    //     paging.value?.reload()
    // })
}

// 选择天
function closePopup(obj) {
    if (!obj) return
    nowDayName.value = obj.label
    nowDayId.value = obj.value
    paging.value.setLocalPaging(totalList.value[obj.value - 1]?.list)
    isShowBackToday.value = nowDayId.value != dayjs().day()
}

// 回到今天
function backToday() {
    const storeData = system.apps.schoolTable
    if (thisWeek.value == selectWeek.value) {
        const value = dayjs().day()
        const label = storeData.myTableData?.weekMap[value - 1]
        closePopup({ value, label })
    } else {
        emit("backWeek")
    }
}

// 修改提醒
function editRemind(item) {
    selectItem.value = item
    inputValue.value = item.beforeClassRemind
    inputPopupRef.value.open()
}

function dialogInputConfirm(value) {
    if (!value) return
    http.post("/app/timetable/updateTimetable", { id: selectItem.value.id, beforeClassRemind: value }).then((res) => {
        uni.showToast({
            title: res.message,
            icon: "none"
        })
    })
}

defineExpose({ getMyTable, getList })
</script>

<style lang="scss" scoped>
.my_tab {
    .loading_box {
        height: 80vh;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .sift_box {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 30rpx;
        background: $uni-bg-color;
        .select_day {
            display: flex;
            align-items: center;
            font-weight: 400;
            font-size: 28rpx;
            color: #666666;
            line-height: 40rpx;
            .triangle_down {
                width: 0;
                height: 0;
                overflow: hidden;
                font-size: 0;
                line-height: 0;
                border-width: 10rpx;
                margin-top: 10rpx;
                margin-left: 10rpx;
                border-style: solid dashed dashed dashed;
                border-color: var(--primary-color) transparent transparent transparent;
            }
        }
    }
    .table_list {
        margin: 30rpx;
        .item {
            padding: 30rpx;
            background: $uni-bg-color;
            box-shadow: 0rpx 8rpx 8rpx 0rpx #dcf5ee80;
            border-radius: 20rpx;
            margin-bottom: 30rpx;
            .title_box {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding-bottom: 30rpx;
                border-bottom: 1rpx solid $uni-border-color;
                .title {
                    font-weight: 500;
                    font-size: 30rpx;
                    color: #666666;
                    line-height: 42rpx;
                }
                .date {
                    font-weight: 500;
                    font-size: 30rpx;
                    color: #666666;
                    line-height: 42rpx;
                }
            }
            .course_box {
                display: flex;
                align-items: center;
                padding: 30rpx 0;
                .course_status {
                    width: 42rpx;
                    height: 42rpx;
                    .image_icon {
                        width: 42rpx;
                        height: 42rpx;
                    }
                }
                .course_name {
                    font-weight: 500;
                    font-size: 30rpx;
                    color: #666666;
                    line-height: 42rpx;
                    padding-left: 18rpx;
                    word-break: break-all;
                }
            }
            .remind_box {
                display: flex;
                align-items: flex-start;
                .status {
                    width: 12rpx;
                    height: 12rpx;
                    background: #666666;
                    border-radius: 50%;
                    margin: 12rpx 10rpx 0 0;
                }
                .success_status {
                    background: var(--primary-color);
                }
                .remind_text {
                    flex: 1;
                    font-weight: 400;
                    font-size: 26rpx;
                    color: #666666;
                    line-height: 36rpx;
                    word-break: break-all;
                }
            }
            .edit_remind {
                margin-top: 30rpx;
                display: flex;
                justify-content: flex-end;
                font-weight: 400;
                font-size: 26rpx;
                color: var(--primary-color);
                line-height: 36rpx;
            }
        }
    }
    .back_today {
        position: fixed;
        bottom: 126rpx;
        left: 16rpx;
        width: 140rpx;
        height: 140rpx;
        .back_icon {
            width: 140rpx;
            height: 140rpx;
        }
    }
}
:deep(.uni-button-color) {
    color: var(--primary-color) !important;
}
</style>
