import dayjs from "dayjs"

// 默认选中级联第一个
export async function defaultArrId(data) {
    async function recurse(item) {
        if (item.children && item.children.length > 0) {
            return item.children[0].id
        } else if (item.children) {
            return null
        }

        return null
    }
    for (let i = 0; i < data.length; i++) {
        let currentItem = data[i]
        let result = recurse(currentItem)
        if (result !== null) {
            return result
        }
    }
    return null
}

export const objList = [
    {
        code: "earlySelfStudyList",
        value: "早自习",
        class: "early_self_study"
    },
    {
        code: "morningList",
        value: "上午",
        class: "morning"
    },
    {
        code: "afternoonList",
        value: "下午",
        class: "afternoon"
    },
    {
        code: "nightList",
        value: "晚上",
        class: "night"
    },
    {
        code: "lateSelfStudyList",
        value: "晚自习",
        class: "late_self_study"
    }
]

// 筛选课程
export async function setArrCourse(arr, obj) {
    if (arr && arr.length) {
        arr.forEach((i) => {
            objList.forEach((c) => {
                obj[c.code].forEach((y) => {
                    y.course = y.sequence == i.section ? i : y.course || {}
                })
            })
        })
    } else {
        objList.forEach((c) => {
            obj[c.code].forEach((y) => {
                y.course = {}
            })
        })
    }
}

export const isNoData = computed(() => {
    return (obj) => {
        return (obj.earlySelfStudyList && obj.earlySelfStudyList.length) || (obj.morningList && obj.morningList.length) || (obj.afternoonList && obj.afternoonList.length) || (obj.lateSelfStudyList && obj.lateSelfStudyList.length)
    }
})

// 对比当前课程是否已过期
export const expire = computed(() => {
    return (item) => {
        if (!item || !item.date) return true
        const unixStr = dayjs(`${item.date} ${item.endTime}`).unix()
        const nowUnixStr = dayjs(dayjs().format("YYYY-MM-DD HH:mm")).unix()
        return nowUnixStr > unixStr
    }
})
