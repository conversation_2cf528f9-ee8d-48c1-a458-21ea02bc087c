.sift_box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    background: $uni-bg-color;
    .select_day {
        display: flex;
        align-items: center;
        font-weight: 400;
        font-size: 28rpx;
        color: #666666;
        line-height: 40rpx;
    }
    .picker_list {
        align-items: center;
        display: flex;
        justify-content: flex-end;
        font-weight: 400;
        font-size: 28rpx;
        color: #666666;
        line-height: 40rpx;
        .picker_item {
            word-break: break-all;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
            max-width: 50%;
        }
    }
    .triangle_down {
        width: 0;
        height: 0;
        overflow: hidden;
        font-size: 0;
        line-height: 0;
        border-width: 10rpx;
        margin-top: 10rpx;
        margin-left: 10rpx;
        border-style: solid dashed dashed dashed;
        border-color: var(--primary-color) transparent transparent transparent;
    }
}

.loading_box {
    height: 80vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

.table_box {
    background: $uni-bg-color;
    padding: 30rpx;
    margin-top: 30rpx;
}
.table_content {
    display: flex;
    flex-direction: column;
    .table_item {
        width: 100%;
        display: flex;
        font-weight: 400;
        font-size: 28rpx;
        color: $uni-text-color-grey;
        line-height: 40rpx;
        .period_time {
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 400;
            font-size: 28rpx;
            border-left: 1rpx solid #90e2cb;
            border-right: 1rpx solid #90e2cb;
            color: $uni-text-color;
            width: 116rpx;
            line-height: 40rpx;
        }
        .right_box {
            display: flex;
            flex: 1;
            flex-direction: column;

            .festival {
                display: flex;
                .festival_item {
                    width: 58rpx;
                    min-height: 98rpx;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-right: 1rpx solid #90e2cb;
                }
                .course {
                    display: flex;
                    align-items: center;
                    color: $uni-text-color;
                    flex: 1;
                    padding-left: 10rpx;
                    border-right: 1rpx solid #90e2cb;
                    padding: 10rpx;
                    word-break: break-all;
                }
                .course_grey {
                    color: $uni-text-color-grey;
                }
            }
        }
    }
}
.early_self_study {
    background: #ffe8e9;
}
.morning {
    background: #c6f5f2;
}
.afternoon {
    background: #c7e9fc;
}
.late_self_study {
    background: #d4d5fc;
}
.night {
    background: #fdf1e1;
}
.border_top_class {
    border-top: 1rpx solid #90e2cb;
}
.border_bottom_class {
    border-bottom: 1rpx solid #90e2cb;
}
.back_today {
    position: fixed;
    bottom: 126rpx;
    left: 16rpx;
    width: 140rpx;
    height: 140rpx;
    .back_icon {
        width: 140rpx;
        height: 140rpx;
    }
}
