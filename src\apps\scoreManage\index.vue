<template>
    <!-- 成绩管理 -->
    <div>
        <!-- 家长是一个页面 -->
        <eltern-index v-if="identityType == 'eltern'" :identityType="identityType" />
        <!-- 老师和学生一个页面 -->
        <teacher-index v-else :identityType="identityType" />
    </div>
</template>

<script setup>
import elternIndex from "./components/elternIndex.vue"
import teacherIndex from "./components/teacherIndex.vue"
import useStore from "@/store"
const { user } = useStore()

const identityType = computed(() => {
    const roleCode = user?.identityInfo?.roleCode
    if (roleCode == "eltern") {
        return "eltern"
    } else if (roleCode == "student") {
        return "student"
    } else {
        return "teacher"
    }
})
</script>

<style lang="scss" scoped></style>
