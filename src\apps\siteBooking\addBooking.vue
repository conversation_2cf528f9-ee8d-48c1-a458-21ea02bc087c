<template>
    <view class="add_booking_page">
        <uni-nav-bar statusBar fixed left-icon="left" title="预约" :border="false" @clickLeft="clickLeft" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
        <view class="booking_type">
            <text class="type_lable">预约类型：</text>
            <view class="select_type" @click="selectTypeRef.open()">
                <text>{{ form.siteBookingTypeName }}</text>
                <uni-icons class="icons_class" type="right" size="20" color="#666"></uni-icons>
            </view>
        </view>
        <view class="form_box">
            <uni-forms ref="formRef" :modelValue="form" label-position="top">
                <uni-forms-item label="预约主题" required name="name">
                    <view class="input_field input_name">
                        <input type="text" v-model="form.name" placeholder="请输入" maxlength="30" />
                        <view class="text_num">{{ `${form.name?.length || 0}/30` }}</view>
                    </view>
                </uni-forms-item>
                <uni-forms-item label="场地" required name="siteId">
                    <view
                        class="input_field"
                        :style="{
                            color: form.siteName ? '#333' : '#666'
                        }"
                        @click="selectSiteRef.open(form.siteBookingTypeId)"
                    >
                        <text v-if="form.siteName">
                            {{ form.siteName }}
                        </text>
                        <text v-else>请选择</text>
                    </view>
                </uni-forms-item>
                <uni-forms-item label="开始" required name="startTime">
                    <view class="input_field booking_datetime">
                        <uni-datetime-picker :border="false" :clearIcon="false" type="datetime" placeholder="请选择" v-model="form.startTime" />
                    </view>
                </uni-forms-item>
                <uni-forms-item label="预约时长" required name="durationName">
                    <view
                        class="input_field"
                        :style="{
                            color: form.durationName ? '#333' : '#666'
                        }"
                        @click="selectDuration.open()"
                    >
                        {{ form.durationName || "请选择" }}
                    </view>
                </uni-forms-item>
                <uni-forms-item label="参会人">
                    <view
                        class="input_field"
                        :style="{
                            color: form.userName ? '#333' : '#666'
                        }"
                        @click="openSelector"
                    >
                        {{ form.userName || "请选择" }}
                    </view>
                </uni-forms-item>
                <uni-forms-item label="预约说明">
                    <view class="textarea_field">
                        <textarea auto-height v-model="form.describe" placeholder="请输入300字以内" maxlength="300" />
                    </view>
                </uni-forms-item>
            </uni-forms>
        </view>

        <view class="sign_in">
            <view class="lable">需要签到</view>
            <switch checked @change="switchChange" color="#11C685" style="transform: scale(0.7)" />
        </view>
        <view class="footer">
            <button class="btn" @click.stop="submit">提交</button>
        </view>
        <!-- 选择场地 -->
        <select-site ref="selectSiteRef" @confirm="confirmSite" />

        <!-- 选择人员类型 -->
        <yd-select-popup title="请选择预约类型" ref="selectTypeRef" :list="typeList" @closePopup="closeType" :fieldNames="{ value: 'id', label: 'name' }" :selectId="[form.siteBookingTypeId]" />

        <!-- 选择预约时长 -->
        <duration-popup ref="selectDuration" @confirm="confirmDuration" />

        <!-- 选择参会人 -->
        <yd-selector ref="selectorRef" @confirm="confirmUser" />
    </view>
</template>

<script setup>
import dayjs from "dayjs"
import DurationPopup from "./components/durationPopup.vue"
import SelectSite from "./components/selectSite.vue"

const formRef = ref(null)
const selectTypeRef = ref(null)
const selectDuration = ref(null)
const selectSiteRef = ref(null)
const selectorRef = ref(null)
const typeList = ref([])
const form = ref({
    signIn: 1
})

onLoad(async (option) => {
    Object.keys(option).forEach((key) => {
        option[key] = decodeURIComponent(option[key])
    })
    await getTypeList()
    if (option.isEdit == "true") {
        // 编辑
        await http.get("/app/siteBooking/get", { id: option.id }).then(({ data }) => {
            const a = ["duration", "describe", "id", "signIn", "startTime", "name", "siteBookingTypeId"]
            a.forEach((i) => {
                form.value[i] = data[i]
            })
            form.value.siteName = data.siteInfo?.name || null
            form.value.siteId = data.siteInfo.id || null
            form.value.userName = data.signList?.map((i) => i.userName).join("、")
            form.value.siteBookingSignList = data.signList?.map((i) => {
                return {
                    userId: i.userId,
                    userType: i.userType
                }
            })
            form.value.durationName = `${data.duration.hour}小时 ${data.duration.min}分钟`
        })
    } else {
        form.value = option
        form.value.signIn = 1
    }
    typeList.value.forEach((i) => {
        if (form.value.siteBookingTypeId == i.id) {
            form.value.siteBookingTypeName = i.name
        }
    })
})

function clickLeft() {
    uni.navigateBack()
}

onReady(() => {
    const rule = {
        name: {
            rules: [
                {
                    required: true,
                    errorMessage: "请输入预约主题名称"
                }
            ]
        },
        siteId: {
            rules: [
                {
                    required: true,
                    errorMessage: "请选择预约场地"
                }
            ]
        },
        startTime: {
            rules: [
                {
                    required: true,
                    errorMessage: "请选择预约开始时间"
                }
            ]
        },
        durationName: {
            rules: [
                {
                    required: true,
                    errorMessage: "请选择预约时长"
                }
            ]
        }
    }
    formRef.value.setRules(rule)
})

function closeType(val) {
    if (!val && val.id == form.value.siteBookingTypeId) return
    form.value.siteBookingTypeId = val.id
    form.value.siteBookingTypeName = val.name
}

function confirmSite(id, item) {
    form.value.siteName = item.siteName || null
    form.value.siteId = id || null
    selectSiteRef.value.close()
}

function confirmDuration(obj, item) {
    form.value.duration = obj
    form.value.durationName = `${item.hourItem.label} ${item.minItem.label}`
    selectDuration.value.close()
}

function openSelector() {
    const typeList = [
        {
            type: "people_dept",
            name: "老师",
            selectLevel: "people_dept" // 选填
        },
        {
            type: "student",
            name: "学生",
            selectLevel: "student" // 必填
        }
    ]
    selectorRef.value.open(typeList)
}

function switchChange(e) {
    form.value.signIn = e.detail.value ? 1 : 0
}

function confirmUser(ids, selected) {
    console.log(selected)
    form.value.siteBookingSignList = selected.map((i) => {
        return {
            userId: i.id,
            classesId: i.pid,
            dept: i.pName,
            userType: i.treeType == 1 ? 0 : 1
        }
    })
    form.value.userName = selected.map((i) => i.name).join("、")
    selectorRef.value.close()
}

// 获取预约类型
async function getTypeList() {
    await http.get("/app/siteBookingType/getBookingSiteNum").then((res) => {
        typeList.value = res.data
    })
}

function submit() {
    formRef.value
        .validate()
        .then((res) => {
            try {
                const obj = { ...form.value, startTime: dayjs(form.value.startTime).format("YYYY-MM-DD HH:mm") }
                http.post("/app/siteBooking/create", obj).then((res) => {
                    uni.showToast({
                        title: res.message,
                        icon: "none"
                    })
                    uni.navigateBack()
                })
            } catch (error) {
                console.log(error)
            }
        })
        .catch((err) => {
            let result = []
            for (let k in form.value) {
                const arr = ["name", "siteId", "startTime", "durationName"]
                if (arr.includes(k) && form.value[k]) {
                    result.push(k)
                }
            }
            if (result && result.length === 4) {
                const obj = { ...form.value, startTime: dayjs(form.value.startTime).format("YYYY-MM-DD HH:mm") }
                http.post("/app/siteBooking/create", obj).then((res) => {
                    uni.showToast({
                        title: res.message,
                        icon: "none"
                    })
                    uni.navigateBack()
                })
            }
            console.log(result, form.value)
            console.log(err, "错误")
        })
}
</script>

<style lang="scss" scoped>
.add_booking_page {
    background: $uni-bg-color-grey;
    min-height: calc(100vh - 152rpx);
    padding-bottom: 176rpx;

    .booking_type {
        padding: 30rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: $uni-bg-color;

        .type_lable {
            font-weight: 400;
            font-size: 28rpx;
            color: $uni-text-color;
            line-height: 40rpx;
        }

        .select_type {
            display: flex;
            align-items: center;
            font-weight: 400;
            font-size: 28rpx;
            color: $uni-text-color-grey;

            .icons_class {
                margin-top: 4rpx;
            }
        }
    }

    .form_box {
        margin-top: 20rpx;
        padding: 30rpx;
        background: $uni-bg-color;

        .form_item {
            display: flex;
            flex-direction: column;
        }

        .input_field {
            padding: 20rpx;
            min-height: 40rpx;
            background: $uni-bg-color-grey;
            display: flex;
            align-items: center;
            position: relative;

            input {
                width: 100%;
                height: 80rpx;
            }

            :deep(.uni-icons) {
                display: none;
            }

            :deep(.uni-date-x) {
                background: none !important;
                justify-content: flex-start !important;
                text-align: left !important;
            }

            :deep(.uni-date__x-input) {
                padding: 0 !important;
            }

            .text_num {
                position: absolute;
                top: 20rpx;
                right: 30rpx;
                font-weight: 400;
                font-size: 28rpx;
                color: #999999;
                line-height: 40rpx;
            }
        }

        .booking_datetime {
            padding: 0rpx 20rpx;
        }

        .input_name {
            padding: 0rpx 120rpx 0 20rpx;
        }

        .uni-input-placeholder {
            font-size: 28rpx;
        }

        .textarea_field {
            background: $uni-bg-color-grey;
            padding: 20rpx;
            min-height: 160rpx;

            textarea {
                width: 100%;
            }
        }
    }

    .sign_in {
        margin-top: 20rpx;
        padding: 30rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: $uni-bg-color;

        .lable {
            font-weight: 400;
            font-size: 28rpx;
            color: $uni-text-color;
            line-height: 40rpx;
        }
    }

    .footer {
        position: fixed;
        bottom: 0;
        left: 0;
        padding: 30rpx;
        width: calc(100vw - 60rpx);
        background: $uni-bg-color;

        .btn {
            background: var(--primary-color);
            border-radius: 10rpx;
            font-weight: 400;
            font-size: 32rpx;
            color: #ffffff;
            height: 92rpx;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
}
</style>
