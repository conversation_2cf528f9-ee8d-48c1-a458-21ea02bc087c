<template>
    <view class="detail_page">
        <uni-nav-bar statusBar fixed left-icon="left" :title="isExamine == 'true' ? '查看' : '预约详情'" :border="false" @clickLeft="routerBack" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
        <view class="page_content">
            <!-- 预约详情 -->
            <view class="detail">
                <view class="booking_title">
                    <text class="title">{{ info.name || "-" }}</text>
                    <view class="sitetype" v-if="!!info.siteBookingTypeName">{{ info.siteBookingTypeName }}</view>
                    <view class="sign_in_form" v-if="info.status == 2 && info.signIn" @click="goSignInForm">
                        <image class="image" src="@nginx/workbench/siteBooking/sign_in_icon.png" mode="scaleToFill" />
                        签到表
                    </view>
                </view>
                <view class="detail_item" v-for="item in detailLable" :key="item.value">
                    <text class="lable">{{ item.label }}</text>
                    <text class="value">
                        <text v-if="item.value === 'signList'">
                            {{ info[item.value]?.map((i) => i.userName)?.join("，") }}
                        </text>
                        <text v-else-if="item.value == 'signIn'">{{ info[item.value] ? "需签到" : "无需签到" }}</text>
                        <text v-else>{{ info[item.value] }}</text>
                    </text>
                </view>
            </view>

            <!-- 预约场地 -->
            <view class="detail">
                <view class="booking_title">
                    <text class="title">预约场地</text>
                    <view class="sitetype" v-if="!!info.siteBookingTypeName">{{ info.siteBookingTypeName }}</view>
                </view>
                <view class="detail_item" v-for="item in siteLable" :key="item.value">
                    <text class="lable">{{ item.label }}</text>
                    <text class="value" v-if="info.siteInfo">
                        <text v-if="item.value === 'location'">
                            {{ info.siteInfo?.name }}
                            <text v-if="info.siteInfo?.floor">{{ info.siteInfo.floor }}-</text>
                            <text v-if="info.siteInfo?.roomNum">{{ info.siteInfo.roomNum }}</text>
                        </text>
                        <text v-else>{{ info.siteInfo[item.value] || "-" }}</text>
                    </text>
                </view>
            </view>

            <view class="approve_detail" v-if="isExamine && examineStatus != 0">
                <text class="approve_title">审核信息</text>
                <view class="approve_list">
                    <view class="approve_item">
                        <view class="approve">
                            <view class="left">
                                <view class="avatar">
                                    <image class="image" v-if="info.sponsorAvatar" :src="info.sponsorAvatar" mode="scaleToFill" />
                                    <text v-else>{{ info.sponsorName }}</text>
                                </view>
                                <view class="approve_status">
                                    <text class="status_title">发起申请</text>
                                    <text class="name">{{ info.sponsorName }}</text>
                                </view>
                            </view>
                            <view class="right">
                                {{ info.sponsorTime }}
                            </view>
                            <view class="line"></view>
                            <view class="status_icon">
                                <image :src="statusIcon[2]" mode="scaleToFill" />
                            </view>
                        </view>
                    </view>

                    <view class="approve_item">
                        <view class="approve">
                            <view class="left">
                                <view
                                    class="avatar"
                                    :style="{
                                        borderColor: statusColor[info.status]
                                    }"
                                >
                                    <image class="image" src="@nginx/components/teacher.png" mode="scaleToFill" />
                                </view>
                                <view class="approve_status">
                                    <text class="status_title">审批人</text>
                                    <view>
                                        <text v-for="(item, index) in info.siteInfo?.list" :key="item.id" class="name">
                                            {{ item.name }}
                                            <text v-if="index != info.siteInfo.list?.length - 1">，</text>
                                        </text>
                                        <text
                                            v-if="[0, 1, 2].includes(info.status)"
                                            :style="{
                                                color: statusColor[info.status]
                                            }"
                                            >{{ info.status == 1 && info.siteInfo.list.length > 1 ? "(其中1人审批即可)" : statusDetailText[info.status] }}</text
                                        >
                                    </view>
                                </view>
                            </view>
                            <view class="right">
                                {{ info.approvalTimeStr }}
                            </view>
                            <view class="status_icon">
                                <image :src="statusIcon[info.status]" mode="scaleToFill" />
                            </view>
                        </view>
                        <view class="reason" v-if="info.status == 0">
                            {{ info.refuseReason }}
                        </view>
                    </view>
                </view>
            </view>
            <view v-if="examineStatus || myStatus">
                <view class="footer" v-if="examineStatus == 2">
                    <button class="btn refuse" @click="refuseApprove">拒绝</button>
                    <button :disabled="passLoading" :loading="passLoading" class="btn pass" @click="passApprove">通过</button>
                </view>
                <view class="footer" v-else>
                    <button class="btn" @click="updateSite">{{ [1, 2].includes(info.status) ? "取消预约" : "重新编辑" }}</button>
                </view>
            </view>
        </view>

        <!-- 拒绝弹框 -->
        <yd-popup ref="refusePopup" :titleflag="false" @confirm="refuseConfirm">
            <view class="refuse_popup">
                <text class="refuse_title">拒绝理由</text>
                <textarea class="textarea" placeholder="请输入拒绝理由（300字以内）" :maxlength="300" v-model="refuseReason" :auto-height="true"></textarea>
            </view>
        </yd-popup>
    </view>
</template>

<script setup>
import { statusColor, statusDetailText, statusIcon, detailLable, siteLable } from "./data"
const siteBookingId = ref(null)
const isExamine = ref(false)
const examineStatus = ref(null)
const myStatus = ref(null)

const refusePopup = ref(null)
const refuseReason = ref("") // 拒绝理由
const info = ref({})
const passLoading = ref(false)

// 获取预约详情
function getBookingDetail() {
    http.get("/app/siteBooking/get", { id: siteBookingId.value }).then((res) => {
        info.value = res.data
    })
}

// 取消预约和重新编辑
function updateSite() {
    // 取消预约
    if ([1, 2].includes(info.value.status)) {
        http.post("/app/siteBooking/update", { status: 3, id: siteBookingId.value }).then((res) => {
            uni.showToast({
                title: res.message,
                icon: "none"
            })
            uni.navigateBack()
        })
    } else {
        // 重新编辑
        navigateTo({
            url: "/apps/siteBooking/addBooking",
            query: {
                siteBookingTypeId: info.value.siteBookingTypeId,
                id: siteBookingId.value,
                isEdit: true
            }
        })
    }
}

// 拒绝申请
function refuseApprove() {
    refusePopup.value.open()
}

// 确认拒绝
function refuseConfirm() {
    if (!refuseReason.value) {
        uni.showToast({
            title: "请输入拒绝理由",
            icon: "none"
        })
        return
    }
    http.post("/app/siteBooking/update", { status: 0, refuseReason: refuseReason.value, id: siteBookingId.value }).then((res) => {
        uni.showToast({
            title: res.message,
            icon: "none"
        })
        uni.navigateBack()
    })
}

// 同意申请
function passApprove() {
    passLoading.value = true
    http.get("/app/siteBookingType/checkApproved", { id: siteBookingId.value })
        .then(async (res) => {
            if (!res.code == 0) return
            await http.post("/app/siteBooking/update", { status: 2, id: siteBookingId.value }).then((res) => {
                uni.showToast({
                    title: res.message,
                    icon: "none"
                })
                uni.navigateBack()
            })
        })
        .finally(() => {
            passLoading.value = false
        })
}

function goSignInForm() {
    navigateTo({
        url: "/apps/siteBooking/signInForm",
        query: {
            siteBookingId: siteBookingId.value
        }
    })
}

onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    isExamine.value = Boolean(options.isExamine) // 是否为自己的参与的
    examineStatus.value = Number(options.examineStatus) // 是否为自己的待处理
    myStatus.value = Number(options.status)
    siteBookingId.value = options.id
    getBookingDetail()
})
</script>

<style lang="scss" scoped>
.detail_page {
    min-height: calc(100vh - 166rpx);
    background: $uni-bg-color-grey;
    padding-bottom: 166rpx;

    .page_content {
        .detail {
            margin-top: 20rpx;
            background: $uni-bg-color;
            padding: 30rpx;

            .booking_title {
                display: flex;
                justify-content: space-between;
                align-items: center;
                border-bottom: 1rpx solid $uni-border-color;
                padding-bottom: 30rpx;

                .title {
                    font-weight: 500;
                    font-size: 30rpx;
                    color: $uni-text-color;
                    line-height: 42rpx;
                }

                .sitetype {
                    width: 94rpx;
                    height: 48rpx;
                    background: var(--primary-bg-color);
                    border-radius: 24rpx;
                    font-weight: 400;
                    font-size: 26rpx;
                    color: var(--primary-color);
                    line-height: 48rpx;
                    text-align: center;
                }

                .sign_in_form {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-weight: 400;
                    font-size: 28rpx;
                    color: var(--primary-color);
                    line-height: 40rpx;

                    .image {
                        width: 40rpx;
                        height: 40rpx;
                    }
                }
            }

            .detail_item {
                margin-top: 30rpx;
                font-weight: 400;
                font-size: 28rpx;
                color: $uni-text-color-grey;
                line-height: 40rpx;

                .value {
                    color: $uni-text-color;
                }
            }
        }

        .approve_detail {
            padding: 30rpx;
            background: $uni-bg-color;
            margin-top: 20rpx;

            .approve_title {
                font-weight: 500;
                font-size: 30rpx;
                color: $uni-text-color;
                line-height: 42rpx;
            }

            .approve_list {
                margin-top: 30rpx;

                .approve_item {
                    margin-bottom: 60rpx;
                    position: relative;

                    .approve {
                        display: flex;
                        justify-content: space-between;

                        .left {
                            display: flex;
                            flex: 1;

                            .avatar {
                                min-width: 76rpx;
                                width: 76rpx;
                                height: 76rpx;
                                background: #bfbfbf;
                                border-radius: 8rpx;
                                border: 2rpx solid var(--primary-color);
                                font-weight: 400;
                                font-size: 24rpx;
                                color: #ffffff;
                                line-height: 76rpx;
                                text-align: center;

                                .image {
                                    width: 100%;
                                    height: 100%;
                                    border-radius: 8rpx;
                                }
                            }

                            .approve_status {
                                display: flex;
                                flex-direction: column;
                                font-weight: 400;
                                font-size: 24rpx;
                                margin-left: 20rpx;

                                .status_title {
                                    color: $uni-text-color;
                                    line-height: 40rpx;
                                }

                                .name {
                                    color: #666666;
                                    line-height: 34rpx;
                                }
                            }
                        }

                        .right {
                            text-align: right;
                            font-weight: 400;
                            font-size: 24rpx;
                            color: #999999;
                            line-height: 34rpx;
                        }
                    }

                    .line {
                        position: absolute;
                        top: 76rpx;
                        left: 40rpx;
                        width: 2rpx;
                        height: 100%;
                        background: var(--primary-color);
                    }

                    .status_icon {
                        width: 28rpx;
                        height: 28rpx;
                        position: absolute;
                        top: 48rpx;
                        left: 66rpx;

                        image {
                            width: 100%;
                            height: 100%;
                        }
                    }

                    .reason {
                        margin: 10rpx 0 0 92rpx;
                        font-weight: 400;
                        font-size: 24rpx;
                        color: $uni-text-color;
                        line-height: 34rpx;
                    }
                }
            }
        }

        .footer {
            position: fixed;
            bottom: 0rpx;
            left: 0;
            width: calc(100vw - 60rpx);
            padding: 30rpx 30rpx 40rpx 30rpx;
            background: $uni-bg-color;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .btn {
                width: 100%;
                height: 92rpx;
                background: var(--primary-color);
                border-radius: 10rpx;
                font-weight: 400;
                font-size: 32rpx;
                color: #ffffff;
                line-height: 92rpx;
                text-align: center;
                flex: 1;
            }

            .refuse {
                background: #ffffff;
                border-radius: 10rpx;
                border: 2rpx solid #d8d8d8;
                margin-right: 15rpx;
                color: #666666;
            }

            .pass {
                background: var(--primary-color);
                border-radius: 10rpx;
                margin-left: 15rpx;
                border: 2rpx solid var(--primary-color);
            }
        }
    }

    .refuse_popup {
        padding-top: 30rpx;

        .refuse_title {
            font-weight: 500;
            font-size: 34rpx;
            color: #333333;
            line-height: 48rpx;
        }

        .textarea {
            margin-top: 30rpx;
            padding: 20rpx;
            width: 94%;
            background: #f9faf9;
            min-height: 160rpx;
            font-size: 28rpx;
            font-weight: 400;
            color: #333333;
            line-height: 40rpx;
            text-align: left;
        }
    }
}
</style>
