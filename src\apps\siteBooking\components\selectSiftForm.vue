<template>
    <view>
        <!-- 筛选 -->
        <uni-popup ref="siftFormPopup" type="bottom" :is-mask-click="false" :safe-area="false">
            <view class="sift_form_page">
                <uni-nav-bar :border="false" @clickLeft="close" left-icon="left" title="筛选" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"></uni-nav-bar>
                <view class="form_box">
                    <view class="form_item">
                        <text class="label">按建筑</text>
                        <view class="value" @click="selectBuildRef.open()">
                            <text>{{ buildItem.name || "请选择" }}</text>
                            <uni-icons type="right" size="20"></uni-icons>
                        </view>
                    </view>
                    <view class="form_item">
                        <text class="label">按场地类型</text>
                        <view class="value" @click="selectSiteTypeRef.open()">
                            <text>{{ siteTypeName || "请选择" }}</text>
                            <uni-icons type="right" size="20"></uni-icons>
                        </view>
                    </view>
                    <view class="form_item galleryful">
                        <text class="label">按场地类型</text>
                        <view class="galleryful_list">
                            <view
                                class="galleryful_item"
                                :class="{
                                    active_item: activeGalleryful.key == item.key
                                }"
                                v-for="(item, index) in galleryfulList"
                                :key="index"
                                @click="selectGalleryful(item)"
                            >
                                {{ item.name }}
                            </view>
                        </view>
                        <text class="customize_title">自定义</text>
                        <view class="customize">
                            <input type="number" v-model="customize.minPeppleNum" placeholder="请输入" />
                            <text class="text">至</text>
                            <input type="number" v-model="customize.maxPeppleNum" placeholder="请输入" />
                        </view>
                    </view>
                </view>
                <view class="footer">
                    <button class="btn reset" @click="reset">重置</button>
                    <button class="btn confirm" @click="confirm">确认</button>
                </view>
            </view>
            <!-- 选择建筑 -->
            <yd-select-popup title="请选择建筑" ref="selectBuildRef" :list="buildList" :fieldNames="{ value: 'id', label: 'name' }" @closePopup="closeBuild" :selectId="[buildItem.id]" />

            <!-- 选择场地类型 -->
            <yd-select-popup title="请选择场地类型" ref="selectSiteTypeRef" :list="siteTypeList" :fieldNames="{ value: 'id', label: 'name' }" @closePopup="closeSiteType" :multiple="true" :selectId="siteTypeIds" />
        </uni-popup>
    </view>
</template>

<script setup>
const emit = defineEmits(["confirm"])
const siftFormPopup = ref(null)
const selectBuildRef = ref(null)
const selectSiteTypeRef = ref(null)
const buildList = ref([])
const siteTypeList = ref([])
const galleryfulList = [
    {
        minPeppleNum: "0",
        maxPeppleNum: 50,
        name: "0-50人",
        key: 1
    },
    {
        minPeppleNum: 51,
        maxPeppleNum: 100,
        name: "51-100人",
        key: 2
    },
    {
        minPeppleNum: 101,
        maxPeppleNum: 150,
        name: "101-150人",
        key: 3
    },
    {
        minPeppleNum: 150,
        maxPeppleNum: 100000,
        name: "150人以上",
        key: 4
    }
]

const activeGalleryful = ref({})
const customize = ref({
    minPeppleNum: null,
    maxPeppleNum: null
})
const buildItem = ref({})
const siteTypeName = ref(null)
const siteTypeIds = ref([])
function selectGalleryful(item) {
    activeGalleryful.value = item
    customize.value = {}
}

function closeBuild(val) {
    if (!val || val.id == buildItem.value.id) return
    buildItem.value = val
}

function closeSiteType(val) {
    if (!val) return
    siteTypeIds.value = val.map((i) => i.id)
    siteTypeName.value = val.map((i) => i.name)?.join("，")
}

function reset() {
    customize.value.minPeppleNum = null
    customize.value.maxPeppleNum = null
    activeGalleryful.value = {}
    buildItem.value = {}
    siteTypeIds.value = []
    siteTypeName.value = null
}

function confirm() {
    const form = {
        buildName: buildItem.value.name,
        buildingId: buildItem.value.id,
        minPeppleNum: activeGalleryful.value.minPeppleNum || customize.value.minPeppleNum,
        maxPeppleNum: activeGalleryful.value.maxPeppleNum || customize.value.maxPeppleNum,
        siteTypeIds: siteTypeIds.value
    }
    if (Number(form.maxPeppleNum) < Number(form.minPeppleNum)) {
        uni.showToast({
            title: "请输入正确的人数范围",
            icon: "none"
        })
        return
    }
    emit("confirm", form)
}

// 打开选择组件
const open = () => {
    siftFormPopup.value.open()
    getBuildList()
    getSiteTypeList()
}

// 关闭弹框
const close = () => {
    siftFormPopup.value.close()
}

function getBuildList() {
    http.get("/app/siteBookingType/buildingSiteList").then((res) => {
        buildList.value = res.data
    })
}

function getSiteTypeList() {
    http.get("/app/site/siteTypeList").then((res) => {
        siteTypeList.value = res.data
    })
}
defineExpose({ open, close })
</script>

<style lang="scss" scoped>
.sift_form_page {
    height: 100vh;
    width: 100vw;
    background: $uni-bg-color-grey;

    .form_box {
        // height: calc(100vh - 268rpx);
        overflow: auto;
        background: $uni-bg-color;
        margin-top: 20rpx;

        .form_item {
            padding: 30rpx;
            border-bottom: 1rpx solid $uni-border-color;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;

            .label {
                color: $uni-text-color;
            }

            .value {
                display: flex;
                align-items: center;
                justify-content: flex-end;
                color: $uni-text-color-grey;
                text-align: right;
                flex: 1;
            }
        }

        .galleryful {
            flex-direction: column;
            border-bottom: none;
            align-items: flex-start;

            .galleryful_list {
                margin: 30rpx 0 10rpx 0;
                display: flex;
                flex-wrap: wrap;
                justify-content: space-between;

                .galleryful_item {
                    width: 320rpx;
                    height: 80rpx;
                    background: #f9faf9;
                    border: 2rpx solid #f9faf9;
                    border-radius: 4rpx;
                    margin-bottom: 20rpx;
                    font-weight: 400;
                    font-size: 28rpx;
                    color: $uni-text-color-grey;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    line-height: 40rpx;
                }

                .active_item {
                    background: var(--primary-bg-color);
                    border: 2rpx solid var(--primary-color);
                    color: var(--primary-color);
                }
            }

            .customize_title {
                font-weight: 400;
                font-size: 24rpx;
                color: #666666;
                line-height: 34rpx;
                padding-bottom: 10rpx;
            }

            .customize {
                display: flex;
                align-items: center;
                justify-content: space-between;
                height: 80rpx;

                .text {
                    font-weight: 400;
                    font-size: 28rpx;
                    color: #666666;
                    line-height: 40rpx;
                    padding: 0 12rpx;
                    text-align: center;
                }

                input {
                    height: 100%;
                    background: #f9faf9;
                    border-radius: 4rpx;
                    text-align: center;
                }
            }
        }
    }

    .footer {
        position: absolute;
        bottom: 20rpx;
        left: 0;
        width: calc(100vw - 60rpx);
        height: 106rpx;
        background: #ffffff;
        border-radius: 10rpx;
        display: flex;
        justify-content: center;
        padding: 30rpx;

        .btn {
            flex: 1;
            font-weight: 400;
            font-size: 32rpx;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .confirm {
            border: 2rpxsolid var(--primary-color);
            background: var(--primary-color);
            border-radius: 0rpx 10rpx 10rpx 0rpx;
            color: #ffffff;
        }

        .reset {
            border: 2rpx solid var(--primary-color);
            border-radius: 10rpx 0rpx 0rpx 10rpx;
            color: var(--primary-color);
            background: #ffffff;
        }
    }
}
</style>
