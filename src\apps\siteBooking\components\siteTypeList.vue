<template>
    <view class="site_type_list">
        <text class="tip_text">点击下方预约类型，即可进入预约：</text>
        <view class="type_list" v-if="typeList && typeList.length">
            <view class="type_item" @click="changeType(item)" v-for="item in typeList" :key="item.id">
                <view class="type_image">
                    <image class="image" :src="typeImage(item.type)" mode="widthFix" />
                    <text class="name ellipsis">{{ item.name }}</text>
                </view>
            </view>
        </view>
        <yd-empty text="暂无预约类型" v-else />
    </view>
</template>

<script setup>
const emit = defineEmits(["changeType"])
const typeList = ref([])
const imageUrl = {
    1: "@nginx/workbench/siteBooking/meeting_type.png",
    2: "@nginx/workbench/siteBooking/classroom_type.png",
    3: "@nginx/workbench/siteBooking/activity_type.png",
    4: "@nginx/workbench/siteBooking/default_type.png"
}

const typeImage = computed(() => {
    return (type) => {
        if ([1, 2, 3].includes(type)) {
            return imageUrl[type]
        } else {
            return imageUrl[4]
        }
    }
})

function getTypeList() {
    http.get("/app/siteBookingType/getBookingSiteNum").then((res) => {
        typeList.value = res.data
    })
}

function changeType(item) {
    emit("changeType", item)
}

onMounted(() => {
    getTypeList()
})
</script>

<style lang="scss" scoped>
.site_type_list {
    padding: 30rpx;
    .tip_text {
        font-weight: 400;
        font-size: 28rpx;
        color: $uni-text-color-grey;
        line-height: 40rpx;
    }
    .type_list {
        display: flex;
        max-width: calc(100vw - 60rpx);
        overflow-x: auto;
        .type_item {
            .type_image {
                position: relative;
                margin: 20rpx 18rpx 20rpx 0;
                width: 150rpx;
                height: 180rpx;
                .image {
                    width: 100%;
                    height: 100%;
                }
                .name {
                    width: 100%;
                    position: absolute;
                    top: 30rpx;
                    left: 0;
                    text-align: center;
                    font-weight: 600;
                    font-size: 28rpx;
                    color: $uni-text-color-inverse;
                    line-height: 40rpx;
                }
            }
        }
    }
    :deep(.no_data) {
        margin-top: 20rpx !important;
    }
}
</style>
