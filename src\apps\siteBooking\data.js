import useStore from "@/store"
import dayjs from "dayjs"
import isoWeek from "dayjs/plugin/isoWeek"
dayjs.extend(isoWeek)

// 这一周日期的开始和结束时间
export function getWeekStartEnd(day) {
    const startOfWeek = day.startOf("isoWeek").format("YYYY-MM-DD")
    const endOfWeek = day.endOf("isoWeek").format("YYYY-MM-DD")
    return [startOfWeek, endOfWeek]
}

// 获取这一天的对象
export function getCurrentDayInfo(day) {
    const dateFormatted = day.format("YYYY-MM-DD")
    const dayFormatted = day.format("MM-DD")
    const weekText = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"][day.day()]
    return { date: dateFormatted, day: dayFormatted, week: weekText, weekNum: day.day() }
}

// 这一周日期数组
export function getDatesInRange(dateList) {
    if (dateList && dateList.length > 1) {
        const dates = []
        let currentDate = dayjs(dateList[0]) // 开始日期
        const end = dayjs(dateList[1]) // 结束日期
        while (currentDate.isBefore(end) || currentDate.isSame(end)) {
            const dateFormatted = currentDate.format("YYYY-MM-DD")
            const dayFormatted = currentDate.format("MM-DD")
            const weekText = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"][currentDate.day()]
            dates.push({ date: dateFormatted, day: dayFormatted, week: weekText, weekNum: currentDate.day() })
            currentDate = currentDate.add(1, "day") // 逐日添加
        }
        return dates
    }
    return []
}

export function createTimeList() {
    let time = []
    for (let i = 8; i < 24; i++) {
        time.push({
            h: i
        })
    }
    return time.map(({ h }) => {
        return [15, 30, 45, 59].map((m) => ({
            h,
            m,
            flag: false
        }))
    })
}

export function setColoe(start, end, h, m) {
    const ms = 60 * 1000 * 15
    const date = new Date(start)
    const year = date.getFullYear() // 年
    const month = date.getMonth() + 1 // 月
    const day = date.getDate() // 日
    const hour = date.getHours() // 时
    const minutes = date.getMinutes() // 分
    const startTime = new Date(start).getTime()
    const endTime = new Date(end).getTime() + 60 * 1000
    const time = new Date(`${year}/${month}/${day} ${h}:${m}`)

    return new Date(time).getTime() <= endTime && new Date(time).getTime() > new Date(startTime).getTime() ? "red" : "#fff"
}

export const statusList = ref([
    {
        id: null,
        name: "全部"
    },
    {
        id: 0,
        name: "已拒绝"
    },
    {
        id: 1,
        name: "审批中"
    },
    {
        id: 2,
        name: "已通过"
    },
    {
        id: 3,
        name: "已取消"
    },
    {
        id: 4,
        name: "已过期"
    }
])
export const statusText = {
    null: "全部",
    0: "已拒绝",
    1: "审批中",
    2: "已通过",
    3: "已取消",
    4: "已过期"
}

export const statusBg = {
    0: "#fd4f45",
    1: "#faad14",
    2: "var(--primary-color)",
    3: "#bfbfbf",
    4: "#595959"
}

export const statusColor = {
    0: "#F5222D",
    1: "#FAAD14",
    2: "var(--primary-color)"
}

export const statusDetailText = {
    0: "（已拒绝）",
    1: "（待审核）",
    2: "（已同意）"
}

export const statusIcon = {
    0: "@nginx/workbench/siteBooking/refuse_status.png",
    1: "@nginx/workbench/siteBooking/pending_status.png",
    2: "@nginx/workbench/siteBooking/passed_statue.png"
}

const { user } = useStore()
export const identityType = computed(() => {
    const roleCode = user?.identityInfo?.roleCode
    if (roleCode == "eltern") {
        return "eltern"
    } else if (roleCode == "student") {
        return "student"
    } else {
        return "teacher"
    }
})

export const tabs = computed(() => {
    if (identityType.value == "eltern") {
        return [
            {
                name: "我参与的",
                value: "myParticipate",
                type: 0
            }
        ]
    } else {
        return [
            {
                name: "我参与的",
                value: "myParticipate",
                type: 0
            },
            {
                name: "我的预约",
                value: "myBooking",
                type: 1
            },
            {
                name: "待处理",
                value: "bookingApproval",
                type: 2
            }
        ]
    }
})

export const detailLable = [
    {
        label: "签到状态：",
        value: "signIn"
    },
    {
        label: "预约时间：",
        value: "bookingTime"
    },
    {
        label: "预约人：",
        value: "sponsorName"
    },
    {
        label: "参会人：",
        value: "signList"
    },
    {
        label: "预约说明：",
        value: "describe"
    }
]

export const siteLable = [
    {
        label: "场地类型：",
        value: "siteTypeName"
    },
    {
        label: "场地位置：",
        value: "location"
    },
    {
        label: "容纳人数：",
        value: "peppleNum"
    },
    {
        label: "设备：",
        value: "deviceNames"
    }
]
