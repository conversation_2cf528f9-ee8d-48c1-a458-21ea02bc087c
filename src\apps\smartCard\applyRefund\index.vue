<template>
    <!-- 申请退款 applyRefund-->
    <view class="apply_refund">
        <NavBar title="申请退款" :clickLeft="clickLeft"> </NavBar>
        <view class="content">
            <view class="box">
                <List :title="state.form.title" :time="state.form.payTime" :money="state.form.payAmount" :status="state.form.orderStatus"> </List>

                <view class="flow_statement">
                    <uni-list-item v-for="item in refundList" :key="item.key" :title="item.name" :rightText="orderDetailText(item.key)" />
                </view>
                <view class="refund_reason">
                    <text style="color: red">*</text>
                    <text style="font-size: 14px; color: #666666ff">退款原因</text>
                    <view class="record_box">
                        <uni-easyinput class="reset-easyinput" type="textarea" :maxlength="maxlength" :inputBorder="false" v-model="state.refundReason" placeholder="请输入"> </uni-easyinput>
                        <view class="num_box">{{ state.refundReason.length }}/{{ maxlength }}</view>
                    </view>
                </view>
            </view>
        </view>
        <view class="footer">
            <button class="mini-btn" type="primary" :disabled="!state.refundReason.length" @click="handleSave">确定</button>
        </view>
    </view>
</template>

<script setup>
import NavBar from "../components/navBar.vue"
import List from "../components/list.vue"

const refundList = [
    { name: "本次申请退款金额：", key: "payAmount" },
    { name: "已退款总额：", key: "totalRefundAmount" },
    { name: "一卡通余额：", key: "balance" },
    { name: "可退款金额：", key: "refundAmount" },
    { name: "订单编号：", key: "tradeNo" },
    { name: "交易流水号：", key: "orderNo" },
    { name: "创建时间：", key: "createTime" },
    { name: "支付时间：", key: "payTime" },
    { name: "支付渠道：", key: "payMethod" }
]
const maxlength = 200
const state = reactive({
    form: {},
    refundReason: "",
    applyRefundId: "",
    personId: ""
})

const orderDetailText = computed(() => {
    return (item) => {
        if (item == "payMethod") {
            return state.form[item] == 1 ? "微信" : "支付宝"
        }
        if (typeof state.form[item] == "number") {
            return state.form[item] || "0"
        }
        return state.form[item] || "-"
    }
})
// 申请退款原因
const handleSave = () => {
    const { form, refundReason, personId } = state
    const params = {
        personId,
        refundReason,
        refundAmount: form.refundAmount,
        id: form.id
    }
    http.post("/unicard/app/order/refund-request", params).then(({ message }) => {
        uni.showToast({ title: message })
        clickLeft()
    })
}
const initPage = async () => {
    const params = { id: state.applyRefundId }
    const { data } = await http.post("/unicard/app/order/refund-request-query", params)
    state.form = data
}
onLoad((item) => {
    state.applyRefundId = item.id || ""
    state.personId = item.personId || ""
    initPage()
})

function clickLeft() {
    uni.navigateBack()
}
</script>
<style lang="scss" scoped>
.apply_refund {
    background: $uni-bg-color-grey;
    height: 100vh;

    .content {
        background: $uni-bg-color-grey;
        height: calc(100vh - 110px);
        overflow: hidden auto;

        .box {
            background: $uni-bg-color;
            margin-top: 30rpx;
            padding: 30rpx;

            :deep(.list) {
                padding: 0;
                margin-top: 0;

                .uni-badge {
                    color: var(--primary-color);
                }
            }

            .flow_statement {
                // 虚线上边框
                border-top: 1rpx dashed $uni-border-color;
                border-bottom: 1rpx solid $uni-border-color;
                padding: 20rpx 0;
                margin: 14rpx 0;

                :deep(.border--left) {
                    display: none;
                }

                :deep(.uni-list-item__container) {
                    padding: 15rpx 0;
                }

                :deep(.uni-list-item__extra) {
                    .uni-list-item__extra-text {
                        font-weight: 400;
                        font-size: 28rpx;
                        color: #999999;
                    }
                }
            }

            .refund_reason {
                .record_box {
                    position: relative;

                    .reset-easyinput {
                        :deep(.uni-easyinput__content-textarea) {
                            border-radius: 10rpx;
                            overflow: hidden;
                            background-color: #f6f6f6ff;
                            padding: 10rpx 10rpx 35rpx;
                        }
                    }

                    .num_box {
                        text-align: right;
                        color: $uni-text-color-grey;
                        position: absolute;
                        bottom: 40rpx;
                        right: 40rpx;
                        font-weight: 400;
                        font-size: 28rpx;
                    }
                }
            }
        }
    }

    .footer {
        padding: 20px 32rpx;
        background: $uni-bg-color;
        display: flex;
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;

        .mini-btn {
            background-color: var(--primary-color);
            flex: 1;
        }
    }
}
</style>
