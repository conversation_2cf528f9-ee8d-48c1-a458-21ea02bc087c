<template>
    <!-- 卡片详情 cardDetail-->
    <view class="card_detail">
        <NavBar title="卡片详情" :clickLeft="clickLeft"> </NavBar>
        <view class="content">
            <uni-list-item v-for="item in cardForm" :key="item.key" :title="item.name" :showArrow="item.showArrow" :clickable="item.showArrow" @click="onClick(item.key)">
                <template v-slot:footer>
                    <image v-if="item.key == 'imgPath'" class="slot-image" :src="state.form[item.key]"></image>
                    <text v-else-if="item.key == 'validityStartDate'" class="slot-text"> {{ state.form.validityStartDate }} - {{ state.form.validityEndDate }} </text>
                    <text v-else class="slot-text">
                        {{ state.form[item.key] || "-" }}
                    </text>
                </template>
            </uni-list-item>
        </view>
        <view class="footer">
            <button class="mini-btn" type="primary" plain="true" size="mini" @click="handleTo('recharge')">充值</button>

            <button v-if="state.form.cardStatus == 'normal'" class="mini-btn" type="primary" plain="true" size="mini" @click="handleTo('reportLoss')">挂失</button>

            <button v-if="state.form.cardStatus === 'lost'" class="mini-btn" type="primary" plain="true" size="mini" @click="handleTo('unLost')">解挂</button>

            <button class="mini-btn" type="primary" plain="true" size="mini" @click="handleTo('record')">记录</button>
        </view>
    </view>
</template>

<script setup>
import { onLoad } from "@dcloudio/uni-app"
import NavBar from "../components/navBar.vue"
import useStore from "@/store"
const { user } = useStore()
const cardForm = [
    { name: "头像", key: "imgPath" },
    { name: "姓名", key: "name" },
    { name: "卡号", key: "cardNo" },
    { name: "余额", key: "balance" },
    { name: "卡片状态", key: "cardStatusName" },
    { name: "卡片有效期", key: "validityStartDate" },
    { name: "发卡日期", key: "activatedTime" },
    { name: "卡片押金", key: "cashPledge" }
]
const popup = ref(null)
const state = reactive({
    form: {
        schoolName: "",
        badgeUrl: "",
        name: "",
        studentId: "",
        imgPath: "",
        cardNo: "",
        validityStartDate: "",
        validityEndDate: "",
        cashPledge: ""
    },
    personId: "",
    studentId: ""
})

const handleTo = (routType) => {
    const { name, personId, cardNo } = state.form
    let query = {
        studentName: name,
        cardNo,
        personId
    }
    if (routType == "unLost") {
        query.type = "unboxing"
        routType = "reportLoss"
    } else if (routType == "recharge") {
        // 充值
        query.personId = state.personId
        query.studentId = state.studentId
    }
    navigateTo({
        url: `/apps/smartCard/${routType}/index`,
        query
    })
}
const initPage = () => {
    let params = { id: state.personId }
    http.post("/unicard/app/card/info", params).then(({ data }) => {
        state.form = data
    })
}
onShow((item) => {
    initPage()
})
onLoad((item) => {
    state.personId = item.personId || ""
    state.studentId = item.studentId || ""
})

function clickLeft() {
    uni.navigateTo({
        url: "/apps/smartCard/index"
    })
}
</script>

<style lang="scss" scoped>
.card_detail {
    background: $uni-bg-color-grey;
    height: 100vh;

    .content {
        background: $uni-bg-color;

        .slot-image {
            width: 80rpx;
            height: 80rpx;
            border-radius: 50%;
        }

        .slot-text {
            font-weight: 400;
            font-size: 28rpx;
            color: #999999;
        }

        :deep(.uni-icons) {
            padding-left: 0;
        }

        :deep(.uni-list-item__container) {
            align-items: center;
        }
    }

    :deep(.container--right) {
        align-items: center;
    }

    .footer {
        padding: 20px 32rpx;
        background: $uni-bg-color;
        display: flex;
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;

        .mini-btn {
            border-color: var(--primary-color);
            color: var(--primary-color);
            flex: 1;
            margin: 0 10rpx;
            padding: 10rpx 0;
        }
    }
}
</style>
