<template>
    <view class="list">
        <view class="handle">
            <view>{{ title }}</view>
            <uni-badge v-if="badgesCompute.text" class="uni-badge-left-margin" :inverted="true" size="normal" :text="badgesCompute.text" :type="badgesCompute.key" />
        </view>
        <view class="content">
            <view class="time">时间：{{ time || "-" }}</view>
            <view class="money">¥ {{ payAmount(props.money) }}</view>
        </view>
        <slot name="slot-chapter"></slot>
    </view>
</template>

<script setup>
import { payAmount } from "../components/index.js"

const props = defineProps({
    title: { type: String, default: "" },
    time: { type: String, default: "" },
    money: { type: Number, default: null },
    status: { type: Number, default: null }
})

// 未支付：orderStatus: 0 ,1
// 已支付： orderStatus: 2,3,4,5
// 已关闭：orderStatus  6 ,7
const badgesCompute = computed(() => {
    const _orderStatus = props.status || null
    if ([0, 1].includes(_orderStatus)) {
        return { text: "未支付", key: "warning" }
    }
    if ([2, 3, 4, 5].includes(_orderStatus)) {
        return { text: "已支付", key: "primary" }
    }
    if ([6, 7].includes(_orderStatus)) {
        return { text: "已关闭", key: "" }
    }
    return { text: "", key: "info" }
})
</script>

<style lang="scss" scoped>
.list {
    background-color: $uni-bg-color;
    border-radius: 16rpx;
    padding: 24rpx 30rpx;
    margin: 20rpx 0;

    .handle {
        color: $uni-text-color;
        margin-bottom: 16rpx;
        font-weight: 400;
        font-size: 32rpx;

        display: flex;
        justify-content: space-between;
        align-items: center;
        .uni-badge,
        :deep(.uni-badge) {
            color: $uni-color-primary !important;
        }
    }

    .content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: $uni-text-color;

        .time {
            color: $uni-text-color-grey;
            font-weight: 400;
            font-size: 28rpx;
        }

        .money {
            font-weight: 600;
            font-size: 36rpx;
        }
    }
}
</style>
