<template>
    <view class="smart_card_home">
        <!-- 一卡通系统 smartCard-->
        <uv-skeletons :loading="state.loading" :skeleton="state.skeleton"></uv-skeletons>
        <z-paging ref="paging" v-model="state.dataList" @query="initPage" @onRefresh="onRefresh" :auto="false">
            <template #top>
                <uni-nav-bar statusBar fixed left-icon="left" @clickLeft="clickLeft" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()">
                    <template #left>
                        <view class="logo-school">
                            <image class="logo" :src="state.userForm.badgeUrl"></image>
                            <text class="school">{{ state.userForm.schoolName }}</text>
                        </view>
                    </template>
                </uni-nav-bar>
                <view class="card">
                    <view class="card_top">
                        <view class="left">
                            <view class="user" @click="onClickChange">
                                <image class="logo" :src="state.userForm.imgPath"></image>
                                <view class="name">{{ state.userForm.name }}</view>
                                <image class="icons" v-if="identityType === 'eltern'" :src="'https://file.1d1j.cn/cloud-mobile/smartCard/childSwitc.png'" />
                            </view>
                            <view class="tips" @click="handleJumpTo('cardDetail')"
                                >卡 号：{{ state.userForm.cardNo }}
                                <uni-icons type="right" size="12" color="#fff" v-if="state.userForm.cardNo"></uni-icons>
                            </view>
                            <view class="tips" style="margin-bottom: 0"> 有效期：{{ state.userForm.validityStartDate }}-{{ state.userForm.validityEndDate }} </view>
                        </view>
                        <view class="right">
                            <view class="money">{{ state.userForm.balance }}</view>
                            <view class="tips" style="margin: 2px 0 10px">一卡通余额</view>
                            <view class="tips">
                                <uni-tag text="充值" size="mini" type="warning" :custom-style="customStyle" @click="handleJumpTo('recharge')" />
                                <uni-tag text="明细" size="mini" type="primary" :custom-style="amountcustomStyle" @click="handleJumpTo('amountDetail')" />
                            </view>
                        </view>
                    </view>
                    <view class="card_support_order">
                        <view class="support_order_item">
                            <view class="num"
                                >{{ state.consumpOrderForm.todayPayAmount || 0 }}
                                <text class="unit">元</text>
                            </view>
                            <view class="tips">今日消费</view>
                        </view>
                        <view class="support_order_item" @click="handleJumpTo('myOrder')">
                            <view class="num"
                                >{{ state.consumpOrderForm.totalOrderNum || 0 }}
                                <text class="unit">单</text>
                            </view>
                            <view class="tips">我的订单</view>
                        </view>
                    </view>
                </view>
            </template>
            <view class="content">
                <uni-section class="rese-section" size="normal" title="订单记录" type="line">
                    <view class="" v-for="item in state.dataList" :key="item.id" @click="handleJumpTo('myOrderDetail', item.id)">
                        <List :title="item.title" :time="item.payTime" :money="item.payAmount" />
                    </view>
                </uni-section>
            </view>
            <template #empty>
                <yd-empty text="暂无数据" />
            </template>
            <uni-popup ref="childPopup" type="bottom" background-color="#fff" borderRadius="20rpx 20rpx 0 0">
                <view class="child-switch">
                    <view class="handle">
                        <text class="title">切换孩子</text>
                        <uni-icons class="close" type="closeempty" size="16" @click="childPopup.close()"></uni-icons>
                    </view>
                    <view class="child-list">
                        <radio-group @change="radioChange">
                            <label class="uni-list-cell" v-for="item in studentInfo" :key="item.studentId">
                                <view>{{ item.studentName }}</view>
                                <view>
                                    <radio :value="item.studentId" :checked="item.studentId === state.personId" color="#00b781" />
                                </view>
                            </label>
                        </radio-group>
                    </view>
                </view>
            </uni-popup>
        </z-paging>
    </view>
</template>

<script setup>
import { sendAppEvent, checkPlatform } from "@/utils/sendAppEvent.js"
import NavBar from "./components/navBar.vue"
import List from "./components/list.vue"
import useStore from "@/store"
const { user } = useStore()

const customStyle = {
    backgroundColor: "#FF5519",
    borderColor: "#FF5519",
    borderRadius: "5px",
    marginLeft: "10px",
    color: "#fff",
    fontSize: "10px"
}
const amountcustomStyle = {
    backgroundColor: "#FF871FFF",
    borderColor: "#FF871FFF",
    borderRadius: "5px",
    marginLeft: "10px",
    color: "#fff",
    fontSize: "10px"
}

const userInfo = computed(() => user.userInfo)
const studentInfo = computed(() => user.studentInfo)
const identityType = computed(() => user?.identityInfo?.roleCode || "")
const childPopup = ref(false)
const paging = ref(null)

const state = reactive({
    dataList: [],
    consumpOrderForm: {
        todayPayAmount: 0,
        totalOrderNum: 0
    },
    userForm: {
        schoolName: "",
        badgeUrl: "",
        name: "",
        imgPath: "",
        cardNo: "",
        validityStartDate: "",
        validityEndDate: "",
        cashPledge: "",
        personId: ""
    },
    personId: "",
    loading: true,
    skeleton: [
        {
            type: "flex",
            num: 3,
            children: [
                {
                    type: "avatar",
                    num: 1,
                    style: "marginRight: 10rpx;"
                },
                {
                    type: "line",
                    num: 3,
                    gap: "30rpx",
                    style: ["width: 200rpx;", null, "width:400rpx;"]
                }
            ]
        }
    ]
})

// 弹切换孩子框
const onClickChange = () => {
    if (identityType.value == "eltern" && studentInfo.value.length > 1) {
        childPopup.value.open()
    }
}
// 跳转各种页面
const handleJumpTo = (to, _id) => {
    console.log("to", to)

    // recharge - 充值
    if (to === "recharge" && !state.userForm.cardNo) {
        uni.showToast({
            title: "请联系老师发放一卡通后进行使用",
            icon: "none",
            duration: 2000
        })
        return
    }
    let url = `/apps/smartCard/${to}/index`
    let query = { id: state.userForm?.id || "", personId: state.userForm.personId }
    // 订单记录
    if (to === "myOrderDetail") {
        url = "/apps/smartCard/myOrder/detail"
        query.id = _id
    } else if (to === "cardDetail") {
        // 卡 号：
        if (state.userForm.personId == "-9999") {
            uni.showToast({
                title: "请联系老师发放一卡通后进行使用",
                icon: "none",
                duration: 2000
            })
            return
        }
        // 再带个学生id过去
        query.studentId = state.personId
    }
    // 充值
    else if (to === "recharge") {
        query.personId = state.personId || state.userForm.personId
    }
    navigateTo({ url, query })
}
const initPage = (pageNo, pageSize) => {
    const params = {
        personId: state.userForm.personId,
        pageNo,
        pageSize
    }

    state.loading = true
    http.post("/unicard/app/order/pay-order-page", params)
        .then(({ data }) => {
            paging.value?.complete(data.list || [])
        })
        .catch(() => {
            paging.value?.complete([])
        })
        .finally(() => {
            state.loading = false
        })
}
// 获取个人信息
const usreInfo = async () => {
    let params = { studentId: state.personId }
    if (identityType.value == "eltern" && !state.personId) {
        params.studentId = studentInfo.value[0]?.studentId || ""
    }
    if (identityType.value == "student" && !state.personId) {
        params.studentId = userInfo.value.identityUserId || ""
    }
    const { data } = await http.post("/unicard/app/person/info", params)
    state.userForm = data
    state.userForm.name = data.name || userInfo.value.name
    //   家长身份的话就拿第一个孩子的信息
    if (identityType.value == "eltern" && !state.personId) {
        state.personId = studentInfo.value[0]?.studentId || ""
        state.userForm.name = studentInfo.value[0]?.studentName || ""
        state.userForm.imgPath = studentInfo.value[0]?.imgPath || ""
    }
    if (identityType.value == "student" && !state.personId) {
        state.personId = userInfo.value.identityUserId || ""
    }
}

// 获取消费记录
const getConsumptionOrder = async () => {
    let params = { personId: state.userForm.personId }
    const { data } = await http.post("/unicard/app/order/statistics", params)
    state.consumpOrderForm = data
}
// 下拉刷新
const onRefresh = async () => {
    await usreInfo()
}
const init = async () => {
    await usreInfo()
    paging.value?.refreshToPage(1)
    await getConsumptionOrder()
}

// 切换孩子
const radioChange = async (evt) => {
    state.personId = evt.detail.value
    childPopup.value.close()
    init()
}

onMounted(async () => {
    state.personId = ""
    state.loading = true
    setTimeout(() => {
        init()
    }, 500)
})

function clickLeft() {
    // #ifdef H5
    const roleArr = ["yide-ios-app", "yide-android-app", "yide-Harmony-app"]
    if (roleArr?.includes(checkPlatform())) {
        sendAppEvent("backApp", {}) ||
            uni.switchTab({
                url: "/pages/workbench/index"
            })
        return
    }
    // #endif
    uni.switchTab({
        url: "/pages/workbench/index"
    })
}
</script>

<style lang="scss" scoped>
.smart_card_home {
    background: linear-gradient(180deg, $uni-bg-color 0%, #f5f7f9 19%, #f2f7f8 100%);
    height: 100vh;

    // #ifdef MP-WEIXIN
    :deep(.uni-section) {
        background: #f4f7f8 !important;
    }

    :deep(.line) {
        background-color: $uni-color-primary;
    }

    // #endif
    :deep(.uni-navbar__header-btns-left) {
        width: 100vw !important;
    }

    .logo-school {
        display: flex;
        align-items: center;
        flex: 1;

        .logo {
            width: 56rpx;
            height: 56rpx;
            margin-right: 16rpx;
            border-radius: 50%;
        }

        .school {
            font-weight: 500;
            font-size: 36rpx;
            color: #000000;
            // 字数超出隐藏
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }

    .card {
        background: var(--primary-color) url("https://file.1d1j.cn/cloud-mobile/smartCard/banner.png") no-repeat center;
        border-radius: 16rpx;
        margin: 20rpx;
        padding: 44rpx;
        color: $uni-bg-color;

        .card_top {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .tips {
                font-weight: 400;
                font-size: 24rpx;
                margin: 10rpx 0;
            }

            .left {
                flex: 1;

                .tips {
                    margin: 16rpx 0;
                    font-size: 26rpx;
                }

                .user {
                    display: flex;
                    align-items: center;

                    .logo {
                        width: 52rpx;
                        height: 52rpx;
                        border-radius: 50%;
                    }

                    .name {
                        font-weight: 500;
                        font-size: 32rpx;
                        margin: 0 8rpx;
                    }

                    .icons {
                        width: 32rpx;
                        height: 32rpx;
                    }
                }
            }

            .right {
                text-align: right;
                width: 200rpx;

                .tips {
                    margin: 0;

                    :deep(.uni-tag) {
                        font-weight: 400 !important;
                        font-size: 24rpx !important;
                        margin-left: 24rpx;
                    }
                }

                .money {
                    font-weight: 500;
                    font-size: 48rpx;
                }
            }
        }

        .card_support_order {
            display: flex;
            align-items: center;
            justify-content: space-around;
            margin-top: 20rpx;
            height: 148rpx;
            background: $uni-bg-color;
            border-radius: 16rpx;

            .support_order_item {
                color: #000000;
                text-align: center;

                .num {
                    font-weight: 500;
                    font-size: 42rpx;

                    .unit {
                        font-size: 20rpx;
                    }
                }

                .tips {
                    font-weight: 400;
                    font-size: 28rpx;
                    color: rgba(0, 0, 0, 0.49);
                }
            }
        }
    }

    .content {
        margin: 20rpx;

        .rese-section {
            background-color: transparent;

            :deep(.uni-section-content),
            :deep(.uni-section-header) {
                padding: 0 !important;
            }

            :deep(.line) {
                background-color: var(--primary-color);
            }
        }
    }

    .child-switch {
        min-height: 400rpx;

        .handle {
            text-align: center;
            position: relative;
            height: 80rpx;
            line-height: 80rpx;

            .close {
                position: absolute;
                right: 20rpx;
                top: 0;
            }
        }

        .child-list {
            .uni-list-cell {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 20rpx;
                border-bottom: 1rpx solid #f2f2f2;

                :deep(.uni-radio-input) {
                    border: none;
                    background-color: transparent;
                }
            }
        }
    }
}
</style>
