<template>
    <!-- 订单详情 my_order_detail-->
    <view class="my_order_detail" ref="paging">
        <NavBar title="订单详情" @clickLeft="clickLeft"> </NavBar>
        <view class="content">
            <List :title="state.form.title" :time="state.form.payTime" :money="state.form.payAmount" :status="state.form.orderStatus" @click="amountDetailsItem(state.form.id)">
                <template #slot-chapter v-if="[2, 3, 5, 7].includes(state.form.orderStatus)">
                    <image class="chapter" :src="imgStatus" />
                </template>
            </List>
            <view class="flow_statement">
                <uni-list-item :border="false" v-for="item in refundList" :key="item.key" :title="item.name" :rightText="state.form[item.key] || '-'" />
            </view>
        </view>
        <view class="footer">
            <button v-if="[1, 2, 5, 6, 7].includes(state.form.orderStatus)" class="mini-btn" type="default" size="mini" @click="deletePopup.open()">删除订单</button>

            <button
                v-if="[3, 4, 5, 7].includes(state.form.orderStatus) || (state.form.orderStatus == 2 && state.form.refundStatus == 3)"
                class="mini-btn" type="default" size="mini" @click="handleRefundDetail">退款详情</button>

            <template v-if="state.form.businessType == 1">
                <button v-if="[3].includes(state.form.orderStatus)" class="mini-btn" type="default" size="mini" @click="handleCancelWithdraw">取消申请</button>

                <button v-if="[2, 5].includes(state.form.orderStatus)" class="mini-btn" type="default" size="mini" @click="handleApplyRefund">申请退款</button>
            </template>
        </view>

        <uni-popup ref="popup" border-radius="10px" background-color="#fff">
            <view class="popup-content">
                <view class="header">
                    <text class="title">退款详情</text>
                    <uni-icons class="close" type="closeempty" size="20" @click="popup.close()"></uni-icons>
                </view>
                <view class="body">
                    <uni-swiper-dot class="uni-swiper-dot-box" @clickItem="clickItem" :info="state.refundDdetailList" :current="state.current" mode="dot" :dots-styles="dotsStyles" field="content">
                        <swiper class="swiper-box" @change="changeSwiper" :current="state.swiperDotIndex">
                            <swiper-item v-for="(item, index) in state.refundDdetailList" :key="index">
                                <uni-list-item :border="false" v-for="it in popupList" :key="it.key" :title="it.name" :rightText="rightTextCompute(item, it)" />

                                <template v-if="item.refundStatus == 2">
                                    <uni-list-item :border="false" title="退款到账时间" :rightText="item.refundTime || '-'" />
                                    <uni-list-item :border="false" title="退款方向" :rightText="item.refundDestination || '-'" />
                                </template>

                                <uni-list-item v-if="item.refundStatus == 3" :border="false" title="退款失败原因" :rightText="item.refundDestination || item.auditRemarks || '-'" />
                            </swiper-item>
                        </swiper>
                    </uni-swiper-dot>
                </view>
            </view>
        </uni-popup>

        <uni-popup ref="deletePopup" border-radius="10px" background-color="#fff" :is-mask-click="false">
            <view class="delete-popup-content">
                <view class="header">
                    <!-- <text class="title">提示</text> -->
                    <uni-icons class="close" type="closeempty" size="20" @click="deletePopup.close()"></uni-icons>
                </view>
                <view class="body"> 确定删除订单？删除后无法恢复 </view>
                <view class="footer-btn">
                    <button class="mini-btn" @click="deletePopup.close()" tyle="default" size="mini">取消</button>
                    <button class="mini-btn" type="primary" @click="handleSaveDeleteOrder" tyle="primary" size="mini">确定</button>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script setup>
import NavBar from "../components/navBar.vue"
import List from "../components/list.vue"
import { computed } from "vue"
const deletePopup = ref(null)
const popup = ref(null)
const dotsStyles = {
    backgroundColor: "#D8D8D8",
    border: "1px #D8D8D8 solid",
    color: "#fff",
    selectedBackgroundColor: "#00b781",
    selectedBorder: "1px #00b781 solid"
}
const popupList = [
    { name: "退款原因", key: "refundReason" },
    { name: "本次退款金额", key: "payAmount" },
    { name: "已退款金额", key: "totalRefundAmount" },
    { name: "退款金额", key: "refundAmount" },
    { name: "退款编号", key: "refundNo" },
    { name: "申请退款时间", key: "createTime" }
]
const refundList = [
    { name: "订单编号：", key: "tradeNo" },
    { name: "交易流水号：", key: "orderNo" },
    { name: "创建时间：", key: "createTime" },
    { name: "支付时间：", key: "payTime" },
    { name: "支付渠道：", key: "payMethodName" }
]

const rightTextCompute = computed(() => {
    return (item, it) => {
        if (typeof item[it.key] === "number") {
            return item[it.key] || "0"
        }
        return item[it.key] || "-"
    }
})
// 已支付： orderStatus: 2,3,4,5 退款状态判断： 等于2时，判断是否有部分退款的 ，
// 3,4,5 直接展示： 3.审核中 4.退款中 5.退款失败
// 已关闭：orderStatus  6 ,7  ,6不展示信息， 7展示退款成功
// successStatus 成功   failStatus 失败  processStatus 审核中  3. partialRefund 部分退款
const imgStatus = computed(() => {
    const imgStatusMap = {
        2: "partialRefund", // 部分退款
        3: "processStatus", // 审核中
        // 4: 'processStatus',   // 退款中
        5: "failStatus", // 退款失败
        7: "successStatus" // 成功
    }
    if (state.form.orderStatus == 2) {
        if (state.form.refundStatus == 3) {
            return `https://file.1d1j.cn/cloud-mobile/smartCard/partialRefund.png`
        }
        return ""
    }
    return `https://file.1d1j.cn/cloud-mobile/smartCard/${imgStatusMap[state.form.orderStatus]}.png`
})
// refundStatus 退款状态 0.未退款 1.已退款 2.退款失败 3.部分退款
const state = reactive({
    payStatus: "warning",
    orderId: "",
    form: {},
    refundDdetailList: {
        refundReason: "",
        refundAmount: "",
        refundNo: "",
        createTime: "",
        refundTime: "",
        refundDestination: ""
    },
    treat: true,
    current: 0,
    swiperDotIndex: 0,
    personId: ""
})
const clickItem = (index) => {
    state.current = index
    state.swiperDotIndex = index
}
const changeSwiper = (evt) => {
    state.current = evt.detail.current
    state.swiperDotIndex = evt.detail.current
}
// 取消申请
const handleCancelWithdraw = () => {
    http.post("/unicard/app/order/cancel-apply", { id: state.form.id }).then(({ message }) => {
        uni.showToast({ title: message, icon: "none" })
        initPage()
    })
}
// 删除订单
const handleSaveDeleteOrder = () => {
    http.post("/unicard/app/order/delete-order", { id: state.form.id }).then(({ message }) => {
        uni.showToast({ title: message, icon: "none" })
        deletePopup.value.close()
        uni.navigateBack()
    })
}
// 退款详情
const handleRefundDetail = async () => {
    const { data } = await http.post("/unicard/app/order/refund-details", { id: state.form.id })
    state.refundDdetailList = data
    popup.value.open()
}
// 申请退款
const handleApplyRefund = () => {
    navigateTo({
        url: "/apps/smartCard/applyRefund/index",
        query: { id: state.form.id, personId: state.personId }
    })
}
// 详情
const initPage = () => {
    http.post("/unicard/app/order/details", { id: state.orderId }).then(({ data }) => {
        state.form = data
    })
}
onShow(() => {
    initPage()
})
onLoad((item) => {
    state.orderId = item.id || ""
    state.type = item.type || ""
    state.personId = item.personId || ""
})

function clickLeft() {
    // 返回到上一页
    // uni.navigateBack(1)
    const url = "/apps/smartCard/index"
    if (state.type) {
        url = `/apps/smartCard/${state.type}/index`
    }
    navigateTo({
        url,
        query: { id: state.orderId }
    })
}
</script>

<style lang="scss" scoped>
.my_order_detail {
    background: linear-gradient(180deg, $uni-bg-color 0%, #f5f7f9 19%, #f2f7f8 100%);

    .logo-school {
        display: flex;
        align-items: center;
        flex: 1;

        .logo {
            width: 56rpx;
            height: 56rpx;
            margin-right: 16rpx;
            border-radius: 50%;
        }

        .school {
            font-weight: 500;
            font-size: 36rpx;
            color: #000000;
        }
    }

    .content {
        height: calc(100vh - 120rpx);
        background-color: $uni-bg-color;
        padding: 0 30rpx;

        :deep(.list) {
            position: relative;
            padding: 24rpx 0;

            .chapter {
                width: 160rpx;
                height: 130rpx;
                position: absolute;
                right: 160rpx;
                top: 0;
            }
        }

        .flow_statement {
            // 虚线上边框
            border-top: 1rpx dashed $uni-border-color;
            padding: 20rpx 0;
            margin: 14rpx 0;

            :deep(.uni-list-item__container) {
                padding: 15rpx 0;

                .uni-list-item__extra-text {
                    font-size: 28rpx;
                }
            }
        }
    }

    .footer {
        padding: 10px 32rpx;
        border-top: 1rpx solid $uni-border-color;
        background: $uni-bg-color;
        text-align: right;
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;

        .mini-btn {
            margin: 0 10rpx;
            border-radius: 30rpx;

            &[type="default"] {
                border: 1rpx solid #dfdfdfff;
                color: #666666ff;
                background-color: $uni-bg-color;
            }

            &[type="primary"] {
                background-color: var(--primary-color);
            }
        }
    }
}

.popup-content {
    padding: 20rpx 30rpx;
    width: 80vw;

    .header {
        position: relative;
        text-align: center;

        .title {
            font-weight: 500;
            font-size: 34rpx;
            color: #333333;
        }

        .close {
            position: absolute;
            right: 0;
            top: 0;
        }
    }

    .body {
        :deep(.swiper-box) {
            height: 848rpx !important;

            .uni-list-item__extra-text {
                font-size: 28rpx;
            }

            .uni-list-item__extra {
                flex: 2;
            }
        }

        :deep(.uni-swiper__dots-box) {
            height: 45rpx;
        }

        :deep(.uni-list-item__extra-text) {
            max-height: 300rpx;
            overflow: hidden auto;
        }
    }
}

.delete-popup-content {
    padding: 20rpx 30rpx;
    width: 80vw;

    .header {
        position: relative;
        text-align: center;

        .title {
            font-weight: 500;
            font-size: 34rpx;
            color: #333333;
        }

        .close {
            position: absolute;
            right: 0;
            top: 0;
        }
    }

    .body {
        text-align: center;
        margin: 60rpx 0;
    }

    .footer-btn {
        display: flex;

        .mini-btn {
            &[type="primary"] {
                background-color: var(--primary-color);
            }
        }
    }
}
</style>
