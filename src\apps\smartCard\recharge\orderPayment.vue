<template>
    <!-- 订单支付 orderPayment-->
    <view class="order_payment">
        <NavBar title="订单支付" :clickLeft="clickLeft"> </NavBar>
        <view class="content">
            <view class="money-time">
                <view class="money">
                    <text style="font-size: 36rpx; margin-right: -10rpx">¥</text>
                    {{ payAmount(state.form.payAmount) }}
                </view>
                <view class="time-tips">
                    <text>支付剩余时间</text>
                    <uni-countdown :show-day="false" :hour="state.hour" :minute="state.minute" :second="state.second"
                        format="HH:mm:ss" @timeup="timeup" />
                </view>
            </view>
            <radio-group @change="radioChange">
                <label>
                    <uni-list-item class="list-item" title="订单信息">
                        <template v-slot:footer>
                            <view>{{ state.infos }}</view>
                        </template>
                    </uni-list-item>
                </label>
                <label v-for="item in state.payMethodList" :key="item.configTypeId">
                    <uni-list-item class="list-item" :title="item.payMethodName">
                        <template v-slot:header>
                            <image class="slot-image" :src="payIcon[item.payMethodId]" mode="widthFix"></image>
                        </template>
                        <template v-slot:footer>
                            <view>
                                <radio :value="item" :disabled="!state.infos"
                                    :checked="item.payMethodId === state.activePayMethodId" color="#00b781" />
                            </view>
                        </template>
                    </uni-list-item>
                </label>
            </radio-group>
        </view>
        <view class="footer">
            <button class="mini-btn" type="primary" :disabled="!state.form.payMethodId" :loading="state.loadingSave"
                @click="handleSave">确定支付</button>
        </view>

        <uni-popup ref="orderTipPopup" border-radius="10px" background-color="#fff" :is-mask-click="false">
            <view class="popup-content">
                <view class="header">
                    <text class="title">请确认微信支付是否已完成</text>
                    <uni-icons class="close" type="closeempty" size="20" @click="orderTipPopup.close()"></uni-icons>
                </view>
                <view class="body" @click="handerCompletedPay('payment')"> 已完成支付 </view>
                <view class="footer-btn" @click="handerCompletedPay('repay')"> 支付遇到问题，重新支付 </view>
            </view>
        </uni-popup>
        <img v-if="isGuideMap" src="https://file.1d1j.cn/cloud-mobile/smartCard/guideMap.png" alt="引导图"
            class="guide-map" @click="isGuideMap = false" />
    </view>
</template>

<script setup>
import dayjs from "dayjs"
import { checkPlatform } from "@/utils/sendAppEvent.js"
import { payAmount } from "../components/index.js"
import NavBar from "../components/navBar.vue"
import { isWechatBrowser } from "@/utils/wechat.js"

// 判断不同设备
const orderTipPopup = ref(null)
const isGuideMap = ref(false)

const payIcon = ["https://file.1d1j.cn/cloud-mobile/smartCard/alipay.png", "https://file.1d1j.cn/cloud-mobile/smartCard/weChat.png", "https://file.1d1j.cn/cloud-mobile/smartCard/intracloud.png"]

const state = reactive({
    payMethodList: [],
    activePayMethodId: "",
    infos: "",
    form: {
        tradeNo: "",
        payAmount: null,
        payMethodId: 0,
        configTypeId: "",
        openid: "",
        payType: 4, // 4：h5支付 1:公众号支付 5：小程序支付
        paySource: "h5支付"
    },
    hour: "0",
    minute: "0",
    second: "0",
    loadingSave: false,
    tradeNo: "",
    h5Url: "",
    appId: "",
    studentId: ""
})

// 倒计时时间到
const timeup = () => {
    // uni.showToast({
    //     title: "支付时间已到",
    //     icon: "none",
    //     duration: 2000
    // })
}

// 选择支付方式
const radioChange = (evt) => {
    const { payMethodId, configTypeId } = evt.detail.value
    state.activePayMethodId = payMethodId || ""
    state.form.payMethodId = payMethodId || ""
    state.form.configTypeId = configTypeId || ""
}

function onBridgeReady(params) {
    window.WeixinJSBridge.invoke("getBrandWCPayRequest", params, function (res) {
        if (res.err_msg == "get_brand_wcpay_request:ok") {
            console.log("支付成功")
            orderTipPopup.value.open()
            uni.removeStorageSync('setForm')
        } else {
            console.log("支付失败")
        }
    }),
        function (res) {
            if (res.err_msg == "get_brand_wcpay_request:ok") {
                orderTipPopup.value.open()
                // 使用以上方式判断前端返回,微信团队郑重提示：
                //res.err_msg将在用户支付成功后返回ok，但并不保证它绝对可靠，商户需进一步调用后端查单确认支付结果。
            }
        }
}

// 提交支付订单余额
const handleSave = () => {
    state.loadingSave = true
    http.post("/unicard/app/pay-center/pay-submit", state.form)
        .then(({ data }) => {
            // 如果是公众号嵌套前提前获取openid
            const params = {
                ...data.resultInfo.jsApiResult,
                package: data.resultInfo.jsApiResult.packageStr
            }
            if (isWechatBrowser()) {
                onBridgeReady(params)
            } else {
                // #ifdef MP-WEIXIN
                onBridgeReady(params)
                // #endif

                // #ifdef H5
                const roleArr = ["wx-miniprogram"]
                if (roleArr?.includes(checkPlatform())) {
                    isGuideMap.value = true
                    return
                } else {
                    // payH5({ ...data, ...state.form })
                    if (["development", "uat"].includes(import.meta.env.VITE_APP_NAME)) {
                        _redirect_url = `https://local-h5.1yide.com/${window.location.hash}&tradeNo=${state.form.tradeNo}&payMethodId=${state.form.payMethodId}`
                    } else if (["uatrelease"].includes(import.meta.env.VITE_APP_NAME)) {
                        // http://wisdomappuat.1yide.com 预发布
                        _redirect_url = `https://wisdomappuat.1yide.com/${window.location.hash}&tradeNo=${state.form.tradeNo}&payMethodId=${state.form.payMethodId}`
                    } else {
                        // 正式环境
                        _redirect_url = `https://mclouds.1yide.com/${window.location.hash}&tradeNo=${state.form.tradeNo}&payMethodId=${state.form.payMethodId}`
                    }
                    window.location.href = `${data.resultInfo.h5Url}&redirect_url=${encodeURIComponent(_redirect_url)}`
                }
                // #endif
            }
        })
        .finally(() => {
            state.loadingSave = false
        })
}
// 支付完成后 再手动判定是否确认完成支付
const handerCompletedPay = (item) => {
    http.post("/unicard/app/pay-center/order-pay-query", { tradeNo: state.form.tradeNo || state.tradeNo }).then(({ data }) => {
        //  0.处理中 1.已支付 2.支付失败/未支付
        if ([0, 1].includes(data.payResStatus)) {
            uni.showToast({
                title: data.payResStatus ? "充值已成功" : "充值处理中",
                icon: "none",
                duration: 2000
            })
            orderTipPopup.value.close()
            uni.reLaunch({
                url: `/apps/smartCard/index`
            })
        } else {
            if (item === "repay") {
                handleSave()
            } else {
                uni.showToast({
                    title: "该订单未完成支付，请进行支付。",
                    icon: "none",
                    duration: 2000
                })
            }
        }
    })
}
// 获取支付方式
const initPage = async () => {
    const params = { payType: "" }
    const { data } = await http.post("/unicard/app/pay-center/pay-method-list", params)
    state.payMethodList = data
    if (data.length > 0) {
        state.activePayMethodId = data[0].payMethodId
        state.form.payMethodId = data[0].payMethodId
        state.form.configTypeId = data[0].configTypeId
    }
}

// 充值-预下单
const rechargePrepay = async () => {
    const { personId, payAmount } = state.form
    const params = { personId, payAmount }
    const { data } = await http.post("/unicard/app/pay-center/recharge-prepay", params)
    const { infos, tradeNo, appId, payEndTime } = data
    state.form.tradeNo = tradeNo
    state.infos = infos
    state.appId = appId
    state.hour = dayjs(payEndTime).format("HH")
    state.minute = dayjs(payEndTime).format("mm")
    state.second = dayjs(payEndTime).format("ss")
}
function getWxCode() {
    return new Promise((resolve, reject) => {
        uni.login({
            provider: 'weixin',
            success({ errMsg, code }) {
                if (code) {
                    resolve(code)

                } else {
                    uni.showToast({
                        title: `${errMsg}`,
                        icon: 'none'
                    })
                    console.log('获取微信code失败', errMsg, code)
                    reject(new Error("get weixin login code error"))
                }
            },
            fail(err) {
                console.log('获取微信code fail', err)
                reject(new Error(err))
            }
        });
    })
}
const authorize = async () => {
    const _openId = uni.getStorageSync('openId')
    // 如何有_openId则直接支付 没有则跳授权
    if (_openId && _openId !== 'undefined') {
        state.form.openid = _openId
    } else {
        if (isWechatBrowser()) {
            // 众号
            navigateTo({
                url: `/apps/smartCard/payAuth/index`,
                query: {
                    configTypeId: state.form.configTypeId,
                    appId: state.appId,
                    back_url: encodeURIComponent(window.location.origin),
                },
            })
        }
    }
}
// 获取openId
const getOpenId = async (item) => {
    let _setquery = uni.setStorageSync('setForm')
    if (_setquery) {
        _setquery = JSON.parse(_setquery)
    }
    const { code, schoolId, appId, configTypeId } = item
    const params = {
        code,
        schoolId: _setquery?.schoolId || schoolId || '',
        appId: _setquery?.appId || appId || '',
        configTypeId: _setquery?.configTypeId || configTypeId || ''
    }
    await http.post("/unicard/callback/wechat/gzh/getAccessToken", params).then(({ data }) => {
        state.form.openid = data.openid
        state.form = _setquery
    })
};
const init = async (item) => {
    let _setquery = uni.setStorageSync('setForm')
    // 有缓存拿缓存  无缓存拿参数
    if (_setquery) {
        _setquery = JSON.parse(_setquery)
        state.form = _setquery
        state.form.payAmount = _setquery.payAmount || item.payAmount || ""
        state.form.personId = _setquery.personId || item.personId || ""
        state.tradeNo = _setquery.tradeNo || item.tradeNo || ""
        state.activePayMethodId = _setquery.payMethodId || item.payMethodId || ""
        state.studentId = _setquery.studentId || item.studentId || ""
        await initPage()
        await rechargePrepay()
    }


    if (item.code) {
        // 这个时候肯定是有_setquery了
        getOpenId(item)
        return
    } else {
        state.form.payAmount = item.payAmount || ""
        state.form.personId = item.personId || ""
        state.tradeNo = item.tradeNo || ""
        state.activePayMethodId = item.payMethodId || ""
        state.studentId = item.studentId || ""
        await initPage()
        await rechargePrepay()
        if (isWechatBrowser()) {
            //  检测是否在微信内置浏览器中
            state.form.payType = 1
            state.form.paySource = "公众号支付"
            uni.setStorageSync("setForm", JSON.stringify({ ...state.form, appId: state.appId, studentId: state.studentId }));

            authorize()
            return
        }
        // #ifdef MP-WEIXIN
        state.form.payType = 5
        state.form.paySource = "小程序支付"
        const jsCode = await getWxCode()
        await getOpenId({ code: jsCode, configTypeId: state.form.configTypeId, appId: state.appId, studentId: state.studentId })
        uni.setStorageSync("setForm", JSON.stringify({ ...state.form, appId: state.appId, studentId: state.studentId }));
        // #endif

    }
    // #ifdef H5 || H5-WEIXIN
    state.form.payAmount = item.payAmount || ""
    state.form.personId = item.personId || ""
    state.tradeNo = item.tradeNo || ""
    state.activePayMethodId = item.payMethodId || ""
    state.studentId = item.studentId || ""
    await initPage()
    await rechargePrepay()
    // 回显支付方式
    nextTick(() => {
        // 支付回调后获取支付方式回显
        if (state.activePayMethodId) {
            state.form.configTypeId = state.payMethodList?.find((item) => item.payMethodId == state.activePayMethodId)?.configTypeId || ""
        }
        item.payMethodId && orderTipPopup.value.open()
    })
    // #endif
}
onLoad(async (item) => {
    Object.keys(item).forEach((key) => {
        item[key] = decodeURIComponent(item[key])
    })
    init(item)
})

function clickLeft() {
    navigateTo({
        url: `/apps/smartCard/recharge/index`,
        query: {
            studentId: state.studentId || '',
            personId: state.form.personId
        }
    })
}
</script>

<style lang="scss" scoped>
.order_payment {
    background: $uni-bg-color-grey;
    height: 100vh;

    .content {
        padding: 30rpx;

        .money-time {
            text-align: center;
            padding: 63rpx 0;

            .money {
                font-weight: 600;
                font-size: 72rpx;
                color: #333333;
            }

            .time-tips {
                margin: 10rpx 0;
                font-weight: 400;
                font-size: 24rpx;
                color: #666666;
                display: flex;
                justify-content: center;
                align-items: center;
            }
        }

        .list-item {
            margin-bottom: 20rpx;
            padding: 20rpx;
            border-radius: 23rpx;

            .slot-image {
                width: 40rpx;
                height: 40rpx;
                margin-right: 20rpx;
            }
        }
    }

    .footer {
        padding: 20px 32rpx;
        background: $uni-bg-color;
        display: flex;
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;

        .mini-btn {
            background-color: var(--primary-color);
            flex: 1;
        }
    }

    .popup-content {
        width: 80vw;

        .header {
            position: relative;
            text-align: center;
            padding: 36rpx 0;

            .title {
                font-weight: 400;
                font-size: 34rpx;
                color: #333333;
            }

            .close {
                position: absolute;
                right: 10rpx;
                top: 10rpx;
            }
        }

        .body {
            text-align: center;
            border-top: 1rpx solid $uni-border-color;
            border-bottom: 1rpx solid $uni-border-color;
            padding: 30rpx 0;
            font-weight: 400;
            font-size: 34rpx;
            color: #ff0000;
        }

        .footer-btn {
            display: flex;
            justify-content: center;
            padding: 36rpx 0;
            font-weight: 400;
            font-size: 30rpx;
            color: #999999;
        }
    }

    .guide-map {
        width: 100vw;
        height: 100vh;
        position: fixed;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 9999;
    }
}
</style>
