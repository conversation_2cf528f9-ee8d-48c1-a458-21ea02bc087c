<template>
    <!-- 卡片记录 record-->
    <z-paging class="record" ref="paging" v-model="state.dataList" @query="initPage" :auto="false">
        <template #top>
            <NavBar title="卡片记录" :clickLeft="clickLeft"> </NavBar>
        </template>
        <view class="content">
            <view class="card" v-for="item in state.dataList" :key="item.key">
                <view class="handler">
                    <view class="title">{{ item.createTime }}</view>
                    <view class="extra">卡片操作：{{ item.operationTypeName }}</view>
                </view>
                <view class="body">
                    <view class="list-item">
                        <text class="tag" :class="item.operationType">{{ item.cardStatusName }}</text>
                        <text class="cardNo">卡号：{{ item.cardNo }}</text>
                    </view>

                    <template v-if="item.operationType === 'lost'">
                        <view class="list-item flex">
                            <text>姓名：{{ item.personName }}</text>
                            <text>挂失方式：{{ item.operationChannel }}</text>
                        </view>
                    </template>

                    <template v-if="item.operationType === 'unLost'">
                        <view class="list-item flex">
                            <text>姓名：{{ item.personName }}</text>
                            <text>解挂方式：{{ item.operationChannel }}</text>
                        </view>
                    </template>

                    <template v-if="item.operationType === 'replaced'">
                        <view class="list-item flex">
                            <text>姓名：{{ item.personName }}</text>
                            <text>原卡余额：{{ item.nowBalance }}</text>
                        </view>
                        <view class="list-item flex">
                            <text>新卡余额：{{ item.reissueBalance }}</text>
                            <text>补卡时间：{{ item.createTime }}</text>
                        </view>
                    </template>

                    <template v-if="item.operationType === 'canceled'">
                        <view class="list-item flex">
                            <text>姓名：{{ item.personName }}</text>
                            <text>卡内押金：{{ item.cashPledge }}</text>
                        </view>
                        <view class="list-item flex">
                            <text>卡内金额：{{ item.nowBalance }}</text>
                            <text>退款金额：{{ item.refundAmount }}</text>
                        </view>
                        <view class="list-item flex">
                            <text>余额退回方式：{{ item.payMethod }}</text>
                        </view>
                    </template>

                    <view class="list-item more">
                        <view ref="textRef" class="text" :class="{ active: state.mores[item.id] }">
                            {{ textRemark(item) }}
                        </view>
                        <uni-icons :type="!state.mores[item.id] ? 'down' : 'up'" @click="state.mores[item.id] = !state.mores[item.id]" size="18"></uni-icons>
                    </view>
                </view>
            </view>
        </view>
        <template #empty>
            <yd-empty text="暂无数据" />
        </template>
    </z-paging>
</template>

<script setup>
import NavBar from "../components/navBar.vue"
import useStore from "@/store"
const { user } = useStore()

const operationObj = {
    lost: "挂失原因：",
    unLost: "解挂原因：",
    replaced: "补卡原因：",
    canceled: "注销原因：",
    activated: "发卡原因："
}
const textRemark = computed(() => {
    return (item) => {
        return operationObj[item.operationType] + item.remark
    }
})
const paging = ref(null)
const state = reactive({
    dataList: [],
    personId: "",
    mores: {}
})

const initPage = (pageNo, pageSize) => {
    const params = {
        pageNo,
        pageSize,
        personId: state.personId
    }
    http.post("/unicard/app/card/page", params)
        .then(({ data }) => {
            paging.value?.complete(data.list || [])
            data.list.forEach((v) => {
                state.mores[v.id] = false
            })
        })
        .catch(() => {
            paging.value?.complete([])
        })
}

onMounted(async () => {
    paging.value?.reload()
})
onLoad((item) => {
    state.personId = item.personId || ""
})

function clickLeft() {
    uni.navigateBack()
}
</script>

<style lang="scss" scoped>
.record {
    background: $uni-bg-color-grey;
    height: 100vh;

    .content {
        .card {
            margin: 30rpx;
            background: $uni-bg-color;
            box-shadow: 0rpx 0rpx 8rpx 0rpx rgba(226, 226, 226, 0.5);
            border-radius: 16rpx;
            padding: 24rpx;

            .handler {
                display: flex;
                justify-content: space-between;
                align-items: center;
                border-bottom: 1rpx solid #d8d8d8;
                padding-bottom: 24rpx;
                font-weight: 400;
                font-size: 28rpx;
                color: #666666;
            }

            .body {
                padding-top: 12rpx;
                font-weight: 400;
                font-size: 28rpx;
                color: $uni-text-color-grey;

                .list-item {
                    margin: 16rpx 0;
                    display: flex;
                    align-items: center;

                    &.flex {
                        justify-content: space-between;
                    }

                    &.more {
                        background: #f6f8fa;
                        border-radius: 8rpx;
                        padding: 12rpx;
                        align-items: flex-start;
                        justify-content: space-between;
                        margin-top: 32rpx;

                        .text {
                            // 字数超出隐藏
                            overflow: hidden;
                            text-overflow: ellipsis;
                            display: -webkit-box;
                            -webkit-line-clamp: 1;
                            -webkit-box-orient: vertical;

                            &.active {
                                display: block;
                                -webkit-line-clamp: 9999;
                            }
                        }
                    }

                    .mini-btn {
                        margin: 0;
                        border-color: var(--primary-color) !important;
                        color: var(--primary-color);
                        border-radius: 30rpx;
                    }

                    .tag {
                        background-color: var(--primary-color);
                        color: $uni-bg-color;
                        font-size: 20rpx;
                        padding: 6rpx 12rpx;
                        border-radius: 12rpx 0 12rpx 0;
                        margin-right: 10rpx;

                        &.gs {
                            background-color: $uni-color-warning;
                        }

                        &.lost {
                            background-color: #ff943b;
                        }
                    }

                    .cardNo {
                        font-weight: 500;
                        font-size: 32rpx;
                        color: #333333;
                    }
                }
            }
        }
    }
}
</style>
