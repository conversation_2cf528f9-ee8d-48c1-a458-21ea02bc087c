<template>
    <!-- 挂失 reportLoss-->
    <view class="report_loss">
        <NavBar :title="state.isUnboxing ? '解挂' : '挂失'" :clickLeft="clickLeft"> </NavBar>
        <view class="content">
            <template v-for="item in cardForm" :key="item.key">
                <view class="main" v-if="item.key == 'record'">
                    <uni-list-item :title="state.isUnboxing ? '解挂原因' : item.name">
                        <template v-slot:header>
                            <text style="color: red">*</text>
                        </template>
                    </uni-list-item>
                    <view v-if="item.key == 'record'" class="record">
                        <uni-easyinput class="reset-easyinput" type="textarea" :maxlength="maxlength" :inputBorder="false" v-model="state.remark" placeholder="请输入"> </uni-easyinput>
                    </view>
                    <view class="num_box">{{ state.remark.length }}/{{ maxlength }}</view>
                </view>
                <uni-list-item v-else :title="item.name" :rightText="state.form[item.key]"> </uni-list-item>
            </template>
        </view>
        <view class="footer">
            <button class="mini-btn" type="primary" :disabled="!state.remark.length" @click="handleSave">确认</button>
        </view>
    </view>
</template>

<script setup>
import NavBar from "../components/navBar.vue"
import useStore from "@/store"
const { user } = useStore()

const cardForm = [
    { name: "姓名", key: "name" },
    { name: "卡号", key: "cardNo" },
    { name: "挂失原因", key: "record" }
]
const maxlength = 500

const state = reactive({
    form: {},
    remark: "",
    personId: "",
    isUnboxing: ""
})
// 提交原因
const handleSave = () => {
    const params = {
        personId: state.personId,
        remark: state.remark
    }
    // 挂失
    let API = "/unicard/app/card/lost"
    if (state.isUnboxing) {
        // 解挂
        API = "/unicard/app/card/un-lost"
    }
    http.post(API, params).then(({ message }) => {
        uni.showToast({
            title: message,
            icon: "none"
        })
        clickLeft()
    })
}

onLoad((item) => {
    Object.keys(item).forEach((key) => {
        item[key] = decodeURIComponent(item[key])
    })
    state.personId = item.personId || ""
    // 从卡片记录中来解挂
    state.isUnboxing = item.type || ""
    state.form.cardNo = item.cardNo || ""
    state.form.name = item.studentName || ""
})

function clickLeft() {
    uni.navigateBack()
}
</script>

<style lang="scss" scoped>
.report_loss {
    background: $uni-bg-color-grey;
    height: 100vh;

    .content {
        background: $uni-bg-color;

        .slot-image {
            width: 80rpx;
            height: 80rpx;
            border-radius: 50%;
        }

        .slot-text {
            font-weight: 400;
            font-size: 28rpx;
            color: $uni-text-color-grey;
        }

        :deep(.uni-icons) {
            padding-left: 0;
        }

        .record {
            padding: 0 24rpx 20rpx;

            .reset-easyinput {
                :deep(.uni-easyinput__content-textarea) {
                    border-radius: 10rpx;
                    overflow: hidden;
                    background-color: #f6f6f6ff;
                    padding: 10rpx 10rpx 35rpx;
                }
            }
        }

        .main {
            position: relative;

            .num_box {
                text-align: right;
                font-weight: 400;
                font-size: 24rpx;
                color: $uni-text-color-grey;
                position: absolute;
                bottom: 40rpx;
                right: 40rpx;
            }
        }
    }

    .footer {
        padding: 20px 32rpx;
        background: $uni-bg-color;
        display: flex;
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;

        .mini-btn {
            background-color: var(--primary-color);
            flex: 1;
        }
    }
}
</style>
