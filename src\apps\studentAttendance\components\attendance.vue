<template>
    <view class="attendance">
        <view class="title_box">
            <view class="title">{{ typeTitle[type] + "考勤" }}</view>
            <view class="view_details" @click="goDefinite">
                查看明细
                <uni-icons type="right" size="18"></uni-icons>
            </view>
        </view>
        <view class="select_box" v-if="type != 0 && identityType == 'teacher'">
            <text class="select_title">选择人员类型</text>
            <view class="select_condition" @click="selectUserTypeFn">
                {{ userTypeTitle[userType] }}
            </view>
        </view>

        <view class="select_box" v-if="type == 1">
            <text class="select_title">选择事件</text>
            <view class="select_condition" @click="selectEventFn">
                {{ eventItem.name || "选择事件" }}
            </view>
        </view>
        <view class="select_box" v-if="type == 2">
            <text class="select_title">选择课堂考勤类型</text>
            <view class="select_condition" @click="selectCourseFn">
                {{ courseType.label || "选择课堂考勤类型" }}
            </view>
        </view>

        <!-- 选择日期时间 -->
        <select-date @changeDate="changeDate" />

        <!-- 选择视图（年级班级） -->
        <select-grade-class v-if="type != 1 && identityType == 'teacher'" :gradeClassList="gradeClassList" @changeData="changeGradeClass" />
        <!-- 条形图 -->
        <charts :data="goOutSchoolCharts" v-show="type == 0" />
        <charts :data="eventCharts" v-show="type == 1" />
        <charts :data="courseCharts" v-show="type == 2" />

        <!-- 选择人员类型 -->
        <yd-select-popup title="请选择人员类型" ref="selectUserTypeRef" :list="userTypeList" @closePopup="closeUserType" :selectId="[userType]" />

        <!-- 选择事件 -->
        <yd-select-popup title="请选择事件" ref="selectEventRef" :list="eventList" :fieldNames="{ value: 'id', label: 'name' }" @closePopup="closeEvent" :selectId="[eventItem.id]" />

        <!-- 选择课堂考勤类型 -->
        <yd-select-popup title="请选择课堂考勤类型" ref="selectCourseRef" :list="courseTypeList" @closePopup="closeCourseType" :selectId="[courseType.value]" />
    </view>
</template>

<script setup>
import charts from "./charts.vue"
import SelectGradeClass from "./selectGradeClass.vue"
import SelectDate from "./selectDate.vue"
import { typeTitle, userTypeTitle, userTypeList, courseTypeList } from "../data"

const props = defineProps({
    type: {
        type: Number,
        default: null
    },
    gradeClassList: {
        type: Array,
        default: () => []
    },
    student: {
        type: Object,
        default: () => {}
    },
    identityType: {
        type: String,
        default: "teacher"
    }
})
const userType = ref(1)
const selectUserTypeRef = ref(null)
const selectEventRef = ref(null)
const selectCourseRef = ref(null)
const grade = ref({})
const classes = ref({})
const eventItem = ref({})
const courseType = ref({ value: 2, label: "行政班课堂考勤" })
const eventList = ref([])
const timeObj = ref({
    startDate: "",
    endDate: ""
})
const type = computed(() => props.type)
const gradeClassList = computed(() => props.gradeClassList)
const studentObj = computed(() => props.student)
const identityType = computed(() => props.identityType)

const goOutSchoolCharts = ref({})
const eventCharts = ref({})
const courseCharts = ref({})

// 选择班级
function onChangeClass(e) {
    if (e.detail?.value && e.detail.value.length) {
        classes.value = e.detail.value[e.detail.value.length - 1]
    }
    grade.value = {}
    getData()
}

// 获取事件下拉数据
async function getEventList() {
    await http
        .post("/attweb/app/eventStatistical/attendanceEventList", {
            ...timeObj.value,
            userType: userType.value
        })
        .then((res) => {
            eventList.value = res.data
        })
}

// 选择事件
function closeEvent(item) {
    if (!item || item.id == eventItem.value.id) return
    eventItem.value = item
    getData()
}

// 选择人员类型
function closeUserType(item) {
    if (!item || item.value == userType.value) return
    userType.value = item.value
    getData()
}

// 选择课堂考勤类型
function closeCourseType(item) {
    if (!item || item.value == courseType.value) return
    courseType.value = item
    getData()
}

async function getData() {
    const url = identityType.value == "teacher" ? "/attweb/app/eventStatistical/teacherView" : "/attweb/app/eventStatistical/studentViewCount"
    const params = {
        ...timeObj.value,
        type: type.value
    }
    if (identityType.value == "teacher") {
        // 出入校和课程要传班级id和年级id
        if (type.value != 1) {
            params.rollId = grade.value.id || null
            params.classesId = classes.value.value || null
        }
        // 课程选择人员类型和课堂考勤类型
        if (type.value == 2) {
            params.userType = userType.value
            params.type = courseType.value.value || type.value
        }
    } else {
        // 家长传学生
        params.studentId = studentObj.value.id
        if (type.value == 2) {
            params.type = courseType.value.value || type.value
        }
    }
    // 事件传人员类型和时间ID
    if (type.value == 1) {
        await getEventList()
        params.userType = userType.value
        params.attendanceId = eventItem.value.id || null
    }

    http.post(url, params).then((res) => {
        if (identityType.value == "teacher") {
            if (type.value == 0) {
                goOutSchoolCharts.value = res.data[0] || {}
            } else if (type.value == 1) {
                eventCharts.value = res.data[0] || {}
            } else {
                courseCharts.value = res.data[0] || {}
            }
        } else {
            if (type.value == 0) {
                goOutSchoolCharts.value = res.data || {}
            } else if (type.value == 1) {
                eventCharts.value = res.data || {}
            } else {
                courseCharts.value = res.data || {}
            }
        }
    })
}

function selectCourseFn() {
    selectCourseRef.value.open()
}

function selectEventFn() {
    selectEventRef.value.open()
}

function selectUserTypeFn() {
    selectUserTypeRef.value.open()
}
function changeGradeClass({ ydGrade, ydClasses }) {
    grade.value = ydGrade
    classes.value = ydClasses
    getData()
}

// 选择时间
function changeDate(time) {
    timeObj.value = time
    getData()
}

function goDefinite() {
    navigateTo({
        url: "/apps/studentAttendance/details",
        query: {
            type: type.value,
            studentId: studentObj.value.id
        }
    })
}

defineExpose({ getData })
</script>

<style lang="scss" scoped>
.attendance {
    margin-top: 20rpx;
    background-color: $uni-bg-color;
    padding: 30rpx;
    .title_box {
        display: flex;
        padding-bottom: 30rpx;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1rpx solid $uni-border-color;
        .title {
            font-weight: 500;
            font-size: 28rpx;
            color: $uni-text-color;
            line-height: 40rpx;
        }
        .view_details {
            font-weight: 400;
            font-size: 28rpx;
            color: #666666;
            line-height: 40rpx;
        }
    }
    .select_grade_class {
        background: var(--primary-bg-color);
        border-radius: 10rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20rpx;
        .select_title {
            font-weight: 400;
            font-size: 28rpx;
            color: #666666;
            line-height: 40rpx;
            flex: 1;
        }
        .select_grade {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 400;
            font-size: 28rpx;
            color: $uni-text-color;
            line-height: 40rpx;
            &::after {
                content: "";
                display: block;
                border: 10rpx solid transparent;
                border-top: 10rpx solid var(--primary-color);
                border-bottom-width: 1px;
                margin-left: 10rpx;
            }
        }
        .select_class {
            flex: 1;
            padding: 20rpx 30rpx;
            .triangle_class {
                display: flex;
                align-items: center;
                justify-content: flex-end;
                font-weight: 400;
                font-size: 28rpx;
                color: $uni-text-color;
                line-height: 40rpx;
                &::after {
                    content: "";
                    display: block;
                    border: 10rpx solid transparent;
                    border-top: 10rpx solid var(--primary-color);
                    border-bottom-width: 1px;
                    margin-left: 10rpx;
                }
            }
            :deep(.selected) {
                display: flex;
            }
            :deep(.selected-item-active) {
                border-bottom: 2px solid var(--primary-color);
            }
            :deep(.check) {
                border: 2px solid var(--primary-color);
                border-left: 0;
                border-top: 0;
            }
        }
    }
    .select_box {
        display: flex;
        align-items: center;
        padding: 30rpx 0;
        border-bottom: 1rpx solid $uni-border-color;
        justify-content: space-between;
        .select_title {
            font-weight: 400;
            font-size: 28rpx;
            color: #666666;
            line-height: 40rpx;
        }
        .select_condition {
            display: flex;
            align-items: center;
            font-weight: 400;
            font-size: 28rpx;
            color: $uni-text-color;
            line-height: 40rpx;

            &::after {
                content: "";
                display: block;
                border: 10rpx solid transparent;
                border-top: 10rpx solid var(--primary-color);
                border-bottom-width: 1px;
                margin-left: 10rpx;
            }
        }
    }
}
</style>
