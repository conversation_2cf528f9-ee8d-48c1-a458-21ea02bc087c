<template>
    <view class="yd_charts">
        <!-- 条形图 -->
        <qiun-data-charts type="column" :canvas2d="true" :chartData="chartData" :opts="chartsOpts" />
    </view>
</template>

<script setup>
import qiunDataCharts from "@/subModules/components/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue"
const props = defineProps({
    data: {
        type: Object,
        default: () => {}
    }
})

const chartsOpts = {
    extra: {
        tooltip: {
            showBox: false
        },
        column: {
            type: "group",
            width: 30
        }
    },
    legend: {
        show: false
    }
}

const chartData = ref({})

const defData = ref({
    categories: ["正常", "缺勤", "迟到", "请假", "早退"],
    series: [
        {
            data: [
                {
                    value: 0,
                    color: "var(--primary-color)"
                },
                {
                    value: 0,
                    color: "#FD4F45"
                },
                {
                    value: 0,
                    color: "#FC941F"
                },
                {
                    value: 0,
                    color: "#333333"
                },
                {
                    value: 0,
                    color: "#1EC1C3"
                }
            ]
        }
    ]
})

watch(
    () => props.data,
    (val) => {
        setTimeout(() => {
            const chartDataNum = {
                1: val.normalNum || 0,
                2: val.absenteeismNum || 0,
                3: val.beLateNum || 0,
                4: val.leaveNum || 0,
                5: val.leaveEarlyNum || 0
            }
            defData.value.series[0].data.forEach((item, index) => {
                console.log(chartDataNum[index + 1])
                item.value = chartDataNum[index + 1]
            })
            chartData.value = defData.value
        }, 500)
    },
    {
        immediate: true,
        deep: true
    }
)
</script>

<style lang="scss" scoped>
.yd_charts {
    margin-top: 20rpx;
    position: relative;
}
</style>
