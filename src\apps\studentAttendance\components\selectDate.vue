<template>
    <view class="select_date">
        <view class="date_box">
            <text class="date_label">选择日期</text>
            <view class="date_list">
                <view class="date_item" v-for="item in dateList" :key="item.key" :class="{ active_class: activeDate == item.key }" @click="changeTypeFn(item)">
                    {{ item.label }}
                </view>
            </view>
        </view>
        <view class="date_picker">
            <uni-datetime-picker v-if="activeDate == 'day'" :border="false" :clearIcon="false" type="date" placeholder="选择时间" v-model="date" @change="changeDate" />
            <uni-datetime-picker v-else :border="false" :clearIcon="false" type="daterange" placeholder="选择时间" v-model="date" @change="changeDate" />
        </view>
    </view>
</template>

<script setup>
import dayjs from "dayjs"
import isoWeek from "dayjs/plugin/isoWeek"
dayjs.extend(isoWeek)

const emit = defineEmits(["changeDate"])

// 前七天日期区间（自定义）
const getLastSevenDaysRange = computed(() => {
    const sevenDaysAgo = dayjs().subtract(7, "day")
    const startDate = sevenDaysAgo.format("YYYY-MM-DD")
    const endDate = dayjs().format("YYYY-MM-DD")
    return [startDate, endDate]
})

// 这一周日期区间
const getMonthStartEnd = computed(() => {
    const startOfMonth = dayjs().startOf("month").format("YYYY-MM-DD")
    const endOfMonth = dayjs().endOf("month").format("YYYY-MM-DD")
    return [startOfMonth, endOfMonth]
})

// 这个月的日期区间
const getWeekStartEnd = computed(() => {
    const startOfWeek = dayjs().startOf("isoWeek").format("YYYY-MM-DD")
    const endOfWeek = dayjs().endOf("isoWeek").format("YYYY-MM-DD")
    return [startOfWeek, endOfWeek]
})

const dateList = ref([
    {
        value: dayjs().format("YYYY-MM-DD"),
        label: "日",
        type: "date",
        key: "day"
    },
    {
        value: getWeekStartEnd.value,
        label: "周",
        type: "daterange",
        key: "week"
    },
    {
        value: getMonthStartEnd.value,
        label: "月",
        type: "daterange",
        key: "moon"
    },
    {
        value: getLastSevenDaysRange.value,
        label: "自定义",
        type: "daterange",
        key: "userDef"
    }
])
const date = ref(dayjs().format("YYYY-MM-DD"))
const activeDate = ref("day")

function changeTypeFn(item) {
    activeDate.value = item.key
    date.value = item.value
    changeDate()
}

function changeDate() {
    if (activeDate.value == "day") {
        emit("changeDate", { startDate: date.value, endDate: date.value })
    } else {
        emit("changeDate", { startDate: date.value[0], endDate: date.value[1] })
    }
}

onMounted(() => {
    changeDate()
})
</script>

<style lang="scss" scoped>
.select_date {
    width: 100%;
    .date_box {
        display: flex;
        padding: 30rpx 0 10rpx 0;
        align-items: center;
        .date_label {
            font-weight: 400;
            font-size: 28rpx;
            color: #666666;
            line-height: 40rpx;
            margin-right: 28rpx;
        }
        .date_list {
            display: flex;
            flex: 1;
            align-items: center;
            .date_item {
                flex: 1;
                height: 56rpx;
                background: $uni-bg-color;
                border-radius: 28rpx;
                margin: 0 10rpx;
                border: 1rpx solid $uni-border-color;
                font-weight: 400;
                font-size: 26rpx;
                color: $uni-text-color-grey;
                line-height: 56rpx;
                text-align: center;
            }
            .active_class {
                border: 1rpx solid var(--primary-color);
                color: var(--primary-color);
            }
        }
    }
    .date_picker {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        &::after {
            content: "";
            display: block;
            border: 10rpx solid transparent;
            border-top: 10rpx solid var(--primary-color);
            border-bottom-width: 1px;
            margin-left: 10rpx;
        }
        :deep(.uni-date) {
            flex: none;
            width: auto;
        }
        :deep(.icon-calendar) {
            display: none !important;
        }
    }
}
</style>
