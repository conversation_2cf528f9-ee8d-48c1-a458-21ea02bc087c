<template>
    <!-- 选择视图（年级班级） -->
    <view class="select_grade_class">
        <text class="select_title">选择视图</text>
        <view class="select_grade" @click="selectGradeRef.open()">
            <text class="ellipsis"> {{ grade.showName || "年级" }}</text>
        </view>
        <view class="select_class">
            <uni-data-picker v-model="classes.id" :localdata="gradeClassList" @change="onChangeClass" :map="{ text: 'showName', value: 'id' }" v-slot:default="{ data, error }" popup-title="请选择班级">
                <view v-if="error" class="error">
                    <text>{{ error }}</text>
                </view>
                <view class="triangle_class">
                    <text class="ellipsis">
                        {{ data[data.length - 1]?.text || "班级" }}
                    </text>
                </view>
            </uni-data-picker>
        </view>
    </view>
    <!-- 选择年级 -->
    <yd-select-popup title="请选择年级" ref="selectGradeRef" :list="gradeClassList" :fieldNames="{ value: 'id', label: 'showName' }" @closePopup="closeGrade" :selectId="[grade.id]" />
</template>

<script setup>
const emit = defineEmits(["changeData"])
const props = defineProps({
    gradeClassList: {
        type: Array,
        default: () => []
    }
})
const selectGradeRef = ref(null)
const grade = ref({})
const classes = ref({})

const gradeClassList = computed(() => props.gradeClassList)

// 选择班级
function onChangeClass(e) {
    if (e.detail?.value && e.detail.value.length) {
        classes.value = e.detail.value[e.detail.value.length - 1]
    }
    grade.value = {}
    emit("changeData", { ydGrade: grade.value, ydClasses: classes.value })
}

// 选择年级
function closeGrade(item) {
    if (!item || item.id == grade.value.id) return
    grade.value = item
    classes.value = {}
    emit("changeData", { ydGrade: grade.value, ydClasses: classes.value })
}
</script>

<style lang="scss" scoped>
.select_grade_class {
    background: var(--primary-bg-color);
    border-radius: 10rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 20rpx;
    .select_title {
        font-weight: 400;
        font-size: 28rpx;
        color: #666666;
        line-height: 40rpx;
        flex: 1;
    }
    .select_grade {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 400;
        font-size: 28rpx;
        color: $uni-text-color;
        line-height: 40rpx;
        &::after {
            content: "";
            display: block;
            border: 10rpx solid transparent;
            border-top: 10rpx solid var(--primary-color);
            border-bottom-width: 1px;
            margin-left: 10rpx;
        }
    }
    .select_class {
        flex: 1;
        padding: 20rpx 30rpx;
        .triangle_class {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            font-weight: 400;
            font-size: 28rpx;
            color: $uni-text-color;
            line-height: 40rpx;
            &::after {
                content: "";
                display: block;
                border: 10rpx solid transparent;
                border-top: 10rpx solid var(--primary-color);
                border-bottom-width: 1px;
                margin-left: 10rpx;
            }
        }
        :deep(.selected) {
            display: flex;
        }
        :deep(.selected-item-active) {
            border-bottom: 2px solid var(--primary-color);
        }
        :deep(.check) {
            border: 2px solid var(--primary-color);
            border-left: 0;
            border-top: 0;
        }
    }
}
</style>
