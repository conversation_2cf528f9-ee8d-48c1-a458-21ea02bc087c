export const typeTitle = { 0: "出入校", 1: "事件", 2: "课程" }
export const userTypeTitle = { 1: "学生", 2: "老师" }
export const userTypeList = [
    { value: 1, label: "学生" },
    { value: 2, label: "老师" }
]
export const courseTypeList = [
    { value: 2, label: "行政班课堂考勤" },
    { value: 3, label: "走班课堂考勤" }
]

export const tabs = ref([
    {
        name: "出入校考勤",
        value: "goOutSchool",
        type: 0
    },
    {
        name: "事件考勤",
        value: "event",
        type: 1
    },
    {
        name: "课程考勤",
        value: "course",
        type: 2
    }
])
