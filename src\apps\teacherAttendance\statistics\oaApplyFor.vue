<template>
    <view class="page_class">
        <!-- #ifdef H5-WEIXIN || H5 -->
        <iframe class="webview" :src="src" frameborder="0"></iframe>
        <!-- #endif -->
        <!-- #ifdef MP-WEIXIN || APP-PLUS -->
        <web-view class="webview" :src="src"></web-view>
        <!-- #endif -->
    </view>
</template>

<script setup>
import { getToken } from "@/utils/storageToken.js"
import { checkPlatform } from "@/utils/sendAppEvent.js"

const src = ref("")
const token = getToken()?.replace("Bearer ", "")
const platform = ["yide-ios-app", "yide-android-app", "yide-Harmony-app"].includes(checkPlatform())
onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    src.value = `${import.meta.env.VITE_BASE_OAH5}/#/leave?fromKey=${options.fromKey}&fillTime=${options.fillTime}&token=${token}&skipDetail=${platform}`
})
</script>

<style lang="scss" scoped>
.page_class {
    width: 100vw;
    height: 100vh;
    .webview {
        height: 100%;
        width: 100%;
    }
}
</style>
