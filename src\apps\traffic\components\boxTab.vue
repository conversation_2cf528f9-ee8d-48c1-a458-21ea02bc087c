<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: jingrou
 * @Date: 2023-02-02 15:54:13
 * @LastEditors: jingrou
 * @LastEditTime: 2023-03-09 15:19:56
-->
<template>
    <view class="box_tab">
        <text class="item_tab" v-for="(item, index) in titleArr" :key="`item_${index}`" :class="{ tab_active: item.sectionId === tabId }" @click="handelClick(item)">{{ item.sectionName }}</text>
    </view>
</template>

<script setup>
const props = defineProps({
    titleArr: {
        type: Array,
        default: [
            // { name: "小学", id: '' },
            // { name: "初中", id: '' },
            // { name: "高中", id: '' },
            // { name: "大学", id: '' },
        ]
    },
    tabId: {
        type: String,
        default: ""
    }
})
const emit = defineEmits(["handelClick"])
const handelClick = (item) => {
    emit("handelClick", item)
}
</script>

<style lang="scss" scoped>
.box_tab {
    padding: 30rpx 30rpx 0 30rpx;

    .item_tab {
        display: inline-block;
        padding: 10rpx 30rpx;
        text-align: center;
        box-sizing: border-box;
        background: $uni-bg-color-grey;
        font-size: 28rpx;
        color: #999;
        margin-right: 28rpx;
        border-radius: 4rpx;
        background: $uni-bg-color;
    }

    .tab_active {
        background: $uni-bg-color;
        color: var(--primary-color);
        border: 2rpx solid var(--primary-color);
    }
}
</style>
