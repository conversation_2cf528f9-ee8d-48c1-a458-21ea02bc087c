<template>
    <!-- 通行 -->
    <div>
        <!-- 家长是一个页面 -->
        <eltern-index v-if="identityType == 'eltern'" :identityType="identityType" />
        <!-- 老师和学生一个页面 -->
        <teacher-index v-else :identityType="identityType" :appCode="appCode" ref="teacherIndexRef" />
        <view class="confirm_popup">
            <yd-popup ref="confirmRef" :titleflag="false" @confirm="dialogConfirm" confirmText="我知道了" :canceFlag="false">
                <view class="welcome">
                    <image src="@nginx/workbench/traffic/welcome_img.png" class="welcome_img" mode="widthFix" />
                    <view class="tip_box">
                        <text class="tip_title">欢迎使用通行</text>
                        <text class="tip_content">您可以查看权限下所有班级学生及教职工通行情况，及时掌握学生及教职工出入校动态</text>
                    </view>
                </view>
            </yd-popup>
        </view>
    </div>
</template>

<script setup>
import elternIndex from "./components/elternIndex.vue"
import teacherIndex from "./components/teacherIndex.vue"
import useStore from "@/store"
const { user, system } = useStore()

const teacherIndexRef = ref(null)
const confirmRef = ref(null)
const appCode = ref(null)
const identityType = computed(() => {
    const roleCode = user?.identityInfo?.roleCode
    if (roleCode == "eltern") {
        return "eltern"
    } else if (roleCode == "student") {
        return "student"
    } else {
        return "teacher"
    }
})

function dialogConfirm() {
    system.setAppData({ sys: "traffic", data: { guide: "hide" } })
    teacherIndexRef.value.showMyGuide()
}

onMounted(() => {
    if (!system.apps?.traffic?.guide) {
        confirmRef.value.open()
    }
})

onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    appCode.value = options.code
})
</script>

<style lang="scss" scoped>
.confirm_popup {
    :deep(.confirm_popup_content) {
        padding: 0rpx !important;
    }
}
.welcome {
    .welcome_img {
        width: 80vw;
    }
    .tip_box {
        display: flex;
        flex-direction: column;
        padding: 40rpx;
        justify-content: center;
        align-items: center;
        .tip_title {
            font-weight: 500;
            font-size: 34rpx;
            color: #333333;
            line-height: 48rpx;
        }
        .tip_content {
            margin-top: 30rpx;
            font-weight: 400;
            font-size: 28rpx;
            color: #999999;
            line-height: 40rpx;
        }
    }
}
</style>
