<template>
    <view class="details">
        <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="routerBack" title="审批详情" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
        <image class="seal_status" v-if="state.createForm.approveStatus !== 1" :src="state.stauss[state.createForm.approveStatus]?.icon"></image>
        <view class="hander">
            <view class="title" v-if="state.route.isTeacher === 'true'">{{ state.createForm.fromUserName }}的访客审批</view>
            <view class="title" v-else>拜访{{ state.createForm.toUserName }}的申请详情</view>
            <view>
                <text
                    class="stauts"
                    :style="{
                        color: state.stauss[state.createForm.approveStatus]?.color
                    }"
                    v-if="state.createForm.approveStatus === 1"
                >
                    等待{{ state.createForm.approveUserName }}处理
                </text>
                <text
                    v-else
                    class="stauts"
                    :style="{
                        color: state.stauss[state.createForm.approveStatus]?.color
                    }"
                >
                    {{ state.stauss[state.createForm.approveStatus]?.text }}
                </text>
            </view>
        </view>
        <view
            class="content"
            :class="{
                active: state.route.isApproval === 'true' && state.createForm.approveStatus === 1
            }"
        >
            <view class="select-visitors">
                <view class="title_box form">访客列表</view>
                <view class="uni-list">
                    <view class="uni-list-cell" v-for="item in fromUserNameList" :key="item.$attrsid">
                        <view class="cell-info">
                            <view class="user">
                                <view class="icon">{{ item.visitorUser.name.substring(0, 1) }}</view>
                                <view class="user-id">
                                    <view>
                                        <view>{{ item.visitorUser.name }}</view>
                                        <view class="ID">身份证：{{ item.visitorUser.idCard }}</view>
                                    </view>
                                </view>
                            </view>
                        </view>
                        <view class="radio">
                            <image v-if="state.verifyType.includes('2')" class="picture_img" @click="clickShowZoomRef(item.visitorUser.picture)" :src="item.visitorUser.picture"> </image>
                            <view v-if="state.verifyType.includes('1')">
                                <image class="picture_img" v-if="item.qrCode" :src="item.qrCode" @click="clickShowZoomRef(item.qrCode)"></image>
                                <view class="picker_image_wait" v-else :class="{ active: state.createForm.approveStatus == 3 }">
                                    {{ state.createForm.approveStatus == 3 ? "已拒绝" : "等待审批通过生成" }}
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
            <view class="content-item">
                <view class="title_box form 1">访问信息</view>
                <view class="item" v-for="item in state.detailsForm" :key="item.key">
                    <view class="item_hander">{{ item.label }}</view>
                    <view class="item_content">
                        {{ state.createForm[item.key] }}
                    </view>
                </view>
            </view>
            <view class="ellipsiss">注意：申请通过后，在访问当天需携带身份证原件进行人证比对</view>
        </view>
        <view class="footer" v-if="state.route.isApproval === 'true' && state.createForm.approveStatus === 1">
            <view class="buttom_groun" v-if="state.route.isTeacher === 'true'">
                <button class="btn refuse" plain type="warn" :loading="state.confirmLoading" @click="onRefuseAgree(false)">拒绝</button>
                <button class="btn agree" type="primary" :loading="state.confirmLoading" @click="onRefuseAgree(true)">同意</button>
            </view>
            <!-- <button v-else plain type="primary" block :loading="state.confirmLoading" @click="onRevoke">撤销</button> -->
        </view>
        <view>
            <uni-popup ref="showZoomRef" posinset="center">
                <cover-image style="width: 80vw; height: 100%" :src="state.coverImagePpicture"></cover-image>
            </uni-popup>
        </view>
    </view>
</template>

<script setup>
import useStore from "@/store"
import { decodeURI, getCache } from "./utils.js"

const { user } = useStore()
const showZoomRef = ref()
const fromUserNameList = ref([])
const state = reactive({
    route: { isApproval: "false", isTeacher: "false" },
    coverImagePpicture: "",
    inputBorder: false,
    // 1：二维码 2：人脸 3：人证
    verifyType: [],
    stauss: {
        // 1审批中 2通过 3拒绝 4撤销
        1: { text: "审批中", color: "#FFC328FF", icon: "" },
        2: {
            text: "审批通过",
            color: "#11C685",
            icon: "@nginx/workbench/visitorSystem/passIcon.png"
        },
        3: {
            text: "审批拒绝",
            color: "#FD4F45FF",
            icon: "@nginx/workbench/visitorSystem/refuseIcon.png"
        },
        4: {
            text: "已撤销",
            color: "#999999FF",
            icon: "@nginx/workbench/visitorSystem/revokeIcon.png"
        },
        5: {
            text: "已过期",
            color: "#999999FF",
            icon: "@nginx/workbench/visitorSystem/expireIcon.png"
        }
    },
    confirmLoading: false,
    cardTypes: ["", "身份证", "其他"],
    actions: [
        { name: "大陆身份证", key: "check" },
        { name: "香港身份证", key: "hgID" },
        { name: "香港身份证", key: "macaoID" },
        { name: "港澳护照", key: "hgmacaoID" }
    ],
    detailsForm: [
        // { label: "审批编号", key: "id" },
        // { label: "访客姓名", key: "fromUserName" },
        // { label: "访客手机号", key: "phone" },
        // { label: "证件类型", key: "idCardType" },
        // { label: "证件号", key: "idCard" },
        { label: "拜访对象", key: "toUserName" },
        { label: "来访开始时间", key: "startTime" },
        { label: "来访结束时间", key: "endTime" },
        // { label: "访客人脸照片", key: "picture", type: "2" },
        // { label: "访客二维码（仅访问时间内有效）", key: "qrCode", type: "1" },
        { label: "对象所在班级或场地", key: "businessName" },
        { label: "到访原因", key: "reason" }
    ],
    createForm: {}
})
const mySchoolId = computed(() => user.schoolInfo.id)

// 拒绝// 同意
const onRefuseAgree = (isAgree) => {
    let { id } = state.route
    id = id ? id : state.createForm.approveId
    const params = { id, isAgree }
    // 审批同意拒绝
    http.post("/cloud/visitor/approve/operate", params).then(({ data, message }) => {
        uni.showToast({
            title: message,
            duration: 2000,
            icon: "none"
        })
        if (!state.route.noTodo && state.route.routeName && state.route.routeName !== "undefined") {
            // window.parent.postMessage({ visitorSystemBackApp: true }, "*")
            uni.navigateBack()
        } else {
            navigateTo({
                url: "/apps/visitorSystem/visitorApproval",
                query: { ...state.route, _noTodo: "visitorRecord" }
            })
        }
    })
}

// 撤销
// const onRevoke = () => {
//     state.confirmLoading = true
//     const { callId } = state.route
// 撤销审批
//     http.post("/app/visitor/records/cancel", { id: callId })
//         .then((res) => {
//             const { message, code } = res
//             uni.showToast({
//                 title: message,
//                 duration: 2000,
//                 icon: "none"
//             })
//             !code && getVisitorDetailsInfo()
//         })
//         .finally(() => (state.confirmLoading = false))
// }

// const encryptIdNumbers = computed(() => {
//   return (idNumber) => {
//     if (idNumber) {
//       // 将身份证号码的中间部分替换为指定符号
//       const symbol = "*";
//       const startIndex = 4;
//       const endIndex = idNumber.length - 4;
//       const encryptedPart = symbol.repeat(endIndex - startIndex);
//       return idNumber.replace(
//         idNumber.substring(startIndex, endIndex),
//         encryptedPart
//       );
//     }
//     return "";
//   };
// });

// 访客申请详情
const getVisitorDetailsInfo = async () => {
    const { callId } = state.route
    // 访客申请详情
    await http
        .get("/app/visitor/records/get", { id: callId })
        .then(({ data }) => {
            state.createForm = data
            fromUserNameList.value = data.recordsUserList
        })
        .finally(() => {
            uni.stopPullDownRefresh()
        })
}
// 判断是否是人脸
const getVisitorGlobalGetInfo = async () => {
    const params = { schoolId: "" }
    const encrypt = getCache("encrypt")
    if (encrypt && encrypt != "undefined") {
        let obj = decodeURI(encrypt)
        params.schoolId = obj.schoolId
    }
    // 访客机全局设置信息
    await http.get("/app/visitor/setting/global/get", params).then(({ data }) => {
        state.verifyType = data?.verifyType || []
    })
}
const clickShowZoomRef = (item) => {
    if (!item) return
    // state.coverImagePpicture = item
    // showZoomRef.value.open()
    uni.previewImage({
        urls: [item],
        current: 0
    })
}
const handerAmplify = (item) => {
    let urls = item ? [item] : ""
    if (urls) {
        uni.previewImage({
            urls,
            current: "",
            success: (res) => {},
            fail: (res) => {},
            complete: (res) => {}
        })
    }
}
onLoad(async (item) => {
    const { schoolId, token } = item
    state.route = item
    state.route.schoolId = schoolId || mySchoolId.value
    // 原生app 进来 没有schoolId  就获取学校id
    if (!schoolId && token) {
        // 访客机验证方式设置
        await http.get("/cloud/visitor/setting/global/get").then(({ data }) => {
            state.route.schoolId = data.schoolId
            state.route.isWx = "false"
        })
    }
    // 停止当前页面下拉刷新

    uni.setNavigationBarTitle({
        title: "审批详情" // 新标题内容
    })
    await getVisitorGlobalGetInfo()
    await getVisitorDetailsInfo()
})

onPullDownRefresh(() => {
    getVisitorDetailsInfo()
})
</script>

<style scoped lang="scss">
.details {
    // 圖片
    .seal_status {
        width: 180rpx;
        height: 180rpx;
        position: fixed;
        z-index: 999;
        right: 32rpx;
        top: 80rpx;
        /* #ifdef MP-WEIXIN */
        top: calc(120rpx + var(--status-bar-height));
        /* #endif */
    }

    .hander {
        // position: sticky;
        // top: 80rpx;
        // left: 0;
        // right: 0;
        z-index: 99;
        padding: 28rpx;
        border-bottom: 20rpx solid $uni-bg-color-grey;
        background-color: $uni-bg-color;

        .title {
            font-size: 30rpx;
            font-weight: 600;
        }

        .stauts {
            font-size: 28rpx;
        }

        .picker {
            :deep(label) {
                font-weight: 500;
                color: $uni-text-color;
                font-size: 30rpx;
            }
        }
    }

    .content {
        .content-item {
            background: $uni-bg-color;
        }

        &.active {
            padding-bottom: 140rpx;
        }

        .item {
            padding: 30rpx 28rpx 0;

            .item_hander {
                font-size: 26rpx;
                color: #666666ff;
            }

            .item_content {
                font-size: 30rpx;
                color: $uni-text-color;
                margin: 12rpx 0;
            }
        }

        .ellipsiss {
            font-size: 24rpx;
            padding: 20rpx;
            background-color: $uni-bg-color-grey;
            color: $uni-text-color-grey;
        }
    }

    .footer {
        padding: 30rpx;
        background: $uni-bg-color;
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;

        uni-button[type="primary"] {
            color: $uni-text-color-inverse;
            background-color: var(--primary-color);

            &[plain] {
                color: var(--primary-color);
                border: 1px solid var(--primary-color);
                background-color: transparent;
            }
        }

        .buttom_groun {
            display: flex;
            justify-content: space-around;

            .btn {
                margin: 0 10rpx;
                flex: 1;
            }
            .refuse {
                border: 1rpx solid $uni-color-error;
                color: $uni-color-error;
            }
            .agree {
                background: var(--primary-color);
            }
        }
    }
}

.title_box {
    width: 100%;
    text-align: center;
    line-height: 88rpx;
    font-size: 34rpx;
    font-weight: 500;
    color: $uni-text-color;
}

.select-visitors {
    border: 1px solid $uni-bg-color-grey;
    border-right-width: 0;
    border-left-width: 0;
    background: $uni-bg-color;
    border-bottom: 20rpx solid $uni-bg-color-grey;
}

.form {
    text-align: left;
    text-indent: 10px;
}

.uni-list {
    background: $uni-bg-color;

    .three-point {
        color: var(--primary-color);
        font-size: 50rpx;
    }

    .uni-list-cell {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 20rpx;
        padding-bottom: 20rpx;

        &:not(:last-child) {
            border-bottom: 3rpx solid $uni-border-color;
        }

        :deep(.uni-checkbox-input) {
            border-radius: 50%;
            border-color: uni-bg-color;
            overflow: hidden;

            svg {
                color: $uni-bg-color;
                background: var(--primary-color);
                transform: translate(-50%, -50%) scale(1);

                path {
                    fill: $uni-bg-color;
                }
            }
        }

        .cell-info {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .user {
                display: flex;
                align-items: center;

                .icon {
                    width: 60rpx;
                    height: 60rpx;
                    line-height: 60rpx;
                    margin-right: 20rpx;
                    border-radius: 50%;
                    font-size: 28rpx;
                    text-align: center;
                    background: var(--primary-color);
                    color: $uni-bg-color;
                }

                .user-id {
                    font-size: 28rpx;
                    display: flex;

                    .ID {
                        color: $uni-text-color-placeholder;
                        margin-top: 10rpx;
                    }
                }
            }
        }

        .radio {
            display: flex;
            align-items: center;

            // img {
            //     width: 55px;
            //     height: 55px;
            //     margin: 0 5px;
            // }

            .picture_img {
                width: 60px;
                height: 55px;
            }

            .picker_image_wait {
                text-align: center;
                transform: scale(0.8);
                padding: 6px 3px;
                width: 55px;
                height: 55px;
                line-height: 18px;
                font-size: 14px;
                background-color: $uni-bg-color-grey;
                border: 1rpx dashed #d9d9d9ff;
                color: #00000073;

                &.active {
                    color: red;
                    line-height: 55px;
                }
            }
        }
    }
}
</style>
