<template>
    <view class="system">
        <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="访客系统" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
        <view class="systems_search">
            <uni-search-bar class="search" :class="{ active: state.searchTotal }" clearButton="none" placeholder="请输入要搜索的内容" radius="20" align="left" v-model.trim="state.name" @cancel="handerCancel" @confirm="handerConfirm" />
            <div class="system_search_total" v-if="state.searchTotal">搜索结果：共 {{ state.searchTotal }} 条</div>
        </view>
        <div v-if="state.systems.length" style="margin-top: 20rpx">
            <uni-list class="systems_list" :border="false">
                <view class="list-item" v-for="item in state.systems" :key="item" @click="handerToDetails(item.id)">
                    <view class="item_hander">
                        <view class="title">{{ item.title }}</view>
                        <view class="itme">{{ item.createTime }}</view>
                    </view>
                    <view class="item_content">
                        <view class="user">
                            <image class="image" src="@nginx/workbench/visitorSystem/defaultAvatar.png"> </image>
                            <view class="name">{{ item.fromUserName }}</view>
                        </view>
                        <view class="staus" v-if="state.stauss[item.approveStatus]" :style="{ color: state.stauss[item.approveStatus].color }">{{ state.stauss[item.approveStatus].text }} </view>
                    </view>
                </view>
            </uni-list>
            <uni-load-more iconType="circle" :status="state.status" :contentText="state.contentText" />
        </div>
        <yd-empty text="暂无数据" :isMargin="true" v-else />
        <!-- 家长申请功能 -->
        <!-- <view class="create_apply" @click="handerToCreate">
            <view>发起</view>
            <view>申请</view>
        </view> -->
    </view>
</template>

<script setup name="visitorList">
import { sendAppEvent, checkPlatform } from "@/utils/sendAppEvent.js"
import { getCache, decodeURI } from "./utils.js"
import { getToken } from "@/utils/storageToken.js"

let route = { isTeacher: "false", isWx: "false" }
const state = reactive({
    route: { isTeacher: "false", isWx: "false" },
    scrollTop: 0,
    status: "more",
    contentText: {
        contentdown: "查看更多", //more
        contentrefresh: "加载中", // loading
        contentnomore: "没有更多" //noMore
    },
    searchTotal: 0,
    stauss: {
        // 1审批中 2通过 3拒绝 4撤销
        1: { text: "审批中", color: "#FFC328FF" },
        2: { text: "审批通过", color: " var(--primary-color)" },
        3: { text: "审批拒绝", color: "#FD4F45FF" },
        4: { text: "已撤销", color: "#999999FF" }
    },
    name: "",
    personType: 1, //人员类型 1：访客 2：临时人员
    pagination: {
        pageNo: 1,
        pageSize: 10,
        total: 0
    },
    systems: [],
    manageUserId: "",
    schoolId: ""
})

const getVisitorList = async () => {
    const { personType, name, pagination, schoolId, manageUserId } = state
    const params = {
        ...pagination,
        name,
        personType,
        // encrypt,
        manageUserId,
        schoolId
    }
    state.status = "loading"
    await http
        .post("/app/visitor/records/page", params)
        .then(({ data }) => {
            const { list, pageNo, pageSize, total } = data
            state.pagination = { pageNo, pageSize, total }
            if (name) {
                // 搜索条数
                state.searchTotal = total
                state.systems = list || []
            } else {
                state.searchTotal = 0
                if (state.systems.length) {
                    state.systems = state.systems.concat(list)
                } else {
                    state.searchTotal = 0
                    if (state.systems.length) {
                        state.systems = state.systems.concat(list)
                    } else {
                        state.systems = list || []
                    }
                }
            }
        })
        .finally(() => {
            state.status = "noMore"
            uni.stopPullDownRefresh()
        })
}

// 跳创建申请
const handerToCreate = () => {
    const token = getToken()
    navigateTo({ url: "/apps/visitorSystem/create", query: { ...route, token: token, manageUserId: state.manageUserId } })
}

// 跳详情
const handerToDetails = (callId) => {
    const params = {
        callId,
        noTodo: "index"
    }
    if (state.route.isTeacher) {
        params.isTeacher = state.route.isTeacher
    }
    if (state.route.isWx) {
        params.isWx = state.route.isWx
    }
    navigateTo({ url: "/apps/visitorSystem/details", query: params })
}
// 重置数据 查询
const handerConfirm = () => {
    state.pagination = {
        pageNo: 1,
        pageSize: 10,
        total: 0
    }
    state.systems = []
    uni.pageScrollTo({
        scrollTop: 0,
        duration: 300
    })
    getVisitorList()
}
// 删除清空
const handerCancel = () => {
    state.name = ""
    handerConfirm()
}
onPullDownRefresh(() => {
    state.name = ""
    handerConfirm()
})

const getByPhoneInfo = async (phone) => {
    let params = {}
    if (phone) {
        params.phone = phone
    }
    await http.post("/app/visitor/manageUser/getByPhone", params).then(({ data }) => {
        const { id } = data
        state.manageUserId = id
    })
}
// 根据token获取学校id
const getSettingFn = async () => {
    await http.get("/app/visitor/setting/global/get").then(({ data }) => {
        state.schoolId = data.schoolId
    })
}
onLoad(async (item) => {
    state.route = item
    // 停止当前页面下拉刷新
    uni.stopPullDownRefresh()
    uni.setNavigationBarTitle({
        title: "访客系统"
    })
    // window.parent.postMessage({ visitorSystemShow: true }, "*")
    // uni.navigateBack()
    const encryptObj = getCache("encrypt") ? decodeURI(getCache("encrypt")) : {}
    state.schoolId = encryptObj.schoolId || item.schoolId
    if (!item.schoolId) {
        await getSettingFn()
    }
    await getByPhoneInfo(encryptObj.phone)
    await getVisitorList()
})

onReachBottom(() => {
    const { pagination } = state
    if (pagination.total > pagination.pageSize * pagination.pageNo) {
        state.pagination.pageNo++
        getVisitorList()
    }
})

function clickLeft() {
    // #ifdef H5 || H5-WEIXIN
    const roleArr = ["yide-ios-app", "yide-android-app", "yide-Harmony-app"]
    const { noTodo, routeName } = state.route
    // 如果是内部应用跳转， 直接返回上一页。 state.route.noTodo  是上一页的路由
    if (!noTodo && routeName && routeName !== "undefined") {
        uni.navigateBack()
        // window.parent.postMessage({ visitorSystemBackApp: true }, "*")
        return
    }
    if (!noTodo && roleArr.includes(checkPlatform())) {
        sendAppEvent("backApp", {})
    } else {
        // window.history.back();
        uni.navigateBack()
    }
    // #endif
    // #ifdef MP-WEIXIN || APP-PLUS
    uni.navigateBack()
    // #endif
}
</script>

<style lang="scss">
.system {
    position: relative;
    height: 100vh;
    background-color: $uni-bg-color-grey;

    .systems_search {
        background: $uni-bg-color;
        .search {
            &.active {
                border: none;
            }
        }

        .system_search_total {
            font-size: 26rpx;
            padding: 20rpx 40rpx;
            background-color: $uni-bg-color-grey;
        }
    }

    .systems_list {
        padding: 20rpx 40rpx 0;

        &.active {
            padding-top: 180rpx;
        }

        .list-item:not(:last-child) {
            border-bottom: 2rpx solid $uni-border-color;
        }

        .item_content,
        .item_hander {
            display: flex;
            justify-content: space-between;
        }

        .item_hander {
            margin-top: 20rpx;
        }

        .title {
            font-size: 32rpx;
            font-weight: 600;
            flex: 1;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }

        .itme {
            font-size: 24rpx;
        }

        .item_content {
            margin: 20px 0;
            align-items: center;

            .user {
                display: flex;
                align-items: center;
                flex: 1;

                .image {
                    width: 60rpx;
                    height: 60rpx;
                }
            }

            .name {
                margin: 0 10rpx;
                font-size: 28rpx;
                color: $uni-text-color-grey;
                flex: 1;
            }

            .staus {
                font-size: 28rpx;
                width: 120rpx;
                text-align: right;
            }
        }
    }

    .create_apply {
        position: fixed;
        bottom: 226rpx;
        right: 30rpx;
        width: 112rpx;
        height: 112rpx;
        background: var(--primary-color);
        box-shadow: 0rpx 8rpx 8rpx 0rpx #11c68533;
        color: $uni-bg-color;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        flex-direction: column;
        font-size: 28rpx;
        align-items: center;
    }

    // #ifdef MP-WEIXIN || APP-PLUS
    .systems_search {
        // top: 0;

        // .system_search_total {
        //   bottom: -6rpx;
        // }

        .search {
            border-bottom: none;
        }

        :deep(.uni-searchbar) {
            border-bottom: 20px solid $uni-bg-color-grey;
        }
    }

    .systems_list {
        padding: 0;

        :deep(.uni-list) {
            padding: 10rpx 40rpx;

            .uni-border-bottom,
            .uni-border-top {
                display: none;
            }
        }

        .top {
            color: #8c8c8cff;
            font-size: 28rpx;
        }
    }

    // #endif

    .empty_image {
        width: 350rpx;
        height: 350rpx;
    }
}

.title_box {
    width: 100%;
    text-align: center;
    line-height: 88rpx;
    font-size: 34rpx;
    font-weight: 500;
    color: $uni-text-color;
}
</style>
