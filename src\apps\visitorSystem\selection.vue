<template>
    <view class="search-page">
        <!-- 顶部搜索栏 -->
        <view class="search-header">
            <view class="search-title">
                <view class="back-icon" @click="goBack">
                    <uni-icons type="left" size="20"></uni-icons>
                </view>
                <view class="title">
                    <text>搜索</text>
                </view>
            </view>
            <view class="search-query">
                <view class="search-input">
                    <view class="search-input">
                        <uni-search-bar v-model.trim="state.form.name" :focus="true" placeholder="请输入两个及以上字的内容" @input="handleSearch" @confirm="handleSearch" @clear="clearSearch" :radius="100" />
                    </view>
                </view>
            </view>
        </view>

        <!-- 搜索结果 -->
        <view class="search-result" v-if="mockData.length">
            <view class="result-count">
                <text>搜索结果：共{{ mockData.length }}条</text>
            </view>
            <view class="result-list">
                <radio-group @change="selectItem">
                    <label class="result-item" v-for="item in mockData" :key="item.id">
                        <radio :value="item" :checked="item.id === selectedId" color="#07C160" />
                        <text class="item-text">{{ item.name }}</text>
                        <text class="item-desc">（{{ item.deptName }}）</text>
                    </label>
                </radio-group>
            </view>
        </view>

        <!-- 底部确认按钮 -->
        <view class="footer">
            <view class="selected-box">
                <view class="selected-info">
                    <text class="table">已选择：</text>
                    <view class="ellipsis-clamp" v-if="selectedItem">
                        {{ selectedItem.name }}
                        （{{ selectedItem.deptName }}）
                    </view>
                </view>
                <button class="confirm-btn" @click="handleConfirm">确定</button>
            </view>
        </view>
    </view>
</template>

<script setup>
const mockData = ref([])
const selectedId = ref("")
const selectedItem = ref(null)
const state = reactive({
    query: null,
    form: {
        schoolId: "",
        name: ""
    }
})

// 选择项目
const selectItem = (item) => {
    selectedId.value = item.detail.value.id
    selectedItem.value = item.detail.value
}

// 搜索处理
const handleSearch = (event) => {
    // 去除前后空格
    event = event.trim()
    state.form.name = event
    // 可以在这里添加额外的搜索逻辑
    if (event.length > 1) {
        const params = {
            schoolId: state.form.schoolId,
            name: event
        }

        uni.showLoading({
            title: "加载中..."
        })
        http.post("/app/visitor/user/listTeacher", params)
            .then(({ data }) => {
                mockData.value = data
            })
            .finally(() => {
                uni.hideLoading()
            })
    }
    if (event?.length < 2) {
        mockData.value = []
    }
}

// 清空搜索
const clearSearch = () => {
    state.form.name = ""
    selectedId.value = ""
    selectedItem.value = null
    mockData.value = []
}

// 返回上一页
const goBack = () => {
    emit("selected", selectedItem.value)
}
const emit = defineEmits(["selected"])
// 确认选择
const handleConfirm = () => {
    if (selectedItem.value) {
        // 这里可以处理确认后的逻辑
        emit("selected", selectedItem.value)
    } else {
        goBack()
    }
}
onLoad(async (item) => {
    state.query = item
    state.form.schoolId = item.schoolId || ""
})
</script>

<style lang="scss" scoped>
$color6: #666;

.search-page {
    min-height: 100vh;
    background-color: #f5f5f5;

    .search-header {
        background-color: $uni-text-color-inverse;
        padding: 20rpx 0;

        .search-query,
        .search-title {
            display: flex;
            align-items: center;
        }

        .back-icon {
            padding: 0 20rpx;
        }

        .title {
            flex: 1;
            text-align: center;
        }
    }

    .search-header {
        .search-input {
            flex: 1;
        }

        :deep(.uni-searchbar__cancel) {
            padding: 0 20rpx;
            color: var(--primary-color);
            font-size: 28rpx;
        }
    }

    .search-result {
        .result-count {
            font-size: 26rpx;
            color: $uni-text-color;
            padding: 20rpx;
        }

        .result-item {
            display: flex;
            align-items: center;
            padding: 30rpx 20rpx;
            background-color: $uni-text-color-inverse;
            margin-bottom: 2rpx;

            .item-text {
                font-weight: 600;
                margin-left: 20rpx;
                font-size: 32rpx;
                color: $uni-text-color;
            }

            .item-desc {
                font-size: 32rpx;
                color: #8c8c8c;
                margin-left: 20rpx;
            }
        }
    }

    .footer {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 20rpx 20rpx 32rpx;
        background-color: $uni-text-color-inverse;
        border-top: 1rpx solid #d8d8d8;

        .selected-box {
            display: flex;
            justify-content: space-between;

            .selected-info {
                flex: 1;
                font-size: 26rpx;
                color: $color6;
                align-items: center;
                display: flex;

                .table {
                    width: 120rpx;
                    color: $uni-text-color;
                }

                .ellipsis-clamp {
                    width: 460rpx;
                    max-height: 68rpx;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 2;
                    overflow: hidden;
                }
            }

            .confirm-btn {
                margin: 0;
                width: 120rpx;
                background-color: var(--primary-color);
                color: $uni-text-color-inverse;
                border-radius: 8rpx;
                font-size: 32rpx;
            }
        }
    }
}
</style>
