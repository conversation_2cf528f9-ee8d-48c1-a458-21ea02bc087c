// Base64 解码
function fromUtf8Array(bytes) {
    let str = "",
        i = 0,
        c = 0,
        c2 = 0,
        c3 = 0
    while (i < bytes.length) {
        c = bytes[i]
        if (c < 128) {
            str += String.fromCharCode(c)
            i++
        } else if (c > 191 && c < 224) {
            c2 = bytes[i + 1]
            str += String.fromCharCode(((c & 31) << 6) | (c2 & 63))
            i += 2
        } else {
            c2 = bytes[i + 1]
            c3 = bytes[i + 2]
            str += String.fromCharCode(((c & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63))
            i += 3
        }
    }
    return str
}

function base64Decode(encodedStr) {
    const decodedBytes = atob(encodedStr)
    const utf8Array = new Uint8Array([...decodedBytes].map((char) => char.charCodeAt(0)))
    return fromUtf8Array(utf8Array)
}

export const decodeURI = (item) => {
    let encrypts = base64Decode(item)
    let data = []
    const obj = {}
    if (encrypts) {
        if (encrypts.indexOf("&") != -1) {
            // 有多个参数时
            data = encrypts.split("&")
            if (data.length) {
                data.forEach((v) => {
                    const data = v.split("=")
                    obj[data[0]] = data[1]
                })
            }
        } else {
            // 只有一个参数时
            data = encrypts.split("=")
            if (data.length) {
                obj[data[0]] = data[1]
            }
        }
    }
    return obj
}
// Base64编码加入
function toUtf8Array(str) {
    const utf8 = []
    for (let i = 0; i < str.length; i++) {
        let charcode = str.charCodeAt(i)
        if (charcode < 0x80) utf8.push(charcode)
        else if (charcode < 0x800) {
            utf8.push(0xc0 | (charcode >> 6), 0x80 | (charcode & 0x3f))
        } else if (charcode < 0xd800 || charcode >= 0xe000) {
            utf8.push(0xe0 | (charcode >> 12), 0x80 | ((charcode >> 6) & 0x3f), 0x80 | (charcode & 0x3f))
        } else {
            i++
            charcode = ((charcode & 0x3ff) << 10) | (str.charCodeAt(i) & 0x3ff)
            utf8.push(0xf0 | (charcode >> 18), 0x80 | ((charcode >> 12) & 0x3f), 0x80 | ((charcode >> 6) & 0x3f), 0x80 | (charcode & 0x3f))
        }
    }
    return utf8
}

function base64Encode(str) {
    const utf8Array = toUtf8Array(str)
    return btoa(String.fromCharCode(...utf8Array))
}

export const encodeURI = (item) => {
    // return window.btoa(item) || window.btoa(window.encodeURIComponent(item))
    return base64Encode(item)
}
// 设置 cookie 值的函数
/**
 * @param {*} key: key; value: 缓存值; expirationDays: 过期时间 1 设置一个过期时间为一天的缓存
 * @return {*}
 */

export const setCache = (key, value, expirationDays) => {
    const now = new Date()
    const item = {
        value: value,
        expiry: now.getTime() + expirationDays * 24 * 60 * 60 * 1000 // 过期时间
    }
    uni.setStorageSync(key, item)
}

// 获取 cookie 值的函数
/**
 * @param {*} key: 缓存名;
 * @return {*}
 */
export const getCache = (key) => {
    const item = uni.getStorageSync(key)
    // 如果缓存不存在
    if (!item) {
        return null
    }
    const now = new Date()
    // 如果缓存过期
    if (now.getTime() > item.expiry) {
        uni.removeStorageSync("refreshToken")
        uni.removeStorageSync(key)
        return null
    }
    return item.value
}

/**
 * 清除指定名字缓存
 * @param {*} k: 缓存名;
 * @return {*}
 */
export const remove = (k) => {
    uni.removeStorageSync(k)
    uni.removeStorageSync(k + dtime)
}
/**
 * @description: 复制链接
 * @return {string}
 */
export function copyLink(text) {
    const textarea = document.createElement("textarea") // 构建textarea
    textarea.value = text // 设置内容
    document.body.appendChild(textarea) // 添加临时实例
    textarea.select() // 选择实例内容
    document.execCommand("Copy") // 执行复制
    document.body.removeChild(textarea) // 删除临时实例
}

/**
 * 清除所有缓存(包括token)慎用!!!
 * @param {*}
 * @return {*}
 */
export const clear = () => {
    uni.clearStorageSync()
}

/**
 * @name 文件压缩
 * @description
 * 1、将文件转img对象
 * 2、获取文件宽高比例
 * 3、自定义等比例缩放宽高属性，这里我用的是固定800宽度，高度是等比例缩放
 * 4、canvas重新绘制图片
 * 5、canvas转二进制对象转文件对象，返回
 * 6、ImgWidth
 *
 * @returns { File } 文件
 */
export const compressImage = (file, quality = 0.8, ImgWidth = 250) => {
    return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.readAsDataURL(file)
        reader.onload = function (event) {
            const img = new Image()
            img.src = event.target.result
            img.onload = function () {
                const canvas = document.createElement("canvas")
                const ctx = canvas.getContext("2d") // 获取文件宽高比例
                const { width: originWidth, height: originHeight } = img
                // 自定义等比例缩放宽高属性，这里我用的是固定90宽度，高度是等比例缩放
                const scale = +(originWidth / originHeight).toFixed(2) // 比例取小数点后两位)
                const targetWidth = ImgWidth // 固定宽
                const targetHeight = Math.round(ImgWidth / scale) // 等比例缩放高
                const width = targetWidth
                const height = targetHeight
                canvas.width = width
                canvas.height = height
                ctx.drawImage(img, 0, 0, width, height)
                canvas.toBlob(
                    (blob) => {
                        const blobUrl = URL.createObjectURL(blob)
                        resolve(blobUrl)
                    },
                    "image/png",
                    quality
                )
            }
            img.onerror = function (error) {
                reject(error)
            }
        }
        reader.onerror = function (error) {
            reject(error)
        }
    })
}
