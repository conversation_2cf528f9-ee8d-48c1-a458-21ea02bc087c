<!-- 写一个放在页面底部的bottomTabBar -->
<template>
    <view class="bottomTabBar">
        <view class="tabBox">
            <view class="tabItem" @click="goVote">
                <view><image class="tabIcon1" src="https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png" alt="" /></view>
                <view class="tabText">投票</view>
            </view>
            <view class="tabItem" @click="goStatistics">
                <view><image class="tabIcon1" src="https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png" alt="" /></view>
                <view class="tabText">统计</view>
            </view>
            <view class="tabItem" @click="initiate">
                <view><image class="tabIcon1" src="https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png" alt="" /></view>
                <view class="tabText">发起</view>
            </view>
        </view>
    </view>
</template>

<script setup>
const goVote = () => {
    uni.navigateTo({
        url: "/apps/vote/teacher/index"
    })
}

const goStatistics = () => {
    uni.navigateTo({
        url: "/apps/vote/statistics/index"
    })
}
const initiate = () => {
    uni.navigateTo({
        url: "/apps/vote/voteCreate/index"
    })
}
</script>

<style scoped>
.bottomTabBar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 166rpx;
    background-color: $uni-bg-color-grey;
    border-top: 1rpx solid #d8d8d8;
}

.tabBox {
    display: flex;
    align-items: center;
    justify-content: space-around;
    padding-top: 10rpx;
}
.tabItem {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.tabIcon1 {
    width: 44rpx;
    height: 44rpx;
}

.tabText {
    font-weight: 400;
    font-size: 20rpx;
    color: var(--primary-color);
}
</style>
