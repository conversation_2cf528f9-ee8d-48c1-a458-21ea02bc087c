<template>
    <view class="vote-detail-page">
        <!-- 主图片显示区域 -->
        <view class="video-container">
            <image class="video-placeholder" :src="state.voteInfo.url || '@nginx/vote/defaultVote.png'" mode="aspectFill" />

            <view class="vote-status" v-if="state.voteInfo.status === 1">
                <image src="@nginx/vote/timeIcon.png" class="status-dot"></image>
                <text class="status-text">距离结束还剩：</text>
                <Countdown :end-time="state.voteInfo.endTime" number-color="#ffffff" label-color="#333333" number-size="36rpx" label-size="36rpx" backgroundColor="#333333" :show-day="true" :show-hour="true" :show-minute="true" :show-second="false" @timeup="onTimeUp(item)" />
            </view>
            <view v-else class="vote-status_not">
                <text class="vote-status_not_title">{{ getStatusText(state.voteInfo.status) }}</text>
            </view>
        </view>

        <!-- 投票信息区域 -->
        <view class="vote-info">
            <view claas="vote-title">
                {{ state.voteInfo.title }}
            </view>

            <view class="vote-description">
                <text class="description-text"> {{ state.voteInfo.desc }} </text>
            </view>
        </view>

        <!-- 投票选项卡片区域 使用双容器Flexbox布局（更简洁） -->
        <view class="vote-options">
            <view class="waterfall-wrapper">
                <view class="waterfall-column_left">
                    <view class="option-card-wrapper" v-for="(option, index) in leftItems" :key="option.id">
                        <view class="option-card" :class="{ selected: selectedOption === option.id }" @click="selectOption(option)">
                            <!-- 图片占位区域 -->
                            <view>
                                <view class="avatar-container" v-if="option.files[0] && option.files[0].url">
                                    <image class="candidate-avatar" :src="option.files[0].url" mode="aspectFill" />
                                </view>
                                <view class="avatar-container" v-else> </view>
                            </view>

                            <!-- 左上角选择标识 -->
                            <view class="selection-indicator" v-if="state.voteInfo.type === 1 || state.voteInfo.type === 3">
                                <radio style="pointer-events: none" color="#11C685" v-if="selectedOption === option.id" value="r1" :checked="true" />
                                <radio style="pointer-events: none" color="#11C685" v-else value="r1" :checked="false" />
                            </view>

                            <view class="candidate-number_box"> {{ option.originalIndex + 1 }}号 </view>

                            <!-- 信息区域 -->
                            <view class="card-info" @click="selectOption(option)">
                                <text class="candidate-name">{{ option.title }}</text>
                                <text class="vote-count">{{ option.countNum }} <text class="vote-count-unit">票</text></text>
                                <text class="candidate-description">{{ option.desc }}</text>
                                <!-- 根据类型存在的一个票数选择器 -->
                                <uni-number-box :min="0" :step="1" v-model="option.nums" v-if="state.voteInfo.type === 2 || state.voteInfo.type === 4" class="number-box" @change="(val) => changeValue(option, val)" />
                            </view>
                        </view>
                    </view>
                </view>
                <view class="waterfall-column_right">
                    <view class="option-card-wrapper" v-for="(option, index) in rightItems" :key="option.id">
                        <view class="option-card" :class="{ selected: selectedOption === option.id }" @click="selectOption(option)">
                            <!-- 图片占位区域 -->
                            <view>
                                <view class="avatar-container" v-if="option.files[0] && option.files[0].url">
                                    <image class="candidate-avatar" :src="option.files[0].url" mode="aspectFill" />
                                </view>
                                <view class="avatar-container" v-else> </view>
                            </view>

                            <!-- 左上角选择标识 -->
                            <view class="selection-indicator" v-if="state.voteInfo.type === 1 || state.voteInfo.type === 3">
                                <radio style="pointer-events: none" color="#11C685" v-if="selectedOption === option.id" value="r1" :checked="true" />
                                <radio style="pointer-events: none" color="#11C685" v-else value="r1" :checked="false" />
                            </view>

                            <view class="candidate-number_box"> {{ option.originalIndex + 1 }}号 </view>

                            <!-- 信息区域 -->
                            <view class="card-info" @click="selectOption(option)">
                                <text class="candidate-name">{{ option.title }}</text>
                                <text class="vote-count">{{ option.countNum }} <text class="vote-count-unit">票</text></text>
                                <text class="candidate-description">{{ option.desc }}</text>

                                <!-- 根据类型存在的一个票数选择器 -->
                                <!-- {{option.nums}} -->
                                <uni-number-box :min="0" :step="1" v-model="option.nums" v-if="state.voteInfo.type === 2 || state.voteInfo.type === 4" class="number-box" @change="(val) => changeValue(option, val)" />
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <!-- 底部固定按钮 -->
        <view class="bottom-actions">
            <button :disabled="state.voteInfo.status !== 1" class="vote-button" type="primary" @click="handleVote">投票</button>
            <!-- <button class="share-button" @click="handleShare">分享</button> -->
        </view>
    </view>
</template>

<script setup>
import { ref, computed, onMounted } from "vue"

import { onLoad } from "@dcloudio/uni-app"
import Countdown from "../comp/Countdown.vue"

const state = reactive({
    voteId: null,
    voteInfo: {},
    options: []
})

// 计算属性：分离左右两列的数据
const leftItems = computed(() => {
    const result = []
    state.options.forEach((item, index) => {
        if (index % 2 === 0) {
            // 偶数下标：0,2,4,6...
            result.push({ ...item, originalIndex: index, nums: 0 })
        }
    })
    return result
})

const rightItems = computed(() => {
    const result = []
    state.options.forEach((item, index) => {
        if (index % 2 === 1) {
            // 奇数下标：1,3,5,7...
            result.push({ ...item, originalIndex: index, nums: 0 })
        }
    })
    return result
})

// 当前选中的投票选项
const selectedOption = ref(null)

const getStatusText = (status) => {
    const statusMap = {
        0: "未开始",
        1: "进行中",
        2: "已结束",
        3: "已暂停"
    }
    return statusMap[status] || "未知状态"
}

const selectOption = (item) => {
    console.log(item, "123213")

    selectedOption.value = item.id

    // 添加触觉反馈
    uni.vibrateShort()

    // 显示选择提示
    uni.showToast({
        title: `已选择${item.title}`,
        icon: "none",
        duration: 1500
    })
}

/**
 * 处理投票提交
 */
const handleVote = () => {
    if (state.voteInfo.status === 0) {
        uni.showToast({
            title: "投票尚未开始",
            icon: "none"
        })
        return
    }

    let voteDetails = []
    // 如果是单选投票的话 只有一个
    if (state.voteInfo.type === 1 || state.voteInfo.type === 3) {
        voteDetails = [
            {
                optionId: selectedOption.value,
                nums: 1
            }
        ]
    }
    if (state.voteInfo.type === 2 || state.voteInfo.type === 4) {
        // 如果是多选选投票的话 有多个, 需要循环出leftItems rightItems  nums不为0的选项, 有
        leftItems.value.forEach((item) => {
            if (item.nums !== 0) {
                voteDetails.push({
                    optionId: item.id,
                    nums: item.nums
                })
            }
        })
        rightItems.value.forEach((item) => {
            if (item.nums !== 0) {
                voteDetails.push({
                    optionId: item.id,
                    nums: item.nums
                })
            }
        })
        if (!voteDetails.length) {
            uni.showToast({
                title: "请先选择一个选项",
                icon: "none"
            })
            return
        }
        // 根据 state.voteInfo.setting.maxOption   state.voteInfo.setting.minOption  来限制最多选多个个选手和最少选多少个选手
        if (voteDetails.length < state.voteInfo.setting.minOption) {
            uni.showToast({
                title: `最少选择${state.voteInfo.setting.minOption}个`,
                icon: "none"
            })
            return
        }
        if (voteDetails.length > state.voteInfo.setting.maxOption) {
            uni.showToast({
                title: `最多选择${state.voteInfo.setting.maxOption}个`,
                icon: "none"
            })
            return
        }
    }

    console.log(voteDetails, "voteDetails")

    http.post("/app/vote/clickVote", {
        voteId: state.voteId,
        voteDetails: voteDetails
    }).then((res) => {
        uni.showToast({
            title: "投票成功",
            icon: "success"
        })
        // 刷新票数显示
        getVote()
    })
}

const changeValue = (opt, val) => {
    opt.nums = val
}
// 页面加载时初始化
onMounted(() => {
    console.log("投票详情页面加载完成")
    // 这里可以添加页面初始化逻辑，比如获取投票数据等
})

// 获取投票详情
const getVote = () => {
    http.post("/app/vote/get", {
        voteId: state.voteId
    }).then((res) => {
        console.log(res, "详情详情详情详情432432432423")
        state.voteInfo = res.data
        state.options = res.data.options
    })
}

onLoad((options) => {
    state.voteId = options.voteId
    getVote()
})
</script>

<style lang="scss" scoped>
.vote-detail-page {
    min-height: 100vh;
    background-color: $uni-bg-color-grey;
    padding-bottom: 160rpx; // 为底部固定按钮预留空间
}

// 视频播放区域样式
.video-container {
    position: relative;

    padding: 30rpx;

    background-color: #ffffff;
    margin-bottom: 20rpx;

    .video-placeholder {
        width: 100%;
        height: 280rpx;
        border-radius: 20rpx;
    }
    .vote-status {
        display: flex;
        align-items: center;
        padding-top: 20rpx;
        padding-bottom: 20rpx;

        .status-dot {
            width: 50rpx;
            height: 50rpx;
            margin-right: 6rpx;
        }

        .status-text {
            font-weight: 600;
            font-size: 36rpx;
            color: #333333;
        }
    }
}

// 投票信息区域样式
.vote-info {
    background-color: #fff;
    padding: 30rpx;
    margin-bottom: 20rpx;

    .vote-description {
        .description-text {
            font-size: 26rpx;
            color: #666;
            line-height: 1.6;
        }
    }
}

// 投票选项卡片区域样式 - 瀑布流布局
.vote-options {
    padding: 0 20rpx;

    .option-card-wrapper {
        box-sizing: border-box; // 包含边框和内边距在宽度计算内

        .option-card {
            position: relative;
            background-color: #fff;
            border-radius: 20rpx;
            box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
            border: 3rpx solid transparent;
            overflow: hidden; // 防止内容溢出

            // 选中状态样式
            &.selected {
                border-color: var(--primary-color);
            }

            // 左上角选择标识
            .selection-indicator {
                position: absolute;
                top: 20rpx;
                left: 20rpx;
            }

            // 头像容器
            .avatar-container {
                display: flex;
                justify-content: center;
                width: 100%;
                min-height: 200rpx; // 设置最小高度，避免无图片时高度为0
                background-color: #f5f5f5; // 无图片时的背景色

                .candidate-avatar {
                    width: 100%;
                    height: auto; // 自适应高度
                    min-height: 200rpx; // 最小高度
                    border-radius: 20rpx 20rpx 0 0; // 只圆角上方
                }
            }

            // 信息区域
            .card-info {
                text-align: center;
                padding: 30rpx;

                .candidate-name {
                    display: block;
                    margin-bottom: 8rpx;
                    font-weight: 400;
                    font-size: 28rpx;
                    color: #333333;
                    word-wrap: break-word; // 长文本换行
                }

                .vote-count {
                    display: block;
                    font-size: 28rpx;
                    color: var(--primary-color);
                    font-weight: 400;
                    margin-bottom: 12rpx;

                    .vote-count-unit {
                        color: #333333;
                    }
                }

                .candidate-description {
                    display: block;
                    margin-bottom: 12rpx;
                    font-weight: 400;
                    font-size: 26rpx;
                    color: #333333;
                    line-height: 1.4; // 增加行高提升可读性
                    word-wrap: break-word; // 长文本换行
                }
            }
        }
    }
}

// 底部固定按钮样式
.bottom-actions {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #fff;

    padding-top: 30rpx;
    padding-right: 30rpx;
    padding-bottom: calc(44rpx + env(safe-area-inset-bottom));
    padding-left: 30rpx;
    border-top: 1rpx solid #e5e5e5;
    display: flex;
    gap: 20rpx;
    box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);

    .vote-button {
        flex: 1;
        background-color: var(--primary-color);
        color: #fff;
        border: none;
        border-radius: 10rpx;
        height: 92rpx;
        font-size: 32rpx;
        font-weight: 400;

        &[disabled] {
            background-color: #c8c9cc;
            color: #999;
        }
    }

    .share-button {
        flex: 1;
        background-color: #fff;
        color: #333;
        border: 2rpx solid #e5e5e5;
        border-radius: 12rpx;
        height: 88rpx;
        font-size: 32rpx;

        &:active {
            background-color: #f8f9fa;
            transform: scale(0.98);
        }
    }
}

.candidate-number_box {
    position: absolute;
    width: 88rpx;
    height: 40rpx;
    background: var(--primary-color);
    border-radius: 10rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 400;
    font-size: 26rpx;
    color: #ffffff;
    top: 184rpx;
    left: 130rpx;
}

.number-box {
    display: flex;
    align-items: center;
    justify-content: center;
}

.waterfall-wrapper {
    display: flex;
    gap: 30rpx;
}
.waterfall-column_left,
.waterfall-column_right {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 30rpx;
}

.vote-status_not {
    width: 128rpx;
    height: 50rpx;
    background: #595959;
    border-radius: 8rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    .vote-status_not_title {
        font-weight: 600;
        font-size: 32rpx;
        color: #ffffff;
    }
}
</style>
