<!-- 投票统计的主页面入口 投票统计往这里进入 -->
<template>
    <view class="statisticsPage">
        <!-- 顶部筛选区域 - 固定在顶部 -->
        <view class="header-fixed">
            <DropDown :menu="menu" @clickItem="clickItem" ref="dropDownRef"> </DropDown>
        </view>

        <!-- 中间滚动内容区域 -->
        <scroll-view class="scroll-content" scroll-y="true" :scroll-top="state.scrollTop" @scrolltolower="loadMore" @scroll="onScroll" refresher-enabled="true" @refresherrefresh="onRefresh" :refresher-triggered="state.refresherTriggered">
            <!-- 加载状态 -->
            <view v-if="state.loading && state.voteList.length === 0" class="loading-container">
                <uni-load-more status="loading" :content-text="{ contentdown: '点击加载更多', contentrefresh: '正在加载...', contentnomore: '没有更多数据了' }"></uni-load-more>
            </view>

            <!-- 空数据状态 -->
            <view v-else-if="!state.loading && state.voteList.length === 0" class="empty-container">
                <!-- <view class="empty-icon">📊</view> -->
                <view class="empty-text">暂无数据</view>
                <!-- <view class="empty-tip">请尝试调整筛选条件</view> -->
            </view>

            <!-- 投票列表 -->
            <view v-else class="vote-list">
                <view class="vote-item" v-for="(item, index) in state.voteList" :key="item.id" @click="handleClick(item)">
                    <!-- 投票活动图片 -->
                    <view class="vote-image-container">
                        <image class="vote-image" :src="item.url || item.image || 'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png'" mode="aspectFill" />
                    </view>

                    <!-- 投票活动信息 -->
                    <view class="vote-info">
                        <view class="vote-info_topBox">
                            <!-- 动态状态标签 -->
                            <view class="status-tag" :class="getStatusClass(item.status)">
                                <text class="status-text">{{ getStatusText(item.status) }}</text>
                            </view>
                            <view class="vote-title">{{ item.title }}</view>
                        </view>

                        <!-- uni-countdown倒计时组件 -->
                        <view class="countdown-container" v-if="item.status === 1">
                            <view class="time-icon"></view>
                            <view class="countdown-wrapper">
                                <text class="countdown-label">距离结束还剩：</text>
                                <Countdown :end-time="item.endTime" :show-day="true" :show-hour="true" :show-minute="true" :show-second="false" number-color="#333" label-color="#333" @timeup="onTimeUp(item)" />
                            </view>
                        </view>
                        <view class="countdown-container" v-else>
                            <view class="countdown-wrapper">
                                <text class="countdown-label">活动时间：</text>
                                <text class="countdown-label">{{ item.startTime.substring(0, 16).replace(/-/g, ".") }}-{{ item.endTime.substring(0, 16).replace(/-/g, ".") }}</text>
                            </view>
                        </view>
                    </view>
                </view>

                <!-- 加载更多提示 -->
                <uni-load-more :status="state.loadMore.status" :show-text="state.loadMore.showText" :content-text="state.loadMore.contentText" @clickLoadMore="loadMore" />
            </view>
        </scroll-view>

        <!-- 底部导航栏 - 固定在底部 -->
        <view class="footer-fixed">
            <BottomTabBar></BottomTabBar>
        </view>
    </view>
</template>

<script setup>
import BottomTabBar from "../comp/bottomTabBar.vue"
import DropDown from "../comp/dropDown.vue"
import http from "@/utils/http.js"
import Countdown from "../comp/Countdown.vue"
import { reactive, ref, onMounted } from "vue"

const state = reactive({
    loadMore: {
        status: "more", // more	加载前, loading	加载中, no-more	没有更多数据
        showText: true, // 显示文本
        contentText: {
            contentdown: "点击加载更多",
            contentrefresh: "正在刷新...",
            contentnomore: "没有更多数据了"
        }
    },
    pagination: {
        pageNo: 1,
        pageSize: 10,
        total: 0
    },
    scrollTop: 0,
    refresherTriggered: false, // 下拉刷新状态
    voteList: [], // 投票列表数据
    loading: false, // 页面加载状态
    // 筛选参数
    filterParams: {
        type: null, // 投票类型：1单选 2多选 3二选一 4评选
        status: null // 投票状态：0未开始 1进行中 2已结束 3已暂停
    }
})

const menu = ref({
    type: {
        label: "全部",
        child: [
            { label: "全部", value: "all" },
            { label: "单选投票", value: 1 },
            { label: "多选投票", value: 2 },
            { label: "二选一PK投票", value: 3 },
            { label: "评选投票活动", value: 4 }
        ]
    },
    status: {
        label: "全部",
        child: [
            { label: "全部", value: "all" },
            { label: "未开始", value: 0 },
            { label: "进行中", value: 1 },
            { label: "已结束", value: 2 }
            // { label: "已暂停", value: 3 }
        ]
    }
})

const onTimeUp = () => {}

const handleClick = (item) => {
    uni.navigateTo({
        url: `/apps/vote/statistics/details?voteId=${item.id}`
    })
}

/**
 * 获取投票列表数据
 * @param {boolean} isRefresh - 是否为刷新操作
 * @param {boolean} isLoadMore - 是否为加载更多操作
 */
const getVoteList = async (isRefresh = false, isLoadMore = false) => {
    try {
        // 如果是刷新操作，重置分页
        if (isRefresh) {
            state.pagination.pageNo = 1
            state.loadMore.status = "more"
        }

        // 设置加载状态
        if (isLoadMore) {
            state.loadMore.status = "loading"
        } else {
            state.loading = true
        }

        // 构建请求参数
        let params = {
            pageNo: state.pagination.pageNo,
            pageSize: state.pagination.pageSize
        }

        // 添加筛选参数（只有非null值才添加）
        if (state.filterParams.type !== "all") {
            params.type = state.filterParams.type
        }
        if (state.filterParams.status !== "all") {
            params.status = state.filterParams.status
        }

        console.log("请求参数:", params)

        // 调用接口
        const response = await http.post("/app/vote/page", params)

        if (response.code === 0) {
            const { list, total } = response.data

            // 更新分页信息
            state.pagination.total = total

            if (isRefresh || state.pagination.pageNo === 1) {
                // 刷新或首次加载，替换数据
                state.voteList = list || []
                if (isRefresh) {
                    uni.showToast({
                        title: "刷新成功",
                        icon: "success",
                        duration: 1500
                    })
                }
            } else {
                // 加载更多，追加数据
                state.voteList.push(...(list || []))
            }

            // 更新加载更多状态
            if (list && list.length < state.pagination.pageSize) {
                state.loadMore.status = "no-more"
            } else {
                state.loadMore.status = "more"
            }

            console.log("获取投票列表成功:", {
                total: total,
                currentPage: state.pagination.pageNo,
                listLength: list?.length || 0,
                totalItems: state.voteList.length
            })
        }
    } catch (error) {
        console.error("获取投票列表失败:", error)
        uni.showToast({
            title: "获取数据失败",
            icon: "none"
        })
        state.loadMore.status = "more"
    } finally {
        state.loading = false
        state.refresherTriggered = false
    }
}

/**
 * 下拉刷新处理
 */
const onRefresh = () => {
    state.refresherTriggered = true
    getVoteList(true, false)
}

/**
 * 上拉加载更多
 */
const loadMore = () => {
    if (state.loadMore.status === "loading" || state.loadMore.status === "no-more") {
        return
    }

    state.pagination.pageNo++
    getVoteList(false, true)
}

/**
 * 滚动事件处理
 */
const onScroll = (e) => {
    state.scrollTop = e.detail.scrollTop
}

/**
 * 下拉菜单点击事件
 */
const dropDownRef = ref(null)
const clickItem = (item) => {
    console.log("筛选条件变更111111:", item)

    // 更新筛选参数
    if (item.name === "type") {
        state.filterParams.type = item.value
    } else if (item.name === "status") {
        state.filterParams.status = item.value
    }

    // 重新加载数据
    state.pagination.pageNo = 1
    getVoteList(true, false)
}

/**
 * 获取状态文本
 */
const getStatusText = (status) => {
    const statusMap = {
        0: "未开始",
        1: "进行中",
        2: "已结束",
        3: "已暂停"
    }
    return statusMap[status] || "未知状态"
}

/**
 * 获取状态样式类
 */
const getStatusClass = (status) => {
    const classMap = {
        0: "status-not-started",
        1: "status-ongoing",
        2: "status-ended",
        3: "status-paused"
    }
    return classMap[status] || "status-unknown"
}

/**
 * 页面初始化
 */
onMounted(() => {
    // 初始化加载数据
    getVoteList()
})
</script>

<style lang="scss" scoped>
.statisticsPage {
    display: flex;
    flex-direction: column;
    height: 100vh; /* 设置页面高度为视窗高度 */
    overflow: hidden; /* 防止整个页面滚动 */
}

/* 顶部固定区域 */
.header-fixed {
    position: fixed;
    top: calc(44px + env(safe-area-inset-top)); /* 系统导航栏高度(44px) + 安全区域高度 */
    left: 0;
    right: 0;
    z-index: 999;
    background-color: #fff;
    border-bottom: 1rpx solid #eee;
    height: 100rpx; /* DropDown组件的高度 */
}

/* 中间滚动内容区域 */
.scroll-content {
    position: fixed;
    top: calc(44px + env(safe-area-inset-top) + 100rpx); /* 导航栏 + 安全区域 + DropDown高度 */
    bottom: 166rpx; /* 底部区域高度 */
    left: 0;
    right: 0;
    background: #f9faf9;
    width: 100%;
}

/* 加载状态容器 */
.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 100rpx 0;
}

/* 空数据状态容器 */
.empty-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 200rpx 40rpx;
    text-align: center;

    .empty-icon {
        font-size: 120rpx;
        margin-bottom: 40rpx;
        opacity: 0.6;
    }

    .empty-text {
        font-size: 32rpx;
        color: #666;
        margin-bottom: 16rpx;
        font-weight: 500;
    }

    .empty-tip {
        font-size: 28rpx;
        color: #999;
    }
}

/* 底部固定区域 */
.footer-fixed {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 999;
}

// 每个投票项的样式
.vote-item {
    margin-bottom: 30rpx;
    background-color: #ffffff;
    border-radius: 16rpx;
    overflow: hidden;
    margin-bottom: 24rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);

    // 投票图片容器
    .vote-image-container {
        position: relative;
        width: 100%;
        height: 320rpx;

        .vote-image {
            width: 100%;
            height: 100%;
        }
    }

    // 投票信息区域
    .vote-info {
        padding: 24rpx;

        .vote-info_topBox {
            display: flex;
            align-items: center;
            .vote-title {
                font-weight: 600;
                font-size: 30rpx;
                color: #333333;
                padding-left: 10rpx;
                // 超出部分显示省略号
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            // 状态标签
            .status-tag {
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 4rpx 10rpx;
                border-radius: 8rpx;
                .status-text {
                    font-weight: 500;
                    font-size: 20rpx;
                    color: #ffffff;
                }

                // 不同状态的颜色
                &.status-not-started {
                    background-color: #ffc327; // 黄色 - 未开始
                }

                &.status-ongoing {
                    background-color: var(--primary-color); // 绿色 - 进行中
                }

                &.status-ended {
                    background-color: #595959; // 灰色 - 已结束
                }

                &.status-paused {
                    background-color: #595959; // 灰色 - 已暂停
                }

                &.status-unknown {
                    background-color: #595959; // 灰色 - 未知状态
                }
            }
        }

        // 倒计时容器样式
        .countdown-container {
            display: flex;
            align-items: center;
            .time-icon {
                width: 24rpx;
                height: 24rpx;
                background-color: #07c160;
                border-radius: 50%;
                margin-right: 12rpx;
                flex-shrink: 0;
            }

            .countdown-wrapper {
                display: flex;
                align-items: center;
                flex: 1;

                .countdown-label {
                    font-size: 26rpx;
                    color: #666;
                    margin-right: 8rpx;
                }
            }
        }
    }
}

.vote-list {
    padding: 30rpx 30rpx 50rpx 30rpx; /* 增加底部padding，确保最后一项完整显示 */
    min-height: calc(100% + 50rpx); /* 确保内容区域高度足够，包含底部padding */
    box-sizing: border-box;
}

.uni-load-more {
    background: transparent;
    padding: 20rpx 0 30rpx 0; /* 增加底部padding */
    margin-bottom: 20rpx; /* 确保加载更多组件有足够的底部空间 */
}
</style>
