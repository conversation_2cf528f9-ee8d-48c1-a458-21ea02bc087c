<template>
    <view class="optionPage">
        <view class="line_box"></view>
        <view class="opt_box">
            <view class="optionPage_title">
                选项名称： <text class="optionPage_title_name"> {{ state.title }} </text>
            </view>
            <!-- 表格头 -->
            <view class="table_head">
                <view class="table_head_item table_head_option">投票人</view>

                <view class="table_head_item table_head_rank">有效投票次数</view>
            </view>

            <!-- 表格内容 -->
            <view class="table_content">
                <view class="table_row" v-for="item in state.list" :key="item.id">
                    <view class="table_row_item table_row_option">{{ item.userName }}</view>
                    <view class="table_row_item table_row_rank">{{ item.countNum }}</view>
                </view>

                <!-- 空数据提示 -->
                <view v-if="!state.loading && state.list.length === 0" class="empty_tip"> 暂无数据 </view>
            </view>

            <!-- 加载状态提示 -->
            <view v-if="state.loading" class="loading_tip">
                <text>正在加载...</text>
            </view>

            <!-- 没有更多数据提示 -->
            <view v-if="!state.hasMore && state.list.length > 0" class="no_more_tip">
                <text>没有更多数据了</text>
            </view>
        </view>
    </view>
</template>

<script setup>
import { onLoad, onReachBottom } from "@dcloudio/uni-app"
import http from "@/utils/http.js"

const state = reactive({
    voteOptionId: null,
    title: "",
    list: [],
    pagination: {
        pageNo: 1,
        pageSize: 10,
        total: 0
    },
    loading: false, // 是否正在加载数据
    hasMore: true // 是否还有更多数据
})

// 获取投票选项统计数据
const getVoteOptionData = async (isLoadMore = false) => {
    if (state.loading) return

    // 如果是加载更多，但已经没有更多数据了，直接返回
    if (isLoadMore && !state.hasMore) {
        uni.showToast({
            title: "没有更多数据了",
            icon: "none"
        })
        return
    }

    state.loading = true

    try {
        const params = {
            voteOptionId: state.voteOptionId,
            pageNo: state.pagination.pageNo,
            pageSize: state.pagination.pageSize
        }

        const response = await http.post("/app/vote/voteOptionCountNumPage", params)

        if (response.code === 0) {
            const { list, total, pageNo } = response.data

            if (isLoadMore) {
                // 加载更多：将新数据追加到现有列表
                state.list.push(...list)
            } else {
                // 首次加载：直接替换列表数据
                state.list = list
            }

            state.pagination.total = total
            state.pagination.pageNo = pageNo + 1

            // 判断是否还有更多数据
            state.hasMore = state.list.length < total
        }
    } catch (error) {
        console.error("获取投票选项数据失败:", error)
        uni.showToast({
            title: "数据加载失败",
            icon: "none"
        })
    } finally {
        state.loading = false
    }
}

// 页面加载时获取数据
onLoad((options) => {
    console.log(options, "options")
    state.voteOptionId = options.optionId
    state.title = options.title

    // 获取初始数据
    getVoteOptionData()
})

// 触底加载更多
onReachBottom(() => {
    if (state.hasMore && !state.loading) {
        getVoteOptionData(true)
    }
})
</script>

<style lang="scss" scoped>
.optionPage {
    min-height: 100vh;
    background: $uni-bg-color-grey;
}
.line_box {
    height: 20rpx;
    background: $uni-bg-color-grey;
}

.opt_box {
    padding: 30rpx;
    background: $uni-bg-color;
}

.optionPage_title {
    font-weight: 400;
    font-size: 28rpx;
    color: #999999;
    padding-bottom: 20rpx;
}
.optionPage_title_name {
    font-weight: 600;
    font-size: 28rpx;
    color: #333333;
}

.table_head {
    display: flex;
    background: var(--primary-bg-color);
    border-radius: 10rpx;
    padding: 20rpx 30rpx;
    margin-bottom: 20rpx;
    .table_head_item {
        flex: 1;
        text-align: left;
        font-weight: 400;
        font-size: 28rpx;
        color: #333333;
    }
    .table_head_option {
        flex: 0 0 70%;
    }
    .table_head_rank {
        flex: 0 0 30%;
    }
}

.table_content {
    .table_row {
        display: flex;
        padding: 20rpx 30rpx;
        border-bottom: 1rpx solid #f5f5f5;
        .table_row_item {
            flex: 1;
            text-align: left;
            font-weight: 400;
            font-size: 28rpx;
            color: #333333;
        }
        .table_row_rank {
            flex: 0 0 30%;
        }
        .table_row_option {
            flex: 0 0 70%;
        }
    }
}

// 空数据提示样式
.empty_tip {
    text-align: center;
    padding: 60rpx 0;
    font-size: 28rpx;
    color: #999999;
}

// 加载状态提示样式
.loading_tip {
    text-align: center;
    padding: 30rpx 0;
    font-size: 28rpx;
    color: #666666;
}

// 没有更多数据提示样式
.no_more_tip {
    text-align: center;
    padding: 30rpx 0;
    font-size: 24rpx;
    color: #999999;
}
</style>
