<template>
    <view class="container">
        <uni-nav-bar statusBar fixed left-icon="left" :title="titleText" :border="false" @clickLeft="clickLeft" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
        <scroll-view scroll-y class="scroll-content">
            <!-- 基本设置 -->
            <view class="section">
                <view class="section-title">基本设置</view>

                <view class="section_box">
                    <!-- 投票标题 -->
                    <view class="title_box">
                        <textarea v-model="formData.title" key="title" auto-height class="input" placeholder="投票标题（30字内）" maxlength="30"></textarea>
                    </view>

                    <!-- 补充描述 -->
                    <view>
                        <textarea v-model="formData.desc" key="desc" auto-height class="textarea" placeholder="补充描述（选填）" maxlength="200"></textarea>
                    </view>

                    <!-- 活动主图上传 -->
                    <view class="form-item">
                        <view class="upload-area" @click="uploadMainImage">
                            <image v-if="formData.url" :src="formData.url" class="uploaded-image" mode="aspectFill" />
                            <view v-else class="upload-placeholder">
                                <uni-icons type="plusempty" size="30" color="#333333"></uni-icons>
                                <text class="upload-text">点击上传</text>
                            </view>
                            <!-- 删除按钮 -->
                            <view v-if="formData.url" class="delete-image" @click.stop="deleteMainImage">
                                <uni-icons type="closeempty" size="12" color="#FFFFFF"></uni-icons>
                            </view>
                        </view>
                        <view class="label">活动主图（选填）</view>
                    </view>
                </view>
            </view>

            <!-- 选项设置 -->
            <view class="section">
                <view class="section-title">选项设置</view>

                <view class="optBox" v-if="voteStore.type !== 4">
                    <!-- 图文投票开关 -->
                    <view class="switch-item switch-label_top">
                        <text class="switch-label">图文投票</text>
                        <switch style="transform: scale(0.7)" :checked="formData.setting?.hasImage === 1" color="var(--primary-color)" @change="handleImageVoteChange" />
                    </view>

                    <!-- 选项列表 -->
                    <view class="options-list" v-if="voteStore.type === 1 || voteStore.type === 2">
                        <!-- @start="onStart" @update="onUpdate" @end="onEnd" -->
                        <VueDraggable handle=".option-drag" ref="el" v-model="formData.options" :animation="150">
                            <view v-for="(option, index) in formData.options" :key="index + '_' + Math.random()" class="option-item">
                                <!-- 选项内容 -->
                                <view class="option-content">
                                    <!-- 删除按钮 -->
                                    <view class="option-delete" @click="deleteOption(index)">
                                        <uni-icons type="minus-filled" size="20" color="#FF6B6B"></uni-icons>
                                    </view>
                                    <!-- 选项图片（图文投票开启时显示） -->
                                    <view class="option-image-area" v-if="formData.setting.hasImage">
                                        <view class="option-image-container" @click="uploadOptionImage(index)">
                                            <image v-if="option.files[0].url" :src="option.files[0].url" class="option-image" mode="aspectFill" />
                                            <view v-else class="option-image-placeholder">
                                                <text class="image-placeholder-text">添加</text>
                                                <text class="image-placeholder-text">图片</text>
                                            </view>
                                            <!-- 删除图片按钮 -->
                                            <!-- <view v-if="option.image" class="delete-option-image" @click.stop="deleteOptionImage(index)">
                                            <uni-icons type="close" size="12" color="#FFFFFF"></uni-icons>
                                        </view> -->
                                        </view>
                                    </view>

                                    <!-- 选项输入框 -->
                                    <input :key="`option${index}`" v-model="option.title" class="option-input" placeholder="请输入选项标题（30字内）" maxlength="30" />

                                    <!-- 拖拽按钮 -->
                                    <view class="option-drag" @touchstart="handleDragStart(index)" @touchmove="handleDragMove" @touchend="handleDragEnd">
                                        <uni-icons type="bars" size="18" color="#333333"></uni-icons>
                                    </view>
                                </view>
                            </view>
                        </VueDraggable>
                    </view>

                    <!-- 红方蓝方Pk的选项列表 -->
                    <view class="options-list" v-if="voteStore.type === 3">
                        <view v-for="(option, index) in formData.options" :key="option.id" class="option-item">
                            <!-- 选项内容 -->
                            <view class="option-content">
                                <!-- 删除按钮 -->
                                <view
                                    :class="{
                                        'option-PK_type-red': index === 0,
                                        'option-PK_type-blue': index === 1,
                                        'option-PK_type': true
                                    }"
                                    @click="deleteOption(index)"
                                >
                                    {{ index === 0 ? "红方" : "蓝方" }}
                                </view>
                                <!-- 选项图片（图文投票开启时显示） -->
                                <view class="option-image-area" v-if="formData.setting.hasImage">
                                    <view class="option-image-container" @click="uploadOptionImage(index)">
                                        <image v-if="option.files[0].url" :src="option.files[0].url" class="option-image" mode="aspectFill" />
                                        <view v-else class="option-image-placeholder">
                                            <text class="image-placeholder-text">添加</text>
                                            <text class="image-placeholder-text">图片</text>
                                        </view>
                                        <!-- 删除图片按钮 -->
                                        <!-- <view v-if="option.image" class="delete-option-image" @click.stop="deleteOptionImage(index)">
                                            <uni-icons type="close" size="12" color="#FFFFFF"></uni-icons>
                                        </view> -->
                                    </view>
                                </view>

                                <!-- 选项输入框 -->
                                <input :key="`option${index}title`" v-model="option.title" class="option-input" placeholder="请输入选项标题（30字内）" maxlength="30" />
                            </view>
                        </view>
                    </view>

                    <!-- 新增选项按钮 -->
                    <view class="add-option-btn" @click="addOption" v-if="voteStore.type === 1 || voteStore.type === 2">
                        <uni-icons type="plus" size="20" color="var(--primary-color)"></uni-icons>
                        <text class="add-option-text">新增选项（最多1000项）</text>
                    </view>
                </view>

                <!-- 这里是单独的一个评选活动的选项设置的页面 -->
                <view class="optBox_active" v-if="voteStore.type === 4">
                    <view class="optList_item" v-for="(item, index) in formData.options" :key="index + '_' + Math.random()">
                        <view class="optIndex">
                            <text>{{ `选项${index + 1}` }}</text>
                            <uni-icons @click="deleteOption(index)" type="trash" size="16" color="#999999"></uni-icons>
                        </view>
                        <view class="optList_input">
                            <uni-easyinput :inputBorder="false" type="textarea" v-model="item.name" placeholder="请输入参赛名称" maxlength="30"></uni-easyinput>
                        </view>
                        <!-- 补充描述 -->
                        <view>
                            <uni-easyinput :inputBorder="false" type="textarea" v-model="item.desc" placeholder="请输入参赛介绍" maxlength="200"></uni-easyinput>
                        </view>

                        <!-- 评选活动图上传 多张 -->
                        <view class="form-item">
                            <view class="form-item_grid">
                                <view class="upload-area" @click="upload4Image(item)">
                                    <view class="upload-placeholder">
                                        <uni-icons type="plusempty" size="30" color="#333333"></uni-icons>
                                        <text class="upload-text">点击上传</text>
                                    </view>
                                </view>
                                <view
                                    :class="{
                                        'upload-area': true,
                                        'upload-area_hide': !imgItem.url
                                    }"
                                    v-for="(imgItem, imgIndex) in item.files"
                                    :key="imgIndex + '_' + Math.random()"
                                >
                                    <image :src="imgItem.url" class="uploaded-image" mode="aspectFill" />
                                    <!-- 删除按钮 -->
                                    <view class="delete-image" @click.stop="delete4Image(item.files, imgIndex)">
                                        <uni-icons type="closeempty" size="12" color="#FFFFFF"></uni-icons>
                                    </view>
                                </view>
                            </view>
                            <view class="label">参赛图片（必填）</view>
                        </view>
                    </view>
                    <!-- 新增选项按钮 -->
                    <view class="add-option-btn_addbox" @click="addOption">
                        <uni-icons type="plusempty" size="20" color="var(--primary-color)"></uni-icons>
                        <text class="add-option-text">添加选项</text>
                    </view>
                </view>
            </view>

            <!-- 高级设置 -->
            <view class="section">
                <view class="section-title">高级设置</view>

                <view class="setBox">
                    <!-- 活动开始时间 -->
                    <!--  @click="selectStartTime" -->
                    <view class="setting-item">
                        <text class="setting-label">活动开始时间</text>
                        <view class="setting-value">
                            <picker :class="['time-text', { placeholder: !formData.startTime }]" mode="date" :value="formData.startTime" @change="changeStartTime">
                                <view class="uni-input"> {{ formData.startTime || "请选择" }}</view>
                            </picker>

                            <uni-icons type="arrowright" size="16" color="#C0C4CC"></uni-icons>
                        </view>
                    </view>

                    <!-- 活动结束时间 -->
                    <view class="setting-item">
                        <text class="setting-label">活动结束时间</text>
                        <view class="setting-value">
                            <picker :class="['time-text', { placeholder: !formData.endTime }]" mode="date" :value="formData.endTime" @change="changeEndTime">
                                <view class="uni-input"> {{ formData.endTime || "请选择" }}</view>
                            </picker>
                            <uni-icons type="arrowright" size="16" color="#C0C4CC"></uni-icons>
                        </view>
                    </view>

                    <!-- 参与人员范围 -->
                    <view class="setting-item" @click="selectParticipants">
                        <text class="setting-label">参与人员范围</text>
                        <view class="setting-value">
                            <text :class="['time-text personnelScopeNameText', { placeholder: !formData.allUserNameStr }]">
                                {{ formData.allUserNameStr || "请选择" }}
                            </text>
                            <uni-icons type="arrowright" size="16" color="#C0C4CC"></uni-icons>
                        </view>
                    </view>

                    <view class="counter-item" v-if="voteStore.type === 2 || voteStore.type === 4">
                        <text class="counter-label">至少要选</text>
                        <view class="counter-controls"> <uni-number-box :min="0" v-model="formData.setting.minOption" :step="1" /> <text class="counter-label">个选项</text> </view>
                    </view>
                    <view class="counter-item" v-if="voteStore.type === 2 || voteStore.type === 4">
                        <text class="counter-label">最多可选</text>
                        <view class="counter-controls"> <uni-number-box :min="0" v-model="formData.setting.maxOption" :step="1" /> <text class="counter-label">个选项</text> </view>
                    </view>

                    <!-- 投票明细数据仅发起人可见 -->
                    <view class="switch-item">
                        <text class="switch-label">投票明细数据仅发起人可见</text>
                        <switch style="transform: scale(0.7)" :checked="formData.setting.selfSee === 1" color="var(--primary-color)" @change="handleSelfSee" />
                    </view>

                    <!-- 每天投票次数 -->
                    <view class="counter-item">
                        <text class="counter-label">每天投票次数</text>
                        <uni-number-box :min="0" v-model="formData.setting.dayLimit" :step="1" />
                    </view>

                    <!-- 总共投票次数 -->
                    <view class="counter-item">
                        <text class="counter-label">总共投票次数</text>
                        <uni-number-box :min="0" v-model="formData.setting.sumLimit" :step="1" />
                    </view>

                    <!-- 投票明细数据仅发起人可见 -->
                    <view class="switch-item" v-if="voteStore.type === 4">
                        <text class="switch-label">允许用户给同一选手重复投票</text>
                        <switch style="transform: scale(0.7)" :checked="formData.setting.repeat === 1" color="var(--primary-color)" @change="handleRepeat" />
                    </view>
                </view>
            </view>
        </scroll-view>

        <!-- 底部提交按钮 -->
        <view class="submit-wrapper">
            <button class="submit-btn" @click="submitVote">提交</button>
        </view>

        <!-- 选择投票的人 -->
        <yd-selector ref="selectorRef" @confirm="confirmUser" />
    </view>
</template>

<script setup>
import { VueDraggable } from "vue-draggable-plus"
import useVoteStore from "@/store/vote"
const selectorRef = ref(null)

// 使用 Pinia store 获取表单数据
const voteStore = useVoteStore()
// 表单数据 - 直接使用 Pinia store 中的数据
let formData = computed(() => voteStore.voteForm)
console.log(formData, "formDataformDataformData")

const typesMap = {
    1: "单选投票",
    2: "多选投票",
    3: "二选一PK投票",
    4: "评选活动投票",
    // 可以根据需要添加更多类型及其对应的标题文本
    default: "投票创建" // 默认情况或其他未列出的类型将返回此文本
}

const titleText = computed(() => {
    const type = voteStore.type // 使用解构赋值简化代码
    return typesMap[type] || typesMap.default // 直接从映射中获取对应的文本，如果没有匹配项则返回默认文本
})
// 拖拽相关
const dragStartIndex = ref(-1)
const dragCurrentIndex = ref(-1)

const uploadImageToServer = async (file) => {
    try {
        // #ifdef H5 || APP-PLUS
        const url = await http.uploadFile("/file/common/upload", null, { folderType: "app" }, file)
        console.log(url)
        // #endif
        // #ifdef MP-WEIXIN
        const url = await http.uploadFile("/file/common/upload", file.path, { folderType: "app" })
        // #endif
        formData.value.url = url
    } catch (error) {
        console.error("上传图片失败：", error)
    }
}

/**
 * 上传活动主图
 */
const uploadMainImage = () => {
    uni.chooseImage({
        count: 1,
        sourceType: ["album"],
        success: (res) => {
            console.log(res, "1111111")

            // 上传到服务器，获取正式的图片URL
            uploadImageToServer(res.tempFiles[0])
        }
    })
}

const deleteMainImage = () => {
    formData.value.url = ""
}

const delete4Image = (files, imgIndex) => {
    files.splice(imgIndex, 1)
}

const uploadOptImageToServer = async (file, index) => {
    try {
        // #ifdef H5 || APP-PLUS
        const url = await http.uploadFile("/file/common/upload", null, { folderType: "app" }, file)
        // #endif
        // #ifdef MP-WEIXIN
        const url = await http.uploadFile("/file/common/upload", file.path, { folderType: "app" })
        // #endif
        formData.value.options[index].files[0].url = url
    } catch (error) {
        console.error("上传图片失败：", error)
    }
}

/**
 * 上传选项图片
 * @param {number} index - 选项索引
 */
const uploadOptionImage = (index) => {
    uni.chooseImage({
        count: 1,
        sourceType: ["album", "camera"],
        success: (res) => {
            // 上传到服务器，获取正式的图片URL
            uploadOptImageToServer(res.tempFiles[0], index)
        }
    })
}

/**
 * 删除选项图片
 * @param {number} index - 选项索引
 */
const deleteOptionImage = (index) => {
    formData.value.options[index].image = ""
}

// 评选活动上传多张图片
const upload4Image = (item) => {
    uni.chooseImage({
        sourceType: ["album"],
        success: (res) => {
            if (res.tempFiles.length) {
                res.tempFiles.forEach((file) => {
                    // #ifdef MP-WEIXIN
                    http.uploadFile("/file/common/upload", file.path, { folderType: "app" }, file).then((res) => {
                        item.files.push({
                            url: res
                        })
                    })
                    // #endif
                    // #ifdef H5 || APP-PLUS
                    http.uploadFile("/file/common/upload", null, { folderType: "app" }, file).then((res) => {
                        item.files.push({
                            url: res
                        })
                    })
                    // #endif
                })
            }
        }
    })
}

/**
 * 图文投票开关变化处理
 */
const handleImageVoteChange = (e) => {
    formData.value.setting.hasImage = e.detail.value ? 1 : 0 // 这里e.detail.value是布尔值，根据它来更新hasImage为0或1
    if (!formData.value.setting.hasImage) {
        // 关闭图文投票时，清空所有选项上的所有图片
        formData.value.options.forEach((option) => {
            option.files = [
                {
                    url: ""
                }
            ]
        })
    }
}

const handleSelfSee = (e) => {
    formData.value.setting.selfSee = e.detail.value ? 1 : 0
}

const handleRepeat = (e) => {
    formData.value.setting.repeat = e.detail.value ? 1 : 0
}

/**
 * 新增选项
 */

const addOption = () => {
    if (formData.value.options && formData.value.options.length >= 1000) {
        uni.showToast({
            title: "最多只能添加1000个选项",
            icon: "none"
        })
        return
    }
    formData.value.options.push({
        title: "",
        files: [{ url: "" }]
    })
}

/**
 * 删除选项
 * @param {number} index - 选项索引
 */
const deleteOption = (index) => {
    if (formData.value.options.length <= 2) {
        uni.showToast({
            title: "至少需要保留2个选项",
            icon: "none"
        })
        return
    }
    formData.value.options.splice(index, 1)
}

/**
 * 拖拽开始
 * @param {number} index - 开始拖拽的选项索引
 */
const handleDragStart = (index) => {
    dragStartIndex.value = index
    dragCurrentIndex.value = index
}

/**
 * 拖拽移动
 * @param {Event} e - 触摸事件
 */
const handleDragMove = (e) => {
    // 这里可以实现拖拽时的视觉反馈
    console.log("拖拽移动中...")
}

/**
 * 拖拽结束
 */
const handleDragEnd = () => {
    if (dragStartIndex.value !== dragCurrentIndex.value && dragCurrentIndex.value !== -1) {
        // 执行选项位置交换
        const startItem = formData.value.options[dragStartIndex.value]
        const currentItem = formData.value.options[dragCurrentIndex.value]

        formData.value.options[dragStartIndex.value] = currentItem
        formData.value.options[dragCurrentIndex.value] = startItem
    }

    dragStartIndex.value = -1
    dragCurrentIndex.value = -1
}

// 时间选择器切换
const changeStartTime = (e) => {
    formData.value.startTime = e.detail.value
}

const changeEndTime = (e) => {
    formData.value.endTime = e.detail.value
}

/**
 * 选择参与人员范围
 */
const selectParticipants = () => {
    const typeList = [
        {
            type: "people_dept",
            name: "老师",
            selectLevel: "people_dept" // 选填
        },
        {
            type: "student",
            name: "学生",
            selectLevel: "student" // 必填
        }
    ]
    selectorRef.value.open(typeList)
    //    uni.navigateTo({
    //         url: "/apps/vote/selectMember/index"
    //     })
}

// 确认了选择了哪些可以来投票的人
function confirmUser(ids, selected) {
    console.log(selected)

    // treeType  2是老师 1是学生
    // 如果有则返回id 如果没有返回空数组

    formData.value.teacherIds = selected.some((i) => i.treeType === 2) ? selected.filter((i) => i.treeType === 2).map((i) => i.id) : []

    formData.value.studentIds = selected.some((i) => i.treeType === 1) ? selected.filter((i) => i.treeType === 1).map((i) => i.id) : []

    formData.value.allUserNameStr = selected.map((i) => i.name)?.join("、")

    // 关闭选人弹窗
    selectorRef.value.close()
}

/**
 * 提交投票
 */
const submitVote = () => {
    // 表单验证
    if (!formData.value.title.trim()) {
        uni.showToast({
            title: "请输入投票标题",
            icon: "none"
        })
        return
    }
    let validOptions = []

    if (voteStore.type === 4) {
        validOptions = formData.value.options.filter((option) => option.name)

        formData.value.options.forEach((item) => {
            item.title = item.name
        })
    } else {
        validOptions = formData.value.options.filter((option) => option.title)
    }

    if (validOptions.length < 2) {
        uni.showToast({
            title: "至少需要2个有效选项",
            icon: "none"
        })
        return
    }

    // 遍历 options 中的每个选项
    const processedOptions = formData.value.options.map((option) => {
        // 检查当前选项的 files 数组中是否存在 url 为空字符串的对象
        const hasEmptyUrl = option.files.every((file) => file.url === "")

        // 如果存在空 url，则将 files 设为空数组，否则保持原样
        return {
            ...option,
            files: hasEmptyUrl ? [] : option.files.filter((file) => file.url)
        }
    })

    // 构建提交数据
    const submitData = {
        type: voteStore.type, // 投票类型
        ...formData.value,
        // 如果files的值是[{url: ""}] 后端希望给他 []
        options: processedOptions
    }

    console.log("提交投票数据：", submitData)

    // 这里调用API提交数据
    uni.showLoading({
        title: "创建中..."
    })

    let apiUrl = "/app/vote/create"
    if (submitData.id) {
        apiUrl = "/app/vote/update"
    }

    // API调用
    http.post(apiUrl, submitData)
        .then((res) => {
            uni.hideLoading()
            uni.showToast({
                title: "操作成功",
                icon: "success"
            })

            uni.navigateTo({
                url: "/apps/vote/voteCreate/index"
            })
        })
        .catch((err) => {
            uni.hideLoading()
            uni.showToast({
                title: err.message || "提交失败",
                icon: "none"
            })
        })
}

const clickLeft = () => {
    uni.navigateTo({
        url: "/apps/vote/voteCreate/index"
    })
}
</script>

<style lang="scss" scoped>
// 容器样式
.container {
    background: $uni-bg-color-grey;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

// 滚动内容区域
.scroll-content {
    flex: 1;
    padding-bottom: 180rpx; // 为底部按钮留出空间
}

// 区块标题
.section-title {
    margin: 30rpx 30rpx 10rpx 30rpx;

    font-weight: 400;
    font-size: 24rpx;
    color: #666666;
}

.section_box {
    background: #ffffff;
    padding: 30rpx;
}

.optBox {
    background: #ffffff;
    padding: 30rpx;
}

.optList_item {
    background: #ffffff;
    padding: 30rpx;
    margin-bottom: 20rpx;
    .optIndex {
        font-weight: 400;
        font-size: 26rpx;
        color: #999999;
        padding-bottom: 30rpx;
        border-bottom: 1rpx solid #d8d8d8;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
}

.optList_input {
    border-bottom: 1rpx solid #d8d8d8;
}

.setBox {
    padding-left: 30rpx;
    padding-right: 30rpx;
    background: #ffffff;
}

.label {
    font-weight: 400;
    font-size: 22rpx;
    color: #999999;
    padding-top: 10rpx;
}

.input {
    width: 100%;
    padding-bottom: 30rpx;
    background: #ffffff;

    font-size: 36rpx;
    color: #333333;
}

.textarea {
    padding-top: 30rpx;
    padding-bottom: 60rpx;
    width: 100%;
    min-height: 160rpx;
    background: #ffffff;
    font-weight: 400;
    font-size: 28rpx;
    color: #333333;
}

.nameTextarea {
    width: 100%;
    padding: 30rpx 0;
    background: #ffffff;
    font-weight: 400;
    font-size: 28rpx;
    color: #333333;
    min-height: 40rpx;
}

.upload-area_hide {
    display: none !important;
}
// 上传区域样式
.upload-area {
    width: 180rpx;
    height: 180rpx;
    background: #ffffff;

    border: 2rpx dashed #666666;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    .uploaded-image {
        width: 100%;
        height: 100%;
    }

    .upload-placeholder {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .upload-text {
        font-weight: 400;
        font-size: 24rpx;
        color: #666666;
    }

    .delete-image {
        position: absolute;
        top: -10rpx;
        right: -10rpx;
        width: 36rpx;
        height: 36rpx;
        background: #404040;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

// 开关项样式
.switch-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 30rpx 0;
    border-bottom: 1rpx solid #f0f0f0;

    &:last-child {
        border-bottom: none;
    }
}

.switch-label {
    font-weight: 400;
    font-size: 28rpx;
    color: #333333;
}

.switch-label_top {
    padding-top: 0;
}

// 选项列表样式
.options-list {
    margin: 30rpx 0;
}

.option-item {
    margin-bottom: 40rpx;
}

.option-image-container {
    width: 80rpx;
    height: 80rpx;
    background: #eeeeee;

    overflow: hidden;
    position: relative;

    .option-image {
        width: 100%;
        height: 100%;
    }

    .option-image-placeholder {
        width: 100%;
        height: 100%;
        background: #eeeeee;

        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .image-placeholder-text {
        font-weight: 400;
        font-size: 24rpx;
        color: #bbbbbb;
    }

    .delete-option-image {
        position: absolute;
        top: -8rpx;
        right: -8rpx;
        width: 28rpx;
        height: 28rpx;
        background: #ff4757;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

.option-content {
    display: flex;
    align-items: center;
    gap: 20rpx;
}

.option-delete {
    flex-shrink: 0;
    width: 40rpx;
    height: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}

.option-PK_type {
    flex-shrink: 0;
    width: 64rpx;
    height: 40rpx;

    border-radius: 4rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 400;
    font-size: 24rpx;
}

.option-PK_type-red {
    background: #ffdee0;
    color: #f5222d;
}

.option-PK_type-blue {
    background: #deeaff;
    color: #2e79ff;
}
.option-input {
    flex: 1;
    // height: 88rpx;
    background: #ffffff;

    padding: 0 24rpx;

    font-weight: 400;
    font-size: 30rpx;
    color: #333333;
}

.option-drag {
    flex-shrink: 0;
    width: 40rpx;
    height: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}

// 新增选项按钮
.add-option-btn {
    display: flex;
}

.add-option-btn_addbox {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 92rpx;
    background: #ffffff;
    border-radius: 10rpx;
    border: 2rpx solid var(--primary-color);
    margin-left: 30rpx;
    margin-right: 30rpx;
}

.add-option-text {
    font-weight: 400;
    font-size: 30rpx;
    color: var(--primary-color);
    padding-left: 20rpx;
}

// 设置项样式
.setting-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 30rpx 0;
    border-bottom: 1rpx solid #f0f0f0;

    &:last-child {
        border-bottom: none;
    }
}

.setting-label {
    font-weight: 400;
    font-size: 28rpx;
    color: #333333;
}

.setting-value {
    display: flex;
    align-items: center;
    gap: 16rpx;
}

.time-text {
    font-size: 30rpx;
    color: #999999;

    &.placeholder {
        color: #999999;
    }
}

.personnelScopeNameText {
    display: inline-block;
    width: 300rpx;
    text-align: right;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

// 计数器样式
.counter-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 30rpx 0;
    border-bottom: 1rpx solid #f0f0f0;

    &:last-child {
        border-bottom: none;
    }
}

.counter-label {
    font-weight: 400;
    font-size: 28rpx;
    color: #333333;
}

.counter-controls {
    display: flex;
    align-items: center;
    gap: 12rpx;
}

.counter-btn {
    width: 60rpx;
    height: 60rpx;
    border: 2rpx solid #e4e7ed;
    border-radius: 8rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    &:active {
        background: #f5f7fa;
    }
}

.counter-value {
    font-size: 32rpx;
    color: #303133;
    font-weight: 500;
    min-width: 40rpx;
    text-align: center;
}

// 底部提交按钮
.submit-wrapper {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 30rpx;
    background: #ffffff;
    border-top: 1rpx solid #e4e7ed;
    z-index: 99;
}

.submit-btn {
    width: 100%;
    height: 88rpx;
    background: var(--primary-color);
    border-radius: 12rpx;
    color: #ffffff;
    font-size: 32rpx;
    font-weight: 500;
    border: none;

    &:active {
        background: #00b085;
    }

    &::after {
        border: none;
    }
}

.title_box {
    border-bottom: 1rpx solid #d8d8d8;
}

// 3列布局
.form-item_grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20rpx;
}

:deep(.uni-textarea-placeholder) {
    font-size: 14px;
}

:deep(.uni-textarea-textarea) {
    font-size: 14px;
}
</style>
<style lang="scss">
.container {
    .uni-navbar--fixed {
        z-index: 89 !important;
    }
}
</style>
