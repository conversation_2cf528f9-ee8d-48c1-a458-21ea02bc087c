<template>
    <view class="container">
        <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="投票" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
        <!-- 单选投票卡片 -->
        <view class="vote-card" @click="handleVoteType(1)">
            <view class="card-content">
                <view class="title">单选投票</view>
                <view class="desc">每个参与者只能选择一个选项投票</view>
            </view>
            <uni-icons type="arrowright" size="18" color="#C0C4CC"></uni-icons>
        </view>

        <!-- 多选投票卡片 -->
        <view class="vote-card" @click="handleVoteType(2)">
            <view class="card-content">
                <view class="title">多选投票</view>
                <view class="desc">每个参与者只能选择多个选项投票</view>
            </view>
            <uni-icons type="arrowright" size="18" color="#C0C4CC"></uni-icons>
        </view>

        <!-- 二选一PK投票卡片 -->
        <view class="vote-card" @click="handleVoteType(3)">
            <view class="card-content">
                <view class="title">二选一PK投票</view>
                <view class="desc">快速创建红方、蓝方二选一对比投票</view>
            </view>
            <uni-icons type="arrowright" size="18" color="#C0C4CC"></uni-icons>
        </view>

        <!-- 评选投票活动卡片 -->
        <view class="vote-card" @click="handleVoteType(4)">
            <view class="card-content">
                <view class="title">评选投票活动</view>
                <view class="desc">用于优秀人物评选等场景</view>
            </view>
            <uni-icons type="arrowright" size="18" color="#C0C4CC"></uni-icons>
        </view>
        <view class="footerBox">
            <view class="footer">
                <view class="item" @click="goVote">
                    <view>
                        <uni-icons fontFamily="antiBullying" :size="20">{{ "&#xe615;" }}</uni-icons>
                    </view>
                    <view class="view">投票</view>
                </view>
                <view class="item" @click="goStatistics">
                    <view>
                        <uni-icons fontFamily="antiBullying" :size="20">{{ "&#xe615;" }}</uni-icons>
                    </view>
                    <view class="view">统计</view>
                </view>
                <view class="item active">
                    <view>
                        <uni-icons color="var(--primary-color)" fontFamily="antiBullying" :size="20">{{ "&#xe615;" }}</uni-icons>
                    </view>
                    <view class="view">发起</view>
                </view>
            </view>
        </view>
    </view>
</template>

<script setup>
import { ref } from "vue"

import useVoteStore from "@/store/vote"

import BottomTabBar from "../comp/bottomTabBar.vue"

// 使用 Pinia store 获取表单数据
const voteStore = useVoteStore()

/**
 * 处理投票类型选择
 * @param {string} type - 投票类型 1|2|3|4
 */
const handleVoteType = (type) => {
    voteStore.setVoteType(type)
    voteStore.resetData()
    uni.navigateTo({
        url: "/apps/vote/voteCreate/createForm"
    })
}

const goVote = () => {
    uni.navigateTo({
        url: "/apps/vote/teacher/index"
    })
}

function clickLeft() {
    uni.reLaunch({ url: "/pages/workbench/index" })
}

const goStatistics = () => {
    uni.navigateTo({
        url: "/apps/vote/statistics/index"
    })
}
</script>

<style lang="scss" scoped>
// 容器样式
.container {
    background: $uni-bg-color-grey;
    display: flex;
    flex-direction: column;
    height: calc(100vh - 104rpx); /* 设置页面高度为视窗高度 */
    overflow: hidden; /* 防止整个页面滚动 */
}

// 投票卡片样式
.vote-card {
    height: 194rpx;
    background: #ffffff;
    box-shadow: 0rpx 8rpx 8rpx 0rpx rgba(220, 245, 238, 0.5);
    border-radius: 20rpx;
    margin-top: 30rpx;
    margin-left: 30rpx;
    margin-right: 30rpx;
    padding: 40rpx 30rpx;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;

    // 添加点击效果
    &:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
    }

    // 最后一个卡片不需要下边距
    &:last-child {
        margin-bottom: 0;
    }
}

// 卡片内容区域
.card-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

// 标题样式
.title {
    font-size: 32rpx;
    font-weight: 600;
    color: #303133;
    line-height: 44rpx;
    margin-bottom: 16rpx;
}

// 描述样式
.desc {
    font-size: 28rpx;
    color: #909399;
    line-height: 40rpx;
    opacity: 0.8;
}

// 箭头图标容器
.arrow-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40rpx;
    height: 40rpx;
}

.footerBox {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
}

.footer {
    width: 100%;
    display: flex;
    justify-content: space-evenly;
    height: 120rpx;
    background: $uni-bg-color;
    box-shadow: 0rpx 4rpx 12rpx 4rpx rgba(197, 197, 197, 0.5);
    font-size: 20rpx;
    .item {
        view-align: center;
        display: flex;
        flex-direction: column;
        justify-content: center;
        width: 100rpx;
        text-align: center;
        .view {
            margin-top: 8rpx;
        }
    }
    .active {
        color: var(--primary-color);
    }
}
</style>
