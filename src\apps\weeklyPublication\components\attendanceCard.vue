<template>
    <view
        class="attendance_card card_bg"
        :style="{
            background: `url(${cardBg}) no-repeat`
        }"
    >
        <view class="header">
            <text class="title">{{ info?.title || "学生考勤" }}</text>
            <text class="company">
                <slot name="company"></slot>
            </text>
        </view>
        <view class="list">
            <view class="list_item">
                <view class="item_num">{{ info?.inOutAttendance || 0 }}%</view>
                <view class="item_title">出入校出勤率</view>
            </view>
            <view class="list_item">
                <view class="item_num">{{ info?.classroomAttendance || 0 }}%</view>
                <view class="item_title">课堂出勤率</view>
            </view>
            <view class="list_item">
                <view class="item_num">{{ info?.error || 0 }}</view>
                <view class="item_title">共有异常数据</view>
            </view>
        </view>
    </view>
</template>

<script setup>
import useStore from "@/store"
const { user } = useStore()
const props = defineProps({
    info: {
        type: Object,
        default: () => {}
    }
})
const cardBg = computed(() => {
    return user.identityInfo?.roleCode === "dorm_admin" ? "https://alicdn.1d1j.cn/announcement/20230727/7582f3053f32448798f5a7f424f38f41.png" : "@nginx/workbench/weeklyPublication/statistics_card.png"
})
</script>

<style lang="scss" scoped>
.attendance_card {
    padding: 28rpx 0rpx;
    box-sizing: border-box;
    margin: 0 auto;
    border-radius: 20rpx;
    .header {
        height: 40rpx;
        line-height: 40rpx;
        padding: 0 32rpx;
        .title {
            font-size: 32rpx;
            font-weight: 500;
            color: $uni-text-color-inverse;
        }
        .company {
            font-size: 26rpx;
            font-weight: 400;
            color: #dcf5ec;
            margin-left: 10rpx;
        }
    }
    .list {
        display: flex;
        margin-top: 26rpx;
        justify-content: space-around;
        align-items: center;
        .list_item {
            flex: 1;
            color: #dcf5ec;
            position: relative;
        }
        .list_item::after {
            content: "";
            position: absolute;
            height: 60rpx;
            width: 2rpx;
            background: #eef4fc;
            top: 18rpx;
            right: -1rpx;
            opacity: 0.13;
        }
        .list_item:last-child {
            &::after {
                content: none;
            }
        }
    }
}
.item_num {
    text-align: center;
    font-size: 36rpx;
}
.item_title {
    text-align: center;
    font-size: 24rpx;
}
.card_bg {
    // background: url("@nginx/workbench/weeklyPublication/statistics_card.png") no-repeat;
    background-size: 100%;
    margin: 0 auto;
    width: 690rpx;
    height: 216rpx;
}
</style>
