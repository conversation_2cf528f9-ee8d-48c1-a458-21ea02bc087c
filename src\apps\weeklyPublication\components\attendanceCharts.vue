<template>
    <view class="yd_charts">
        <!-- 条形图 -->
        <qiun-data-charts :canvas2d="true" type="column" :chartData="chartData" :opts="chartsOpts" />
    </view>
</template>

<script setup>
import qiunDataCharts from "@/subModules/components/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue"

const props = defineProps({
    data: {
        type: Object,
        default: () => {}
    }
})

const chartsOpts = {
    legend: {
        show: false
    },
    yAxis: {
        gridType: "dash",
        dashLength: 8
    },
    extra: {
        tooltip: {
            showBox: false
        },
        column: {
            type: "group",
            width: 20
        }
    }
}

const chartData = ref()

const defaultDate = ref({
    categories: ["缺勤", "迟到", "请假", "早退"],
    series: [
        {
            name: "",
            data: [
                { value: 0, color: "#FD4F45" },
                { value: 0, color: "#FC941F" },
                { value: 0, color: "#333333" },
                { value: 0, color: "#1EC1C3" }
            ]
        }
    ]
})

watch(
    () => props.data,
    (val) => {
        setTimeout(() => {
            const chartDataNum = {
                1: val?.absenceCount || 0,
                2: val?.lateCount || 0,
                3: val?.leaveCount || 0,
                4: val?.leaveEarlyCount || 0
            }
            defaultDate.value.series[0].data.forEach((item, index) => {
                item.value = Number(chartDataNum[index + 1])
            })
            chartData.value = defaultDate.value
        }, 500)
    },
    {
        immediate: true,
        deep: true
    }
)
</script>

<style lang="scss" scoped>
.yd_charts {
    background: #ffffff;
    border-radius: 20rpx;
    padding: 30rpx 20rpx;
    .charts_box {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 30rpx;
        .charts_title {
            font-weight: 500;
            font-size: 28rpx;
            color: #1e1e1e;
            line-height: 35rpx;
        }
        .right {
            display: flex;
            align-items: center;
            font-weight: 400;
            font-size: 26rpx;
            color: var(--primary-color);
            line-height: 36rpx;
        }
    }
}
</style>
