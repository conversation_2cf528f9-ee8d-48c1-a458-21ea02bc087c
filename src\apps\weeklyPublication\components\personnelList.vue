<template>
    <view class="list_class">
        <uni-collapse ref="collapse" accordion>
            <view v-if="list && list.length > 0">
                <view v-for="item in list" :key="item.id" class="collapse_item">
                    <view class="total_class">{{ item.total }}次</view>
                    <uni-collapse-item :title="item.name">
                        <view v-for="(it, ix) in item.list" :key="ix" class="item_list">
                            <text class="time_type">
                                <text> {{ `${it.requiredTimeMd} ${it.attendanceName}` }}</text>
                            </text>
                            <text
                                class="status"
                                :style="{
                                    color: statusColor[it.status]
                                }"
                            >
                                {{ statusText[it.status] }}
                            </text>
                        </view>
                    </uni-collapse-item>
                </view>
            </view>
            <yd-empty v-else :isMargin="true" />
        </uni-collapse>
    </view>
</template>

<script setup>
import { computed } from "vue"
import { statusText, statusColor } from "../data"
const props = defineProps({
    list: {
        type: Array,
        default: () => []
    }
})

const list = computed(() => props.list)
</script>

<style lang="scss" scoped>
.list_class {
    .item_list {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: var(--primary-bg-color);
        border-bottom: 1rpx solid $uni-border-color;
        padding: 30rpx;
    }
    .collapse_item {
        position: relative;
        .total_class {
            font-weight: 400;
            font-size: 28rpx;
            color: #333333;
            line-height: 30rpx;
            position: absolute;
            top: 32rpx;
            right: 70rpx;
        }
    }
}
</style>
