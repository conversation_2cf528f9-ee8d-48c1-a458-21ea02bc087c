<template>
    <view class="yd_charts">
        <view class="charts_box">
            <text class="charts_title">学生考勤</text>
            <view class="right" @click="goDetails">
                <text>查看详情</text>
                <uni-icons type="right" size="18" color="var(--primary-color)"></uni-icons>
            </view>
        </view>
        <!-- 条形图 -->
        <qiun-data-charts :canvas2d="true" type="column" :chartData="chartData" :opts="chartsOpts" />
    </view>
</template>

<script setup>
import qiunDataCharts from "@/subModules/components/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue"
const props = defineProps({
    data: {
        type: Object,
        default: () => {}
    }
})

const chartsOpts = {
    yAxis: {
        gridType: "dash",
        dashLength: 8,
        data: [
            {
                unit: "%"
            }
        ]
    },
    extra: {
        tooltip: {
            showBox: true,
            showArrow: false,
            borderColor: "var(--primary-color)",
            borderRadius: 6,
            borderOpacity: 1,
            legendShape: "circle",
            borderWidth: 1,
            bgColor: "#fff",
            bgOpacity: 1,
            fontColor: "#333"
        },
        column: {
            type: "group",
            width: 20
        }
    },
    legend: {
        show: false
    }
}

const chartData = ref()
const defaultData = ref({
    categories: ["出入校", "课堂"],
    series: [
        {
            name: "",
            color: "var(--primary-color)",
            data: [
                { value: 0, color: "var(--primary-color)" },
                { value: 0, color: "var(--primary-color)" }
            ]
        }
    ]
})

watch(
    () => props.data,
    (val) => {
        setTimeout(() => {
            const chartDataNum = {
                1: val.inOutAttendance || 0,
                2: val.classroomAttendance || 0
            }
            defaultData.value.series[0].data.forEach((item, index) => {
                item.value = Number(chartDataNum[index + 1] || 0)
            })
            chartData.value = defaultData.value
        }, 500)
    },
    {
        immediate: true,
        deep: true
    }
)

function goDetails() {
    navigateTo({
        url: `/apps/weeklyPublication/attendanceWeekly/details`
    })
}
</script>

<style lang="scss" scoped>
.yd_charts {
    margin-top: 20rpx;
    background: #ffffff;
    box-shadow: 0rpx 8rpx 8rpx 0rpx rgba(220, 245, 238, 0.5);
    border-radius: 20rpx;
    padding: 30rpx 20rpx;
    .charts_box {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 30rpx;
        .charts_title {
            font-weight: 500;
            font-size: 28rpx;
            color: #1e1e1e;
            line-height: 35rpx;
        }
        .right {
            display: flex;
            align-items: center;
            font-weight: 400;
            font-size: 26rpx;
            color: var(--primary-color);
            line-height: 36rpx;
        }
    }
}
</style>
