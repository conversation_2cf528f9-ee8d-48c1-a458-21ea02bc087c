// 获取最后一层子项的ID
export async function getLastChildId(node) {
    if (node.children && node.children.length > 0) {
        // 如果当前节点有子节点，递归调用此函数直到找到最深层级的节点
        return getLastChildId(node.children[0])
    } else {
        // 返回当前节点的ID
        return node.id
    }
}

export const tabs = ref([
    {
        name: "出入校",
        value: "goOutSchool"
    },
    {
        name: "课程考勤",
        value: "course"
    }
])

export const tabsId = {
    goOutSchool: 0,
    course: 2
}

export const statusList = [
    {
        name: "缺勤",
        value: 1
    },
    {
        name: "迟到",
        value: 2
    },
    {
        name: "请假",
        value: 5
    },
    {
        name: "早退",
        value: 3
    }
]

export const statusText = {
    1: "缺勤",
    2: "迟到",
    5: "请假",
    3: "早退"
}

export const statusColor = {
    1: "#FD4F45", // 缺勤
    2: "#FC941F", // 迟到
    3: "#1EC1C3", // 早退
    5: "#333333" // 请假
}

import useStore from "@/store"
const { user } = useStore()
export const identityType = computed(() => {
    const roleCode = user?.identityInfo?.roleCode
    if (roleCode == "eltern") {
        return "eltern"
    } else if (roleCode == "student") {
        return "student"
    } else {
        return "teacher"
    }
})

export const chartOpts = {
    padding: [0, 40, -20, 5],
    backgroundColor: "#ffffff",
    dataLabel: false, // 关闭数据标签，这会去掉饼状图上的文字和百分比
    legend: {
        show: true,
        position: "right", // 将图例位置设置为右侧
        float: "center", // 在右侧居中显示
        padding: 5,
        itemGap: 10,
        itemWidth: 18,
        itemHeight: 10,
        columnNum: 3, // 每行显示两个图例项
        textStyle: {
            fontSize: 12,
            color: "#666666"
        }
    },
    title: {
        name: "出勤率",
        fontSize: 15,
        color: "#666666",
        offsetY: 30
    },
    subtitle: {
        name: "0%",
        fontSize: 22,
        color: "#333333",
        offsetY: -24
    },
    extra: {
        ring: {
            ringWidth: 48, // 环形图的宽度
            activeOpacity: 0.5,
            activeRadius: 10,
            offsetAngle: 0
        }
    }
}
