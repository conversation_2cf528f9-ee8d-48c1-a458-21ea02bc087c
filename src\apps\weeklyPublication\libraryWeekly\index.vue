<template>
    <div class="weekly_lib">
        <uni-nav-bar statusBar fixed left-icon="left" title="图书馆周报" :border="false" @clickLeft="clickLeft" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
        <!-- 筛选条件 -->
        <select-filter ref="filterRef" @changeDate="changeDate" class="filter" :isContent="true">
            <template #left>
                <view class="ellipsis" @click="selectLibRef.open()"> {{ libraryObj.name || "选择图书馆" }} </view>
            </template>
            <template #content>
                <!-- 选择学生 -->
                <view class="ellipsis" @click="selectStudentRef.open()"> {{ studentObj.studentName || "选择学生" }} </view>
            </template>
        </select-filter>

        <!-- 馆务周报 -->
        <view class="main" v-if="affairsWeekly">
            <text>馆务周报</text>
            <view class="summary">
                <view class="item">
                    <image class="image" mode="scaleToFill" src="@nginx/workbench/weeklyPublication/lib_one.png" />
                    本周到馆人数
                    <text class="num">
                        {{ affairsWeekly.weeklyAttendanceNum }}
                    </text>

                    人
                </view>
                <view class="item">
                    <image class="image" mode="scaleToFill" src="@nginx/workbench/weeklyPublication/lib_two.png" />
                    本周借阅图书数量
                    <text class="num">
                        {{ affairsWeekly.weeklyLoanNum }}
                    </text>
                    册</view
                >
                <view class="item last">
                    <image class="image" mode="scaleToFill" src="@nginx/workbench/weeklyPublication/lib_three.png" />

                    本周馆藏数量
                    <text class="num">
                        {{ affairsWeekly.weeklyItemNum }}
                    </text>
                    册</view
                >
            </view>
        </view>

        <!-- 读者周报 -->
        <view class="main" v-if="borrowerWeekly">
            <text>读者周报</text>
            <view class="summary">
                <view class="item">
                    <image class="image" mode="scaleToFill" src="@nginx/workbench/weeklyPublication/lib_four.png" />
                    本周出入图书馆 <text class="num"> {{ borrowerWeekly.weeklyBorrowerAccessNum }}</text
                    >次
                </view>
                <view class="item">
                    <image class="image" mode="scaleToFill" src="@nginx/workbench/weeklyPublication/lib_five.png" />
                    本周借阅图书数量 <text class="num"> {{ borrowerWeekly.weeklyBorrowerLoanNum }} </text> 册
                </view>
                <view
                    class="item"
                    :class="{
                        last: !borrowerWeekly.bookTypeList.length
                    }"
                >
                    <image class="image" mode="scaleToFill" src="@nginx/workbench/weeklyPublication/lib_six.png" />
                    借阅图书类型
                </view>
                <view class="type_list" v-if="borrowerWeekly.bookTypeList && borrowerWeekly.bookTypeList.length > 0">
                    <view
                        class="type_class"
                        :class="{
                            last_class: index == borrowerWeekly.bookTypeList?.length - 1
                        }"
                        v-for="(item, index) in borrowerWeekly.bookTypeList"
                        :key="index"
                    >
                        <view class="round"></view>
                        <text class="text">
                            {{ item.typeName + "：" + item.typeCount + " 册" }}
                        </text>
                    </view>
                </view>
            </view>
        </view>
        <!-- 图书馆列表 -->
        <yd-select-popup ref="selectLibRef" title="请选择图书馆" :list="libraryList" @closePopup="closeLib" :fieldNames="{ value: 'id', label: 'name' }" :selectId="[libraryObj.id]" />

        <!-- 学生列表 -->
        <yd-select-popup v-if="identityType != 'teacher'" ref="selectStudentRef" title="请选择学生" :list="studentList" @closePopup="closeStudent" :fieldNames="{ value: 'studentId', label: 'studentName' }" :selectId="[studentObj.studentId]" />
    </div>
</template>

<script setup>
import SelectFilter from "../components/selectFilter.vue"
import { identityType } from "../data"
import useStore from "@/store"

const { user } = useStore()
const filterRef = ref(null)
const selectStudentRef = ref(null)
const studentList = ref([])
const studentObj = ref({})

const selectLibRef = ref(null)
const libraryList = ref([])
const libraryObj = ref({})

const queryDate = ref({})

const affairsWeekly = ref(null)
const borrowerWeekly = ref(null)

function getLibWeekly() {
    const param = {
        libraryId: libraryObj.value?.id,
        studentId: studentObj.value?.studentId,
        ...queryDate.value
    }
    http.post("/app/library/queryLibraryWeekly", param).then((res) => {
        affairsWeekly.value = res.data.affairsWeekly
        borrowerWeekly.value = res.data.borrowerWeekly
    })
}

// 选择学生
function closeStudent(val) {
    if (!val && val.studentId == studentObj.value.studentId) return
    studentObj.value = val
    getLibWeekly()
}

// 选择时间
function changeDate(date) {
    queryDate.value = date
    getLibWeekly()
}

// 选择图书馆
function closeLib(val) {
    if (!val && val.id == libraryObj.value.id) return
    libraryObj.value = val
    getLibWeekly()
}

// 获取图书馆
async function getLibList() {
    await http.post("/app/library/list").then((res) => {
        libraryList.value = res.data
        libraryObj.value = res.data[0]
    })
}

onMounted(async () => {
    if (identityType.value !== "teacher") {
        studentList.value = user.studentInfo
        studentObj.value = user.studentInfo[0]
    }
    queryDate.value = await filterRef.value.getDate()
    await getLibList()
    getLibWeekly()
})

const clickLeft = () => {
    uni.navigateBack()
}
</script>

<style lang="scss" scoped>
.weekly_lib {
    background: $uni-bg-color-grey;
    min-height: 100vh;
    .filter {
        :deep(.filter_item) {
            max-width: 200rpx;
        }
    }
    .main {
        padding: 30rpx;
        position: relative;
        .summary {
            min-height: 100rpx;
            background: #ffffff;
            border-radius: 20rpx;
            padding: 34rpx 24rpx;
            margin-top: 20rpx;

            .image {
                width: 36rpx;
                height: 36rpx;
                margin-right: 14rpx;
            }
            .item {
                font-weight: 400;
                font-size: 26rpx;
                color: #1e1e1e;
                line-height: 36rpx;
                display: flex;
                align-items: center;
                margin-bottom: 36rpx;
            }
            .last {
                margin-bottom: 0;
            }
            .num {
                color: var(--primary-color);
            }
        }
        .type_list {
            .type_class {
                align-items: center;
                display: flex;
                margin-bottom: 24rpx;
                margin-left: 52rpx;
                .round {
                    width: 10rpx;
                    height: 10rpx;
                    background: var(--primary-color);
                    margin-right: 10rpx;
                    border-radius: 50%;
                }
                .text {
                    font-size: 26rpx;
                    font-weight: 400;
                    color: #666666;
                }
            }
            .last_class {
                margin-bottom: 0;
            }
        }
    }
}
</style>
