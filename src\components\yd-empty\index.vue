<template>
    <view
        class="no_data"
        :style="{
            margin: isMargin ? '200rpx auto' : '0'
        }"
    >
        <view class="no_data_box">
            <div class="no_data_img" :style="{ width: widthSize + 'rpx', height: heightSize + 'rpx', background: `url(${emptyImg}) no-repeat`, backgroundSize: 'contain' }"></div>
            <view class="no_data_desc" :style="textStyle">{{ text }}</view>
        </view>
        <view class="no_data_handle" v-if="showBtn">
            <slot>
                <button class="btn" :style="btnStyle" @click="emit('ok')">{{ btnText }}</button>
            </slot>
        </view>
    </view>
</template>

<script setup>
import useStore from "@/store"
const { user } = useStore()

const emit = defineEmits(["ok"])
const props = defineProps({
    // 使用了z-pinging的不要设置为true
    emptyImg: {
        type: String,
        default: ""
    },
    isMargin: {
        type: Boolean,
        default: false
    },
    text: {
        type: String,
        default: "暂无数据！"
    },
    btnText: {
        type: String,
        default: "立即刷新"
    },
    widthSize: {
        type: [String, Number],
        default: "360"
    },
    heightSize: {
        type: [String, Number],
        default: "210"
    },
    showBtn: {
        type: Boolean,
        default: false
    },
    btnStyle: {
        type: String,
        default: ""
    },
    textStyle: {
        type: String,
        default: ""
    }
})

const emptyImg = computed(() => {
    if (props.emptyImg) {
        return props.emptyImg
    } else {
        return user.identityInfo?.roleCode === "dorm_admin" ? "@nginx/components/dormEmpty.png" : "@nginx/components/empty.png"
    }
})
</script>

<style lang="scss" scoped>
.no_data {
    padding: 0 40rpx;

    .no_data_img {
        // background: url("@nginx/components/empty.png") no-repeat;

        width: 360rpx;
        height: 210rpx;
        display: block;
        margin: 0 auto;
    }

    .no_data_desc {
        padding-top: 24rpx;
        font-weight: 400;
        font-size: 28rpx;
        color: #8c8c8c;
        line-height: 40rpx;
        text-align: center;
        margin-bottom: 50rpx;
    }

    .no_data_handle {
        text-align: center;

        .btn {
            display: inline-block;
            margin: 40rpx auto 0 auto;
            background: none !important;
            font-weight: 600;
            font-size: 36rpx;
            color: var(--primary-color);

            &::after {
                content: "";
                border: none;
            }
        }
    }
}
</style>
