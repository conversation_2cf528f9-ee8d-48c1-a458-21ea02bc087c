<template>
    <!--uni-popup整体属性参考原生文档，type支持五个弹出方向属性（其他属性慎用）-->
    <uni-popup ref="popupRef" :animation="animation" :type="type" :background-color="popupBgc" :is-mask-click="isMaskcClick" :mask-background-color="maskColor" :border-radius="borderRadius" @maskClick="maskClick">
        <!--titleflag控制标题显隐-->
        <!--titleColor控制标题颜色-->
        <!--title控制标题文本内容-->
        <view v-if="titleflag" class="confirm_popup_title" :style="{ color: titleColor }">
            <view>{{ title }}</view>
        </view>
        <!--popupWidth控制弹窗宽度，内容宽度即为主体宽度-->
        <!--content插槽控制内容-->
        <view class="confirm_popup_content" :style="{ color: contentColor, width: popupWidth + '!important' }">
            <slot></slot>
        </view>
        <!--btnsflag控制底部按钮区域显隐-->
        <!--canceFlag控制取消按钮显隐-->
        <!--cancelColor控制取消按钮颜色-->
        <!--cancelText控制取消按钮文本-->
        <!--confirmColor控制确认按钮颜色-->
        <!--confirmText控制确认按钮文本-->
        <view class="btns" v-if="btnsflag">
            <view v-if="canceFlag" class="btns_cancel" @click="cancel" :style="{ color: cancelColor }">{{ cancelText }} </view>
            <view class="btns_confirm" @click="confirm" :style="{ color: confirmColor }">{{ confirmText }} </view>
        </view>
    </uni-popup>
</template>
<script setup>
// 弹窗ref
const popupRef = ref(false)
// 向父传递取消、确认、点击遮罩
const emit = defineEmits(["close", "confirm", "maskClick"])
defineProps({
    // 弹窗宽度
    popupWidth: {
        type: String,
        default: "80vw"
    },
    // 控制弹出方式
    type: {
        type: String,
        default: "center"
    },
    // 点击遮罩是否关闭弹窗
    isMaskcClick: {
        type: Boolean,
        default: false
    },
    // 遮罩层颜色
    maskColor: {
        type: String,
        default: "rgba(0,0,0,0.4)"
    },
    // 是否开启动画
    animation: {
        type: Boolean,
        default: false
    },
    // 弹窗背景色
    popupBgc: {
        type: String,
        default: "#ffffff"
    },
    // 圆角
    borderRadius: {
        type: String,
        default: "20rpx"
    },
    // 标题显隐
    titleflag: {
        type: Boolean,
        default: true
    },
    // 标题文本
    title: {
        type: String,
        default: "提示"
    },
    // 标题文本颜色
    titleColor: {
        type: String,
        default: "black"
    },
    // 自定义内容文本颜色
    contentColor: {
        type: String,
        default: "#333"
    },
    // 按钮区域显隐
    btnsflag: {
        type: Boolean,
        default: true
    },
    // 取消按钮文本
    cancelText: {
        type: String,
        default: "取消"
    },
    // 确认按钮文本
    confirmText: {
        type: String,
        default: "确认"
    },
    // 取消按钮文本颜色
    cancelColor: {
        type: String,
        default: "#C5C5C5"
    },
    // 确认按钮文本颜色
    confirmColor: {
        type: String,
        default: "var(--primary-color)"
    },
    // 取消按钮显隐
    canceFlag: {
        type: Boolean,
        default: true
    }
})

// 弹出
function open() {
    popupRef.value.open()
}

// 取消
function cancel() {
    popupRef.value.close()
    emit("close")
}

// 确认
function confirm() {
    popupRef.value.close()
    emit("confirm")
}

// 点击遮罩
function maskClick() {
    emit("maskClick")
}

defineExpose({
    open,
    popup: popupRef
})
</script>
<!--解除样式隔离-->
<script>
export default {
    options: {
        styleIsolation: "shared"
    }
}
</script>
<style lang="scss" scoped>
:deep(.uni-popup__wrapper) {
    font-size: 32rpx;
    text-align: center;
    .confirm_popup_title {
        height: 100rpx;
        line-height: 100rpx;
        font-weight: 600;
    }
    .confirm_popup_content {
        padding: 0 40rpx 40rpx 40rpx;
        font-size: 34rpx;
        color: $uni-text-color;
    }
    .btns {
        display: flex;
        align-items: center;
        border-top: 1px solid $uni-border-color;
        .btns_cancel,
        .btns_confirm {
            flex: 1;
            height: 100rpx;
            line-height: 100rpx;
        }
        .btns_cancel {
            border-right: 1px solid $uni-border-color;
        }
    }
}
</style>
