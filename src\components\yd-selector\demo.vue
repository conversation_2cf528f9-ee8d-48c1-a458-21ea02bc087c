<template>
    <view>
        <view class="btn" @click="openSelector">demo</view>
        <yd-selector ref="selectorRef" @confirm="confirmFn" />
    </view>
</template>

<script setup>
const selectorRef = ref(null)
// selectLevel
// 学籍班级层级： school学校  campus校区  academics专业  grade年级  classes班级  student学生
// 部门层级： dept部门 people_dept部门下的人
// 角色层级： role角色 people_role角色下的人
// 外部层级： external外部组  people_external外部组员
function openSelector() {
    // type: 选择的类型————classes班级，student学生，parent家长，parent部门，people_dept部门下的人，role角色，people_role角色下的人，external外部组，people_external外部组成员
    // name: 选择的类型名称，tab切换使用,
    // selectLevel: 选择的层级 // 如果是选班级学生的必须填，因为其他的层级和type相同
    const typeList = [
        {
            type: "dept",
            name: "部门",
            selectLevel: "dept" // 选填
        },
        // {
        //     type: "people_dept",
        //     name: "老师",
        //     selectLevel: "people_dept" // 选填
        // },
        // {
        //     type: "role",
        //     name: "角色",
        //     selectLevel: "role" // 选填
        // },
        // {
        //     type: "people_role",
        //     name: "老师",
        //     selectLevel: "people_role" // 选填
        // },
        // {
        //     type: "external",
        //     name: "外部组", // 选组
        //     selectLevel: "external" // 选填
        // },
        // {
        //     type: "people_external",
        //     name: "外部人员", // 选组成员
        //     selectLevel: "people_external" // 选填
        // },
        // {
        //     type: "student",
        //     name: "学生",
        //     selectLevel: "student" // 必填
        // },
        // {
        //     type: "parent",
        //     name: "家长",
        //     selectLevel: "parent" // 必填
        // },
        {
            type: "classes",
            name: "班级",
            selectLevel: "classes" // 必填
        }
    ]
    selectorRef.value.open(typeList)
}

function confirmFn(ids, selected) {
    console.log(ids, selected, "选择的id和id对象")
}
</script>

<style lang="scss" scoped>
.btn {
    width: 100rpx;
    height: 60rpx;
    background: red;
    color: $uni-text-color-inverse;
    margin: auto;
}
</style>
