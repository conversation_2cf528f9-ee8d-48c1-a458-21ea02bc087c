### 使用方法
* 注意
+ 同一个页面不同的输入框需要设置不同的canvasId和canvasIds，否则在同一个页面会出现冲突
```
<template>
	<view class="content">
		<signInput ref="sign" canvasId="twoDrowCanvas" canvasIds="twoRotateCanvas" :header="header" :action="action"
			@signToUrl="signToUrl">
		</signInput>
	</view>
</template>
```
```
<script>
	import signInput from "@/components/am-sign-input/am-sign-input.vue"
	export default {
		components: {
			signInput
		},
		data() {
			return {
				action: "", //上传服务器的地址
				header: {}, //图片上传携带头部信息
			}
		},
		methods: {
			/**
			 * @param {Object} e
			 * 签名完成回调
			 */
			signToUrl(e) {
				if (e.error_code && e.error_code === '201') {
					uni.showToast({
						title: e.msg,
						icon: 'none'
					})
					return
				}
			},
		}
	}
</script>
```
```
<style lang="scss">
	.content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}
</style>
```

#### 实际效果演示H5
- 打开演示后，按F12调整到手机调试模式查看效果
[实际效果演示](https://static-mp-2766f90e-0e50-4c71-87fb-0ab51aedcf85.next.bspapp.com/signInput/#/)

### 参数说明Props

参数|类型|说明|必传
---|---|---|---
action|String|生成图片后上传的接口地址|true
canvasId|String|canvasId|true
canvasIds|String|canvasIds与上一个id不可重复|true
header|Object|文件上传携带的头部属性|true
outSignWidth|Number|输出图片文件大小-宽度|false
outSignHeight|Number|输出图片文件大小-高度|false
minSpeed|Number|画笔最小速度|false
minWidth|Number|线条最小粗度|false
maxWidth|Number|线条最大粗度|false
openSmooth|Boolean|开启平滑线条（笔锋）|false
maxHistoryLength|Number|历史最大长度(用于撤销的步数)|false
maxWidthDiffRate|Number|最大差异率|false
undoScan|Number|撤销重新渲染偏移缩放校准|false
bgColor|String|背景色如#ffffff 不传则为透明|false

### 相关同源插件
- 以页面形式展现
- [电子签名组件](https://ext.dcloud.net.cn/plugin?id=5768)

### 相关致谢
- 插件参考 [大佬的npm库](https://github.com/linjc/smooth-signature)