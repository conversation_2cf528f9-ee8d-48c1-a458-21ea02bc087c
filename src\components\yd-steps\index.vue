<template>
    <div class="yd_steps" :class="locatedObj[located]">
        <div class="step_list">
            <div class="step_item" @click="clickStep(setpIndex, step)" v-for="(step, setpIndex) in list" :key="setpIndex">
                <div
                    :style="{
                        height: `${lineHeight}rpx`
                    }"
                    :class="{
                        active_line: setpIndex <= active,
                        inactive_line: setpIndex >= active,
                        hide_line: setpIndex == 0
                    }"
                    class="line"
                ></div>
                <div class="step_icon_box">
                    <slot name="icon" :setp="{ setpIndex, step }">
                        <uni-icons v-if="icon" :type="icon" :size="iconSize" :color="setpIndex <= active ? activeColor : inactiveColor"></uni-icons>
                    </slot>
                </div>
                <div
                    class="line"
                    :style="{
                        height: `${lineHeight}rpx`
                    }"
                    :class="{
                        active_line: setpIndex <= active,
                        inactive_line: setpIndex >= active,
                        hide_line: setpIndex == list.length - 1
                    }"
                ></div>
            </div>
        </div>
        <div class="item_list">
            <div
                @click="clickStep(index, item)"
                class="text_item"
                :class="{
                    active_text: active == index,
                    inactive_text: active != index
                }"
                v-for="(item, index) in list"
                :key="index"
            >
                {{ item.title }}
            </div>
        </div>
    </div>
    <!-- UI设计已经进行过的文本不为选中色 -->
</template>

<script setup>
const emit = defineEmits(["clickStep"])
const props = defineProps({
    // 步骤条列表
    list: {
        type: Array,
        default: () => []
    },
    // 文本位于步骤条的顶部或底部 'top' | 'bottom'
    located: {
        type: String,
        default: "bottom"
    },
    // 进行中的值（使用的下标）
    active: {
        type: Number,
        default: 0
    },
    // 未进行/未选中的顔色
    inactiveColor: {
        type: String,
        default: "#999999"
    },
    // 进行中/已选中的顔色
    activeColor: {
        type: String,
        default: "var(--primary-color)"
    },
    // 进度的线高度（粗细）默认4rpx
    lineHeight: {
        type: Number,
        default: 4
    },
    // uniapp中的图标库
    icon: {
        type: String,
        default: "checkbox-filled"
    },
    // 图标大小
    iconSize: {
        type: Number,
        default: 26
    }
})

// 文案位置枚举
const locatedObj = {
    top: "text-top",
    bottom: "text-bottom"
}

const clickStep = (index, item) => {
    emit("clickStep", index, item)
}
</script>

<style lang="scss" scoped>
.yd_steps {
    display: flex;
    .step_list {
        display: flex;
        .step_item {
            flex: 1;
            display: flex;
            align-items: center;
            .line {
                flex: 1;
                background: v-bind("props.activeColor");
            }
            .inactive_line {
                background: v-bind("props.inactiveColor");
            }
            .active_line {
                background: v-bind("props.activeColor");
            }
            .hide_line {
                opacity: 0;
            }
            .step_icon_box {
            }
        }
    }
    .item_list {
        display: flex;
        .text_item {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 400;
            font-size: 28rpx;
            line-height: 40rpx;
        }
        .inactive_text {
            color: v-bind("props.inactiveColor");
        }
        .active_text {
            color: v-bind("props.activeColor");
        }
    }
}
.text-top {
    flex-direction: column-reverse;
    .step_list {
        margin-top: 18rpx;
    }
}
.text-bottom {
    flex-direction: column;
    .step_list {
        margin-bottom: 18rpx;
    }
}
</style>
