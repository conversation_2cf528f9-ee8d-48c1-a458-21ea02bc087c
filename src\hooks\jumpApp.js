import useStore from "@/store"
let store = null
export default (app) => {
    if (!store) {
        store = useStore()
    }
    /**用户角色 */
    const roleCode = computed(() => store.user?.identityInfo?.roleCode)
    const { code, routePath } = app
    const urlObj = {
        visitorSystem: `/apps/${routePath}/visitorRecord`, // 访客系统
        schoolAssignment: `/apps/${routePath}/${roleCode.value == "eltern" ? "parent" : "teacher"}/index`, // 作业
        attendance: `/apps/${routePath}/${roleCode.value == "teacher" ? "classTeacher" : "lecturer"}/index`,
        Intelligence: `/apps/intelligence/index`, // 智能体
        vote: `/apps/${routePath}/${roleCode.value === "eltern" ? "eltern" : "teacher"}/index`, // 投票老师和家长分开去不同的地方
        allotRoom: "/apps/dormManage/allotRoom/index", // 宿舍-智能分寝
        dormTransfer: "/apps/dormManage/dormTransfer/index", // 宿舍-寝室调动
        dormRanking: "/apps/dormManage/dormRanking/ranking", // 宿舍-宿舍德育
        dormAttendance: "/apps/dormManage/dormAttendance/index", // 宿舍-宿舍考勤
        dormTraffic: "/apps/dormManage/dormTraffic/index", // 宿舍-宿舍通行
        todo: "/package/chat/todo/index"
    }
    // 点餐比较特殊
    if (code == "canteenMachine") {
        navigateTo({
            url: urlObj[code] || `/apps/${routePath}/index`,
            query: {
                routePath
            }
        })

        return
    }
     // 公文管理
    if (code == "officialDocs") {
        navigateTo({
            url: urlObj[code] || `/apps/${routePath}/home/<USER>
            query: {
                routePath
            }
        })

        return
    }
    navigateTo({
        url: urlObj[code] || `/apps/${routePath}/index`,
        query: {
            code,
            routePath
        }
    })
}
