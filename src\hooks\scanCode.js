import wx from "weixin-js-sdk"
import navigateTo from "@/utils/navigateTo.js"
import useStore from "@/store"
import http from "@/utils/http.js"
// TODO: 目前已知扫码有选座和同行(其余后面再补充)
const typeUrl = {
    random: "", // 通行路径待确认
    seat: "" // 座位路径待确认
}

// 全局store
let store = null

export default function () {
    if (!store) {
        store = useStore()
    }
    // #ifdef H5
    // TODO: 域名待确认
    http.post("/app/wx/signature/get", {
        url: window.location.href.split("#")[0],
        schoolId: store.user.userInfo.schoolId
    }).then((res) => {
        wx.config({
            debug: false,
            appId: res.data.appId ?? res.data.appid, // 必填，公众号的唯一标识
            timestamp: res.data.timestamp, // 必填，生成签名的时间戳
            nonceStr: res.data.nonceStr ?? res.data.noncestr, // 必填，生成签名的随机串
            jsApiList: ["scanQRCode"], // 必填，需要使用的JS接口列表
            signature: res.data.signature
        })
        wx.ready(function () {
            wx.scanQRCode({
                onlyFromCamera: true,
                success: function (res) {
                    console.log("我打开扫码了！")
                    console.log(res.resultStr, "res.resultStr;")
                },
                fail: function (res) {
                    console.log("扫描失败!", res.errMsg)
                }
            })
        })
    })
    // #endif

    // #ifndef H5
    uni.scanCode({
        // onlyFromCamera: true,
        success: function (res) {
            // TODO: 权限或者其它情况待完善
            const [key, code] = res.result
            if (!typeUrl[key]) return
            uni.showToast({
                title: "跳转指定页面待完善",
                icon: "none"
            })
            // navigateTo({
            //     url: typeUrl[key],
            //     query: {
            //         key: code
            //     }
            // })
        },
        fail: (e) => {
            console.log("error:", e)
        }
    })
    // #endif
}
