{
    "name": "一加壹",
    "appid": "__UNI__EFB7D74",
    "description": "",
    "versionName": "1.0.0",
    "versionCode": "100",
    "transformPx": false,
    /* 5+App特有相关 */
    "app-plus": {
        "usingComponents": true,
        "nvueStyleCompiler": "uni-app",
        "compilerVersion": 3,
        "splashscreen": {
            "alwaysShowBeforeRender": true,
            "waiting": true,
            "autoclose": true,
            "delay": 0
        },
        /* 模块配置 */
        "modules": {
            "Barcode": {}
        },
        /* 应用发布信息 */
        "distribute": {
            /* android打包配置 */
            "android": {
                "permissions": [
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ]
            },
            /* ios打包配置 */
            "ios": {
                "dSYMs": false
            },
            /* SDK配置 */
            "sdkConfigs": {}
        }
    },
    /* 快应用特有相关 */
    "quickapp": {},
    /* 小程序特有相关 */
    "mp-weixin": {
        "appid": "wx9cbdfa3f4f88cf30",
        "setting": {
            "urlCheck": false,
            "minified": true,
            "postcss": true
        },
        "plugins": {
            "captcha": {
                "version": "1.0.4",
                "provider": "wx1fe8d9a3cb067a75"
            }
        },
        "optimization": {
            "subPackages": true
        },
        "lazyCodeLoading": "requiredComponents",
        "usingComponents": true,
        "permission": {}
    },
    "mp-alipay": {
        "usingComponents": true
    },
    "mp-baidu": {
        "usingComponents": true
    },
    "mp-toutiao": {
        "usingComponents": true
    },
    "uniStatistics": {
        "enable": false
    },
    "vueVersion": "3",
    "locale": "zh-Hans",
    "fallbackLocale": "zh-Hans",
    "h5": {
        "title": "一加壹",
        "sdkConfigs": {
            "maps": {
                "amap": {
                    "key": "41a32b85bc37cf2fe1b0ed049874f5ce",
                    "securityJsCode": "https://cloud.yyide.com/_AMapService/api",
                    "serviceHost": "http://*************:5173/_AMapService/api"
                }
            }
        }
    }
}
