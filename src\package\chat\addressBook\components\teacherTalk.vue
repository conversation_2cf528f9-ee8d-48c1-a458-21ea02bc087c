<template>
    <div>
        <div v-if="isNoData">
            <div class="address_head">
                <div class="address_school">
                    <image mode="aspectFill" class="img" :src="state.data.schoolBadgeUrl || '@nginx/chat/uploadImg.png'" alt="" />
                    <span>{{ state.data.schoolName }}</span>
                </div>
                <div class="bread" v-if="state.bread && state.bread.length > 0">
                    <div v-for="(item, key) in state.bread" :key="key" @click="breadClick(item, key)" class="bread_item">
                        <span>{{ item.name }}</span>
                        <uni-icons type="right" :color="key == state.bread.length - 1 ? '#999999' : 'var(--primary-color)'" size="16"></uni-icons>
                    </div>
                </div>
            </div>
            <!-- 部门 -->
            <div v-if="isDeptVOList">
                <uni-list :border="false" v-for="item in state.data.deptVOList" :key="item.id">
                    <!-- 部门 -->
                    <div v-if="item.children">
                        <uni-list-item style="border-bottom: 1rpx solid #ebedf0; flex: 1" :border="false" v-for="cItem in item.children" :key="cItem.id" :title="cItem.name" showArrow clickable @click="handleDept(item, cItem)"></uni-list-item>
                    </div>
                    <!-- 人员 -->
                    <div v-if="item.employeeAddBookDTOList">
                        <uni-list-item style="border-bottom: 1rpx solid #ebedf0; flex: 1" :border="false" v-for="eItem in item.employeeAddBookDTOList" :key="eItem.id" clickable>
                            <template #body>
                                <div class="personnel" @click="personnelInfo(eItem, 'dept')">
                                    <image mode="aspectFill" class="img" :src="eItem.avatar || '@nginx/chat/identity_teacher.png'" alt="" />
                                    <span>{{ eItem.name }}</span>
                                </div>
                            </template>
                        </uni-list-item>
                    </div>
                </uni-list>
            </div>
            <!-- 班级 -->
            <div style="margin-top: 20px" v-if="isClassList">
                <uni-list :border="false" v-for="item in state.data.classAddBookDTOList" :key="item.id">
                    <div class="class_student" v-if="item.elternAddBookDTOList" @click="personnelInfo(item, 'class')">
                        <image mode="aspectFill" class="image" :src="item.avatar || '@nginx/chat/identity_teacher.png'" alt="" />
                        <uni-list-item :border="false" :title="item.name">
                            <template #body>
                                <view class="body_box">
                                    {{ item.name }}
                                    <div class="label" v-for="cItem in item.elternAddBookDTOList" :key="cItem.id">
                                        <span> {{ cItem.name }}</span>
                                        <div class="tip">
                                            {{ relationsText[cItem.relations] }}
                                        </div>
                                    </div>
                                </view>
                            </template>
                        </uni-list-item>
                    </div>
                    <uni-list-item v-else @click="handleClass(item)" :title="item.name" showArrow clickable />
                </uni-list>
            </div>
        </div>
        <!-- 无数据 -->
        <yd-empty text="暂无数据" :isMargin="true" v-else />
    </div>
</template>

<script setup>
const emit = defineEmits(["personnel"])
const props = defineProps({
    data: {
        type: Object,
        default: () => {}
    }
})
const state = reactive({
    data: {},
    isShowType: null,
    bread: []
})

const relationsText = {
    0: "",
    1: "父亲",
    2: "母亲",
    3: "爷爷",
    4: "奶奶",
    5: "外公",
    6: "外婆",
    7: "其它"
}

watch(
    () => props.data,
    (val) => {
        state.data = val
        state.bread.push({ name: val.schoolName, id: null, ...val })
    }
)

function breadClick(item, index) {
    if (index == 0) {
        state.data.deptVOList = state.isShowType == "dept" ? item.deptVOList : props.data.deptVOList
        state.data.classAddBookDTOList = state.isShowType == "dept" ? props.data.classAddBookDTOList : item.classAddBookDTOList
        state.bread = [item]
        state.isShowType = null
    } else {
        if (state.isShowType == "dept") {
            state.data.deptVOList = [item]
        } else {
            state.data.classAddBookDTOList = item.studentList
        }
        state.bread = state.bread.slice(0, index + 1)
    }
}

function handleDept(item, cItem) {
    state.isShowType = "dept"
    state.data.deptVOList = item.children
    state.bread = [...state.bread, cItem]
}

function handleClass(cItem) {
    state.isShowType = "class"
    state.data.classAddBookDTOList = cItem.studentList
    state.bread = [...state.bread, cItem]
}

// 人员信息
function personnelInfo(eItem, type) {
    emit("personnel", { ...eItem, type })
}

const isDeptVOList = computed(() => {
    return state.isShowType != "class" && state.data.deptVOList && state.data.deptVOList.length > 0
})

const isClassList = computed(() => {
    return state.isShowType != "dept" && state.data.classAddBookDTOList && state.data.classAddBookDTOList.length > 0
})

const isNoData = computed(() => {
    return (state.data.deptVOList && state.data.deptVOList.length > 0) || (state.data.classAddBookDTOList && state.data.classAddBookDTOList.length > 0)
})
</script>

<style lang="scss" scoped>
:deep(.uni-list-item) {
    border-bottom: 1rpx solid #ebedf0;
}
.address_head {
    min-height: 88rpx;
    margin: 20rpx 0rpx;
    display: flex;
    flex-direction: column;
}
.personnel {
    display: flex;
    align-items: center;
    font-weight: 400;
    font-size: 28rpx;
    color: #333333;
    line-height: 40rpx;
    .img {
        min-width: 50rpx;
        width: 50rpx;
        height: 50rpx;
        border-radius: 50%;
        padding-right: 10rpx;
    }
}
.address_school {
    min-height: 88rpx;
    background: $uni-bg-color;
    padding: 0rpx 30rpx;
    display: flex;
    align-items: center;
    font-weight: 600;
    font-size: 30rpx;
    color: #333333;
    line-height: 42rpx;
    .img {
        width: 64rpx;
        height: 64rpx;
        padding-right: 20rpx;
    }
}
.bread::-webkit-scrollbar {
    display: none;
}
.bread {
    padding: 0rpx 40rpx;
    background: $uni-bg-color;
    display: flex;
    font-weight: 500;
    font-size: 28rpx;
    color: var(--primary-color);
    align-items: center;
    margin-bottom: 20rpx;
    max-width: 100%;
    overflow: auto;
    padding-bottom: 20rpx;

    .bread_item {
        white-space: nowrap;
    }
    :last-child {
        color: #999999;
    }
}
.class_student {
    display: flex;
    .body_box {
        width: 100%;
        font-size: 28rpx;
        color: #3b4144;
    }
    :deep(.uni-list-item) {
        flex: 1;
    }
    .image {
        width: 50rpx;
        height: 50rpx;
        border-radius: 50%;
        margin: 20rpx 0rpx 20rpx 20rpx;
    }
    .label {
        margin-top: 10rpx;
        border-top: 1px solid #f3f4f5;
        padding-top: 16rpx;
        display: flex;
        align-items: center;
        .tip {
            min-width: 20rpx;
            background: var(--primary-bg-color);
            color: var(--primary-color);
            padding: 6rpx;
            margin-left: 40rpx;
        }
    }
}
</style>
