<template>
    <div class="address_book_page">
        <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="通讯录" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
        <view class="search_input">
            <input type="text" class="query_input_text" v-model="searchValue" placeholder="请输入搜索内容" @input="onSearch" />
            <!-- <uni-easyinput primaryColor="var(--primary-color)" prefixIcon="search" v-model="searchValue" placeholder="请输入搜索内容" @input="onSearch"></uni-easyinput> -->
        </view>
        <div v-if="isShowSearch">
            <search-talk @personnel="gotoPersonnel" v-show="searchValue" :data="searchData" />
        </div>
        <div v-else>
            <parent-talk @personnel="gotoPersonnel" v-show="type == 1" :data="parentTree" />
            <teacher-talk @personnel="gotoPersonnel" v-show="type == 2" :data="teacherTree" />
        </div>
    </div>
</template>

<script setup>
import SearchTalk from "./components/searchTalk.vue"
import TeacherTalk from "./components/teacherTalk.vue"
import ParentTalk from "./components/parentTalk.vue"
import useStore from "@/store"

const { user } = useStore()
const searchValue = ref("")
const teacherTree = ref({})
const parentTree = ref({})
const searchData = ref({})
const isShowSearch = ref(false)
const type = computed(() => {
    const roleCode = user?.identityInfo?.roleCode
    console.log(user?.identityInfo?.roleCode)

    if (roleCode == "eltern") {
        return 1
    } else if (roleCode == "student") {
        return 1
    } else {
        return 2
    }
})

function clickLeft() {
    if (isShowSearch.value) {
        searchValue.value = ""
        isShowSearch.value = false
        getData()
    } else {
        routerBack()
    }
}

function gotoPersonnel(item) {
    console.log(item, "item")

    navigateTo({
        url: "/package/chat/addressBook/personnelInfo",
        query: { ...item, elternAddBookDTOList: JSON.stringify(item.elternAddBookDTOList) }
    })
}

function getTeacherData() {
    http.get("/app/addressBookApp/getEmployeeAddressBook").then((res) => {
        teacherTree.value = res.data
    })
}

function getParentData() {
    http.get("/app/addressBookApp/getElternAddressBook").then((res) => {
        parentTree.value = res.data
    })
}

function getData() {
    if (type.value == 2) {
        getTeacherData()
    } else {
        getParentData()
    }
}
const onSearch = (val) => {
    if (val) {
        isShowSearch.value = true
        const searchObj = {
            name: val.detail.value || "",
            type: type.value
        }
        http.post("/app/addressBookApp/searchAddressBook", searchObj).then((res) => {
            searchData.value = res.data
        })
    } else {
        isShowSearch.value = false
        getData()
    }
}
onMounted(() => {
    console.log(type.value, "type")
    getData()
})
</script>

<style lang="scss" scoped>
.address_book_page {
    background: $uni-bg-color-grey;
    min-height: 100vh;
    .search_input {
        background: $uni-bg-color;
        padding: 20rpx 30rpx;
        .query_input_text {
            padding: 10rpx 20rpx;
            border-radius: 30rpx;
            font-size: 30rpx;
            background: $uni-bg-color-grey;
        }
    }
}
</style>
