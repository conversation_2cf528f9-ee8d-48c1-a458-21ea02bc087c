<template>
    <div class="todo_page">
        <z-paging ref="paging" @query="queryList" v-model="todoList">
            <template #top>
                <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="routerBack" title="待办" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
                <!-- tabs切换 -->
                <uv-tabs lineWidth="20" lineColor="var(--primary-color)" :current="tabsCurrent" :scrollable="false" :activeStyle="{ color: 'var(--primary-color)' }" :inactiveStyle="{ color: '#999999' }" :customStyle="{ background: '#fff' }" :list="tabsList" @click="tabsClick"></uv-tabs>
            </template>
            <view class="todo_list">
                <view class="todo_item" v-for="item in todoList" :key="item.id" @click="gotoDetail(item)">
                    <view class="time" v-if="item.startTime">{{ item.startTime }}</view>
                    <view class="box">
                        <view class="title_box">
                            <text v-if="item.background" class="type_logo iconfont" :style="{ background: item.background, borderRadius: '50%' }" :class="item.logo"></text>
                            <text class="type_logo" v-else :style="{ background: `url(${item.logo})`, backgroundSize: 'cover' }"></text>
                            <text class="title">{{ item.title }}</text>
                        </view>
                        <view class="content_box">
                            <div class="subtitle">{{ item.subtitle }}</div>
                            <div class="content" v-for="[key, value] of item?.jsonList" :key="key">
                                <text>{{ value }}</text>
                            </div>
                        </view>
                    </view>
                </view>
            </view>
            <template #empty>
                <yd-empty text="暂无数据" />
            </template>
        </z-paging>
    </div>
</template>

<script setup>
const paging = ref(null)
const tabsList = ref([
    { name: "全部", id: 0 },
    { name: "待办", id: 1 },
    { name: "已办", id: 2 },
    { name: "逾期", id: 3 }
])
const todoList = ref([])
const tabsCurrent = ref(0)
// tabs切换点击
function tabsClick(item) {
    tabsCurrent.value = item.id
    paging.value.reload()
}
// 调用List数据
function queryList(pageNo, pageSize) {
    const status = tabsCurrent.value == 0 ? null : tabsCurrent.value
    http.post("/app/ruTask/page", { pageNo, pageSize, status }).then(({ data }) => {
        const todolist = data?.list?.map((item) => {
            return {
                ...item,
                jsonList: item.content ? Object.entries(JSON.parse(item.content)) : []
            }
        })
        paging.value.complete(todolist)
    })
}

function gotoDetail(item) {
    const { taskType, callId, userType, status, id, schoolId } = item
    // 老版本审批待办详情
    if (["libCirculation", "libReservation"].includes(taskType)) {
        navigateTo({
            url: "/apps/library/index",
            query: { taskType }
        })
    } else if (taskType == "oaApproveOld") {
        // return
        navigateTo({
            url: "/package/chat/todo/oldOaApproval",
            query: item
        })
    } else if (taskType == "visitorSystem") {
        // 访客
        navigateTo({
            url: "/apps/visitorSystem/details",
            query: {
                isApproval: status == 1 ? true : false,
                callId,
                routeName: "toDo",
                isTeacher: true,
                schoolId
            }
        })
    } else if (taskType == "patrol") {
        // 巡查巡课
        const url = status == 2 ? "/apps/patrol/result" : "/apps/patrol/submit"
        navigateTo({
            url,
            query: {
                id: callId,
                app: 1
            }
        })
    } else {
        // oA审批 正常跳转
        navigateTo({
            url: "/package/chat/todo/oaApproval",
            query: {
                id: callId,
                status: status == 2 ? 1 : status
            }
        })
    }
}
</script>

<style lang="scss" scoped>
.todo_page {
    background: $uni-bg-color-grey;
    min-height: 100vh;
    .todo_list {
        padding: 30rpx;
        .todo_item {
            margin-bottom: 30rpx;
            display: flex;
            align-items: center;
            flex-direction: column;
            justify-content: center;
            .time {
                text-align: center;
                font-weight: 400;
                font-size: 24rpx;
                color: #b3b3b3;
                line-height: 34rpx;
                margin-bottom: 10rpx;
            }
            .box {
                width: calc(100% - 60rpx);
                min-height: 100rpx;
                background: $uni-bg-color;
                border-radius: 20rpx;
                padding: 40rpx 30rpx;
                .title_box {
                    display: flex;
                    align-items: center;
                    border-bottom: 1rpx solid $uni-border-color;
                    padding-bottom: 30rpx;
                    margin-bottom: 28rpx;
                    .type_logo {
                        color: $uni-text-color-inverse;
                        width: 42rpx;
                        height: 42rpx;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 30rpx;
                    }
                    .title {
                        margin-left: 12rpx;
                        font-weight: 600;
                        font-size: 30rpx;
                        color: $uni-text-color;
                        line-height: 42rpx;
                    }
                }
                .content_box {
                    .subtitle {
                        font-weight: 600;
                        font-size: 30rpx;
                        color: $uni-text-color;
                        line-height: 42rpx;
                        margin-bottom: 20rpx;
                    }
                    .content {
                        margin: 10rpx 0rpx;
                        font-weight: 400;
                        font-size: 26rpx;
                        color: $uni-text-color;
                        line-height: 36rpx;
                    }
                }
            }
        }
    }
}
</style>
