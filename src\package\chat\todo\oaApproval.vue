<template>
    <div>
        <!-- #ifdef H5-WEIXIN || H5 -->
        <iframe class="webview" :src="src" frameborder="0"></iframe>
        <!-- #endif -->
        <!-- #ifdef MP-WEIXIN || APP-PLUS -->
        <web-view class="webview" :src="src"></web-view>
        <!-- #endif -->
    </div>
</template>

<script setup>
import { getToken } from "@/utils/storageToken.js"
import { checkPlatform } from "@/utils/sendAppEvent.js"

const src = ref("")
const token = getToken()?.replace("Bearer ", "")
onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    const { id, status } = options
    const platform = ["yide-ios-app", "yide-android-app", "yide-Harmony-app"].includes(checkPlatform())
    src.value = `${import.meta.env.VITE_BASE_OAH5}/#/approve?id=${id}&status=${status}&token=${token}&skipDetail=${platform}`
})
</script>

<style lang="scss" scoped>
.webview {
    height: 100vh;
    width: 100vw;
}
</style>
