<template>
    <yd-page-view ref="page" class="comp-page" :hideBottom="false">
        <!--  -->
        <template #top>
            <uni-nav-bar left-icon="left" statusBar fixed title="组件中心" @clickLeft="back" :border="false" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()">
                <template #right>
                    <view class="sort_btn" @click="openSortPage">调整排序</view>
                </template>
            </uni-nav-bar>
            <uv-tabs :list="state.tabs" :current="state.active" @click="clickTabs" :activeStyle="{ color: 'var(--primary-color)' }" :inactiveStyle="{ color: '#606266' }" lineWidth="20" :customStyle="{ background: '#fff' }" lineColor="var(--primary-color)"></uv-tabs>
        </template>
        <template #bottom>
            <view class="save-warp">
                <button type="primary" class="btn-primary">保存</button>
                <view class="safe-area"></view>
            </view>
        </template>
        <!--  -->

        <!--  -->
        <view class="container">
            <!--  -->
        </view>
    </yd-page-view>
</template>

<script setup>
import { reactive, onMounted } from "vue"
const page = ref(null)

const state = reactive({
    tabs: [
        { name: "课表", key: "schoolTable" },
        { name: "考勤", key: "" },
        { name: "今日作业", key: "" },
        { name: "信息发布", key: "" },
        { name: "通行", key: "" },
        { name: "待办", key: "" },
        { name: "打卡", key: "" },
        { name: "教师考勤", key: "" },
        { name: "学生评价", key: "" },
        { name: "考试管理", key: "" },
        { name: "投票活动", key: "" },
        { name: "收集表", key: "" }
    ],
    active: 0
})

function back() {
    uni.navigateBack()
}
function clickTabs(item) {}
// 调整排序
function openSortPage() {
    uni.navigateTo({
        url: "/package/home/<USER>"
    })
}

function initPage() {}

onMounted(() => {
    initPage()
})
</script>

<style lang="scss" scoped>
.comp-page {
    .sort_btn {
        font-size: 14px;
        color: var(--primary-color);
    }
    .save-warp {
        padding: 15px 15px 22px 15px;
        background-color: #fff;
        .safe-area {
            padding-bottom: env(safe-area-inset-bottom);
        }
        .btn-primary {
            background-color: var(--primary-color);
        }
    }
}
</style>
