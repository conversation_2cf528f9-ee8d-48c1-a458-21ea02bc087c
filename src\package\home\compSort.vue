<template>
    <yd-page-view ref="page"  class="comp-sort" title="调整排序">      
       <view class="container">            
           <!--  -->

       </view>
   </yd-page-view>
</template>

<script setup>
import { reactive,onMounted } from 'vue'

const state = reactive({

})

function initPage() {

}

onMounted(()=>{
   initPage()
})

</script>

<style lang='scss' scoped>

.comp-sort {
   
}

</style>