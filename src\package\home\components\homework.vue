<template>
    <div class="class_schedule">
        <text class="class_schedule_title">今日作业</text>
        <swiper class="work_swipe" indicator-color="#05B878" v-if="state.workList.length > 0">
            <swiper-item v-for="(item, index) in state.workList" :key="index">
                <div class="class_schedule_card" @click="gotoHomework(item)">
                    <div class="workCon">
                        <div class="imgBox">
                            <image class="img_url" mode="aspectFill" src="@nginx/home/<USER>/work.png" />
                        </div>
                        <div>
                            <div class="van-ellipsis workTitle" v-for="(items, indexs) in item.list.slice(0, 4)" :key="items.id + indexs">
                                <span>{{ indexs + 1 }}</span
                                >、<span>{{ items.title }}</span>
                            </div>
                        </div>
                        <div class="fromClass">
                            <div>
                                来自<span>{{ item.classesName }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </swiper-item>
        </swiper>
        <div class="cardNoData" v-else>
            <div class="class_schedule_card">
                <div class="workCon">
                    <div class="imgBox">
                        <image class="img_url" mode="aspectFill" src="@nginx/home/<USER>/work.png" />
                    </div>
                    <div>
                        <div class="ellipsis workTitle">
                            <span>暂无作业</span>
                        </div>
                    </div>
                    <div class="fromClass"></div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
import useStore from "@/store"
const state = reactive({
    workList: []
})

const { user } = useStore()
const roleCode = computed(() => user?.identityInfo?.roleCode) // 用户信息
function gotoHomework() {
    const url = roleCode.value == "eltern" ? "/apps/schoolAssignment/parent/index" : "/apps/schoolAssignment/teacher/index"
    navigateTo({ url, query: { isHome: true } })
}

function getList() {
    http.get("/app/work/selectWorkHome").then((res) => {
        state.workList = res.data
    })
}

defineExpose({ getList })
</script>

<style scoped lang="scss">
.class_schedule {
    padding: 20rpx 30rpx;
    background: $uni-bg-color-grey;

    .class_schedule_title {
        padding-left: 4rpx;
        font-size: 30rpx;
        font-weight: 500;
        color: $uni-text-color;
    }

    .class_schedule_card {
        height: 100%;
        font-size: 30rpx;
        color: $uni-text-color;
        margin-top: 20rpx;
        width: 100%;
        background: $uni-bg-color;
        box-shadow: 0rpx 16rpx 16rpx 0rpx rgba(220, 245, 238, 0.5);
        border-radius: 20rpx;
        position: relative;

        .workCon {
            padding: 20rpx;

            .imgBox {
                width: 200rpx;
                height: 150rpx;
                position: absolute;
                top: 30rpx;
                right: 70rpx;
                .img_url {
                    width: 100%;
                    height: 100%;
                }
            }
        }
    }
}

.fromClass {
    padding-top: 34rpx;
    font-weight: 400;
    color: $uni-text-color-grey;
    font-size: 24rpx;
}

.work_swipe .swiper-item {
    height: 332rpx;
}

.cardNoData {
    height: 332rpx;
}

.workTitle {
    max-width: 400rpx;
}
</style>
