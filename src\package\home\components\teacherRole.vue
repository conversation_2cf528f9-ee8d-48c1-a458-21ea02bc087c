<template>
    <yd-page-view ref="page" :refresherOnly="true" @onRefresh="onRefresh" page-background="#0000" background-color="#0000" :hideBottom="false" color="#fff" :hideTop="true" :tabBarCurrent="0">
        <view class="container">
            <!-- 任课班级 -->
            <view class="my_class_box">
                <text class="title">任课班级</text>
                <view class="class_list">
                    <view class="item_class" v-for="item in classesTeachList" :key="item.classesId" @click="changeClass(item)">
                        <image class="class_icon" :src="item.classesIcon || '@nginx/components/class.png'" mode="scaleToFill" />
                        <text class="classes_name">{{ item.classesName }}</text>
                    </view>
                </view>
            </view>
            <!-- 快捷应用 -->
            <view class="app_content">
                <view class="app_category" v-for="(item, index) in appItems" :key="index" @click="jumpApp(item)">
                    <image :src="`@nginx/home/<USER>/${item.img}.png`" class="img"></image>
                    <view>{{ item.name }}</view>
                </view>
            </view>
            <!-- 考勤数据 -->
            <view class="attendance_content">
                <swiper style="height: 500rpx" :current="state.attendanceCurrent" circular :indicator-dots="true" indicator-active-color="var(--primary-color)" @change="(e) => change(e, 'attendanceCurrent')">
                    <swiper-item v-for="(item, index) in state.attendanceList" :key="index">
                        <view class="attendance_box">
                            <view class="main">
                                <view class="top_msg" v-if="item.classesName">{{ item.classesName }} {{ item.requiredTime }}</view>
                                <view class="top_msg" v-else>{{ "暂无考勤" }}</view>
                                <l-circularProgress class="yd_circularProgress" :canvasId="'canvasId' + index" :bgCanvasId="'bgCanvasId' + index" :fontShow="false" :percent="percent(item)" type="halfCircular" :lineWidth="14" progressColor="var(--primary-color)" bgColor="#F0F2F5" :boxWidth="240" :boxHeight="150">
                                    <view class="yd_circular_box">
                                        <view>
                                            <text class="left">实到 {{ item.actualNum || "-" }}</text> / <text>应到 {{ item.totalNum || "-" }}</text>
                                        </view>
                                    </view>
                                </l-circularProgress>
                            </view>
                            <view class="footer">
                                <!-- TODO: 字段待确认 -->
                                <view class="footer_box">迟到 {{ "-" }}</view>
                                <view class="footer_box">请假 {{ item.leaveNum || "-" }}</view>
                                <view class="footer_box danger" :style="{ color: dayjs().isBefore(dayjs(item.endTime)) ? '#f5222d' : '#8C8C8C' }">{{ dayjs().isBefore(dayjs(item.endTime)) ? "缺勤" : "未签到" }} {{ item.absenteeismNum || "-" }}</view>
                            </view>
                        </view>
                    </swiper-item>
                </swiper>
            </view>
            <!-- 课程 -->
            <view class="course_content">
                <swiper style="height: 360rpx" :current="state.courseCurrent" circular :indicator-dots="true" indicator-active-color="var(--primary-color)" @change="(e) => change(e, 'courseCurrent')">
                    <swiper-item v-for="(item, index) in state.courseList" :key="index">
                        <view class="course_box">
                            <view class="top_text">{{ courseType[item.type] || "暂无课程" }}</view>
                            <view class="main">
                                <view class="l_box">
                                    <view class="img_box">
                                        <image v-if="item.subjectName" :src="`@nginx/home/<USER>/${imgMap[item.subjectName]}.png`" class="img" />
                                        <image v-else src="@nginx/home/<USER>/emptyWeek.png" class="img" />
                                    </view>
                                    <view>{{ item.subjectName || "暂无课程" }}</view>
                                </view>
                                <view class="r_box">
                                    <view>上课班级 : {{ item.typeName || "-" }}</view>
                                    <view>上课时间 : {{ item.startTime }}-{{ item.endTime }}</view>
                                    <view>节 次 : {{ item.sectionName || "-" }}</view>
                                </view>
                            </view>
                        </view>
                    </swiper-item>
                </swiper>
            </view>
            <!-- 作业 -->
            <view class="work_content">
                <swiper style="height: 360rpx" :current="state.workCurrent" circular :indicator-dots="true" indicator-active-color="var(--primary-color)" @change="(e) => change(e, 'workCurrent')">
                    <swiper-item v-for="(item, index) in state.workList" :key="index">
                        <view class="work_box">
                            <view class="top_text">今日作业（{{ item.classesName || "暂无作业" }}）</view>
                            <view class="main">{{ item.title }}</view>
                            <view class="footer" v-if="item.publisherName">
                                <view class="l_box">
                                    <view class="profile">{{ nickName(item.publisherName) }}</view>
                                    <view>{{ item.publisherName }} | {{ item.releaseTime?.slice(5) }}发布</view>
                                </view>
                                <view class="r_box"
                                    >已提交 <view style="color: var(--primary-color); margin-left: 5rpx">{{ item.completed }}</view> / {{ item.total }}</view
                                >
                            </view>
                        </view>
                    </swiper-item>
                </swiper>
            </view>
            <!-- 打卡 -->
            <view class="punch_content">
                <swiper style="height: 280rpx" :current="state.taskCurrent" circular :indicator-dots="true" indicator-active-color="var(--primary-color)" @change="(e) => change(e, 'taskCurrent')">
                    <swiper-item v-for="(item, index) in state.taskList" :key="index">
                        <view class="punch_box">
                            <view class="top_text">打卡（{{ item.className || "暂无数据" }}）</view>
                            <view class="main">{{ item.taskName }}</view>
                            <view class="footer" v-if="item.className">
                                <view class="l_box">
                                    <view class="profile">{{ nickName(item.createBy) }}</view>
                                    <view>{{ item.createBy }} | {{ item.createTime?.slice(5) }}发布</view>
                                </view>
                                <view class="r_box"
                                    >已提交 <view style="color: var(--primary-color); margin-left: 5rpx">{{ item.todaySignCount }}</view> / {{ item.allSignCount }}</view
                                >
                            </view>
                        </view>
                    </swiper-item>
                </swiper>
            </view>
        </view>
    </yd-page-view>
</template>

<script setup>
import dayjs from "dayjs"
import { useShowLoading } from "@/hooks"
import LCircularProgress from "@/subModules/components/l-circular-progress/components/l-circular-progress/l-circular-progress.vue"
import useStore from "@/store"

const { startLoading, stopLoading } = useShowLoading()
const { user } = useStore()
const page = ref(null)
const state = reactive({
    attendanceCurrent: 0,
    courseCurrent: 0,
    workCurrent: 0,
    taskCurrent: 0,
    attendanceList: [],
    courseList: [],
    workList: [],
    taskList: [],
    session: {
        teacherList: []
    },
    allCourseList: [], // 课程列表
    countTime: {
        hour: 0,
        minute: 0,
        second: 0
    }
})

const appItems = ref([
    { code: "schoolAssignment", routePath: "schoolAssignment", img: "zuoye", name: "作业" },
    { code: "punchTheClock", routePath: "punchTheClock", img: "daka", name: "打卡" },
    { code: "attendance", routePath: "attendance", img: "kaoqin", name: "考勤" },
    { code: "examManage", routePath: "examManage", img: "kaoshi", name: "考试" }
])

const imgMap = {
    语文: "yuwen",
    数学: "shuxue",
    英语: "yingyu",
    物理: "wuli",
    化学: "huaxue",
    生物: "shengwu",
    政治: "zhengzhi",
    历史: "lishi",
    地理: "dili",
    体育: "tiyu",
    美术: "meishu",
    音乐: "yinyue",
    计算机课: "computed"
}

const classesTeachList = computed(() => user.userInfo.classesTeachList)
const percent = computed(() => (val) => {
    if (Object.keys(val).length === 0) return 0
    return Math.floor((actualNum / totalNum) * 100)
})
const selectClassId = ref(null)

function onRefresh() {
    setTimeout(() => {
        // 1.5秒之后停止刷新动画
        page.value.paging.complete([])
    }, 500)
}

const change = (e, type) => {
    state[type] = e.detail.current
}

const nickName = (val) => {
    if (!val) return " "
    return val.substring(0, 1)
}

const init = () => {
    getAttendance()
    getTask()
    getWork()
    getWeekCourse()
}

onMounted(() => {
    if (classesTeachList.value && classesTeachList.value.length) {
        selectClassId.value = classesTeachList.value[0].id
        init()
    }
})

// 获取考勤数据
function getAttendance() {
    http.get("/app/teach/attendance/teachHomepage", {}).then((res) => {
        state.attendanceList = res.data || [{}]
    })
}

// 获取打卡任务
function getTask() {
    http.get("/attweb/attendanceSignTask/app/teacherPublishClassTask", {}).then((res) => {
        console.log("res:", res)
        state.taskList = res.data.length ? res.data : [{}]
        console.log("state.taskList:", state.taskList)
    })
}

// 获取作业
function getWork() {
    http.get("/app/work/teacher/dayWorkList", {}).then((res) => {
        state.workList = res.data.length ? res.data : [{}]
    })
}

const courseType = {
    earlySelfStudyList: "早自习",
    morningList: "上午",
    afternoonList: "下午",
    lateSelfStudyList: "晚自习",
    nightList: "晚上"
}

// 获取周课表
function getWeekCourse() {
    http.post("/app/timetable/getWeekTime", { type: 1, isMyTimetable: true }).then((res) => {
        const {
            data: { sectionList, timetableList }
        } = res
        const date = dayjs().format("YYYY-MM-DD")
        const dateList = timetableList.filter((i) => i.date == date)
        let arr = Object.keys(courseType).map((i) => sortData(sectionList[i], dateList).map((item) => ({ ...item, type: i })))
        arr = arr.flat(Infinity).filter((i) => !isCurrentTimeAfter(i.endTime))
        state.courseList = arr.length ? arr : [{}]
    })
}

function changeClass(item) {
    navigateTo({
        url: "/apps/myStudent/classTeacher/index",
        query: {
            classesId: item.classesId
        }
    })
}

function sortData(arr, sourceData) {
    const sourceMap = new Map(sourceData.map((item) => [item.section, item]))
    return arr.map((item) => {
        const matched = sourceMap.get(item.sequence)
        return matched ? { ...matched, ...item } : item
    })
}

// 判断当前时间是否大于指定的 HH:mm 格式的时间
function isCurrentTimeAfter(timeStr) {
    // 容错处理：如果传入为 null 或非字符串，直接返回 null
    if (timeStr === null || typeof timeStr !== "string") {
        return null
    }

    // 正则校验格式是否正确（HH:mm，24小时制）
    const timeRegex = /^([01]\d|2[0-3]):([0-5]\d)$/
    if (!timeRegex.test(timeStr)) {
        return null
    }

    // 获取当前时间
    const now = new Date()
    const currentHour = now.getHours()
    const currentMinute = now.getMinutes()

    // 解析传入的时间
    const [hourStr, minuteStr] = timeStr.split(":")
    const inputHour = parseInt(hourStr, 10)
    const inputMinute = parseInt(minuteStr, 10)

    // 比较时间
    return currentHour > inputHour || (currentHour === inputHour && currentMinute > inputMinute)
}
</script>

<style lang="scss" scoped>
.container {
    padding: 20rpx 28rpx;
    // 任课班级
    .my_class_box {
        height: 140rpx;
        background: var(--primary-color);
        border-radius: 40rpx;
        padding: 28rpx;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .title {
            font-weight: 600;
            font-size: 28rpx;
            color: #ffffff;
            line-height: 40rpx;
        }
        .class_list {
            display: flex;
            overflow-x: auto;
            max-width: 100%;
            width: 100%;
            .item_class {
                min-width: 200rpx;
                padding-right: 28rpx;
                margin-right: 28rpx;
                border-right: 1rpx solid #fff;
                display: flex;
                align-items: center;
                .class_icon {
                    width: 48rpx;
                    height: 48rpx;
                    border-radius: 50%;
                    margin-right: 10rpx;
                }

                .classes_name {
                    font-weight: 600;
                    font-size: 36rpx;
                    color: #ffffff;
                    line-height: 50rpx;
                }
                &:last-of-type {
                    border-right: none;
                    margin-right: 0;
                }
            }
        }
    }
    .app_content {
        display: flex;
        padding: 30rpx 0;
        margin-bottom: 30rpx;
        .app_category {
            flex: 1;
            display: flex;
            align-items: center;
            flex-direction: column;
            .img {
                width: 80rpx;
                height: 80rpx;
                margin-bottom: 20rpx;
            }
        }
    }
    .attendance_content {
        border-radius: 16rpx;
        border: 4rpx solid #e7e9e8;
        margin-bottom: 40rpx;
        .attendance_box {
            padding: 30rpx;
            .top {
                display: flex;
                justify-content: space-between;
                .right_text {
                    position: relative;
                    text-align: right;
                    width: 250rpx;
                    padding-right: 30rpx;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    -o-text-overflow: ellipsis;
                    &::after {
                        content: "";
                        width: 0;
                        height: 0;
                        display: inline-block;
                        border: 14rpx solid transparent;
                        border-top-color: var(--primary-color);
                        position: absolute;
                        right: 0rpx;
                        top: 15rpx;
                    }
                }
            }
            .main {
                display: flex;
                flex-direction: column;
                align-items: center;
                .top_msg {
                    text-align: center;
                    margin-bottom: 10rpx;
                }
                .yd_circularProgress {
                    margin: 0 auto;
                    position: relative;
                    .yd_circular_box {
                        z-index: 999;
                        width: 480rpx;
                        height: 150rpx;
                        position: absolute;
                        bottom: 0rpx;
                        left: 0;
                        display: flex;
                        align-items: center;
                        justify-content: space-evenly;
                        flex-direction: column;
                        .top_text {
                            position: relative;
                            text-align: right;
                            width: 240px;
                            padding-right: 30rpx;
                            overflow: hidden;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                            -o-text-overflow: ellipsis;
                            margin-bottom: 20rpx;
                            &::after {
                                content: "";
                                width: 0;
                                height: 0;
                                display: inline-block;
                                border: 14rpx solid transparent;
                                border-top-color: var(--primary-color);
                                position: absolute;
                                right: 0rpx;
                                top: 15rpx;
                            }
                        }
                        .left {
                            color: var(--primary-color);
                        }
                    }
                }
            }
            .footer {
                display: flex;
                .footer_box {
                    width: 230rpx;
                    text-align: center;
                }
                .center_box {
                    border-left: 1px solid #d9d9d9;
                    border-right: 1px solid #d9d9d9;
                }
            }
        }
    }
    .course_content {
        border: 1px solid #d9d9d9;
        border-radius: 16rpx;
        margin-bottom: 30rpx;
        .course_box {
            padding-bottom: 20rpx;
            display: flex;
            flex-direction: column;
            height: 280rpx;
            .top_text {
                text-align: center;
                padding: 20rpx 0;
            }
            .main {
                display: flex;
                flex: 1;
                .l_box {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: space-between;
                    .img_box {
                        border-radius: 50%;
                        overflow: hidden;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        .img {
                            width: 160rpx;
                            height: 160rpx;
                        }
                    }
                }
                .r_box {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    justify-content: space-evenly;
                    color: #3b3b3b;
                }
            }
        }
    }
    .work_content {
        border: 1px solid #d9d9d9;
        border-radius: 16rpx;
        margin-bottom: 30rpx;
        .work_box {
            display: flex;
            flex-direction: column;
            padding: 20rpx;
            height: 300rpx;
            background: url("@nginx/home/<USER>/zuoyebg.png") no-repeat;
            background-size: 250rpx 200rpx;
            background-position: 420rpx 20rpx;
            .main {
                flex: 1;
                margin-top: 20rpx;
                color: #676767;
            }
            .footer {
                display: flex;
                justify-content: space-between;
                .l_box {
                    color: #949494;
                    display: flex;
                    align-items: center;
                    .profile {
                        width: 45rpx;
                        height: 45rpx;
                        background: #00b783;
                        color: #fff;
                        text-align: center;
                        line-height: 45rpx;
                        border-radius: 50%;
                        margin-right: 10rpx;
                        font-size: 24rpx;
                    }
                }
                .r_box {
                    font-size: 28rpx;
                    display: flex;
                    align-items: center;
                    color: #949494;
                }
            }
        }
    }
    .punch_content {
        border: 1px solid #d9d9d9;
        border-radius: 16rpx;
        .punch_box {
            display: flex;
            flex-direction: column;
            padding: 20rpx;
            height: 220rpx;
            .main {
                flex: 1;
                margin-top: 20rpx;
                color: #676767;
            }
            .footer {
                display: flex;
                justify-content: space-between;
                .l_box {
                    color: #949494;
                    display: flex;
                    align-items: center;
                    .profile {
                        width: 45rpx;
                        height: 45rpx;
                        background: #00b783;
                        color: #fff;
                        text-align: center;
                        line-height: 45rpx;
                        border-radius: 50%;
                        margin-right: 10rpx;
                        font-size: 24rpx;
                    }
                }
                .r_box {
                    font-size: 28rpx;
                    display: flex;
                    align-items: center;
                    color: #949494;
                }
            }
        }
    }
}
</style>
