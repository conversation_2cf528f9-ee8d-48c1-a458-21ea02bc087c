<template>
    <div class="neutral_login">
        <div class="login">
            <image class="neutral_bg" mode="widthFix" src="@nginx/login/neutralBg1.png" alt="" />
            <div class="login_logo">
                <image class="logo" :src="websiteData.websiteLogo || '@nginx/login/logo.png'" alt="" />
                <span class="title">{{ websiteData.websiteMobileName || "一加壹" }}</span>
            </div>

            <div class="login_form_box">
                <div class="login_footer">
                    <image class="footer_bg" mode="widthFix" src="@nginx/login/neutralBg2.png" alt="" />
                    <span class="filing_desc">{{ websiteData.filingDesc }}</span>
                </div>
                <div class="login_form">
                    <div class="form">
                        <div class="form_title">账号</div>
                        <input :maxlength="11" type="text" class="input" placeholder="请输入手机号" v-model="userInfo.username" />
                    </div>
                    <div class="form form_password">
                        <div class="form_title">密码</div>
                        <!-- #ifdef MP-WEIXIN -->
                        <input :maxlength="11" class="input" v-model="userInfo.password" placeholder="请输入密码" :password="!isShow" />
                        <!-- #endif -->
                        <!-- #ifdef APP-PLUS || H5 -->
                        <input :maxlength="11" class="input" v-model="userInfo.password" placeholder="请输入密码" v-if="isShow" type="text" />
                        <input :maxlength="11" class="input" v-else v-model="userInfo.password" placeholder="请输入密码" type="password" />
                        <!-- #endif -->
                        <div @click="isShow = !isShow" class="eye">
                            <image src="@nginx/login/openPassword.png" class="Fill" v-if="!isShow" />
                            <image src="@nginx/login/closePassword.png" class="Fill" v-else />
                        </div>
                    </div>
                </div>
                <div class="btn_class">
                    <button :loading="loading" class="button" @click="logIn">登录</button>
                    <div class="forget_password">
                        <span @click="forgetPassword">忘记密码</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { computed } from "vue"
const emit = defineEmits(["logIn", "forgetPassword"])
const props = defineProps({
    // 按钮loading
    loading: {
        type: Boolean,
        default: false
    },
    // 中性版页面数据
    websiteData: {
        type: Object,
        default: () => {}
    }
})
const isShow = ref(false) // 密码是否可见
const userInfo = ref({
    username: "",
    password: ""
})

// 登录按钮loading
const loading = computed(() => {
    return props.loading
})

// 中性版页面数据
const websiteData = computed(() => {
    return props.websiteData
})

// 点击登录按钮
const logIn = () => {
    emit("logIn", userInfo.value)
}

const forgetPassword = () => {
    emit("forgetPassword")
}
</script>

<style lang="scss" scoped>
.neutral_login {
    .login {
        position: relative;
        height: 100vh;
        overflow: hidden;

        .neutral_bg {
            width: 100vw;
            height: auto;
        }

        .login_logo {
            position: absolute;
            top: 220rpx;
            left: 60rpx;
            display: flex;
            flex-direction: column;

            .logo {
                width: 140rpx;
                height: 140rpx;
            }

            .title {
                padding-top: 24rpx;
                font-weight: 600;
                font-size: 40rpx;
                color: #000000;
                line-height: 56rpx;
            }
        }

        .login_form_box {
            position: absolute;
            bottom: 0;
            left: 0;
            background: $uni-bg-color;
            height: calc(62vh - 120rpx);
            width: calc(100vw - 120rpx);
            border-radius: 72rpx 72rpx 0px 0px;
            padding: 60rpx;

            .login_form {
                margin-top: 40rpx;
                width: 100%;
                box-sizing: border-box;
                position: relative;

                .form {
                    position: relative;

                    .eye {
                        position: absolute;
                        right: 0;
                        bottom: -10rpx;
                        z-index: 9;
                        width: 100rpx;
                        height: 100rpx;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }

                    .Fill {
                        width: 40rpx;
                        height: 40rpx;
                    }
                }

                .form_password {
                    margin-top: 58rpx;
                }

                .input {
                    border-bottom: 1rpx solid $uni-border-color;
                    height: 80rpx;
                    outline: none;
                    color: $uni-text-color;
                    line-height: 80rpx;
                    margin-top: 14rpx;
                }

                .form_title {
                    font-weight: 600;
                    font-size: 36rpx;
                    color: $uni-text-color;
                    line-height: 50rpx;
                }
            }

            .btn_class {
                margin-top: 100rpx;
                display: flex;
                justify-content: center;
                flex-direction: column;
                align-items: center;

                .button {
                    width: 100%;
                    border-radius: 56rpx;
                    background: #00b781;
                    color: $uni-text-color-inverse;
                    height: 92rpx;
                    border: none;
                }

                .forget_password {
                    padding-top: 24rpx;
                    display: flex;
                    justify-content: flex-end;
                    font-weight: 400;
                    font-size: 28rpx;
                    color: #00b781;
                    line-height: 48rpx;
                    z-index: 9;
                }
            }

            .login_footer {
                position: absolute;
                bottom: 0;
                left: 0;
                display: flex;
                align-items: flex-end;

                .footer_bg {
                    width: 100vw;
                    height: auto;
                }

                .filing_desc {
                    width: 100vw;
                    text-align: center;
                    font-weight: 400;
                    position: absolute;
                    bottom: 40rpx;
                    left: 0;
                    font-size: 24rpx;
                    color: $uni-text-color;
                    line-height: 34rpx;
                }
            }
        }
    }
}
</style>
