<template>
    <yd-page-view class="select_identity">
        <template #top>
            <!-- #ifdef APP-PLUS  || H5 -->
            <uni-nav-bar statusBar fixed :leftWidth="100" :rightWidth="100" :border="false" left-icon="left" @clickLeft="clickLeft">
                <view class="school_title ellipsis">{{ schoolInfo.schoolName || "选择身份" }}</view>
                <template v-slot:right>
                    <!-- #ifdef H5-WEIXIN || H5 -->
                    <div class="select_school" v-if="!switchIdentities && schoolList.length > 1" @click="selectSchool">
                        <span>选择学校</span>
                        <image class="change_over" src="@nginx/login/changeOver.png" alt="" />
                    </div>
                    <!-- #endif -->
                    <!-- #ifdef APP-PLUS -->
                    <div class="select_school" v-if="!switchIdentities && schoolList.length > 1" @click="selectSchool">
                        <span>选择学校</span>
                        <image class="change_over" src="@nginx/login/changeOver.png" alt="" />
                    </div>
                    <!-- #endif -->
                </template>
            </uni-nav-bar>
            <!-- #endif -->
            <!-- #ifdef MP-WEIXIN -->
            <uni-nav-bar statusBar fixed :leftWidth="86" :rightWidth="16" :border="false" left-icon="left" @clickLeft="clickLeft">
                <view class="school_title ellipsis">{{ schoolInfo.schoolName || "选择身份" }}</view>
            </uni-nav-bar>
            <!-- #endif -->
        </template>
        <div class="identity_page">
            <div class="title_box">
                <div class="title">请选择您要使用的身份</div>
                <!-- #ifdef MP-WEIXIN -->
                <div class="change_school" @click="selectSchool" v-if="!switchIdentities && schoolList.length > 1">
                    <span>选择学校</span>
                    <image class="change_over" src="@nginx/login/changeOver2.png" alt="" />
                </div>
                <!-- #endif -->
            </div>
            <div class="identity_list" v-if="roleList && roleList.length > 0">
                <div class="identity_item" :style="{ background: roleBgColor(item.roleCode) }" @click="selectRole(item)" v-for="(item, index) in roleList" :key="index">
                    <div class="item_left">
                        <image class="identity_logo" :src="`@nginx/login${roleImg(item.roleCode)}`" alt="" />
                        <span>我是{{ item.roleName }}</span>
                    </div>
                    <div class="item_right">
                        <div
                            class="label"
                            :style="{
                                background: item.roleCode == 'eltern' ? '#fdb500' : '#00B781'
                            }"
                        >
                            {{ item.identityName }}
                        </div>
                        <uni-icons type="right" size="20" color="#999999"></uni-icons>
                    </div>
                </div>
            </div>
            <yd-empty text="暂无身份" v-else />
        </div>
    </yd-page-view>
    <yd-select-popup
        :fieldNames="{
            value: 'id',
            label: 'schoolName'
        }"
        ref="selectPopupRef"
        title="选择学校"
        :list="schoolList"
        @closePopup="closePopup"
    ></yd-select-popup>
</template>

<script setup>
import useStore from "@/store"
import { onLoad } from "@dcloudio/uni-app"
import { useRole } from "@/hooks"

const instance = getCurrentInstance().proxy
const { user, home, system, local } = useStore()
const { roleImg, roleBgColor } = useRole()
const selectPopupRef = ref(null) // 选择学校的弹框
const schoolInfo = ref({}) // 选择的学校
const schoolList = ref([]) // 学校列表
const roleList = ref([]) // 角色列表
const switchIdentities = ref(false)

const clickLeft = () => {
    uni.navigateBack()
}
// 取消选择学校
const closePopup = (obj) => {
    if (obj) {
        // 切换学校
        schoolInfo.value = obj
        user.setSchoolInfo(obj)
        roleList.value = obj.childrenV3 || []
    }
}

// 选择学校
function selectSchool() {
    selectPopupRef.value.open()
}

// 获取学生
const getYourChildrenInfo = () => {
    http.get("/app/student/getStudentList").then((res) => {
        const arr = res.data?.map((i) => {
            return {
                ...i,
                studentName: i.name,
                studentId: i.id
            }
        })
        user.setStudentInfo(arr)
    })
}

// 获取当前学校类型 K12/大学
const getSchoolType = async () => {
    await http
        .post("/app/school/template/getCommentKey", { commentKey: ["schoolType"] })
        .then((res) => {
            // 设置学校类型,schoolType: 1是K12，2是大学
            const info = { ...user.schoolInfo, schoolType: res.data.schoolType == 2 ? "university" : "K12" }
            user.setSchoolInfo(info)
        })
        .catch(() => {
            user.setSchoolInfo({ ...user.schoolInfo, schoolType: "K12" })
        })
}

const roleCodeType = (roleCode) => {
    console.log(roleCode, "roleCoderoleCoderoleCode")
    if (roleCode == "eltern") {
        return "eltern"
    } else if (roleCode == "student") {
        return "student"
    } else if (roleCode == "dorm_admin") {
        return "dorm_admin"
    } else {
        return "ordinaryTeacher"
    }
}
const identityType = (identity) => {
    if (identity == "eltern") {
        return 2
    } else if (identity == "student") {
        return 0
    } else {
        return 1
    }
}

// 选择角色
function selectRole(item) {
    if (item) {
        http.post("/app/mobile/user/login", { schoolId: schoolInfo.value.id, id: item.id, roleCode: item.roleCode }).then(async (res) => {
            const userInfo = {
                ...res.data,
                roleCode: roleCodeType(res.data?.roleCode),
                identity: identityType(res.data?.identity)
            }
            const identityInfo = {
                ...item,
                roleCode: roleCodeType(item?.roleCode),
                identity: identityType(item?.identity)
            }
            user.setUserInfo(userInfo)
            user.setIdentityInfo(identityInfo)
            // 如果选择了家长的角色 调用接口吧孩子都查出来 然后存入store
            item.roleCode == "eltern" ? getYourChildrenInfo() : user.setStudentInfo([])
            // 获取任课老师和班主任任教的班级
            if (item.roleCode == "headmaster") {
                queryClassMaterList()
            } else if (item.roleCode == "teacher") {
                queryClassTeachList()
            }
            // 设置学校类型 K12/大学
            await getSchoolType()
            // 获取首页快捷入口
            await home.queryQuickList()
            if (user.identityInfo.roleCode === "dorm_admin") {
                system.setPrimaryColor("#4566d5")
                // const query = uni.createSelectorQuery().in(instance)
                // console.log(query)
                // query
                //     .selectAll("view")
                //     .boundingClientRect((data) => {
                //         console.log(data, "datadatadatadatadatadatadatadatadatadatadatadatadatadatadatadatadatadata")
                //         // if (data) {
                //         //     data.page.style.setProperty("--primary-color", color) // 更改为粉红色
                //         //     data.page.style.setProperty("--primary-bg-color", `${color}10`) // 更改背景颜色
                //         // }
                //     })
                //     .exec()
            } else {
                system.setPrimaryColor("#00B781")
            }
            system.switchTab({ id: system.tabBarList[0]?.id || 0 })
            // 根据角色tabBar
            if (local.share) {
                navigateTo({
                    url: "/pages/sharePage/index",
                    query: local.shareOption
                })
            } else {
                // 切换身份默认选中首页
                uni.reLaunch({ url: system.tabBarList[0].pagePath })
            }
        })
    }
}

function getSchoolFn() {
    http.get("/app/mobile/user/school").then((res) => {
        if (res.data && res.data.length > 0) {
            // 默认选择第一个学校
            user.setSchoolInfo(res.data[0])
            schoolList.value = res.data.map((i, index) => {
                return {
                    ...i,
                    isCheck: index == 0 ? true : false
                }
            })
            schoolInfo.value = res.data[0]
            roleList.value = res.data[0] && res.data[0].childrenV3 ? res.data[0].childrenV3 : []
        }
    })
}

// 任课老师角色下管理的班级
function queryClassTeachList() {
    http.get("/cloud/v3/classes/queryClassTeachList").then((res) => {
        user.setClassTeachList(res.data)
    })
}

// 班主任角色下管理的班级
function queryClassMaterList() {
    http.get("/app/master/class/queryClassMaterList").then((res) => {
        user.setClassMaterList(res.data)
    })
}

onLoad((options) => {
    if (!options.type) {
        getSchoolFn()
    }
    switchIdentities.value = options.type == "switchIdentities"
    schoolInfo.value = user.schoolInfo || []
    roleList.value = user.schoolInfo && user.schoolInfo.childrenV3 ? user.schoolInfo.childrenV3 : []
})
</script>

<style lang="scss" scoped>
.select_identity {
    .school_title {
        flex: 1;
        text-align: center;
        font-weight: 500;
        font-size: 34rpx;
        color: #000000;
        line-height: 88rpx;
    }

    .select_school {
        display: flex;
        align-items: center;
        font-weight: 400;
        font-size: 28rpx;
        color: #00b781;
        line-height: 40rpx;

        .change_over {
            width: 44rpx;
            height: 44rpx;
        }
    }

    .identity_page {
        padding: 30rpx;

        .title_box {
            display: flex;
            justify-content: space-between;

            .title {
                font-weight: 500;
                font-size: 40rpx;
                color: $uni-text-color;
                line-height: 56rpx;
            }

            .change_school {
                height: 56rpx;
                background: #00b781;
                border-radius: 28rpx 0rpx 0rpx 28rpx;
                display: flex;
                justify-content: center;
                align-items: center;
                font-weight: 400;
                font-size: 28rpx;
                color: $uni-text-color-inverse;
                line-height: 56rpx;
                width: 188rpx;
                margin-right: -30rpx;

                .change_over {
                    width: 44rpx;
                    height: 44rpx;
                }
            }
        }

        .identity_list {
            display: flex;
            flex-direction: column;
            padding: 30rpx 0rpx;

            .identity_item {
                height: 196rpx;
                display: flex;
                border-radius: 20rpx;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 40rpx;
                padding: 0rpx 30rpx;

                .item_left {
                    font-weight: 500;
                    display: flex;
                    align-items: center;
                    font-size: 36rpx;
                    color: $uni-text-color;
                    line-height: 50rpx;

                    .identity_logo {
                        margin-right: 30rpx;
                        width: 136rpx;
                        height: 136rpx;
                    }
                }

                .item_right {
                    display: flex;
                    align-items: center;

                    .label {
                        padding: 0rpx 16rpx;
                        min-width: 54rpx;
                        height: 48rpx;
                        border-radius: 26rpx;
                        font-weight: 400;
                        font-size: 26rpx;
                        color: $uni-text-color-inverse;
                        line-height: 48rpx;
                        text-align: center;
                        margin-right: 10rpx;
                    }
                }
            }
        }
    }
}
</style>
