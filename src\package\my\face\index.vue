<template>
    <yd-page-view class="face_collect" title="人脸采集" leftIcon="left">
        <view class="tip">
            <uni-icons type="info-filled" size="20" color="var(--primary-color)"></uni-icons>
            <text class="tip_text"> 人脸信息用于学校签到或考勤，在校门闸机、宿舍、教室等场所使用 </text>
        </view>
        <view class="face_img_content">
            <image class="face_image" mode="heightFix" :src="faceInfo && faceInfo.imgPath ? faceInfo.imgPath : defImage"></image>
            <text class="protocol"> 点击 <text class="protocol_link" @click="facePolicy">《一加壹人脸识别用户协议》</text>可查看协议 </text>
            <button :loading="loading" :disabled="loading" class="face_btn" @click="handerPicker">
                {{ faceInfo && faceInfo.imgPath ? "重新采集" : "开始采集" }}
            </button>
        </view>
        <view class="photo_requirements">
            <text class="title">脸照片要求：</text>
            <text class="requirements">1.脸部占画面约1/4，正脸无遮挡（不遮挡眼睛、嘴巴和子），露眉露耳不露齿。 </text>
            <text class="requirements">2.正常光照，亮度均匀，无阴阳脸、无浓妆。</text>
        </view>
    </yd-page-view>
</template>

<script setup>
import useStore from "@/store"
import { onLoad } from "@dcloudio/uni-app"
import { computed } from "vue"

const { user } = useStore()
const userInfo = computed(() => user.userInfo) // 用户信息
const identityInfo = computed(() => user.identityInfo) // 角色信息

const defImage = computed(() => {
    return user.identityInfo?.roleCode === "dorm_admin" ? "@nginx/personalCenter/dormFaceDefault.png" : "@nginx/personalCenter/faceDefault.png"
})

const faceInfo = ref({}) // 人脸信息
const userId = ref(null)

const loading = ref(false)
function facePolicy() {
    navigateTo({
        url: "/package/my/privacyPolicy/facePolicy"
    })
}

function collectFace(url) {
    const params = {
        type: ["eltern", "student"].includes(identityInfo.value.roleCode) ? 1 : 2, //  教职工传2，其他传1
        userId: userId.value || userInfo.value.identityUserId || null,
        imgPath: url
    }
    uni.showLoading({
        title: "人脸采集中..."
    })
    loading.value = true
    http.post("/app/face/collectFace", params)
        .then((res) => {
            if (res.code == 0) {
                getFace()
            } else {
                uni.hideLoading()
                loading.value = false
            }
        })
        .catch(() => {
            uni.hideLoading()
            loading.value = false
        })
}

// #ifdef H5 || H5-WEIXIN
function uploadFile(file) {
    http.uploadFile("/file/common/upload", "", { folderType: "app" }, file)
        .then((url) => {
            if (url) {
                collectFace(url)
            } else {
                loading.value = false
                uni.hideLoading()
            }
        })
        .catch(() => {
            loading.value = false
            uni.hideLoading()
        })
}
// #endif

// #ifdef MP-WEIXIN
function uploadFile(file) {
    http.uploadFile("/file/common/upload", file.path, { folderType: "app" })
        .then((url) => {
            if (url) {
                collectFace(url)
            } else {
                loading.value = false
                uni.hideLoading()
            }
        })
        .catch(() => {
            loading.value = false
            uni.hideLoading()
        })
}
// #endif

// 旋转图片方向
async function rotateImageAndConvertToFile(file, degree) {
    const img = new Image()
    const url = URL.createObjectURL(file)
    img.src = url
    img.onload = () => {
        // 将角度转换为弧度
        const radian = (degree * Math.PI) / 180
        const canvas = document.createElement("canvas")
        const ctx = canvas.getContext("2d")
        // 根据旋转角度调整canvas尺寸
        if (radian === (90 * Math.PI) / 180 || radian === (270 * Math.PI) / 180) {
            canvas.width = img.height
            canvas.height = img.width
        } else {
            canvas.width = img.width
            canvas.height = img.height
        }
        ctx.translate(canvas.width / 2, canvas.height / 2)
        ctx.rotate(radian)
        ctx.drawImage(img, -img.width / 2, -img.height / 2)
        canvas.toBlob((blob) => {
            // 创建一个新的File对象
            const ydFile = new File([blob], "rotated_image.jpg", {
                type: "image/png"
            })
            uploadFile(ydFile)
            // 清理
            URL.revokeObjectURL(url)
        }, "image/png")
    }
}

function handerPicker() {
    uni.chooseImage({
        count: 1, // 最多可以选择的图片张数，默认9
        success: (res) => {
            if (res.tempFiles && res.tempFiles.length > 0) {
                try {
                    uni.showLoading({
                        title: "图片上传中..."
                    })
                    loading.value = true
                    res.tempFiles.forEach((i) => {
                        // #ifdef H5 || H5-WEIXIN
                        // IOS特殊旋转一下照片
                        if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
                            rotateImageAndConvertToFile(i, 360)
                        } else {
                            uploadFile(i)
                        }
                        // #endif
                        // #ifdef MP-WEIXIN
                        console.log(i, "ii")
                        uploadFile(i)
                        // #endif
                    })
                } catch (error) {
                    loading.value = false
                    uni.hideLoading()
                    console.log(error, "error")
                }
            }
        }
    })
}

function getFace() {
    const params = {
        type: ["eltern", "student"].includes(identityInfo.value.roleCode) ? 1 : 2, //  教职工传2，其他传1
        userId: userId.value || userInfo.value.identityUserId || null
    }
    http.post("/app/face/get", params)
        .then((res) => {
            faceInfo.value = res.data
            uni.hideLoading()
            loading.value = false
        })
        .catch(() => {
            uni.hideLoading()
            loading.value = false
        })
}
onLoad((options) => {
    console.log(options)
    userId.value = options.id || null
    getFace()
})
</script>

<style lang="scss" scoped>
.face_collect {
    .tip {
        min-height: 68rpx;
        background: $uni-bg-color-grey;
        padding: 30rpx;

        .tip_text {
            font-weight: 400;
            font-size: 24rpx;
            color: var(--primary-color);
            line-height: 34rpx;
        }
    }

    .face_img_content {
        height: 914rpx;
        background: $uni-bg-color-grey;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 0rpx 30rpx;
        width: calc(100% - 60rpx);

        .face_image {
            width: auto;
            height: 420rpx;
        }

        .protocol {
            font-weight: 400;
            font-size: 24rpx;
            color: $uni-text-color-grey;
            line-height: 34rpx;
            margin: 60rpx 0rpx 72rpx 0rpx;

            .protocol_link {
                color: var(--primary-color);
            }
        }

        .face_btn {
            height: 92rpx;
            background: var(--primary-color);
            border-radius: 10rpx;
            width: 100%;
            line-height: 92rpx;
            font-weight: 400;
            font-size: 32rpx;
            color: $uni-text-color-inverse;
            text-align: center;
        }
    }

    .photo_requirements {
        padding: 30rpx;

        .title {
            font-weight: 400;
            font-size: 24rpx;
            color: #666666;
            margin-bottom: 30rpx;
            display: block;
        }

        .requirements {
            font-weight: 400;
            font-size: 26rpx;
            color: $uni-text-color;
            line-height: 36rpx;
            margin-bottom: 30rpx;
            display: block;
        }
    }
}
</style>
