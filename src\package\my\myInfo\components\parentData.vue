<template>
    <div>
        <!-- 选择学生 -->
        <div class="bar_right" @click="changeStudent" v-if="studentList.length > 1">
            <text class="l_text">{{ student.studentName }}</text>
            <image class="sift_icon" src="@nginx/components/siftIcon.png" alt="" />
        </div>
        <div class="tip">
            <uni-icons type="info-filled" size="18" color="var(--primary-color)"></uni-icons>
            <text class="tip_text">请填写以下个人信息（可选填）</text>
        </div>
        <div class="parent_data_class">
            <uni-section titleColor="var(--primary-color)" title="家长信息">
                <uni-list>
                    <uni-list-item v-for="item in parentInfoLabel" :key="item.code" showArrow clickable :title="item.lable" @click="clickItem(item)">
                        <template v-slot:footer>
                            <view class="right_text">
                                {{ item.mappingObj[dataObj[item.code]] || item.placeholder }}
                            </view>
                        </template>
                    </uni-list-item>
                </uni-list>
            </uni-section>
            <uni-section titleColor="var(--primary-color)" title="学生基础信息">
                <uni-list>
                    <uni-list-item v-for="item in childInfoLabel" :key="item.code" showArrow clickable :title="item.lable" @click="clickItem(item)">
                        <template v-slot:footer>
                            <view class="right_text">
                                <span v-if="['select', 'dataStatic'].includes(item.type)">
                                    {{ item.mappingObj[dataObj[item.code]] || item.placeholder }}
                                </span>
                                <span v-else>
                                    {{ dataObj[item.code] || item.placeholder }}
                                </span>
                            </view>
                        </template>
                    </uni-list-item>
                </uni-list>
            </uni-section>
            <uni-section titleColor="var(--primary-color)" title="学生身份信息">
                <uni-list>
                    <uni-list-item v-for="item in childIdentityLabel" :key="item.code" :showArrow="item.type != 'cascade'" clickable :title="item.lable" @click="clickItem(item)">
                        <template v-slot:footer>
                            <view class="right_text">
                                <!-- 级联（籍贯） -->
                                <div v-if="item.type == 'cascade'">
                                    <uni-data-picker v-model="dataObj[item.code]" placeholder="请选择" popup-title="请选择" :localdata="item.columns" v-slot:default="{ data, error }" @change="selectPickerDataFn" :map="{ text: 'name', value: 'id', children: 'children' }" parent-field="area">
                                        <view v-if="error" class="error">
                                            <text>{{ error }}</text>
                                            <uni-icons type="right" size="16" color="#bbb" style="margin: 0 -10rpx 0 10rpx"></uni-icons>
                                        </view>
                                        <view v-else-if="data.length" class="native_place_list">
                                            <view v-for="(item, index) in data" :key="index">
                                                <span v-if="index != 0">/</span>
                                                {{ item.text }}
                                            </view>
                                            <uni-icons type="right" size="16" color="#bbb" style="margin: 0 -10rpx 0 10rpx"></uni-icons>
                                        </view>
                                        <view v-else>
                                            {{ dataObj[item.code] && dataObj[item.code].length > 0 ? dataObj[item.code] : item.placeholder }}
                                            <uni-icons style="margin: 0 -10rpx 0 10rpx" type="right" size="16" color="#bbb"></uni-icons>
                                        </view>
                                    </uni-data-picker>
                                </div>
                                <span v-else-if="['select', 'dataStatic'].includes(item.type)">
                                    {{ item.mappingObj[dataObj[item.code]] || item.placeholder }}
                                </span>
                                <span v-else>
                                    {{ dataObj[item.code] || item.placeholder }}
                                </span>
                            </view>
                        </template>
                    </uni-list-item>
                </uni-list>
            </uni-section>
        </div>
        <!-- 确认按钮 -->
        <div class="confirm_btn">
            <button class="btn_class" :loading="loading" :disabled="loading" @click="confirmFn">确认</button>
        </div>

        <!-- 选择学生 -->
        <yd-select-popup ref="selectStudentRef" title="请选择学生" :list="studentList" @closePopup="selectStudent" :fieldNames="{ value: 'studentId', label: 'studentName' }" :selectId="[student.studentId]" />

        <!-- 选择器 -->
        <yd-select-popup :multiple="selectMultiple" ref="selectPopupRef" :list="dataColumns" title="请选择" @closePopup="selectClosePopup" :selectId="selectId" />

        <!-- 输入框 -->
        <uni-popup ref="inputPopupRef" type="dialog">
            <uni-popup-dialog ref="inputClose" mode="input" :title="`请输入${inputTitle}`" v-model="inputValue" placeholder="请输入内容" @confirm="dialogInputConfirm"></uni-popup-dialog>
        </uni-popup>

        <!-- 日期 -->
        <uv-calendars color="var(--primary-color)" confirmColor="var(--primary-color)" ref="calendarsRef" mode="single" @confirm="confirmCalendar"></uv-calendars>
    </div>
</template>

<script setup>
import useHook from "../hook/index"
import useStore from "@/store"
const { getData, parentInfoLabel, childInfoLabel, childIdentityLabel } = useHook()

const { user } = useStore()
const dataObj = ref({})
const selectCode = ref(null)
const selectPopupRef = ref(null)
const inputPopupRef = ref(null)
const selectStudentRef = ref(null)
const calendarsRef = ref(null)
const loading = ref(false)
const dataColumns = ref([])
const selectMultiple = ref(false)
const selectId = ref([])
const studentList = ref([])
const inputTitle = ref("")
const inputValue = ref("")
const student = ref({
    studentName: "",
    studentId: null
})

// 选择学生
function selectStudent(val) {
    if (!val) return
    student.value.studentName = val.studentName || ""
    student.value.studentId = val.studentId || null
    init()
}

// 打开选择学生弹框
function changeStudent() {
    selectStudentRef.value.open()
}

async function clickItem(item) {
    const { type, code, columns, multiple, lable } = item
    selectCode.value = code
    // 静态数据和选择框
    if (["select", "dataStatic"].includes(type)) {
        dataColumns.value = columns
        selectMultiple.value = multiple
        selectId.value = multiple ? dataObj.value[code] : [dataObj.value[code]]
        selectPopupRef.value.open()
    }
    // 输入框
    else if (type == "input") {
        inputValue.value = dataObj.value[code]
        inputTitle.value = lable
        inputPopupRef.value.open()
    } else if (type == "date") {
        calendarsRef.value.open()
    }
}

// 修改 籍贯
function selectPickerDataFn(item) {
    const list = item.detail.value
    const nowList = []
    list.forEach((i) => {
        nowList.push(i.value)
    })
    dataObj.value.nativePlaces = nowList
    dataObj.value.nativePlace = nowList.join(",")
}

function selectClosePopup(obj, multiple) {
    if (!obj) return
    if (!multiple) {
        // 字典
        dataObj.value[selectCode.value] = obj.value
    }
}

// 确认日期
function confirmCalendar(obj) {
    if (!obj) return
    dataObj.value[selectCode.value] = obj.fulldate
}

// 确认输入
function dialogInputConfirm(value) {
    if (!value) return
    dataObj.value[selectCode.value] = value
}

// 修改家长信息
function confirmFn() {
    loading.value = true
    http.post("/app/invite/updateEltern", dataObj.value)
        .then((res) => {
            // TODO: 设置完成后是否把性别也存入缓存
            // user.setUserInfo({ ...user.userInfo, gender: dataObj.value.gender })
            uni.showToast({
                title: res.message,
                icon: "none"
            })
            uni.navigateBack()
        })
        .finally(() => {
            loading.value = false
        })
}

// 回显数据
async function getInfoFn() {
    uni.showLoading({
        title: "加载中"
    })
    http.get("/app/invite/getElternInfo", {
        studentId: student.value.studentId
    })
        .then((res) => {
            dataObj.value = res.data
            console.log(dataObj.value)
        })
        .finally(() => {
            uni.hideLoading()
        })
}

async function init() {
    if (!studentList.value.length) return
    await getData(childInfoLabel.value) //  学生基础信息
    await getData(childIdentityLabel.value) // 学生身份信息
    if (user.studentInfo && user.studentInfo.length) {
        await getInfoFn()
    }
}

onMounted(() => {
    studentList.value = user.studentInfo
    student.value.studentName = user.studentInfo[0]?.studentName || ""
    student.value.studentId = user.studentInfo[0]?.studentId || null
    init()
})
</script>

<style lang="scss" scoped>
.bar_right {
    font-weight: 400;
    font-size: 28rpx;
    color: var(--primary-color);
    line-height: 40rpx;
    display: flex;
    padding: 10rpx 30rpx;
    background: $uni-bg-color;

    .l_text {
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        text-align: right;
    }
    .sift_icon {
        width: 28rpx;
        height: 28rpx;
        flex-shrink: 0;
    }
}
.parent_data_class {
    padding-bottom: 200rpx;
    .native_place_list {
        display: flex;
        justify-content: flex-end;
    }
    .right_text {
        font-size: 28rpx;
        font-weight: 400;
        color: #999999;
        line-height: 40rpx;
        text-align: right;
        min-width: 50%;
        max-width: 80%;
    }
}

.confirm_btn {
    position: fixed;
    bottom: 0;
    left: 0;
    padding: 30rpx;
    background: $uni-bg-color;
    width: calc(100vw - 60rpx);
    .btn_class {
        background: var(--primary-color);
        color: $uni-text-color-inverse;
    }
}

:deep(.uni-button-color) {
    color: var(--primary-color) !important;
}
:deep(.uni-list-item__content) {
    min-width: 180rpx;
}
.tip {
    padding: 30rpx;
    background: var(--primary-bg-color);
    display: flex;
    align-items: center;
    .tip_text {
        padding-left: 10rpx;
        color: var(--primary-color);
        font-weight: 400;
        font-size: 24rpx;
        line-height: 34rpx;
    }
}
</style>
