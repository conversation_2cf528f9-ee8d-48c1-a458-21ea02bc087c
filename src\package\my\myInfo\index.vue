<template>
    <yd-page-view class="security" title="我的信息" leftIcon="left">
        <uni-list :border="false">
            <uni-list-item clickable showArrow @click="clickAvatar">
                <template #header>
                    <text class="list_title">头像</text>
                </template>
                <template v-slot:footer>
                    <image class="avatarImg" :src="userInfo.avatar || userInfo.defaultAvatar" mode="aspectFill"></image>
                </template>
            </uni-list-item>
            <uni-list-item title="手机号" clickable :rightText="userInfo.phone || '未设置'"> </uni-list-item>
            <uni-list-item title="性别" clickable :rightText="genderObj[userInfo.gender] || '未设置'"> </uni-list-item>
            <uni-list-item title="生日" clickable :rightText="userInfo.birthday || '未设置'"> </uni-list-item>
            <uni-list-item title="邮箱" clickable showArrow @click="clickUpdateEmail" :rightText="userInfo.email || '未设置'"> </uni-list-item>
        </uni-list>

        <!-- 不等于大学生和家长的时候可以显示这个人脸采集 -->
        <uni-list v-if="!['eltern', 'student'].includes(roleCode)" :border="false" style="margin-top: 20rpx">
            <uni-list-item title="人脸采集" clickable showArrow :rightText="isFace ? '已采集' : '未采集'" @click="clickFace"> </uni-list-item>
        </uni-list>

        <!-- 修改邮箱 -->
        <update-email ref="updateEmailRef" @submit="submitUpdateEmail" />

        <!-- 二次确认人脸协议弹框 -->
        <yd-face ref="facePopupRef" />

        <div class="perfect_more" @click="perfectMoreClick">去完善更多信息</div>
    </yd-page-view>
</template>

<script setup>
import UpdateEmail from "./components/updateEmail.vue"
import useStore from "@/store"
import useHook from "./hook/index"

const { getSystemDictFn, listForEach } = useHook()
const { user, local } = useStore()
const userInfo = computed(() => user?.userInfo) // 用户信息
const roleCode = computed(() => user?.identityInfo?.roleCode) // 角色信息
const updateEmailRef = ref(null)
const isFace = ref(false) // 是否已经采集过人脸
const facePopupRef = ref(null)
const genderObj = ref({})
// 修改用户信息
async function updateCurrentUser({ avatar, email }) {
    await http.post("/app/user/updateCurrentUser", { id: userInfo.value.userId, avatar, email }).then((res) => {
        const info = {
            ...userInfo.value,
            avatar: avatar ? avatar : userInfo.value.avatar,
            email: email ? email : userInfo.value.email
        }
        user.setUserInfo(info)
    })
}

// 上传用户图片
function clickAvatar() {
    uni.chooseImage({
        count: 1,
        success: (res) => {
            console.log(res)
            http.uploadFile("/file/common/upload", res.tempFiles[0].path, { folderType: "app" }).then((url) => {
                if (url) {
                    updateCurrentUser({ avatar: url })
                }
            })
        }
    })
}

// 跳转修改邮箱
function clickUpdateEmail() {
    updateEmailRef.value.open(userInfo.value.email)
}

// 确认修改邮箱
async function submitUpdateEmail(email) {
    await updateCurrentUser({ email })
    updateEmailRef.value.close()
}

// 获取人脸是否已采集
function getFace() {
    const params = {
        type: ["eltern", "student"].includes(roleCode.value) ? 1 : 2, //  教职工传2，其他传1
        userId: userInfo.value.identityUserId || null
    }
    http.post("/app/face/get", params).then((res) => {
        isFace.value = res.data.imgPath ? true : false
    })
}

// 跳转人脸采集页面
function clickFace() {
    if (!local.agreeFace) {
        facePopupRef.value.open()
        return
    }
    navigateTo({
        url: "/package/my/face/index"
    })
}

// 完善更多信息
function perfectMoreClick() {
    navigateTo({
        url: "/package/my/myInfo/perfectMore"
    })
}

onMounted(async () => {
    const list = await getSystemDictFn(["gender"])
    genderObj.value = listForEach(list)
    // 只有老师需要展示调接口
    if (!["eltern", "student"].includes(roleCode.value)) {
        getFace() // 查看是否已经采集了人脸
    }
})
</script>

<style scoped lang="scss">
.avatarImg {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
}
.infoName {
    color: #909399;
    font-size: 28rpx;
}

.list_title {
    font-size: 14px;
    color: #3b4144;
    display: flex;
    align-items: center;
}

.perfect_more {
    position: fixed;
    width: 100vw;
    text-align: center;
    left: 0;
    bottom: 160rpx;
    color: var(--primary-color);
    font-weight: 400;
    font-size: 28rpx;
}
</style>
