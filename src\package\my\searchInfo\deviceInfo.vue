<template>
    <view class="device_info">
        <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" :title="deviceValue[code].pageTitle" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
        <view class="device_item" v-for="(item, index) in dataList" :key="index"> {{ item[deviceValue[code].value] }} </view>
    </view>
</template>

<script setup>
import dayjs from "dayjs"
const dataList = ref([])
const code = ref("")

const deviceValue = {
    deviceModel: {
        value: "clientModel",
        pageTitle: "设备型号"
    },
    deviceSystem: {
        value: "clientSystem",
        pageTitle: "设备系统"
    },
    imei: {
        value: "clientImei",
        pageTitle: "IMEI"
    },
    mac: {
        value: "clientMac",
        pageTitle: "MAC"
    }
}

// 前几天天日期区间（自定义）
function getLastDaysRange(num) {
    const sevenDaysAgo = dayjs().subtract(num, "day")
    return sevenDaysAgo.format("YYYY-MM-DD")
}

function clickLeft() {
    uni.navigateBack()
}

onLoad((options) => {
    code.value = options.code
    const obj = {
        code: options.code,
        createEndTime: dayjs().format("YYYY-MM-DD"),
        createStartTime: getLastDaysRange(Number(options.time))
    }
    http.post("/app/userInfoCollect/getLogList", { ...obj }).then((res) => {
        dataList.value = res.data
    })
})
</script>

<style lang="scss" scoped>
.device_info {
    padding: 0 30rpx;
    .device_item {
        padding: 20rpx 0rpx;
        border-bottom: 1rpx solid $uni-border-color;
        font-size: 28rpx;
        color: $uni-text-color;
    }
}
</style>
