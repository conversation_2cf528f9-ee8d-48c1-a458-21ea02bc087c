<template>
    <z-paging>
        <view class="settingsPage">
            <div class="lineBox"></div>
            <uni-list :border="false">
                <uni-list-item :title="item.name" v-for="item in list" :key="item.code" @click="handleClick(item)" showArrow clickable> </uni-list-item>
            </uni-list>
        </view>
    </z-paging>
</template>

<script setup>
const list = ref([])
const handleClick = (item) => {
    navigateTo({
        url: "/package/my/searchInfo/userInfo/index",
        query: {
            typeCode: item.code,
            typeName: item.name
        }
    })
}
onMounted(async () => {
    const { data } = await http.get("/app/userInfoCollect/getCollectTypeList")
    list.value = data
})
</script>

<style scoped lang="scss">
.lineBox {
    height: 20rpx;
    background: #f0f2f5;
}
</style>
