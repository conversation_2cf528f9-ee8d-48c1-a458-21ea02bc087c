<template>
    <z-paging ref="paging" v-model="dataList" @query="queryList" :auto="false">
        <template #top>
            <!-- #ifdef APP-PLUS || H5 -->
            <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" :title="pageTitle">
                <template #right>
                    <view class="time" @click="selectPopupRef.open()" v-if="['3', '4'].includes(pageCollectType)"> {{ timeText[time] }} </view>
                </template>
            </uni-nav-bar>
            <!-- #endif -->
            <!-- #ifdef MP-WEIXIN -->
            <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" :title="pageTitle" :leftWidth="86" :rightWidth="16"> </uni-nav-bar>
            <view class="time" @click="selectPopupRef.open()" v-if="['3', '4'].includes(pageCollectType)"> {{ timeText[time] }} </view>
            <!-- #endif -->
        </template>
        <view class="container">
            <view class="item_box" v-for="(item, index) in dataList" :key="index">
                <view class="top">{{ item.name }}</view>
                <view class="item">使用目的：{{ item.objective }}</view>
                <view class="item">收集场景：{{ item.scene }}</view>
                <view class="item">收集情况：{{ item.count }}条</view>
                <view class="item" v-if="item.collectType != 4 || item.code != 'positionInfo'">信息内容：<text class="btn" @click="handleClick(item)">点击查看</text></view>
            </view>
        </view>
    </z-paging>
    <yd-select-popup ref="selectPopupRef" title="请选择" :list="timeOption" @closePopup="closePopup" :selectId="[time]" />
</template>

<script setup>
import dayjs from "dayjs"
const paging = ref(null)
const dataList = ref([])
const pageTitle = ref("")
const pageCollectType = ref("")
const selectPopupRef = ref(null)
const time = ref(7)
const typeCode = ref("")

const timeText = {
    7: "近7天",
    30: "近30天",
    90: "近90天",
    180: "近180天"
}

const timeOption = [
    {
        value: 7,
        label: "近7天"
    },
    {
        value: 30,
        label: "近30天"
    },
    {
        value: 90,
        label: "近90天"
    },
    {
        value: 180,
        label: "近180天"
    }
]

// 前几天天日期区间（自定义）
function getLastDaysRange(num) {
    const sevenDaysAgo = dayjs().subtract(num, "day")
    return sevenDaysAgo.format("YYYY-MM-DD")
}

function closePopup(val) {
    if (!val || val.value == time.value) return
    time.value = val.value
    paging.value?.reload()
}

onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    typeCode.value = options.typeCode
    pageCollectType.value = options.typeCode
    pageTitle.value = options.typeName
    nextTick(() => {
        paging.value?.reload()
    })
})

function clickLeft() {
    uni.navigateBack()
}

const queryList = async () => {
    try {
        const params = {
            createStartTime: getLastDaysRange(time.value),
            createEndTime: dayjs().format("YYYY-MM-DD"),
            typeCode: typeCode.value
        }
        const { data } = await http.post("/app/userInfoCollect/getCollectListByType", params)
        paging.value.setLocalPaging(data)
    } catch (err) {
        paging.value.complete(false)
    }
}

const handleClick = (item) => {
    const urlObj = {
        1: "/package/my/myInfo/index",
        2: "/package/my/myInfo/index",
        3: "/package/my/searchInfo/deviceInfo",
        4: "/package/my/searchInfo/loginLog"
    }
    navigateTo({
        url: urlObj[item.collectType],
        query: {
            time: ["3", "4"].includes(item.collectType) ? time.value : 0,
            code: item.code
        }
    })
}
</script>

<style scoped lang="scss">
.container {
    padding: 20rpx 20rpx;
    background: #f9faf9;
    .item_box {
        padding: 0 30rpx;
        padding-bottom: 30rpx;
        background: $uni-bg-color;
        border-radius: 20rpx;
        box-shadow: 0rpx 16rpx 16rpx 0rpx rgba(191, 191, 191, 0.1);
        margin-bottom: 20rpx;
        .top {
            font-size: 30rpx;
            font-weight: 600;
            height: 110rpx;
            line-height: 110rpx;
            border-bottom: 2rpx solid #d8d8d8;
        }
        .item {
            font-size: 26rpx;
            padding-top: 20rpx;
            .btn {
                color: var(--primary-color);
            }
        }
    }
}
.time {
    font-size: 28rpx;
    color: var(--primary-color);
    font-weight: 400;
    line-height: 110rpx;
    // #ifdef MP-WEIXIN
    background: #f9faf9;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding-right: 20rpx;
    //  #endif
}
</style>
