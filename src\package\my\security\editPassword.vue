<template>
    <yd-page-view pageBackground="#fff" :title="phone ? '设置新密码' : '修改密码'" leftIcon="left">
        <view class="edit_password_page">
            <view v-if="phone" class="new_password">
                <text class="phone_title">设置新密码</text>
                <text class="phone_content">系统监测到您的账号密码安全系数较弱，请设置新密码。</text>
            </view>
            <div class="user_phone">当前手机号:{{ phone || userInfo.phone }}</div>
            <!-- 修改手机号 -->
            <uni-forms :modelValue="formData" ref="changePhoneForm" autocomplete="off" :rules="rules" :border="true">
                <uni-forms-item name="verifyCode">
                    <div class="verification_code">
                        <uni-easyinput label="" :clearable="false" v-model="formData.verifyCode" :inputBorder="false" type="digit" placeholder="请输入验证码">
                            <template #right>
                                <a
                                    @click="sendVerificationCode"
                                    :style="{
                                        color: countdown > 0 ? '#CCCCCC' : 'var(--primary-color)'
                                    }"
                                >
                                    {{ countdown > 0 ? `${countdown}秒后重新发送` : "发送验证码" }}</a
                                >
                            </template>
                        </uni-easyinput>
                    </div>
                </uni-forms-item>
                <uni-forms-item label="" name="newPwd">
                    <div class="form">
                        <!-- #ifdef MP-WEIXIN -->
                        <input class="input placeholder_size" placeholder-style="font-size: 20rpx;" v-model="formData.newPwd" placeholder="请输入包含字母、数字、特殊字符，长度为8-20个字符的密码" :password="!isEyeShow" />
                        <!-- #endif -->
                        <!-- #ifdef APP-PLUS || H5 -->
                        <input class="input placeholder_size" placeholder-style="font-size: 20rpx;" v-model="formData.newPwd" placeholder="请输入包含字母、数字、特殊字符，长度为8-20个字符的密码" type="text" v-if="isEyeShow" />
                        <input class="input placeholder_size" placeholder-style="font-size: 20rpx;" v-model="formData.newPwd" placeholder="请输入包含字母、数字、特殊字符，长度为8-20个字符的密码" type="password" v-else />
                        <!-- #endif -->
                        <div @click="isEyeShow = !isEyeShow" class="eye">
                            <image src="@nginx/login/openPassword.png" class="Fill" v-if="!isEyeShow" />
                            <image src="@nginx/login/closePassword.png" class="Fill" v-else />
                        </div>
                    </div>
                </uni-forms-item>
                <uni-forms-item label="" name="confirmPwd" style="border-bottom: 1px #eee solid">
                    <div class="form">
                        <!-- #ifdef MP-WEIXIN -->
                        <input class="input placeholder_size" placeholder-style="font-size: 20rpx;" v-model="formData.confirmPwd" placeholder="请再次输入新密码" :password="!isEyeConfirmShow" :maxlength="20" />
                        <!-- #endif -->
                        <!-- #ifdef APP-PLUS || H5 -->
                        <input class="input placeholder_size" placeholder-style="font-size: 20rpx;" v-model="formData.confirmPwd" placeholder="请再次输入新密码" type="text" v-if="isEyeConfirmShow" :maxlength="20" />
                        <input class="input placeholder_size" placeholder-style="font-size: 20rpx;" v-model="formData.confirmPwd" placeholder="请再次输入新密码" type="password" v-else :maxlength="20" />
                        <!-- #endif -->
                        <div @click="isEyeConfirmShow = !isEyeConfirmShow" class="eye">
                            <image src="@nginx/login/openPassword.png" class="Fill" v-if="!isEyeConfirmShow" />
                            <image src="@nginx/login/closePassword.png" class="Fill" v-else />
                        </div>
                    </div>
                </uni-forms-item>
            </uni-forms>
            <view class="change_submit">
                <button class="change_button" :class="loading ? 'disabled_class' : ''" :disabled="loading" :loading="loading" @click="subChangePwd" block type="primary" native-type="submit">确认</button>
            </view>
        </view>
    </yd-page-view>
</template>

<script setup>
import useStore from "@/store"
import RSA from "@/utils/rsa.js"
import { onLoad } from "@dcloudio/uni-app"
const { user } = useStore()
// 用户信息
const userInfo = computed(() => user.userInfo)
const changePhoneForm = ref(null)
const phone = ref(null)

// 验证码倒计时
let countdown = ref(0)
const loading = ref(false)
const formData = ref({
    verifyCode: ""
})
const isEyeShow = ref(false)
const isEyeConfirmShow = ref(false)

const rules = ref({
    verifyCode: {
        rules: [
            {
                required: true,
                errorMessage: "验证码不能为空"
            }
        ]
    }
})

// 修改手机号
const subChangePwd = () => {
    // 表单验证
    changePhoneForm.value
        .validate()
        .then((res) => {
            const params = {
                phone: phone.value || userInfo.value.phone,
                verifyCode: formData.value.verifyCode,
                newPwd: formData.value.newPwd,
                confirmPwd: formData.value.confirmPwd
            }

            // 加密参数
            const encryptionPwd = {
                paramEncipher: RSA.encrypt(JSON.stringify(params))
            }
            loading.value = true
            http.post("/app/user/v3/updateNewPassword", encryptionPwd)
                .then((res) => {
                    formData.value = {}
                    uni.showToast({
                        title: res.message,
                        icon: "none"
                    })
                    setTimeout(() => {
                        uni.clearStorageSync()
                        navigateTo({ url: "/pages/login/index" })
                    }, 1000)
                })
                .finally(() => {
                    loading.value = false
                })
        })
        .catch((err) => {
            console.log("错误信息：", err)
        })
}

const sendVerificationCode = () => {
    if (countdown.value > 0) {
        return
    }

    http.post("/app/sms/external/message", {
        phone: phone.value || userInfo.value.phone
    }).then(({ data, message }) => {
        if (data) {
            // 发送验证码的过程
            uni.showToast({
                title: message,
                icon: "none",
                duration: 2000
            })

            countdown.value = 180
            const timer = setInterval(() => {
                countdown.value--
                if (countdown.value === 0) {
                    clearInterval(timer)
                }
            }, 1000)
        } else {
            uni.showToast({
                title: "验证码获取失败",
                icon: "none",
                duration: 2000
            })
            countdown.value = 0
        }
    })
}

onLoad((options) => {
    if (options && options.username) {
        phone.value = options.username
    }
})
</script>

<style lang="scss" scoped>
.edit_password_page {
    padding: 32rpx;
}
.form {
    position: relative;
    .eye {
        position: absolute;
        right: 0;
        bottom: -10rpx;
        z-index: 9;
        width: 100rpx;
        height: 100rpx;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .Fill {
        width: 40rpx;
        height: 40rpx;
    }
}
.new_password {
    display: flex;
    padding-bottom: 40rpx;
    flex-direction: column;
    .phone_title {
        font-weight: 400;
        font-size: 36rpx;
        color: #181818;
        padding-bottom: 10rpx;
    }
    .phone_content {
        font-weight: 400;
        font-size: 28rpx;
        color: #666666;
        line-height: 40rpx;
    }
}
.user_phone {
    font-weight: 600;
    font-size: 32rpx;
    color: #181818;
    padding-bottom: 40rpx;
}

.change_submit {
    width: calc(100% - 64rpx);
    margin: 0rpx 32rpx;
    position: fixed;
    bottom: 32rpx;
    left: 0;
    .change_button {
        background: var(--primary-color);
    }
    .disabled_class {
        background: #ccc;
    }
}
.input {
    height: 80rpx;
    outline: none;
    color: $uni-text-color;
    line-height: 80rpx;
    padding-right: 100rpx;
    // #ifdef MP-WEIXIN
    display: inline-block;
    white-space: nowrap;
    width: calc(100% - 100rpx);
    overflow: hidden;
    text-overflow: ellipsis;
    // #endif
    // #ifdef H5 || APP-PLUS
    :deep(.input-placeholder) {
        display: inline-block;
        white-space: nowrap;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    // #endif
}
.placeholder_size {
    // #ifdef H5 || APP-PLUS
    :deep(.input-placeholder) {
        font-size: 20rpx;
        color: #999;
    }
    // #endif
}

.verification_code {
    :deep(.uni-input-placeholder) {
        font-size: 20rpx;
        color: #999;
        margin-left: -10px;
    }
    :deep(.uni-input-input) {
        margin-left: -10px;
    }
}
</style>
