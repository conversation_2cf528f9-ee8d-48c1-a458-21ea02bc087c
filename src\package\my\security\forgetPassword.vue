<template>
    <yd-page-view pageBackground="#fff" class="forget_password" :title="formType == 'setPassword' ? '设置密码' : '忘记密码'">
        <template #left>
            <uni-icons @click="barLeft" type="left" size="18"></uni-icons>
        </template>
        <div class="box">
            <div v-if="formType == 'getCode'">
                <div class="login_form">
                    <div class="form">
                        <input :maxlength="11" type="number" class="input" placeholder="请输入手机号" v-model="getCodeForm.phone" />
                    </div>
                    <div class="form form_code">
                        <input :maxlength="8" class="input" v-model="getCodeForm.code" placeholder="请输入验证码" type="number" />
                        <div class="verification_code" v-if="codeNum != 180">{{ codeNum }} 秒</div>
                        <div @click="handleCode" v-else class="verification_code">获取验证码</div>
                    </div>
                </div>
                <div class="btn_class">
                    <button type="default" @click="toSetPassword" class="button" :class="isCodeDisabled ? 'disabled_class' : ''" :loading="isCodeLoading" :disabled="isCodeDisabled || isCodeLoading">确认</button>
                </div>
            </div>
            <div v-if="formType == 'setPassword'">
                <div class="login_form">
                    <div class="form">
                        <!-- #ifdef MP-WEIXIN -->
                        <input class="input placeholder_size" placeholder-style="font-size: 20rpx;" v-model="setPasswordForm.password" placeholder="请输入包含字母、数字、特殊字符，长度为8-20个字符的密码" :password="!isEyeShow" />
                        <!-- #endif -->
                        <!-- #ifdef APP-PLUS || H5 -->
                        <input class="input placeholder_size" v-model="setPasswordForm.password" placeholder="请输入包含字母、数字、特殊字符，长度为8-20个字符的密码" v-if="isEyeShow" type="text" />
                        <input class="input placeholder_size" v-else v-model="setPasswordForm.password" placeholder="请输入包含字母、数字、特殊字符，长度为8-20个字符的密码" type="password" />
                        <!-- #endif -->
                        <div @click="isEyeShow = !isEyeShow" class="eye">
                            <image src="@nginx/login/openPassword.png" class="Fill" v-if="!isEyeShow" />
                            <image src="@nginx/login/closePassword.png" class="Fill" v-else />
                        </div>
                    </div>
                    <div class="form form_code">
                        <!-- #ifdef MP-WEIXIN -->
                        <input class="input placeholder_size" placeholder-style="font-size: 20rpx;" v-model="setPasswordForm.confirmPwd" placeholder="请再次输入密码" :password="!isEyeConfirmShow" />
                        <!-- #endif -->
                        <!-- #ifdef APP-PLUS || H5 -->
                        <input class="input placeholder_size" v-model="setPasswordForm.confirmPwd" placeholder="请再次输入密码" v-if="isEyeConfirmShow" type="text" />
                        <input class="input placeholder_size" v-else v-model="setPasswordForm.confirmPwd" placeholder="请再次输入密码" type="password" />
                        <!-- #endif -->
                        <div @click="isEyeConfirmShow = !isEyeConfirmShow" class="eye">
                            <image src="@nginx/login/openPassword.png" class="Fill" v-if="!isEyeConfirmShow" />
                            <image src="@nginx/login/closePassword.png" class="Fill" v-else />
                        </div>
                    </div>
                </div>
                <div class="btn_class">
                    <button type="default" @click="confirmSetPassword" class="button" :class="isPasswordDisabled ? 'disabled_class' : ''" :loading="isPasswordLoading" :disabled="isPasswordDisabled || isPasswordLoading">确认</button>
                </div>
            </div>
        </div>
    </yd-page-view>
</template>

<script setup>
import RSA from "@/utils/rsa.js"

const codeNum = ref(180) // 验证码倒计时
const isEyeShow = ref(false)
const isEyeConfirmShow = ref(false)
let time = null
const isPasswordLoading = ref(false)
const formType = ref("getCode") // getCode 获取验证码，setPassword设置密码
// 获取验证码表单
const getCodeForm = ref({
    phone: "",
    code: ""
})
// 设置密码表单
const setPasswordForm = ref({
    password: "",
    confirmPwd: ""
})

const isCodeLoading = ref(false)

// 是否禁用获取验证码确认按钮
const isCodeDisabled = computed(() => {
    return !getCodeForm.value.code || !getCodeForm.value.phone
})

// 是否禁用设置密码确认按钮
const isPasswordDisabled = computed(() => {
    return !setPasswordForm.value.password || !setPasswordForm.value.confirmPwd || setPasswordForm.value.password !== setPasswordForm.value.confirmPwd
})

// 返回按钮
function barLeft() {
    // 如果在设置密码页面点击返回 则返回获取验证码页面 否则就是返回上一级路由
    if (formType.value == "setPassword") {
        formType.value = "getCode"
    } else {
        uni.navigateBack({ delta: 1 })
    }
}

// 验证是否可以点击确认进入设置密码页面
function toSetPassword() {
    const { phone, code } = getCodeForm.value
    if (phone && phone.length == 11 && code) {
        isCodeLoading.value = true
        // 进入设置密码页面
        formType.value = "setPassword"
    } else {
        isCodeLoading.value = false
        uni.showToast({
            title: "请输入正确的手机号或验证码！",
            icon: "none",
            duration: 2000
        })
    }
}

// 修改密码接口
function confirmSetPassword() {
    const params = { paramEncipher: "" }
    const { phone, code } = getCodeForm.value
    const obj = { phone, verifyCode: code, password: setPasswordForm.value.confirmPwd }
    params.paramEncipher = RSA.encrypt(JSON.stringify(obj))
    isPasswordLoading.value = true
    http.post("/app/user/forgetPassword", params)
        .then((res) => {
            formType.value = "getCode"
            uni.showToast({
                title: res.message,
                icon: "none"
            })
            setTimeout(() => {
                uni.clearStorageSync()
                navigateTo({ url: "/pages/login/index" })
            }, 1000)
        })
        .finally(() => {
            isPasswordLoading.value = false
        })
}

// 获取验证码
function handleCode() {
    const { phone } = getCodeForm.value
    if (phone && phone.length == 11) {
        http.post("/app/sms/external/message", { phone }).then((res) => {
            clearInterval(time)
            time = setInterval(() => {
                if (codeNum.value <= 1) {
                    clearInterval(time)
                    codeNum.value = 180
                } else {
                    codeNum.value--
                }
            }, 1000)
        })
    } else {
        uni.showToast({
            title: "请输入正确的手机号！",
            duration: 1000,
            icon: "none"
        })
    }
}
onMounted(() => {
    isPasswordLoading.value = false
})
</script>

<style lang="scss" scoped>
.forget_password {
    background: $uni-bg-color;

    .box {
        padding-top: 44rpx;

        .login_form {
            padding: 0rpx 44rpx;
            background: $uni-bg-color;
            width: 100%;
            box-sizing: border-box;
            position: relative;

            .form {
                position: relative;

                .verification_code {
                    position: absolute;
                    right: 0;
                    bottom: -10rpx;
                    z-index: 9;
                    width: 150rpx;
                    height: 100rpx;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: var(--primary-color);
                    font-size: 28rpx;
                }

                .eye {
                    position: absolute;
                    right: 0;
                    bottom: -10rpx;
                    z-index: 9;
                    width: 100rpx;
                    height: 100rpx;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                .Fill {
                    width: 40rpx;
                    height: 40rpx;
                }
            }

            .form_code {
                margin-top: 58rpx;
            }

            .input {
                border-bottom: 1rpx solid $uni-border-color;
                height: 80rpx;
                outline: none;
                color: $uni-text-color;
                line-height: 80rpx;
                padding-right: 100rpx;
                // #ifdef MP-WEIXIN
                display: inline-block;
                white-space: nowrap;
                width: calc(100% - 100rpx);
                overflow: hidden;
                text-overflow: ellipsis;
                // #endif
                // #ifdef H5 || APP-PLUS
                :deep(.input-placeholder) {
                    display: inline-block;
                    white-space: nowrap;
                    width: 100%;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
                // #endif
            }
            .placeholder_size {
                // #ifdef H5 || APP-PLUS
                :deep(.input-placeholder) {
                    font-size: 20rpx;
                }
                // #endif
            }
        }

        .btn_class {
            position: fixed;
            height: 100rpx;
            bottom: 40rpx;
            left: 0;
            width: calc(100vw - 88rpx);
            padding: 0rpx 44rpx;

            .button {
                width: 100%;
                background: var(--primary-color);
                color: $uni-text-color-inverse;
            }

            .disabled_class {
                background: #ccc;
            }
        }
    }
}
</style>
