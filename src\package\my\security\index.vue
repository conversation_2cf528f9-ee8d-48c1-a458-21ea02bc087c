<template>
    <yd-page-view class="security" title="安全中心" leftIcon="left">
        <uni-list :border="false">
            <uni-list-item v-for="item in list" :key="item.code" showArrow :title="item.title" @click="goPage(item)" clickable></uni-list-item>
        </uni-list>
    </yd-page-view>
</template>

<script setup>
const list = [
    {
        code: "editPassword",
        title: "修改密码"
    },
    {
        code: "forgetPassword",
        title: "忘记密码"
    },
    {
        code: "editPhone",
        title: "修改手机号"
    }
]

function goPage(item) {
    const routerUrl = {
        forgetPassword: "/package/my/security/forgetPassword", // 修改密码
        editPassword: "/package/my/security/editPassword", // 忘记密码
        editPhone: "/package/my/security/editPhone" // 修改手机号
    }
    navigateTo({
        url: routerUrl[item.code]
    })
}
</script>

<style lang="scss" scoped>
.security {
    min-height: 100vh;
    background: $uni-bg-color-grey;
}
</style>
