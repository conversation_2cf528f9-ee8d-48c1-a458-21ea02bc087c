<template>
    <yd-page-view class="settingsPage" title="设置" leftIcon="left">
        <div class="lineBox"></div>
        <uni-list :border="false">
            <!-- TODO: 暂不显示，有空要补上
            <uni-list-item title="消息通知" showArrow clickable> </uni-list-item>
            <uni-list-item title="清理缓存" showArrow clickable> </uni-list-item> -->
            <uni-list-item title="版本号" showArrow clickable>
                <template v-slot:footer>
                    <view class="version">V{{ set.version }}</view>
                </template>
            </uni-list-item>
            <uni-list-item title="隐私政策" showArrow clickable to="/package/my/privacyPolicy/privacyPolicy"> </uni-list-item>
            <uni-list-item title="个人信息查询" showArrow clickable @click="handleInfo"> </uni-list-item>
            <uni-list-item title="注销账号" link="navigateTo" @click="handleLogout" showArrow clickable> </uni-list-item>
        </uni-list>
        <div class="lineBox"></div>
        <view class="quitBox" @click="quitLogin"> 退出登录 </view>
    </yd-page-view>
</template>

<script setup>
import useStore from "@/store"
import set from "/package.json"
const { system } = useStore()
const quitLogin = () => {
    uni.showModal({
        title: "提示",
        content: "确定要退出登录吗？",
        confirmColor: "#00b781",
        cancelColor: "#999",
        confirmText: "确定",
        success(res) {
            if (res.confirm) {
                clearStore()
                uni.clearStorageSync()
                system.setPrimaryColor("var(--primary-color)")
                uni.redirectTo({ url: "/pages/login/index" })
            }
        }
    })
}

const handleLogout = async () => {
    await http.get("/app/user/validUserLogout")
    navigateTo({
        url: "/package/my/settings/logout/index"
    })
}

const handleInfo = () => {
    navigateTo({
        url: "/package/my/searchInfo/index"
    })
}
</script>

<style scoped lang="scss">
.lineBox {
    height: 20rpx;
    background: #f0f2f5;
}
.quitBox {
    height: 104rpx;
    background: $uni-bg-color;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 400;
    font-size: 32rpx;
    color: #333333;
}

.version {
    font-weight: 400;
    font-size: 32rpx;
    color: #999999;
    line-height: 44rpx;
    text-align: center;
    font-style: normal;
}
</style>
