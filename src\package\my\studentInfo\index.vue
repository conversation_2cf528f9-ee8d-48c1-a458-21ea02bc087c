<template>
    <div class="student_info">
        <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="routerBack" title="学生信息" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
        <z-paging ref="paging" use-virtual-list use-page-scroll v-model="dataList" @query="queryList">
            <div class="list">
                <div class="item" v-for="(item, index) in dataList" :key="index">
                    <div class="top">
                        <div class="avatar">
                            <image class="avatar_img" :src="item.avatar || '@nginx/components/student.png'" alt="" />
                        </div>
                        <div class="info">
                            <span class="name">
                                {{ item.name }}
                            </span>
                            <span class="sex"> {{ { 0: "女", 1: "男" }[item.gender] }}/{{ item.className }}</span>
                        </div>
                    </div>
                    <div class="bottom">
                        <div class="is_gather">
                            人脸信息：
                            <uni-icons :type="item.isGather ? 'checkbox-filled' : 'info-filled'" :color="item.isGather ? 'var(--primary-color)' : '#f0ad4e'" size="18" style="margin-top: 10rpx"></uni-icons>
                            {{ item.isGather ? "采集成功" : "未采集" }}
                        </div>
                        <div class="btn" :class="item.isGather ? 'btn_gather' : 'btn_re_gather'" @click="gatherFace(item)">
                            {{ item.isGather ? "重新采集" : "点击采集" }}
                        </div>
                    </div>
                </div>
            </div>
            <template #empty>
                <yd-empty text="暂无数据" />
            </template>
        </z-paging>
        <!-- 二次确认人脸协议弹框 -->
        <yd-face ref="facePopupRef" />
    </div>
</template>

<script setup>
import useStore from "@/store"

const { user, local } = useStore()
const identityInfo = computed(() => user.identityInfo) // 角色信息
const facePopupRef = ref(false) // 人脸采集弹框
const dataList = ref([])
const paging = ref(null)

// 图片列表
function queryList() {
    http.get("/app/student/getFaceList", { identityId: identityInfo.value.id }).then(({ data }) => {
        paging.value.complete(data)
    })
}

function gatherFace(item) {
    if (!local.agreeFace) {
        facePopupRef.value.open()
    } else {
        navigateTo({
            url: "/package/my/face/index",
            query: {
                id: item.id
            }
        })
    }
}
</script>

<style lang="scss" scoped>
.student_info {
    min-height: 100vh;
    background: $uni-bg-color-grey;

    .list {
        padding: 20rpx 20rpx;
        .item {
            padding: 30rpx;
            min-height: 100rpx;
            background: $uni-bg-color;
            border-radius: 20rpx;
            margin-bottom: 20rpx;
            display: flex;
            box-shadow: 0rpx 0rpx 4rpx 4rpx #eee;
            flex-direction: column;
            .top {
                display: flex;
                .avatar {
                    width: 100rpx;
                    height: 100rpx;
                    .avatar_img {
                        width: 100%;
                        height: 100%;
                        border-radius: 20rpx;
                    }
                }
                .info {
                    display: flex;
                    flex-direction: column;
                    margin-left: 20rpx;
                    color: $uni-text-color;
                    justify-content: space-between;
                    padding: 4rpx 0;
                    .name {
                        font-weight: 500;
                        font-size: 30rpx;
                        line-height: 42rpx;
                    }
                    .sex {
                        font-weight: 400;
                        font-size: 24rpx;
                        line-height: 34rpx;
                    }
                }
            }
            .bottom {
                margin-top: 34rpx;
                display: flex;
                justify-content: space-between;
                align-items: center;
                .is_gather {
                    font-weight: 400;
                    font-size: 24rpx;
                    color: #999999;
                    line-height: 34rpx;
                }
                .btn {
                    width: 144rpx;
                    height: 60rpx;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                .btn_gather {
                    background: $uni-bg-color;
                    border-radius: 8rpx;
                    border: 1rpx solid var(--primary-color);
                    font-weight: 400;
                    font-size: 24rpx;
                    color: var(--primary-color);
                }
                .btn_re_gather {
                    background: var(--primary-color);
                    border-radius: 8rpx;
                    font-weight: 400;
                    font-size: 24rpx;
                    color: $uni-text-color-inverse;
                }
            }
        }
    }
}
</style>
