<!--
 * @Description: 动态应用
 * @Date: 2024-11-11 18:05:46
 * @LastEditors: lhq
 * @LastEditTime: 2024-11-12 09:43:16
 * @FilePath: \code\cloud-mobile\src\pages\dynamic\index.vue
-->

<template>
    <yd-page-view class="my" title="日程" :hideBottom="false">
        <!-- 日程 -->
        <Schedule></Schedule>
        <!--  -->
    </yd-page-view>
</template>

<script setup>
import Schedule from "@/apps/schedule"
// 此页面这样设计是为了考虑 这个应用能够使动态的显示在首页，配合store里的dynamicAppName，并且在工作台进入日程没有tabbar
</script>

<style lang="scss" scoped></style>
