<template>
    <role v-if="state.componentId == 'role'" />
    <teacher-role v-if="state.componentId == 'teacherRole'" />
    <headmaster-role v-if="state.componentId == 'headmasterRole'" />
</template>

<script setup>
import role from "@/package/home/<USER>/role.vue"
import TeacherRole from "@/package/home/<USER>/teacherRole.vue"
import HeadmasterRole from "@/package/home/<USER>/headmasterRole.vue"
import useStore from "@/store"
const { user } = useStore()

// TODO: 班主任有切换班级功能,学生考勤需要传入班级id,任课老师不需要传学生id
const roleCode = computed(() => user?.identityInfo?.roleCode)

const state = reactive({
    componentId: null
})

function initPage() {
    if (roleCode.value == "headmaster") {
        // 班主任
        state.componentId = "headmasterRole"
    } else if (roleCode.value == "teacher") {
        // 任教老师
        state.componentId = "teacherRole"
    } else {
        state.componentId = "role"
    }
}

onMounted(() => {
    initPage()
})
</script>

<style lang="scss" scoped>
.container {
}
</style>
