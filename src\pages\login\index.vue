<template>
    <view class="content">
        <cloud-login @forgetPassword="forgetPassword" @logIn="logIn" v-if="loginType == 'cloud'" :loading="submitLoading" />
        <neutral-login @forgetPassword="forgetPassword" v-if="loginType == 'neutral'" :websiteData="websiteData" @logIn="logIn" :loading="submitLoading" />
        <uni-popup ref="alertDialog" type="dialog">
            <uni-popup-dialog type="info" cancelText="关闭" confirmText="同意" title="通知" :content="`您的账号已于${state.logoutTime}提交注销申请，需要撤销后才可登录，是否撤销？`" @confirm="dialogConfirm"></uni-popup-dialog>
        </uni-popup>
        <!-- #ifdef MP-WEIXIN -->
        <t-captcha id="captcha" :app-id="captchaId" @verify="handlerVerify" />
        <!-- #endif -->
    </view>
</template>

<script setup>
import RSA from "@/utils/rsa.js"

import { setToken, getToken } from "@/utils/storageToken.js"
import NeutralLogin from "@/package/login/components/neutralLogin.vue"
import CloudLogin from "@/package/login/components/cloudLogin.vue"
import useStore from "@/store"

const { ctx } = getCurrentInstance()
let captchaId = "193884511"

// 登录按钮loading
const submitLoading = ref(false)
// 默认值null，cloud为一加壹版本， neutral为中性版
const loginType = ref(null)
// 中性版数据
const websiteData = ref({})
// 滑块验证数据
const randStrTicket = reactive({
    randStr: "",
    ticket: ""
})
// 登录的账号密码表单
const loginForm = reactive({
    username: "",
    password: "",
    grant_type: "password", // 授权模式，默认password
    client_id: "yide-h5", //客户端ID: yide-cloud云平台、yide-manage一德后台、yide-open开放平台 移动端H5: yide-h5
    client_secret: "yide1234567" // 客户端密钥
})
const alertDialog = ref(null) // 账号已注销弹框
const state = reactive({
    logoutTime: null // 账号已被注销的时间
})

// 注销账号弹框
const dialogConfirm = () => {
    return new Promise((resolve) => {
        alertDialog.value.close()
        resolve(true)
    })
}

function forgetPassword() {
    navigateTo({
        url: "/package/my/security/forgetPassword"
    })
}

// 校验账号是否已被注销
async function getCheckUserLogoutInfo() {
    http.get("/app/user/checkUserLogout").then(({ data }) => {
        let { isLogout, logoutTime, isInitializePwd, logoutStatus, isAdmin } = data
        // isInitializePwd = true
        // 首先判断是不是已经注销的账号,如果是注销的账号要么他登录不了,要么他自己撤销了 再点一次登录,
        if (isLogout) {
            state.logoutTime = logoutTime
            dialogConfirm().then(() => {
                // on confirm
                http.get("/app/user/cancelLogout").then(({ message }) => {
                    uni.clearStorageSync()
                })
            })
        } else {
            // logoutStatus （枚举）：
            // (1, "校验正常"),
            // (2, "已注销"),
            // (3, "初始化密码（第一次登录）"),
            // (4, "密码更新时间超出限制"),
            // (5, "弱密码"),
            state.isAdmin = false
            if ([3, 4, 5].includes(logoutStatus)) {
                navigateTo({
                    url: "/package/my/security/editPassword",
                    query: {
                        username: loginForm.username
                    }
                })
            } else {
                // 账号正常,又不是初始密码 直接让他登录就好了
                loginForm.username = ""
                loginForm.password = ""
                uni.navigateTo({
                    url: "/package/login/selectIdentity"
                })
            }
        }
    })
}
// 登录获取token
function getTokenLogIn() {
    console.log("loginForm:", loginForm)
    // 加密表单数据
    const paramEncipher = RSA.encrypt(JSON.stringify({ ...loginForm, ...randStrTicket }))
    http._fetch("/auth/oauth/token", { paramEncipher }, "POST", { "Content-Type": "application/x-www-form-urlencoded" })
        .then((res) => {
            submitLoading.value = false
            setToken(res.data.accessToken)
            getCheckUserLogoutInfo()
        })
        .finally(() => {
            submitLoading.value = false
        })
}

// 定义回调函数
function callback(res) {
    // ret 验证结果，0：验证成功。2：用户主动关闭验证码。
    if (res.ret == 0) {
        const str = `【randstr】->【${res.randstr}'】      【ticket】->【'${res.ticket}'】`
        const ipt = document.createElement("input")
        ipt.value = str
        document.body.appendChild(ipt)
        ipt.select()
        document.body.removeChild(ipt)
        randStrTicket.randStr = res.randstr
        randStrTicket.ticket = res.ticket
        // 获取token登录流程
        getTokenLogIn()
    } else {
        submitLoading.value = false
    }
}

// 定义验证码js加载错误处理函数
function loadErrorCallback() {
    // 生成容灾票据或自行做其它处理
    // eslint-disable-next-line node/no-callback-literal
    callback({
        ret: 0,
        randstr: "193884511", // 本次验证的随机串，后续票据校验时需传递该参数。
        ticket: "jPH4ltECNN6xlmAWGvqf34X4X", // 验证成功的票据，当且仅当 ret = 0 时 ticket 有值。
        CaptchaType: 1,
        errorCode: 1001,
        errorMessage: "jsload_error"
    })
}

// 验证码验证结果回调
const handlerVerify = (ev) => {
    console.log("ev:", ev)
    if (ev.detail.ret === 0) {
        // 验证成功
        randStrTicket.randStr = captchaId
        randStrTicket.ticket = ev.detail.ticket
        getTokenLogIn()
    } else {
        console.log("验证失败", ev.detail)
        // 验证失败
        // 请不要在验证失败中调用refresh，验证码内部会进行相应处理
    }
}

// 点击登录按钮
const logIn = (form) => {
    if (form && form.username && form.password) {
        submitLoading.value = true
        loginForm.username = form.username // 登录账号
        loginForm.password = form.password // 登录密码
        // #ifdef APP-PLUS
        // APP滑块验证页面
        navigateTo({
            url: "/package/login/varify",
            events: {
                varify: (res) => {
                    randStrTicket.randStr = res.randstr
                    randStrTicket.ticket = res.ticket
                    // 获取token登录流程
                    getTokenLogIn()
                }
            }
        })
        // #endif
        // #ifdef H5-WEIXIN || H5
        // H5页面调用滑块验证
        try {
            const captcha = new TencentCaptcha("193884511", callback, {
                needFeedBack: false
            })
            // 调用方法，显示验证码
            captcha.show()
        } catch (error) {
            // 加载异常，调用验证码js加载错误处理函数
            loadErrorCallback()
        }
        // #endif
        //  #ifdef MP-WEIXIN
        try {
            ctx.selectComponent("#captcha").show()
            submitLoading.value = true
            // 滑块验证
            // handlerVerify()
            //  没有滑块验证的原来方法
            // getTokenInfo()
        } catch (e) {
            console.log("登录异常：", e)
            console.log("重新刷新")
            submitLoading.value = false
            ctx.selectComponent("#captcha").refresh()
        }
        // #endif
    } else {
        uni.showToast({
            title: "请输入正确的账号密码！",
            icon: "none",
            duration: 2000
        })
    }
}

// 设置浏览器的logo
function setIcon(url) {
    var link = document.querySelector("link[rel*='icon']") || document.createElement("link")
    link.type = "image/x-icon"
    link.href = url
    link.rel = "shortcut icon"
    document.getElementsByTagName("head")[0].appendChild(link)
}
// 获取是否为中性版，如果为中性版本，需要把数据给到组件
onBeforeMount(async () => {
    // #ifdef H5 || H5-WEIXIN
    http._fetch("/app/manage/website/config/getInfo", JSON.stringify({ uri: window.location.hostname }), "POST", {
        "Content-Type": "application/json"
    })
        .then((res) => {
            // 设置中性版页面数据
            if (res && res.data) {
                loginType.value = res.data.isCloud ? "cloud" : "neutral"
                websiteData.value = res.data || {}
                uni.setNavigationBarTitle({
                    title: res.data.websiteMobileName || "一加壹"
                })
                // #ifdef H5-WEIXIN || H5
                // 设置浏览器的logo（小程序无需）
                setIcon(res.data.websiteLogo || "@nginx/login/logo.png")
                // #endif
            } else {
                loginType.value = "cloud"
            }
        })
        .catch(() => {
            loginType.value = "cloud"
        })
    // #endif
    // #ifdef MP-WEIXIN
    loginType.value = "cloud"
    // #endif
})

onLoad(() => {
    const { user, system } = useStore()
    const token = getToken()
    // 判断如果有token，有身份id，学校id，用户id，则直接进入home页面
    if (token && user.identityInfo?.id && user.schoolInfo?.id && user.userInfo?.id) {
        setTimeout(() => {
            uni.reLaunch({ url: system.tabBarList[0]?.pagePath })
        }, 500)
    }
})
</script>

<style scoped lang="scss"></style>
