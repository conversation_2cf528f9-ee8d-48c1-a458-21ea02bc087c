<template>
    <view class="share_page">
        <view class="page">
            <uv-skeletons :loading="true" :animate="true" :skeleton="skeleton"></uv-skeletons>
        </view>
    </view>
</template>

<script setup>
import useStore from "@/store"
import { getToken } from "@/utils/storageToken.js"

const skeleton = [
    {
        type: "line",
        num: 30,
        gap: "20rpx",
        style: ["width: 200rpx;marginBottom: 10rpx;", "height: 200rpx;", "width: 500rpx;"]
    }
]

const myToken = getToken()
const { local, user } = useStore()

const type = {
    groupDetail: "/apps/clubManage/groupDetail/index" // 社团分享的路由
}

onLoad((option) => {
    Object.keys(option).forEach((key) => {
        option[key] = decodeURIComponent(option[key])
    })
    // 如果有分享的类型和token就可以直接进入
    if (option.type && myToken && user.identityInfo.id) {
        navigateTo({
            url: type[option.type],
            query: option
        })
    } else {
        uni.showToast({
            title: "请先登录",
            icon: "none"
        })
        uni.clearStorageSync()
        setTimeout(() => {
            local.setShare(true, option)
            uni.reLaunch({ url: "/pages/login/index" })
        }, 1000)
    }
})
</script>

<style lang="scss" scoped>
.share_page {
    height: calc(100vh - 60rpx);
    padding: 30rpx;
    overflow: hidden;
    .page {
        height: 100%;
        margin-bottom: 30rpx;
        overflow: hidden;
    }
}
</style>
