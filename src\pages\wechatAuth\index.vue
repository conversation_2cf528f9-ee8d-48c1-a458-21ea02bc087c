<template>
  <view class="wechat-auth-container">
    <view class="loading-wrapper">
      <view class="loading-icon">
        <text class="loading-text">{{ loadingText }}</text>
      </view>
      <view class="loading-spinner"></view>
    </view>

    <view v-if="errorMessage" class="error-message">
      <text>{{ errorMessage }}</text>
      <view class="retry-button" @click="retryAuth">
        <text>重新授权</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// 响应式数据
const errorMessage = ref('')
const loadingText = ref('正在处理微信授权...')

/**
 * 获取URL参数（支持hash路由）
 * @param {string} name - 参数名
 * @returns {string|null} 参数值
 */
const getUrlParam = (name) => {
  // 先尝试从hash中获取参数
  const hash = window.location.hash
  if (hash.includes('?')) {
    const hashParams = new URLSearchParams(hash.split('?')[1])
    const value = hashParams.get(name)
    if (value) return value
  }

  // 再从search中获取参数
  const urlParams = new URLSearchParams(window.location.search)
  return urlParams.get(name)
}

/**
 * 跳转到微信授权页面
 */
const redirectToWechatAuth = () => {
  try {
    // 获取appid和backurl参数
    const appid = getUrlParam('appid')
    const backurl = getUrlParam('backurl')

    console.log('获取到的参数:', { appid, backurl })

    // 验证必要参数
    if (!appid) {
      errorMessage.value = '缺少appid参数，请检查授权链接'
      console.error('缺少appid参数')
      return
    }

    if (!backurl) {
      errorMessage.value = '缺少backurl参数，请检查授权链接'
      console.error('缺少backurl参数')
      return
    }

    loadingText.value = '正在跳转到微信授权页面...'

    // 构建微信授权URL
    const redirectUri = encodeURIComponent('https://mcloud.yyide.com/#/wechatAuth')
    const state = encodeURIComponent(backurl)
    const wechatAuthUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appid}&redirect_uri=${redirectUri}&response_type=code&scope=snsapi_base&state=${state}`

    console.log('准备跳转到微信授权页面:', wechatAuthUrl)

    // 跳转到微信授权页面
    window.location.href = wechatAuthUrl

  } catch (error) {
    console.error('跳转微信授权页面时发生错误:', error)
    errorMessage.value = '跳转微信授权页面时发生错误，请重试'
  }
}

/**
 * 处理微信授权回调
 */
const handleWechatCallback = () => {
  try {
    // 获取微信回调的code参数
    const code = getUrlParam('code')
    // 获取state参数（包含目标回调地址）
    const state = getUrlParam('state')

    console.log('微信授权回调参数:', { code, state })

    // 验证必要参数
    if (!code) {
      console.log('未获取到code参数，可能是首次进入页面，准备跳转到微信授权')
      redirectToWechatAuth()
      return
    }

    if (!state) {
      errorMessage.value = '缺少回调地址参数，请检查授权链接'
      console.error('缺少state参数')
      return
    }

    loadingText.value = '授权成功，正在返回原页面...'

    // 解码state参数（回调地址）
    let targetUrl
    try {
      targetUrl = decodeURIComponent(state)
    } catch (e) {
      targetUrl = state
    }

    // 构建回调URL，将code参数传递给目标地址
    const separator = targetUrl.includes('?') ? '&' : '?'
    const callbackUrl = `${targetUrl}${separator}code=${code}`

    console.log('准备跳转到目标地址:', callbackUrl)

    // 延迟跳转，让用户看到成功提示
    setTimeout(() => {
      window.location.href = callbackUrl
    }, 1000)

  } catch (error) {
    console.error('处理微信授权回调时发生错误:', error)
    errorMessage.value = '处理授权回调时发生错误，请重试'
  }
}

/**
 * 重新授权
 */
const retryAuth = () => {
  errorMessage.value = ''
  loadingText.value = '正在处理微信授权...'
  handleWechatCallback()
}

/**
 * 页面加载完成后执行
 */
onMounted(() => {
  // 延迟执行，确保页面完全加载
  setTimeout(() => {
    handleWechatCallback()
  }, 500)
})
</script>

<style scoped>
.wechat-auth-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 40rpx;
}

.loading-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 40rpx;
}

.loading-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-text {
  font-size: 32rpx;
  color: #666;
  font-weight: 500;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e0e0e0;
  border-top: 4rpx solid #07c160;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  margin-top: 40rpx;
  padding: 20rpx 30rpx;
  background-color: #fff2f0;
  border: 2rpx solid #ffccc7;
  border-radius: 8rpx;
  color: #ff4d4f;
  font-size: 28rpx;
  text-align: center;
  max-width: 600rpx;
}
</style>
