<template>
  <view class="wechat-auth-container">
    <view class="loading-wrapper">
      <view class="loading-icon">
        <text class="loading-text">正在处理微信授权...</text>
      </view>
      <view class="loading-spinner"></view>
    </view>
    
    <view v-if="errorMessage" class="error-message">
      <text>{{ errorMessage }}</text>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// 响应式数据
const errorMessage = ref('')

/**
 * 获取URL参数
 * @param {string} name - 参数名
 * @returns {string|null} 参数值
 */
const getUrlParam = (name) => {
  const urlParams = new URLSearchParams(window.location.search)
  return urlParams.get(name)
}

/**
 * 处理微信授权回调
 */
const handleWechatCallback = () => {
  try {
    // 获取微信回调的code参数
    const code = getUrlParam('code')
    // 获取state参数（包含目标回调地址）
    const state = getUrlParam('state')
    
    console.log('微信授权回调参数:', { code, state })
    
    // 验证必要参数
    if (!code) {
      errorMessage.value = '未获取到微信授权码，请重新授权'
      console.error('缺少code参数')
      return
    }
    
    if (!state) {
      errorMessage.value = '缺少回调地址参数，请检查授权链接'
      console.error('缺少state参数')
      return
    }
    
    // 解码state参数（如果是编码的）
    let targetUrl
    try {
      targetUrl = decodeURIComponent(state)
    } catch (e) {
      targetUrl = state
    }
    
    // 构建回调URL，将code参数传递给目标地址
    const separator = targetUrl.includes('?') ? '&' : '?'
    const callbackUrl = `${targetUrl}${separator}code=${code}`
    
    console.log('准备跳转到目标地址:', callbackUrl)
    
    // 跳转到目标地址
    window.location.href = callbackUrl
    
  } catch (error) {
    console.error('处理微信授权回调时发生错误:', error)
    errorMessage.value = '处理授权回调时发生错误，请重试'
  }
}

/**
 * 页面加载完成后执行
 */
onMounted(() => {
  // 延迟执行，确保页面完全加载
  setTimeout(() => {
    handleWechatCallback()
  }, 500)
})
</script>

<style scoped>
.wechat-auth-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 40rpx;
}

.loading-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 40rpx;
}

.loading-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-text {
  font-size: 32rpx;
  color: #666;
  font-weight: 500;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e0e0e0;
  border-top: 4rpx solid #07c160;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  margin-top: 40rpx;
  padding: 20rpx 30rpx;
  background-color: #fff2f0;
  border: 2rpx solid #ffccc7;
  border-radius: 8rpx;
  color: #ff4d4f;
  font-size: 28rpx;
  text-align: center;
  max-width: 600rpx;
}
</style>
