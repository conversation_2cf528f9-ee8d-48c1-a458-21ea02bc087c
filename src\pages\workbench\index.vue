<template>
    <yd-page-view ref="page" class="workbench" title="工作台" :hideBottom="false" :hideLeft="true" :refresherOnly="true" @onRefresh="onRefresh" :tabBarCurrent="isDormAdmin ? 2 : 3">
        <!-- tabs选项卡 -->
        <uv-tabs :list="tabs" :scrollable="!isDormAdmin" :current="activeTab" @click="clickTabs" :activeStyle="{ color: isDormAdmin ? '#4566d5' : 'var(--primary-color)' }" :inactiveStyle="{ color: '#606266' }" lineWidth="20" :customStyle="{ background: '#fff' }" :lineColor="isDormAdmin ? '#4566d5' : 'var(--primary-color)'"></uv-tabs>
        <!-- app应用列表 -->
        <scroll-view v-if="tabs && tabs.length > 0" :enable-flex="true" :scroll-with-animation="true" @scroll="handleScroll" :scroll-y="true" class="app_content" :scroll-top="scrollTop" :scroll-into-view="scrollIntoViewId">
            <div class="app_category" v-for="(item, index) in tabs" :key="index" :id="`category${index}`" :ref="item.name + index">
                <div v-if="item.categoryName == '常用应用' || existingApp(item.apps)">
                    <span class="category_title">{{ item.categoryName }}</span>
                    <div class="app_list">
                        <div v-show="appShow.includes(app.routePath)" v-for="(app, appIndex) in item.apps" :key="appIndex + 'app'" @click="openApp(app)">
                            <view class="app_item">
                                <image class="app_logo" v-if="app.logo" :src="app.logo"></image>
                                <view
                                    v-else
                                    class="app_icon"
                                    :class="`iconfont ${app.extend?.icon}`"
                                    :style="{
                                        color: app.extend?.background
                                    }"
                                ></view>
                                <span class="app_name">{{ app.name }}</span>
                            </view>
                        </div>
                        <!-- 常用应用 -->
                        <div v-if="index == 0 && !isDormAdmin" class="app_item" @click="handleEdit">
                            <image class="app_logo" src="@nginx/workbench/editCommon.png"></image>
                            <span class="app_name">编辑</span>
                        </div>
                    </div>
                </div>
            </div>
        </scroll-view>
        <yd-empty text="暂无数据" :isMargin="true" v-else />
    </yd-page-view>
</template>

<script setup>
import { dormData } from "@/package/workbench/dormAdminData.js"
import { existingApp, appShow } from "@/utils/existingApp"
import { useShowLoading } from "@/hooks"

const page = ref(null)
const tabs = ref([])
const activeTab = ref(null) // tabs的选中下标
const scrollIntoViewId = ref("")
const scrollTop = ref(0)
const instance = getCurrentInstance().proxy
const statusBarHeight = ref(0)
const { system, home, user } = store()
const { startLoading, stopLoading } = useShowLoading()

const appCenterList = computed(() => system.workbench)
// 是否为宿管身份
const isDormAdmin = computed(() => {
    return user.identityInfo.roleCode === "dorm_admin"
})

onLoad(() => {
    uni.getSystemInfo({
        success: (res) => {
            // 获取手机顶部状态栏的高度
            statusBarHeight.value = res.statusBarHeight || 0
        }
    })
})

// 节流函数
function throttle(fn, delay) {
    let lastCall = 0
    return function (...args) {
        const now = new Date().getTime()
        if (now - lastCall < delay) {
            return
        }
        lastCall = now
        return fn(...args)
    }
}
// 打开应用
function openApp(item) {
    jumpApp(item)
}

function setScroll() {
    tabs.value.forEach((item, index) => {
        const query = uni.createSelectorQuery().in(instance)
        query
            .select(`#category${index}`)
            .boundingClientRect(function (data) {
                if (data && data.top > 88 && data.top < 200) {
                    activeTab.value = index
                }
            })
            .exec()
    })
}

// 滚动页面
const handleScroll = throttle(setScroll, 100)

// 点击tabs滚动
async function clickTabs(item) {
    console.log(item)
    const query = uni.createSelectorQuery().in(instance)
    query
        .select(`#category${item.index}`)
        .fields({ rect: true }, function (data) {
            console.log(scrollTop.value, data.top)
            if (data) {
                // #ifdef H5-WEIXIN || H5
                scrollTop.value = scrollTop.value + (data.top - 88)
                // #endif
                // #ifdef MP-WEIXIN || APP-PLUS
                scrollTop.value = scrollTop.value + (data.top - statusBarHeight.value - 88)
                // #endif
            }
        })
        .exec()
}

const handleData = async (data) => {
    const list = data.map((i) => {
        i.apps = i.apps.map((j) => {
            return {
                ...j,
                extend: j.extend && JSON.parse(j.extend)
            }
        })
        return {
            ...i,
            name: i.categoryName
        }
    })
    tabs.value = list.filter((i) => existingApp(i.apps) || i.categoryName == "常用应用")
    system.setWorkbench(tabs.value)
    activeTab.value = 0
    console.log(tabs.value)

    // scrollIntoViewId.value = `#category0`
}

const getAppList = async () => {
    if (isDormAdmin.value) {
        handleData(dormData)
    } else {
        await http.get("/app/appCenter/list").then((res) => {
            handleData(res.data)
        })
    }
}

const handleEdit = () => {
    navigateTo({
        url: "/package/workbench/editCommon",
        events: {
            refresh: function ({ data }) {
                if (data == true) {
                    getAppList()
                    home.queryQuickList()
                }
            }
        }
    })
}

function onRefresh() {
    setTimeout(async () => {
        // 1.5秒之后停止刷新动画
        await getAppList()
        page.value.paging.complete([])
    }, 1000)
}

// onPullDownRefresh(async () => {
//     await getAppList()
//     uni.stopPullDownRefresh()
// })

onMounted(() => {
    nextTick(async () => {
        if (appCenterList.value && appCenterList.value.length > 1) {
            tabs.value = appCenterList.value
            activeTab.value = 0
        } else {
            await getAppList()
        }
    })
})
</script>

<style lang="scss" scoped>
.workbench {
    max-height: calc(100vh - 100rpx);
    height: calc(100vh - 100rpx);
    overflow: hidden;

    .workbench_title {
        flex: 1;
        text-align: center;
        font-weight: 500;
        font-size: 34rpx;
        color: #000000;
        line-height: 88rpx;
    }

    .app_content {
        padding: 10rpx 30rpx;
        max-height: calc(100vh - 316rpx);
        height: calc(100vh - 316rpx);
        overflow-y: auto;
        width: calc(100% - 60rpx);

        .app_category {
            margin-top: 30rpx;

            .category_title {
                font-weight: 500;
                font-size: 30rpx;
                color: $uni-text-color;
                line-height: 42rpx;
            }

            .app_list {
                margin-top: 20rpx;
                min-height: 100rpx;
                background: $uni-bg-color;
                box-shadow: 0rpx 16rpx 16rpx 0rpx #dcf5ee80;
                border-radius: 20rpx;
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 20rpx;
                padding: 20rpx;

                .app_item {
                    padding: 20rpx 0rpx;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;

                    .app_logo {
                        width: 80rpx;
                        height: 80rpx;
                    }
                    .app_icon {
                        font-size: 80rpx;
                    }

                    .app_name {
                        padding-top: 20rpx;
                        font-weight: 400;
                        font-size: 28rpx;
                        color: $uni-text-color;
                        line-height: 40rpx;
                    }
                }
            }
        }
    }
}
</style>
