import { getToken } from "@/utils/storageToken.js"
import useStore from "@/store"
import { checkPlatform } from "@/utils/sendAppEvent.js"
// #ifdef H5 || H5-WEIXIN
import * as dd from "dingtalk-jsapi" // 钉钉
// #endif
export default () => {
    // 免登录白名单
    const whiteList = []
    const routerFn = ["navigateTo", "redirectTo", "reLaunch"]

    routerFn.forEach((name) => {
        uni.addInterceptor(name, {
            invoke(e) {
                const { system } = useStore()

                const arr = system.tabBarList.filter((item) => e.url.includes(item.pagePath))
                console.log(e.url, arr, "e.urle.urle.url")
                if (arr && arr.length) {
                    system.switchTab({
                        id: arr[0].id
                    })
                    // #ifdef H5 || H5-WEIXIN
                    if (checkPlatform() === "dingding") {
                        dd.biz.navigation.setTitle({
                            title: arr[0].text
                        })
                    }
                    // #endif
                }

                e.url = e.url.replace(/\?+/g, "?")
                if (e.url.endsWith("?")) {
                    e.url = e.url.substring(0, e.url.length - 1)
                }
                if (whiteList.includes(e.url)) {
                    return true
                }
                const isLogin = getToken()
                // TODO: 其余特殊情况待完善
                if (isLogin) {
                    return true
                } else {
                    return true
                }
            },
            success(args) {
                // console.log('--success--', args.eventChannel)
            },
            fail(err) {
                uni.showToast({
                    title: "暂无权限!",
                    icon: "none"
                })
            }
        })
    })
}
