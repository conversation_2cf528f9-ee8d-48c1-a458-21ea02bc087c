import { defineStore } from "pinia"

const useCollectTable = defineStore("collectTable", {
    state: () => {
        return {
            saveForm: {
                businessParam: {
                    allowAnonymous: false,
                    collectInWeekend: false,
                    collectMethod: 0,
                    collectRemind: true,
                    remindTime: "09:15",
                    collectObj: "",
                    scopeList: []
                },
                title: "",
                description: "",
                businessType: "collectTable",
                components: []
            }
        }
    },
    getters: {
        updateSaveForm(state) {
            return state.saveForm
        },
        updateComponents(state) {
            return state.saveForm.components
        }
    },
    actions: {
        setSaveForm(item) {
            this.saveForm.businessParam = item
        },
        // 替换
        _updateComponents(item) {
            this.saveForm.components = item
        },
        // 添加问题
        setComponents(item) {
            this.saveForm.components.push(item)
        },
        // 删除问题
        setDetComponents(type, idx, item) {
            if (type === "copy") {
                // 在指定位置插入新元素
                this.saveForm.components.splice(idx, 0, item)
            } else {
                this.saveForm.components.splice(idx, 1)
            }
        },
        resetDetComponents() {
            this.saveForm.title = ""
            this.saveForm.description = ""
            this.saveForm.components = []
            this.saveForm.businessParam = {
                allowAnonymous: false,
                collectInWeekend: false,
                collectMethod: 0,
                collectRemind: true,
                remindTime: "09:15",
                collectObj: "",
                scopeList: []
            }
        }
    }
})

export default useCollectTable
