import { defineStore } from "pinia"

const useCollectTable = defineStore("officialDocs", {
    state: () => {
        return {
            docs: {
                formListData: {},
                submitFormData: {}
            }
        }
    },
    getters: {
        getFormListData(state) {
            return state.docs.formListData
        },
        // 提交表单
        submitFormData(state) {
            return state.docs.submitFormData
        }
    },
    actions: {
        setFormListData(item) {
            this.docs.formListData = item
        },
        // 修改提交表单
        setSubmitFormListKey(key, item) {
            this.docs.submitFormData[key] = item
        },
        // 清掉所有
        setSubmitFormClearKey() {
            this.docs.formListData = {}
            this.docs.submitFormData = {}
        }
    }
})

export default useCollectTable
