import { defineStore } from "pinia"
import useUser from "./user"
import { nextTick } from "vue"
let user = null

const roleTabBar = {
    default: [
        {
            text: "首页",
            pagePath: "/pages/home/<USER>",
            selectedIconPath: "@nginx/home/<USER>/homeSelect.png",
            iconPath: "@nginx/home/<USER>/homeUnSelect.png",
            // tab 上的按钮文字、数字
            badge: 0,
            id: 0
        },
        // TODO: 隐藏
        // {
        //     text: "日程",
        //     pagePath: "/pages/dynamic/index",
        //     selectedIconPath: "@nginx/home/<USER>/scheduleSelect.png",
        //     iconPath: "@nginx/home/<USER>/scheduleUnSelect.png",
        //     // tab 上的按钮文字、数字
        //     badge: 0,
        //     id: 1,
        //     // 应用的名称
        //     dynamicAppName: "schedule"
        // },
        {
            text: "消息",
            pagePath: "/pages/chat/index",
            selectedIconPath: "@nginx/home/<USER>/newsSelect.png",
            iconPath: "@nginx/home/<USER>/newsUnSelect.png",
            badge: 0,
            id: 2
        },
        {
            text: "工作台",
            pagePath: "/pages/workbench/index",
            selectedIconPath: "@nginx/home/<USER>/workbenchSelect.png",
            iconPath: "@nginx/home/<USER>/workbenchUnSelect.png",
            badge: 0,
            id: 3
        },
        {
            text: "我的",
            pagePath: "/pages/my/index",
            selectedIconPath: "@nginx/home/<USER>/mySelect.png",
            iconPath: "@nginx/home/<USER>/myUnSelect.png",
            badge: 0,
            id: 4
        }
    ],
    //  宿舍管理员路由
    dorm_admin: [
        {
            text: "首页",
            pagePath: "/apps/dormManage/home/<USER>",
            selectedIconPath: "@nginx/workbench/dormManage/homeSelect.png",
            iconPath: "@nginx/workbench/dormManage/homeUnSelect.png",
            // tab 上的按钮文字、数字
            badge: 0,
            id: 0
        },
        {
            text: "寝室",
            pagePath: "/apps/dormManage/dormInfo/index",
            selectedIconPath: "@nginx/workbench/dormManage/dormSelect.png",
            iconPath: "@nginx/workbench/dormManage/dormUnSelect.png",
            badge: 0,
            id: 1
        },
        {
            text: "工作台",
            pagePath: "/pages/workbench/index",
            selectedIconPath: "@nginx/workbench/dormManage/workbenchSelect.png",
            iconPath: "@nginx/workbench/dormManage/workbenchUnSelect.png",
            badge: 0,
            id: 2
        },
        {
            text: "消息",
            pagePath: "/pages/chat/index",
            selectedIconPath: "@nginx/workbench/dormManage/newsSelect.png",
            iconPath: "@nginx/workbench/dormManage/newsUnSelect.png",
            badge: 0,
            id: 3
        },
        {
            text: "我的",
            pagePath: "/pages/my/index",
            selectedIconPath: "@nginx/workbench/dormManage/mySelect.png",
            iconPath: "@nginx/workbench/dormManage/myUnSelect.png",
            badge: 0,
            id: 4
        }
    ]
}
const useSystemStore = defineStore("system", {
    state: () => {
        return {
            system: {
                // tabBar
                currentTabBar: 0, // 对应 tabBarList id
                // 储存工作台数据
                workbench: [],
                // role
                role: "admin",
                primaryColor: "#00b781",
                openId: null
            },
            // 存储子应用数据
            apps: {}
        }
    },
    getters: {
        currentTabBar(state) {
            return state.system.currentTabBar
        },
        tabBarList(state) {
            // TODO: 如需角色判断 可以根据tabBar id过滤返回对应得数据，或者后端接口返回此tabBar
            user = useUser()
            const roleCode = user.user?.identityInfo?.roleCode || "default"
            return roleTabBar[roleCode] || roleTabBar.default
        },
        // 设置工作台的列表缓存数据
        workbench(state) {
            return state.system.workbench
        },
        openId(state) {
            return state.system.openId
        },
        primaryColor(state) {
            return state.system.primaryColor
        }
    },
    actions: {
        setOpenId(openId) {
            this.system.openId = openId
        },
        setCurrentTabBar(obj) {
            this.system.currentTabBar = Object.assign(this.system.currentTabBar, obj)
        },
        switchTab({ id, url, query }) {
            if (id != undefined) {
                this.system.currentTabBar = id
            }
            if (url) {
                const item = this.system.tabBarList.find((item) => item.pagePath === url)
                if (!item) {
                    return new Error("无法找到对应的 tabBar,请检查相关字段是否一致")
                }
                this.system.currentTabBar = item.id
            }
        },
        setAppData({ sys, data }) {
            if (!this.apps[sys]) this.apps[sys] = {}
            Object.assign(this.apps[sys], data)
        },
        setWorkbench(list) {
            this.system.workbench = list
        },
        setPrimaryColor(color) {
            // #ifdef H5
            this.system.primaryColor = color
            document.documentElement.style.setProperty("--primary-color", color)
            document.documentElement.style.setProperty("--primary-bg-color", `${color}10`)
            // #endif
        }
    },

    persist: {
        key: "yd-mobile-system",
        paths: ["system"],
        debug: import.meta.env.VITE_USER_NODE_ENV === "production",
        beforeRestore: (ctx) => {
            console.log(`beforeRestore '${ctx.store.$id}'`)
        },
        afterRestore: (ctx) => {
            console.log(`afterRestore '${ctx.store.$id}'`)
        }
    }
})

export default useSystemStore
