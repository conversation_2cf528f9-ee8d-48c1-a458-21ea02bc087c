import { defineStore } from "pinia"

const useVoteStore = defineStore("vote", {
    state: () => {
        return {
            type: 1, // 类型 1：单选投票 2：多选投票 3：二选一PK投票 4：评选活动投票
            voteForm: {
                title: "", // 投票标题
                desc: "", // 补充描述
                url: "", // 活动主图
                startTime: "", // 开始时间
                endTime: "", // 结束时间
                setting: {
                    hasImage: false, // 是否开启图文投票
                    selfSee: false, // 投票明细仅自己可见
                    dayLimit: 0, // 每天多少次
                    sumLimit: 0, // 总投票次数
                    minOption: 0, // 多选的 最少选几个
                    maxOption: 0, // 多选的 最多选几个
                    repeat: 0 // 是否允许给同一选手重复投票：评选用 0否 1是
                },
                options: [
                    {
                        title: "",
                        files: [
                            {
                                url: ""
                            }
                        ]
                    },
                    {
                        title: "",
                        files: [
                            {
                                url: ""
                            }
                        ]
                    }
                ],

                allUserNameStr: "",
                teacherIds: [], // 老师ID集合
                studentIds: [] //  学生ID集合
            }
        }
    },
    getters: {
        // examineList(state) {
        //     return state.local.examineList
        // }
    },
    actions: {
        resetData() {
            this.voteForm = {
                title: "",
                desc: "",
                url: "",
                startTime: "",
                endTime: "",
                setting: {
                    hasImage: false,
                    selfSee: false,
                    dayLimit: 0,
                    sumLimit: 0,
                    minOption: 0,
                    maxOption: 0,
                    repeat: 0
                },
                options: [
                    { title: "", files: [{ url: "" }] },
                    { title: "", files: [{ url: "" }] }
                ],
                allUserNameStr: "",
                teacherIds: [],
                studentIds: []
            } // 重置 voteForm 为默认值对象
        },
        setVoteType(val) {
            this.type = val
        },
        setVoteForm(val) {
            this.voteForm = val
        }
    },

    persist: {
        key: "yd-mobile-vote",
        paths: ["vote"]
    }
})

export default useVoteStore
