## 1.1.1（2022-11-28）
- 修复vue3.0导致的点击无反应
## 1.1.0（2022-11-22）
### 注意！此版本变更了传值方式与方法名，如从旧版本更新需更新写法
- 变更传值方式为v-model双向绑定
- 增加change事件，移除getIndex事件
- 优化代码结构
## 1.0.6（2022-06-29）
- 修复动态传值下标不移动或位置错误
- 精简代码
## 1.0.5（2022-03-08）
- 修复部分问题
## 1.0.4（2022-03-08）
- 修复当外部盒子添加padding样式，滑块位置错误问题
- 区分文字颜色与滑块颜色，分别传入
- 增加修改组件背景色功能
- 增加tabIndex参数，无需调用方法修改选中tab
- 默认隐藏组件滚动条
- 对vue3.x的支持
## 1.0.3（2021-03-04）
去除“windowsWidth”属性，简化代码
## 1.0.2（2021-03-01）
新增隐藏滚动条css文件
更新说明文档
修复点击第一个tab时横线回弹位置错误问题
## 1.0.1（2021-02-26）
修改部分逻辑，支持修改选中tab下标，修复Bug
## 1.0.0（2021-02-26）
此版本为发布的第一个版本