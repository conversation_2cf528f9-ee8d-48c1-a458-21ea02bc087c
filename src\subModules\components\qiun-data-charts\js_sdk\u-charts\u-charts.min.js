/*
 * uCharts (R)
 * 高性能跨平台图表库，支持H5、APP、小程序（微信/支付宝/百度/头条/QQ/360/快手）、Vue、Taro等支持canvas的框架平台
 * Copyright (C) 2021 QIUN (R) 秋云 https://www.ucharts.cn All rights reserved.
 * Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
 * 复制使用请保留本段注释，感谢支持开源！
 *
 * uCharts (R) 官方网站
 * https://www.uCharts.cn
 *
 * 开源地址:
 * https://gitee.com/uCharts/uCharts
 *
 * uni-app插件市场地址：
 * http://ext.dcloud.net.cn/plugin?id=271
 *
 */
"use strict"
var config = { version: "v2.5.0-20230101", yAxisWidth: 15, xAxisHeight: 22, padding: [10, 10, 10, 10], rotate: false, fontSize: 13, fontColor: "#666666", dataPointShape: ["circle", "circle", "circle", "circle"], color: ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4", "#ea7ccc"], linearColor: ["#0EE2F8", "#2BDCA8", "#FA7D8D", "#EB88E2", "#2AE3A0", "#0EE2F8", "#EB88E2", "#6773E3", "#F78A85"], pieChartLinePadding: 15, pieChartTextPadding: 5, titleFontSize: 20, subtitleFontSize: 15, radarLabelTextMargin: 13 }
var assign = function (e, ...t) {
    if (e == null) {
        throw new TypeError("[uCharts] Cannot convert undefined or null to object")
    }
    if (!t || t.length <= 0) {
        return e
    }
    function i(e, a) {
        for (let t in a) {
            e[t] = e[t] && e[t].toString() === "[object Object]" ? i(e[t], a[t]) : (e[t] = a[t])
        }
        return e
    }
    t.forEach((t) => {
        e = i(e, t)
    })
    return e
}
var util = {
    toFixed: function t(e, a) {
        a = a || 2
        if (this.isFloat(e)) {
            e = e.toFixed(a)
        }
        return e
    },
    isFloat: function t(e) {
        return e % 1 !== 0
    },
    approximatelyEqual: function t(e, a) {
        return Math.abs(e - a) < 1e-10
    },
    isSameSign: function t(e, a) {
        return (Math.abs(e) === e && Math.abs(a) === a) || (Math.abs(e) !== e && Math.abs(a) !== a)
    },
    isSameXCoordinateArea: function t(e, a) {
        return this.isSameSign(e.x, a.x)
    },
    isCollision: function t(e, a) {
        e.end = {}
        e.end.x = e.start.x + e.width
        e.end.y = e.start.y - e.height
        a.end = {}
        a.end.x = a.start.x + a.width
        a.end.y = a.start.y - a.height
        var i = a.start.x > e.end.x || a.end.x < e.start.x || a.end.y > e.start.y || a.start.y < e.end.y
        return !i
    }
}
function getH5Offset(t) {
    t.mp = { changedTouches: [] }
    t.mp.changedTouches.push({ x: t.offsetX, y: t.offsetY })
    return t
}
function hexToRgb(t, e) {
    var a = /^#?([a-f\d])([a-f\d])([a-f\d])$/i
    var i = t.replace(a, function (t, e, a, i) {
        return e + e + a + a + i + i
    })
    var r = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(i)
    var o = parseInt(r[1], 16)
    var n = parseInt(r[2], 16)
    var l = parseInt(r[3], 16)
    return "rgba(" + o + "," + n + "," + l + "," + e + ")"
}
function findRange(t, e, a) {
    if (isNaN(t)) {
        throw new Error("[uCharts] series数据需为Number格式")
    }
    a = a || 10
    e = e ? e : "upper"
    var i = 1
    while (a < 1) {
        a *= 10
        i *= 10
    }
    if (e === "upper") {
        t = Math.ceil(t * i)
    } else {
        t = Math.floor(t * i)
    }
    while (t % a !== 0) {
        if (e === "upper") {
            if (t == t + 1) {
                break
            }
            t++
        } else {
            t--
        }
    }
    return t / i
}
function calCandleMA(o, t, e, n) {
    let a = []
    for (let r = 0; r < o.length; r++) {
        let i = { data: [], name: t[r], color: e[r] }
        for (let a = 0, t = n.length; a < t; a++) {
            if (a < o[r]) {
                i.data.push(null)
                continue
            }
            let e = 0
            for (let t = 0; t < o[r]; t++) {
                e += n[a - t][1]
            }
            i.data.push(+(e / o[r]).toFixed(3))
        }
        a.push(i)
    }
    return a
}
function calValidDistance(t, e, a, i, r) {
    var o = r.width - r.area[1] - r.area[3]
    var n = a.eachSpacing * (r.chartData.xAxisData.xAxisPoints.length - 1)
    if (r.type == "mount" && r.extra && r.extra.mount && r.extra.mount.widthRatio && r.extra.mount.widthRatio > 1) {
        if (r.extra.mount.widthRatio > 2) r.extra.mount.widthRatio = 2
        n += (r.extra.mount.widthRatio - 1) * a.eachSpacing
    }
    var l = e
    if (e >= 0) {
        l = 0
        t.uevent.trigger("scrollLeft")
        t.scrollOption.position = "left"
        r.xAxis.scrollPosition = "left"
    } else if (Math.abs(e) >= n - o) {
        l = o - n
        t.uevent.trigger("scrollRight")
        t.scrollOption.position = "right"
        r.xAxis.scrollPosition = "right"
    } else {
        t.scrollOption.position = e
        r.xAxis.scrollPosition = e
    }
    return l
}
function isInAngleRange(t, e, a) {
    function i(t) {
        while (t < 0) {
            t += 2 * Math.PI
        }
        while (t > 2 * Math.PI) {
            t -= 2 * Math.PI
        }
        return t
    }
    t = i(t)
    e = i(e)
    a = i(a)
    if (e > a) {
        a += 2 * Math.PI
        if (t < e) {
            t += 2 * Math.PI
        }
    }
    return t >= e && t <= a
}
function createCurveControlPoints(t, e) {
    function a(t, e) {
        if (t[e - 1] && t[e + 1]) {
            return t[e].y >= Math.max(t[e - 1].y, t[e + 1].y) || t[e].y <= Math.min(t[e - 1].y, t[e + 1].y)
        } else {
            return false
        }
    }
    function c(t, e) {
        if (t[e - 1] && t[e + 1]) {
            return t[e].x >= Math.max(t[e - 1].x, t[e + 1].x) || t[e].x <= Math.min(t[e - 1].x, t[e + 1].x)
        } else {
            return false
        }
    }
    var i = 0.2
    var r = 0.2
    var o = null
    var n = null
    var l = null
    var s = null
    if (e < 1) {
        o = t[0].x + (t[1].x - t[0].x) * i
        n = t[0].y + (t[1].y - t[0].y) * i
    } else {
        o = t[e].x + (t[e + 1].x - t[e - 1].x) * i
        n = t[e].y + (t[e + 1].y - t[e - 1].y) * i
    }
    if (e > t.length - 3) {
        var h = t.length - 1
        l = t[h].x - (t[h].x - t[h - 1].x) * r
        s = t[h].y - (t[h].y - t[h - 1].y) * r
    } else {
        l = t[e + 1].x - (t[e + 2].x - t[e].x) * r
        s = t[e + 1].y - (t[e + 2].y - t[e].y) * r
    }
    if (a(t, e + 1)) {
        s = t[e + 1].y
    }
    if (a(t, e)) {
        n = t[e].y
    }
    if (c(t, e + 1)) {
        l = t[e + 1].x
    }
    if (c(t, e)) {
        o = t[e].x
    }
    if (n >= Math.max(t[e].y, t[e + 1].y) || n <= Math.min(t[e].y, t[e + 1].y)) {
        n = t[e].y
    }
    if (s >= Math.max(t[e].y, t[e + 1].y) || s <= Math.min(t[e].y, t[e + 1].y)) {
        s = t[e + 1].y
    }
    if (o >= Math.max(t[e].x, t[e + 1].x) || o <= Math.min(t[e].x, t[e + 1].x)) {
        o = t[e].x
    }
    if (l >= Math.max(t[e].x, t[e + 1].x) || l <= Math.min(t[e].x, t[e + 1].x)) {
        l = t[e + 1].x
    }
    return { ctrA: { x: o, y: n }, ctrB: { x: l, y: s } }
}
function convertCoordinateOrigin(t, e, a) {
    return { x: a.x + t, y: a.y - e }
}
function avoidCollision(t, e) {
    if (e) {
        while (util.isCollision(t, e)) {
            if (t.start.x > 0) {
                t.start.y--
            } else if (t.start.x < 0) {
                t.start.y++
            } else {
                if (t.start.y > 0) {
                    t.start.y++
                } else {
                    t.start.y--
                }
            }
        }
    }
    return t
}
function fixPieSeries(e, a, t) {
    let i = []
    if (e.length > 0 && e[0].data.constructor.toString().indexOf("Array") > -1) {
        a._pieSeries_ = e
        let t = e[0].data
        for (var r = 0; r < t.length; r++) {
            t[r].formatter = e[0].formatter
            t[r].data = t[r].value
            i.push(t[r])
        }
        a.series = i
    } else {
        i = e
    }
    return i
}
function fillSeries(e, a, i) {
    var r = 0
    for (var o = 0; o < e.length; o++) {
        let t = e[o]
        if (!t.color) {
            t.color = i.color[r]
            r = (r + 1) % i.color.length
        }
        if (!t.linearIndex) {
            t.linearIndex = o
        }
        if (!t.index) {
            t.index = 0
        }
        if (!t.type) {
            t.type = a.type
        }
        if (typeof t.show == "undefined") {
            t.show = true
        }
        if (!t.type) {
            t.type = a.type
        }
        if (!t.pointShape) {
            t.pointShape = "circle"
        }
        if (!t.legendShape) {
            switch (t.type) {
                case "line":
                    t.legendShape = "line"
                    break
                case "column":
                case "bar":
                    t.legendShape = "rect"
                    break
                case "area":
                case "mount":
                    t.legendShape = "triangle"
                    break
                default:
                    t.legendShape = "circle"
            }
        }
    }
    return e
}
function fillCustomColor(t, e, a, i) {
    var r = e || []
    if (t == "custom" && r.length == 0) {
        r = i.linearColor
    }
    if (t == "custom" && r.length < a.length) {
        let t = a.length - r.length
        for (var o = 0; o < t; o++) {
            r.push(i.linearColor[(o + 1) % i.linearColor.length])
        }
    }
    return r
}
function getDataRange(t, e) {
    var a = 0
    var i = e - t
    if (i >= 1e4) {
        a = 1e3
    } else if (i >= 1e3) {
        a = 100
    } else if (i >= 100) {
        a = 10
    } else if (i >= 10) {
        a = 5
    } else if (i >= 1) {
        a = 1
    } else if (i >= 0.1) {
        a = 0.1
    } else if (i >= 0.01) {
        a = 0.01
    } else if (i >= 0.001) {
        a = 0.001
    } else if (i >= 1e-4) {
        a = 1e-4
    } else if (i >= 1e-5) {
        a = 1e-5
    } else {
        a = 1e-6
    }
    return { minRange: findRange(t, "lower", a), maxRange: findRange(e, "upper", a) }
}
function measureText(a, t, e) {
    var i = 0
    a = String(a)
    e = false
    if (e !== false && e !== undefined && e.setFontSize && e.measureText) {
        e.setFontSize(t)
        return e.measureText(a).width
    } else {
        var a = a.split("")
        for (let e = 0; e < a.length; e++) {
            let t = a[e]
            if (/[a-zA-Z]/.test(t)) {
                i += 7
            } else if (/[0-9]/.test(t)) {
                i += 5.5
            } else if (/\./.test(t)) {
                i += 2.7
            } else if (/-/.test(t)) {
                i += 3.25
            } else if (/:/.test(t)) {
                i += 2.5
            } else if (/[\u4e00-\u9fa5]/.test(t)) {
                i += 10
            } else if (/\(|\)/.test(t)) {
                i += 3.73
            } else if (/\s/.test(t)) {
                i += 2.5
            } else if (/%/.test(t)) {
                i += 8
            } else {
                i += 10
            }
        }
        return (i * t) / 10
    }
}
function dataCombine(t) {
    return t.reduce(function (t, e) {
        return (t.data ? t.data : t).concat(e.data)
    }, [])
}
function dataCombineStack(t, e) {
    var a = new Array(e)
    for (var i = 0; i < a.length; i++) {
        a[i] = 0
    }
    for (var r = 0; r < t.length; r++) {
        for (var i = 0; i < a.length; i++) {
            a[i] += t[r].data[i]
        }
    }
    return t.reduce(function (t, e) {
        return (t.data ? t.data : t).concat(e.data).concat(a)
    }, [])
}
function getTouches(t, e, a) {
    let i, r
    if (t.clientX) {
        if (e.rotate) {
            r = e.height - t.clientX * e.pix
            i = (t.pageY - a.currentTarget.offsetTop - (e.height / e.pix / 2) * (e.pix - 1)) * e.pix
        } else {
            i = t.clientX * e.pix
            r = (t.pageY - a.currentTarget.offsetTop - (e.height / e.pix / 2) * (e.pix - 1)) * e.pix
        }
    } else {
        if (e.rotate) {
            r = e.height - t.x * e.pix
            i = t.y * e.pix
        } else {
            i = t.x * e.pix
            r = t.y * e.pix
        }
    }
    return { x: i, y: r }
}
function getSeriesDataItem(e, i, a) {
    var r = []
    var o = []
    var n = i.constructor.toString().indexOf("Array") > -1
    if (n) {
        let t = filterSeries(e)
        for (var l = 0; l < a.length; l++) {
            o.push(t[a[l]])
        }
    } else {
        o = e
    }
    for (let t = 0; t < o.length; t++) {
        let e = o[t]
        let a = -1
        if (n) {
            a = i[t]
        } else {
            a = i
        }
        if (e.data[a] !== null && typeof e.data[a] !== "undefined" && e.show) {
            let t = {}
            t.color = e.color
            t.type = e.type
            t.style = e.style
            t.pointShape = e.pointShape
            t.disableLegend = e.disableLegend
            t.legendShape = e.legendShape
            t.name = e.name
            t.show = e.show
            t.data = e.formatter ? e.formatter(e.data[a]) : e.data[a]
            r.push(t)
        }
    }
    return r
}
function getMaxTextListLength(t, e, a) {
    var i = t.map(function (t) {
        return measureText(t, e, a)
    })
    return Math.max.apply(null, i)
}
function getRadarCoordinateSeries(t) {
    var e = (2 * Math.PI) / t
    var a = []
    for (var i = 0; i < t; i++) {
        a.push(e * i)
    }
    return a.map(function (t) {
        return -1 * t + Math.PI / 2
    })
}
function getToolTipData(t, a, i, r, o) {
    var n = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : {}
    var l = a.chartData.calPoints ? a.chartData.calPoints : []
    let s = {}
    if (r.length > 0) {
        let e = []
        for (let t = 0; t < r.length; t++) {
            e.push(l[r[t]])
        }
        s = e[0][i[0]]
    } else {
        for (let t = 0; t < l.length; t++) {
            if (l[t][i]) {
                s = l[t][i]
                break
            }
        }
    }
    var e = t.map(function (t) {
        let e = null
        if (a.categories && a.categories.length > 0) {
            e = o[i]
        }
        return { text: n.formatter ? n.formatter(t, e, i, a) : t.name + ": " + t.data, color: t.color, legendShape: a.extra.tooltip.legendShape == "auto" ? t.legendShape : a.extra.tooltip.legendShape }
    })
    var h = { x: Math.round(s.x), y: Math.round(s.y) }
    return { textList: e, offset: h }
}
function getMixToolTipData(t, e, a, i) {
    var r = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : {}
    var o = e.chartData.xAxisPoints[a] + e.chartData.eachSpacing / 2
    var n = t.map(function (t) {
        return { text: r.formatter ? r.formatter(t, i[a], a, e) : t.name + ": " + t.data, color: t.color, disableLegend: t.disableLegend ? true : false, legendShape: e.extra.tooltip.legendShape == "auto" ? t.legendShape : e.extra.tooltip.legendShape }
    })
    n = n.filter(function (t) {
        if (t.disableLegend !== true) {
            return t
        }
    })
    var l = { x: Math.round(o), y: 0 }
    return { textList: n, offset: l }
}
function getCandleToolTipData(o, e, n, l, i, t) {
    var r = arguments.length > 6 && arguments[6] !== undefined ? arguments[6] : {}
    var a = n.chartData.calPoints
    let s = t.color.upFill
    let h = t.color.downFill
    let c = [s, s, h, s]
    var d = []
    e.map(function (t) {
        if (l == 0) {
            if (t.data[1] - t.data[0] < 0) {
                c[1] = h
            } else {
                c[1] = s
            }
        } else {
            if (t.data[0] < o[l - 1][1]) {
                c[0] = h
            }
            if (t.data[1] < t.data[0]) {
                c[1] = h
            }
            if (t.data[2] > o[l - 1][1]) {
                c[2] = s
            }
            if (t.data[3] < o[l - 1][1]) {
                c[3] = h
            }
        }
        let e = { text: "开盘：" + t.data[0], color: c[0], legendShape: n.extra.tooltip.legendShape == "auto" ? t.legendShape : n.extra.tooltip.legendShape }
        let a = { text: "收盘：" + t.data[1], color: c[1], legendShape: n.extra.tooltip.legendShape == "auto" ? t.legendShape : n.extra.tooltip.legendShape }
        let i = { text: "最低：" + t.data[2], color: c[2], legendShape: n.extra.tooltip.legendShape == "auto" ? t.legendShape : n.extra.tooltip.legendShape }
        let r = { text: "最高：" + t.data[3], color: c[3], legendShape: n.extra.tooltip.legendShape == "auto" ? t.legendShape : n.extra.tooltip.legendShape }
        d.push(e, a, i, r)
    })
    var x = []
    var f = { x: 0, y: 0 }
    for (let e = 0; e < a.length; e++) {
        let t = a[e]
        if (typeof t[l] !== "undefined" && t[l] !== null) {
            x.push(t[l])
        }
    }
    f.x = Math.round(x[0][0].x)
    return { textList: d, offset: f }
}
function filterSeries(e) {
    let a = []
    for (let t = 0; t < e.length; t++) {
        if (e[t].show == true) {
            a.push(e[t])
        }
    }
    return a
}
function findCurrentIndex(o, n, e, t) {
    var a = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 0
    var l = { index: -1, group: [] }
    var i = e.chartData.eachSpacing / 2
    let r = []
    if (n && n.length > 0) {
        if (!e.categories) {
            i = 0
        } else {
            for (let t = 1; t < e.chartData.xAxisPoints.length; t++) {
                r.push(e.chartData.xAxisPoints[t] - i)
            }
            if ((e.type == "line" || e.type == "area") && e.xAxis.boundaryGap == "justify") {
                r = e.chartData.xAxisPoints
            }
        }
        if (isInExactChartArea(o, e, t)) {
            if (!e.categories) {
                let a = Array(n.length)
                for (let e = 0; e < n.length; e++) {
                    a[e] = Array(n[e].length)
                    for (let t = 0; t < n[e].length; t++) {
                        a[e][t] = Math.abs(n[e][t].x - o.x)
                    }
                }
                let e = Array(a.length)
                let i = Array(a.length)
                for (let t = 0; t < a.length; t++) {
                    e[t] = Math.min.apply(null, a[t])
                    i[t] = a[t].indexOf(e[t])
                }
                let r = Math.min.apply(null, e)
                l.index = []
                for (let t = 0; t < e.length; t++) {
                    if (e[t] == r) {
                        l.group.push(t)
                        l.index.push(i[t])
                    }
                }
            } else {
                r.forEach(function (t, e) {
                    if (o.x + a + i > t) {
                        l.index = e
                    }
                })
            }
        }
    }
    return l
}
function findBarChartCurrentIndex(a, t, e, i) {
    var r = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 0
    var o = { index: -1, group: [] }
    var n = e.chartData.eachSpacing / 2
    let l = e.chartData.yAxisPoints
    if (t && t.length > 0) {
        if (isInExactChartArea(a, e, i)) {
            l.forEach(function (t, e) {
                if (a.y + r + n > t) {
                    o.index = e
                }
            })
        }
    }
    return o
}
function findLegendIndex(o, t, e) {
    let n = -1
    let l = 0
    if (isInExactLegendArea(o, t.area)) {
        let i = t.points
        let r = -1
        for (let t = 0, e = i.length; t < e; t++) {
            let a = i[t]
            for (let e = 0; e < a.length; e++) {
                r += 1
                let t = a[e]["area"]
                if (t && o.x > t[0] - l && o.x < t[2] + l && o.y > t[1] - l && o.y < t[3] + l) {
                    n = r
                    break
                }
            }
        }
        return n
    }
    return n
}
function isInExactLegendArea(t, e) {
    return t.x > e.start.x && t.x < e.end.x && t.y > e.start.y && t.y < e.end.y
}
function isInExactChartArea(t, e, a) {
    return t.x <= e.width - e.area[1] + 10 && t.x >= e.area[3] - 10 && t.y >= e.area[0] && t.y <= e.height - e.area[2]
}
function findRadarChartCurrentIndex(t, e, a) {
    var r = (2 * Math.PI) / a
    var o = -1
    if (isInExactPieChartArea(t, e.center, e.radius)) {
        var n = function t(e) {
            if (e < 0) {
                e += 2 * Math.PI
            }
            if (e > 2 * Math.PI) {
                e -= 2 * Math.PI
            }
            return e
        }
        var l = Math.atan2(e.center.y - t.y, t.x - e.center.x)
        l = -1 * l
        if (l < 0) {
            l += 2 * Math.PI
        }
        var i = e.angleList.map(function (t) {
            t = n(-1 * t)
            return t
        })
        i.forEach(function (t, e) {
            var a = n(t - r / 2)
            var i = n(t + r / 2)
            if (i < a) {
                i += 2 * Math.PI
            }
            if ((l >= a && l <= i) || (l + 2 * Math.PI >= a && l + 2 * Math.PI <= i)) {
                o = e
            }
        })
    }
    return o
}
function findFunnelChartCurrentIndex(t, e) {
    var a = -1
    for (var i = 0, r = e.series.length; i < r; i++) {
        var o = e.series[i]
        if (t.x > o.funnelArea[0] && t.x < o.funnelArea[2] && t.y > o.funnelArea[1] && t.y < o.funnelArea[3]) {
            a = i
            break
        }
    }
    return a
}
function findWordChartCurrentIndex(t, e) {
    var a = -1
    for (var i = 0, r = e.length; i < r; i++) {
        var o = e[i]
        if (t.x > o.area[0] && t.x < o.area[2] && t.y > o.area[1] && t.y < o.area[3]) {
            a = i
            break
        }
    }
    return a
}
function findMapChartCurrentIndex(t, e) {
    var a = -1
    var i = e.chartData.mapData
    var r = e.series
    var o = pointToCoordinate(t.y, t.x, i.bounds, i.scale, i.xoffset, i.yoffset)
    var n = [o.x, o.y]
    for (var l = 0, s = r.length; l < s; l++) {
        var h = r[l].geometry.coordinates
        if (isPoiWithinPoly(n, h, e.chartData.mapData.mercator)) {
            a = l
            break
        }
    }
    return a
}
function findRoseChartCurrentIndex(t, e, a) {
    var i = -1
    var r = getRoseDataPoints(a._series_, a.extra.rose.type, e.radius, e.radius)
    if (e && e.center && isInExactPieChartArea(t, e.center, e.radius)) {
        var o = Math.atan2(e.center.y - t.y, t.x - e.center.x)
        o = -o
        if (a.extra.rose && a.extra.rose.offsetAngle) {
            o = o - (a.extra.rose.offsetAngle * Math.PI) / 180
        }
        for (var n = 0, l = r.length; n < l; n++) {
            if (isInAngleRange(o, r[n]._start_, r[n]._start_ + r[n]._rose_proportion_ * 2 * Math.PI)) {
                i = n
                break
            }
        }
    }
    return i
}
function findPieChartCurrentIndex(t, e, a) {
    var i = -1
    var r = getPieDataPoints(e.series)
    if (e && e.center && isInExactPieChartArea(t, e.center, e.radius)) {
        var o = Math.atan2(e.center.y - t.y, t.x - e.center.x)
        o = -o
        if (a.extra.pie && a.extra.pie.offsetAngle) {
            o = o - (a.extra.pie.offsetAngle * Math.PI) / 180
        }
        if (a.extra.ring && a.extra.ring.offsetAngle) {
            o = o - (a.extra.ring.offsetAngle * Math.PI) / 180
        }
        for (var n = 0, l = r.length; n < l; n++) {
            if (isInAngleRange(o, r[n]._start_, r[n]._start_ + r[n]._proportion_ * 2 * Math.PI)) {
                i = n
                break
            }
        }
    }
    return i
}
function isInExactPieChartArea(t, e, a) {
    return Math.pow(t.x - e.x, 2) + Math.pow(t.y - e.y, 2) <= Math.pow(a, 2)
}
function splitPoints(t, a) {
    var i = []
    var r = []
    t.forEach(function (t, e) {
        if (a.connectNulls) {
            if (t !== null) {
                r.push(t)
            }
        } else {
            if (t !== null) {
                r.push(t)
            } else {
                if (r.length) {
                    i.push(r)
                }
                r = []
            }
        }
    })
    if (r.length) {
        i.push(r)
    }
    return i
}
function calLegendData(l, s, e, t, h) {
    let c = { area: { start: { x: 0, y: 0 }, end: { x: 0, y: 0 }, width: 0, height: 0, wholeWidth: 0, wholeHeight: 0 }, points: [], widthArr: [], heightArr: [] }
    if (s.legend.show === false) {
        t.legendData = c
        return c
    }
    let d = s.legend.padding * s.pix
    let x = s.legend.margin * s.pix
    let f = s.legend.fontSize ? s.legend.fontSize * s.pix : e.fontSize
    let p = 15 * s.pix
    let g = 5 * s.pix
    let u = Math.max(s.legend.lineHeight * s.pix, f)
    if (s.legend.position == "top" || s.legend.position == "bottom") {
        let i = []
        let r = 0
        let o = []
        let n = []
        for (let a = 0; a < l.length; a++) {
            let t = l[a]
            const y = t.legendText ? t.legendText : t.name
            let e = p + g + measureText(y || "undefined", f, h) + s.legend.itemGap * s.pix
            if (r + e > s.width - s.area[1] - s.area[3]) {
                i.push(n)
                o.push(r - s.legend.itemGap * s.pix)
                r = e
                n = [t]
            } else {
                r += e
                n.push(t)
            }
        }
        if (n.length) {
            i.push(n)
            o.push(r - s.legend.itemGap * s.pix)
            c.widthArr = o
            let t = Math.max.apply(null, o)
            switch (s.legend.float) {
                case "left":
                    c.area.start.x = s.area[3]
                    c.area.end.x = s.area[3] + t + 2 * d
                    break
                case "right":
                    c.area.start.x = s.width - s.area[1] - t - 2 * d
                    c.area.end.x = s.width - s.area[1]
                    break
                default:
                    c.area.start.x = (s.width - t) / 2 - d
                    c.area.end.x = (s.width + t) / 2 + d
            }
            c.area.width = t + 2 * d
            c.area.wholeWidth = t + 2 * d
            c.area.height = i.length * u + 2 * d
            c.area.wholeHeight = i.length * u + 2 * d + 2 * x
            c.points = i
        }
    } else {
        let t = l.length
        let e = s.height - s.area[0] - s.area[2] - 2 * x - 2 * d
        let a = Math.min(Math.floor(e / u), t)
        c.area.height = a * u + d * 2
        c.area.wholeHeight = a * u + d * 2
        switch (s.legend.float) {
            case "top":
                c.area.start.y = s.area[0] + x
                c.area.end.y = s.area[0] + x + c.area.height
                break
            case "bottom":
                c.area.start.y = s.height - s.area[2] - x - c.area.height
                c.area.end.y = s.height - s.area[2] - x
                break
            default:
                c.area.start.y = (s.height - c.area.height) / 2
                c.area.end.y = (s.height + c.area.height) / 2
        }
        let i = t % a === 0 ? t / a : Math.floor(t / a + 1)
        let r = []
        for (let e = 0; e < i; e++) {
            let t = l.slice(e * a, e * a + a)
            r.push(t)
        }
        c.points = r
        if (r.length) {
            for (let t = 0; t < r.length; t++) {
                let a = r[t]
                let i = 0
                for (let e = 0; e < a.length; e++) {
                    let t = p + g + measureText(a[e].name || "undefined", f, h) + s.legend.itemGap * s.pix
                    if (t > i) {
                        i = t
                    }
                }
                c.widthArr.push(i)
                c.heightArr.push(a.length * u + d * 2)
            }
            let e = 0
            for (let t = 0; t < c.widthArr.length; t++) {
                e += c.widthArr[t]
            }
            c.area.width = e - s.legend.itemGap * s.pix + 2 * d
            c.area.wholeWidth = c.area.width + d
        }
    }
    switch (s.legend.position) {
        case "top":
            c.area.start.y = s.area[0] + x
            c.area.end.y = s.area[0] + x + c.area.height
            break
        case "bottom":
            c.area.start.y = s.height - s.area[2] - c.area.height - x
            c.area.end.y = s.height - s.area[2] - x
            break
        case "left":
            c.area.start.x = s.area[3]
            c.area.end.x = s.area[3] + c.area.width
            break
        case "right":
            c.area.start.x = s.width - s.area[1] - c.area.width
            c.area.end.x = s.width - s.area[1]
            break
    }
    t.legendData = c
    return c
}
function calCategoriesData(t, i, e, a, r) {
    var o = { angle: 0, xAxisHeight: i.xAxis.lineHeight * i.pix + i.xAxis.marginTop * i.pix }
    var n = i.xAxis.fontSize * i.pix
    var l = t.map(function (t, e) {
        var a = i.xAxis.formatter ? i.xAxis.formatter(t, e, i) : t
        return measureText(String(a), n, r)
    })
    var s = Math.max.apply(this, l)
    if (i.xAxis.rotateLabel == true) {
        o.angle = (i.xAxis.rotateAngle * Math.PI) / 180
        let t = i.xAxis.marginTop * i.pix * 2 + Math.abs(s * Math.sin(o.angle))
        t = t < n + i.xAxis.marginTop * i.pix * 2 ? t + i.xAxis.marginTop * i.pix * 2 : t
        o.xAxisHeight = t
    }
    if (i.enableScroll && i.xAxis.scrollShow) {
        o.xAxisHeight += 6 * i.pix
    }
    if (i.xAxis.disabled) {
        o.xAxisHeight = 0
    }
    return o
}
function getXAxisTextList(t, e, h, c) {
    var a = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : -1
    var i
    if (c == "stack") {
        i = dataCombineStack(t, e.categories.length)
    } else {
        i = dataCombine(t)
    }
    var r = []
    i = i.filter(function (t) {
        if (typeof t === "object" && t !== null) {
            if (t.constructor.toString().indexOf("Array") > -1) {
                return t !== null
            } else {
                return t.value !== null
            }
        } else {
            return t !== null
        }
    })
    i.map(function (t) {
        if (typeof t === "object") {
            if (t.constructor.toString().indexOf("Array") > -1) {
                if (e.type == "candle") {
                    t.map(function (t) {
                        r.push(t)
                    })
                } else {
                    r.push(t[0])
                }
            } else {
                r.push(t.value)
            }
        } else {
            r.push(t)
        }
    })
    var o = 0
    var n = 0
    if (r.length > 0) {
        o = Math.min.apply(this, r)
        n = Math.max.apply(this, r)
    }
    if (a > -1) {
        if (typeof e.xAxis.data[a].min === "number") {
            o = Math.min(e.xAxis.data[a].min, o)
        }
        if (typeof e.xAxis.data[a].max === "number") {
            n = Math.max(e.xAxis.data[a].max, n)
        }
    } else {
        if (typeof e.xAxis.min === "number") {
            o = Math.min(e.xAxis.min, o)
        }
        if (typeof e.xAxis.max === "number") {
            n = Math.max(e.xAxis.max, n)
        }
    }
    if (o === n) {
        var d = n || 10
        n += d
    }
    var l = o
    var x = n
    var f = []
    var p = (x - l) / e.xAxis.splitNumber
    for (var s = 0; s <= e.xAxis.splitNumber; s++) {
        f.push(l + p * s)
    }
    return f
}
function calXAxisData(t, e, a, i) {
    var r = assign({}, { type: "" }, e.extra.bar)
    var o = { angle: 0, xAxisHeight: e.xAxis.lineHeight * e.pix + e.xAxis.marginTop * e.pix }
    o.ranges = getXAxisTextList(t, e, a, r.type)
    o.rangesFormat = o.ranges.map(function (t) {
        t = util.toFixed(t, 2)
        return t
    })
    var n = o.ranges.map(function (t) {
        t = util.toFixed(t, 2)
        return t
    })
    o = Object.assign(o, getXAxisPoints(n, e, a))
    var l = o.eachSpacing
    var s = n.map(function (t) {
        return measureText(t, e.xAxis.fontSize * e.pix, i)
    })
    if (e.xAxis.disabled === true) {
        o.xAxisHeight = 0
    }
    return o
}
function getRadarDataPoints(r, o, n, a, t) {
    var l = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : 1
    var e = t.extra.radar || {}
    e.max = e.max || 0
    var s = Math.max(e.max, Math.max.apply(null, dataCombine(a)))
    var h = []
    for (let e = 0; e < a.length; e++) {
        let t = a[e]
        let i = {}
        i.color = t.color
        i.legendShape = t.legendShape
        i.pointShape = t.pointShape
        i.data = []
        t.data.forEach(function (t, e) {
            let a = {}
            a.angle = r[e]
            a.proportion = t / s
            a.value = t
            a.position = convertCoordinateOrigin(n * a.proportion * l * Math.cos(a.angle), n * a.proportion * l * Math.sin(a.angle), o)
            i.data.push(a)
        })
        h.push(i)
    }
    return h
}
function getPieDataPoints(a, i) {
    var r = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1
    var o = 0
    var n = 0
    for (let e = 0; e < a.length; e++) {
        let t = a[e]
        t.data = t.data === null ? 0 : t.data
        o += t.data
    }
    for (let e = 0; e < a.length; e++) {
        let t = a[e]
        t.data = t.data === null ? 0 : t.data
        if (o === 0) {
            t._proportion_ = (1 / a.length) * r
        } else {
            t._proportion_ = (t.data / o) * r
        }
        t._radius_ = i
    }
    for (let e = 0; e < a.length; e++) {
        let t = a[e]
        t._start_ = n
        n += 2 * t._proportion_ * Math.PI
    }
    return a
}
function getFunnelDataPoints(e, a, i, r) {
    var o = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 1
    for (let t = 0; t < e.length; t++) {
        if (i.type == "funnel") {
            e[t].radius = (e[t].data / e[0].data) * a * o
        } else {
            e[t].radius = ((r * (e.length - t)) / (r * e.length)) * a * o
        }
        e[t]._proportion_ = e[t].data / e[0].data
    }
    return e
}
function getRoseDataPoints(a, i, r, o) {
    var n = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 1
    var l = 0
    var s = 0
    var h = []
    for (let e = 0; e < a.length; e++) {
        let t = a[e]
        t.data = t.data === null ? 0 : t.data
        l += t.data
        h.push(t.data)
    }
    var c = Math.min.apply(null, h)
    var d = Math.max.apply(null, h)
    var x = o - r
    for (let e = 0; e < a.length; e++) {
        let t = a[e]
        t.data = t.data === null ? 0 : t.data
        if (l === 0) {
            t._proportion_ = (1 / a.length) * n
            t._rose_proportion_ = (1 / a.length) * n
        } else {
            t._proportion_ = (t.data / l) * n
            if (i == "area") {
                t._rose_proportion_ = (1 / a.length) * n
            } else {
                t._rose_proportion_ = (t.data / l) * n
            }
        }
        t._radius_ = r + x * ((t.data - c) / (d - c)) || o
    }
    for (let e = 0; e < a.length; e++) {
        let t = a[e]
        t._start_ = s
        s += 2 * t._rose_proportion_ * Math.PI
    }
    return a
}
function getArcbarDataPoints(i, r) {
    var o = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1
    if (o == 1) {
        o = 0.999999
    }
    for (let a = 0; a < i.length; a++) {
        let t = i[a]
        t.data = t.data === null ? 0 : t.data
        let e
        if (r.type == "circle") {
            e = 2
        } else {
            if (r.direction == "ccw") {
                if (r.startAngle < r.endAngle) {
                    e = 2 + r.startAngle - r.endAngle
                } else {
                    e = r.startAngle - r.endAngle
                }
            } else {
                if (r.endAngle < r.startAngle) {
                    e = 2 + r.endAngle - r.startAngle
                } else {
                    e = r.startAngle - r.endAngle
                }
            }
        }
        t._proportion_ = e * t.data * o + r.startAngle
        if (r.direction == "ccw") {
            t._proportion_ = r.startAngle - e * t.data * o
        }
        if (t._proportion_ >= 2) {
            t._proportion_ = t._proportion_ % 2
        }
    }
    return i
}
function getGaugeArcbarDataPoints(i, r) {
    var o = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1
    if (o == 1) {
        o = 0.999999
    }
    for (let a = 0; a < i.length; a++) {
        let t = i[a]
        t.data = t.data === null ? 0 : t.data
        let e
        if (r.type == "circle") {
            e = 2
        } else {
            if (r.endAngle < r.startAngle) {
                e = 2 + r.endAngle - r.startAngle
            } else {
                e = r.startAngle - r.endAngle
            }
        }
        t._proportion_ = e * t.data * o + r.startAngle
        if (t._proportion_ >= 2) {
            t._proportion_ = t._proportion_ % 2
        }
    }
    return i
}
function getGaugeAxisPoints(e, a, t) {
    let i
    if (t < a) {
        i = 2 + t - a
    } else {
        i = a - t
    }
    let r = a
    for (let t = 0; t < e.length; t++) {
        e[t].value = e[t].value === null ? 0 : e[t].value
        e[t]._startAngle_ = r
        e[t]._endAngle_ = i * e[t].value + a
        if (e[t]._endAngle_ >= 2) {
            e[t]._endAngle_ = e[t]._endAngle_ % 2
        }
        r = e[t]._endAngle_
    }
    return e
}
function getGaugeDataPoints(i, r, o) {
    let n = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 1
    for (let a = 0; a < i.length; a++) {
        let e = i[a]
        e.data = e.data === null ? 0 : e.data
        if (o.pointer.color == "auto") {
            for (let t = 0; t < r.length; t++) {
                if (e.data <= r[t].value) {
                    e.color = r[t].color
                    break
                }
            }
        } else {
            e.color = o.pointer.color
        }
        let t
        if (o.endAngle < o.startAngle) {
            t = 2 + o.endAngle - o.startAngle
        } else {
            t = o.startAngle - o.endAngle
        }
        e._endAngle_ = t * e.data + o.startAngle
        e._oldAngle_ = o.oldAngle
        if (o.oldAngle < o.endAngle) {
            e._oldAngle_ += 2
        }
        if (e.data >= o.oldData) {
            e._proportion_ = (e._endAngle_ - e._oldAngle_) * n + o.oldAngle
        } else {
            e._proportion_ = e._oldAngle_ - (e._oldAngle_ - e._endAngle_) * n
        }
        if (e._proportion_ >= 2) {
            e._proportion_ = e._proportion_ % 2
        }
    }
    return i
}
function getPieTextMaxLength(i, r, o, n) {
    i = getPieDataPoints(i)
    let l = 0
    for (let a = 0; a < i.length; a++) {
        let t = i[a]
        let e = t.formatter ? t.formatter(+t._proportion_.toFixed(2)) : util.toFixed(t._proportion_ * 100) + "%"
        l = Math.max(l, measureText(e, t.textSize * n.pix || r.fontSize, o))
    }
    return l
}
function fixColumeData(t, i, r, o, e, n) {
    return t.map(function (t) {
        if (t === null) {
            return null
        }
        var e = 0
        var a = 0
        if (n.type == "mix") {
            e = n.extra.mix.column.seriesGap * n.pix || 0
            a = n.extra.mix.column.categoryGap * n.pix || 0
        } else {
            e = n.extra.column.seriesGap * n.pix || 0
            a = n.extra.column.categoryGap * n.pix || 0
        }
        e = Math.min(e, i / r)
        a = Math.min(a, i / r)
        t.width = Math.ceil((i - 2 * a - e * (r - 1)) / r)
        if (n.extra.mix && n.extra.mix.column.width && +n.extra.mix.column.width > 0) {
            t.width = Math.min(t.width, +n.extra.mix.column.width * n.pix)
        }
        if (n.extra.column && n.extra.column.width && +n.extra.column.width > 0) {
            t.width = Math.min(t.width, +n.extra.column.width * n.pix)
        }
        if (t.width <= 0) {
            t.width = 1
        }
        t.x += (o + 0.5 - r / 2) * (t.width + e)
        return t
    })
}
function fixBarData(t, i, r, o, e, n) {
    return t.map(function (t) {
        if (t === null) {
            return null
        }
        var e = 0
        var a = 0
        e = n.extra.bar.seriesGap * n.pix || 0
        a = n.extra.bar.categoryGap * n.pix || 0
        e = Math.min(e, i / r)
        a = Math.min(a, i / r)
        t.width = Math.ceil((i - 2 * a - e * (r - 1)) / r)
        if (n.extra.bar && n.extra.bar.width && +n.extra.bar.width > 0) {
            t.width = Math.min(t.width, +n.extra.bar.width * n.pix)
        }
        if (t.width <= 0) {
            t.width = 1
        }
        t.y += (o + 0.5 - r / 2) * (t.width + e)
        return t
    })
}
function fixColumeMeterData(t, e, a, i, r, o, n) {
    var l = o.extra.column.categoryGap * o.pix || 0
    return t.map(function (t) {
        if (t === null) {
            return null
        }
        t.width = e - 2 * l
        if (o.extra.column && o.extra.column.width && +o.extra.column.width > 0) {
            t.width = Math.min(t.width, +o.extra.column.width * o.pix)
        }
        if (i > 0) {
            t.width -= n
        }
        return t
    })
}
function fixColumeStackData(t, a, e, i, r, o, n) {
    var l = o.extra.column.categoryGap * o.pix || 0
    return t.map(function (t, e) {
        if (t === null) {
            return null
        }
        t.width = Math.ceil(a - 2 * l)
        if (o.extra.column && o.extra.column.width && +o.extra.column.width > 0) {
            t.width = Math.min(t.width, +o.extra.column.width * o.pix)
        }
        if (t.width <= 0) {
            t.width = 1
        }
        return t
    })
}
function fixBarStackData(t, a, e, i, r, o, n) {
    var l = o.extra.bar.categoryGap * o.pix || 0
    return t.map(function (t, e) {
        if (t === null) {
            return null
        }
        t.width = Math.ceil(a - 2 * l)
        if (o.extra.bar && o.extra.bar.width && +o.extra.bar.width > 0) {
            t.width = Math.min(t.width, +o.extra.bar.width * o.pix)
        }
        if (t.width <= 0) {
            t.width = 1
        }
        return t
    })
}
function getXAxisPoints(t, e, h) {
    var a = e.width - e.area[1] - e.area[3]
    var i = e.enableScroll ? Math.min(e.xAxis.itemCount, t.length) : t.length
    if ((e.type == "line" || e.type == "area" || e.type == "scatter" || e.type == "bubble" || e.type == "bar") && i > 1 && e.xAxis.boundaryGap == "justify") {
        i -= 1
    }
    var r = 0
    if (e.type == "mount" && e.extra && e.extra.mount && e.extra.mount.widthRatio && e.extra.mount.widthRatio > 1) {
        if (e.extra.mount.widthRatio > 2) e.extra.mount.widthRatio = 2
        r = e.extra.mount.widthRatio - 1
        i += r
    }
    var o = a / i
    var n = []
    var l = e.area[3]
    var s = e.width - e.area[1]
    t.forEach(function (t, e) {
        n.push(l + (r / 2) * o + e * o)
    })
    if (e.xAxis.boundaryGap !== "justify") {
        if (e.enableScroll === true) {
            n.push(l + r * o + t.length * o)
        } else {
            n.push(s)
        }
    }
    return { xAxisPoints: n, startX: l, endX: s, eachSpacing: o }
}
function getCandleDataPoints(t, l, s, h, c, d, a) {
    var x = arguments.length > 7 && arguments[7] !== undefined ? arguments[7] : 1
    var e = []
    var f = d.height - d.area[0] - d.area[2]
    t.forEach(function (t, o) {
        if (t === null) {
            e.push(null)
        } else {
            var n = []
            t.forEach(function (t, e) {
                var a = {}
                a.x = h[o] + Math.round(c / 2)
                var i = t.value || t
                var r = (f * (i - l)) / (s - l)
                r *= x
                a.y = d.height - Math.round(r) - d.area[2]
                n.push(a)
            })
            e.push(n)
        }
    })
    return e
}
function getDataPoints(t, a, n, l, s, h, e) {
    var c = arguments.length > 7 && arguments[7] !== undefined ? arguments[7] : 1
    var d = "center"
    if (h.type == "line" || h.type == "area" || h.type == "scatter" || h.type == "bubble") {
        d = h.xAxis.boundaryGap
    }
    var x = []
    var f = h.height - h.area[0] - h.area[2]
    var p = h.width - h.area[1] - h.area[3]
    t.forEach(function (i, t) {
        if (i === null) {
            x.push(null)
        } else {
            var r = {}
            r.color = i.color
            r.x = l[t]
            var o = i
            if (typeof i === "object" && i !== null) {
                if (i.constructor.toString().indexOf("Array") > -1) {
                    let t, e, a
                    t = [].concat(h.chartData.xAxisData.ranges)
                    e = t.shift()
                    a = t.pop()
                    o = i[1]
                    r.x = h.area[3] + (p * (i[0] - e)) / (a - e)
                    if (h.type == "bubble") {
                        r.r = i[2]
                        r.t = i[3]
                    }
                } else {
                    o = i.value
                }
            }
            if (d == "center") {
                r.x += s / 2
            }
            var e = (f * (o - a)) / (n - a)
            e *= c
            r.y = h.height - e - h.area[2]
            x.push(r)
        }
    })
    return x
}
function getLineDataPoints(t, a, n, l, s, h, e, p, c) {
    var c = arguments.length > 8 && arguments[8] !== undefined ? arguments[8] : 1
    var d = h.xAxis.boundaryGap
    var x = []
    var f = h.height - h.area[0] - h.area[2]
    var u = h.width - h.area[1] - h.area[3]
    t.forEach(function (i, t) {
        if (i === null) {
            x.push(null)
        } else {
            var r = {}
            r.color = i.color
            if (p.animation == "vertical") {
                r.x = l[t]
                var o = i
                if (typeof i === "object" && i !== null) {
                    if (i.constructor.toString().indexOf("Array") > -1) {
                        let t, e, a
                        t = [].concat(h.chartData.xAxisData.ranges)
                        e = t.shift()
                        a = t.pop()
                        o = i[1]
                        r.x = h.area[3] + (u * (i[0] - e)) / (a - e)
                    } else {
                        o = i.value
                    }
                }
                if (d == "center") {
                    r.x += s / 2
                }
                var e = (f * (o - a)) / (n - a)
                e *= c
                r.y = h.height - e - h.area[2]
                x.push(r)
            } else {
                r.x = l[0] + s * t * c
                var o = i
                if (d == "center") {
                    r.x += s / 2
                }
                var e = (f * (o - a)) / (n - a)
                r.y = h.height - e - h.area[2]
                x.push(r)
            }
        }
    })
    return x
}
function getColumnDataPoints(t, a, n, l, s, h, e, i, c) {
    var c = arguments.length > 8 && arguments[8] !== undefined ? arguments[8] : 1
    var d = []
    var x = h.height - h.area[0] - h.area[2]
    var f = h.width - h.area[1] - h.area[3]
    t.forEach(function (i, t) {
        if (i === null) {
            d.push(null)
        } else {
            var r = {}
            r.color = i.color
            r.x = l[t]
            var o = i
            if (typeof i === "object" && i !== null) {
                if (i.constructor.toString().indexOf("Array") > -1) {
                    let t, e, a
                    t = [].concat(h.chartData.xAxisData.ranges)
                    e = t.shift()
                    a = t.pop()
                    o = i[1]
                    r.x = h.area[3] + (f * (i[0] - e)) / (a - e)
                } else {
                    o = i.value
                }
            }
            r.x += s / 2
            var e = (x * (o * c - a)) / (n - a)
            r.y = h.height - e - h.area[2]
            d.push(r)
        }
    })
    return d
}
function getMountDataPoints(t, o, n, l, s, h, e, a) {
    var c = arguments.length > 8 && arguments[8] !== undefined ? arguments[8] : 1
    var d = []
    var x = h.height - h.area[0] - h.area[2]
    var i = h.width - h.area[1] - h.area[3]
    var f = s * e.widthRatio
    t.forEach(function (t, e) {
        if (t === null) {
            d.push(null)
        } else {
            var a = {}
            a.color = t.color
            a.x = l[e]
            a.x += s / 2
            var i = t.data
            var r = (x * (i * c - o)) / (n - o)
            a.y = h.height - r - h.area[2]
            a.value = i
            a.width = f
            d.push(a)
        }
    })
    return d
}
function getBarDataPoints(t, o, n, l, e, s, a) {
    var h = arguments.length > 7 && arguments[7] !== undefined ? arguments[7] : 1
    var c = []
    var i = s.height - s.area[0] - s.area[2]
    var d = s.width - s.area[1] - s.area[3]
    t.forEach(function (t, e) {
        if (t === null) {
            c.push(null)
        } else {
            var a = {}
            a.color = t.color
            a.y = l[e]
            var i = t
            if (typeof t === "object" && t !== null) {
                i = t.value
            }
            var r = (d * (i - o)) / (n - o)
            r *= h
            a.height = r
            a.value = i
            a.x = r + s.area[3]
            c.push(a)
        }
    })
    return c
}
function getStackDataPoints(t, s, h, c, g, d, e, x, y) {
    var f = arguments.length > 9 && arguments[9] !== undefined ? arguments[9] : 1
    var p = []
    var u = d.height - d.area[0] - d.area[2]
    t.forEach(function (t, e) {
        if (t === null) {
            p.push(null)
        } else {
            var a = {}
            a.color = t.color
            a.x = c[e] + Math.round(g / 2)
            if (x > 0) {
                var i = 0
                for (let t = 0; t <= x; t++) {
                    i += y[t].data[e]
                }
                var r = i - t
                var o = (u * (i - s)) / (h - s)
                var n = (u * (r - s)) / (h - s)
            } else {
                var i = t
                if (typeof t === "object" && t !== null) {
                    i = t.value
                }
                var o = (u * (i - s)) / (h - s)
                var n = 0
            }
            var l = n
            o *= f
            l *= f
            a.y = d.height - Math.round(o) - d.area[2]
            a.y0 = d.height - Math.round(l) - d.area[2]
            p.push(a)
        }
    })
    return p
}
function getBarStackDataPoints(t, s, h, c, e, d, a, x, g) {
    var f = arguments.length > 9 && arguments[9] !== undefined ? arguments[9] : 1
    var p = []
    var u = d.width - d.area[1] - d.area[3]
    t.forEach(function (t, e) {
        if (t === null) {
            p.push(null)
        } else {
            var a = {}
            a.color = t.color
            a.y = c[e]
            if (x > 0) {
                var i = 0
                for (let t = 0; t <= x; t++) {
                    i += g[t].data[e]
                }
                var r = i - t
                var o = (u * (i - s)) / (h - s)
                var n = (u * (r - s)) / (h - s)
            } else {
                var i = t
                if (typeof t === "object" && t !== null) {
                    i = t.value
                }
                var o = (u * (i - s)) / (h - s)
                var n = 0
            }
            var l = n
            o *= f
            l *= f
            a.height = o - l
            a.x = d.area[3] + o
            a.x0 = d.area[3] + l
            p.push(a)
        }
    })
    return p
}
function getYAxisTextList(t, e, h, c, a) {
    var d = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : -1
    var i
    if (c == "stack") {
        i = dataCombineStack(t, e.categories.length)
    } else {
        i = dataCombine(t)
    }
    var r = []
    i = i.filter(function (t) {
        if (typeof t === "object" && t !== null) {
            if (t.constructor.toString().indexOf("Array") > -1) {
                return t !== null
            } else {
                return t.value !== null
            }
        } else {
            return t !== null
        }
    })
    i.map(function (t) {
        if (typeof t === "object") {
            if (t.constructor.toString().indexOf("Array") > -1) {
                if (e.type == "candle") {
                    t.map(function (t) {
                        r.push(t)
                    })
                } else {
                    r.push(t[1])
                }
            } else {
                r.push(t.value)
            }
        } else {
            r.push(t)
        }
    })
    var o = a.min || 0
    var n = a.max || 0
    if (r.length > 0) {
        o = Math.min.apply(this, r)
        n = Math.max.apply(this, r)
    }
    if (o === n) {
        if (n == 0) {
            n = 10
        } else {
            o = 0
        }
    }
    var l = getDataRange(o, n)
    var x = a.min === undefined || a.min === null ? l.minRange : a.min
    var f = a.max === undefined || a.max === null ? l.maxRange : a.max
    var p = (f - x) / e.yAxis.splitNumber
    var u = []
    for (var s = 0; s <= e.yAxis.splitNumber; s++) {
        u.push(x + p * s)
    }
    return u.reverse()
}
function calYAxisData(a, o, e, n) {
    var l = assign({}, { type: "" }, o.extra.column)
    var t = o.yAxis.data.length
    var s = new Array(t)
    if (t > 0) {
        for (let e = 0; e < t; e++) {
            s[e] = []
            for (let t = 0; t < a.length; t++) {
                if (a[t].index == e) {
                    s[e].push(a[t])
                }
            }
        }
        var h = new Array(t)
        var c = new Array(t)
        var d = new Array(t)
        for (let r = 0; r < t; r++) {
            let i = o.yAxis.data[r]
            if (o.yAxis.disabled == true) {
                i.disabled = true
            }
            if (i.type === "categories") {
                if (!i.formatter) {
                    i.formatter = (t, e, a) => {
                        return t + (i.unit || "")
                    }
                }
                i.categories = i.categories || o.categories
                h[r] = i.categories
            } else {
                if (!i.formatter) {
                    i.formatter = (t, e, a) => {
                        return util.toFixed(t, i.tofix || 0) + (i.unit || "")
                    }
                }
                h[r] = getYAxisTextList(s[r], o, e, l.type, i, r)
            }
            let a = i.fontSize * o.pix || e.fontSize
            d[r] = { position: i.position ? i.position : "left", width: 0 }
            c[r] = h[r].map(function (t, e) {
                t = i.formatter(t, e, o)
                d[r].width = Math.max(d[r].width, measureText(t, a, n) + 5)
                return t
            })
            let t = i.calibration ? 4 * o.pix : 0
            d[r].width += t + 3 * o.pix
            if (i.disabled === true) {
                d[r].width = 0
            }
        }
    } else {
        var h = new Array(1)
        var c = new Array(1)
        var d = new Array(1)
        if (o.type === "bar") {
            h[0] = o.categories
            if (!o.yAxis.formatter) {
                o.yAxis.formatter = (t, e, a) => {
                    return t + (a.yAxis.unit || "")
                }
            }
        } else {
            if (!o.yAxis.formatter) {
                o.yAxis.formatter = (t, e, a) => {
                    return t.toFixed(a.yAxis.tofix) + (a.yAxis.unit || "")
                }
            }
            h[0] = getYAxisTextList(a, o, e, l.type, {})
        }
        d[0] = { position: "left", width: 0 }
        var i = o.yAxis.fontSize * o.pix || e.fontSize
        c[0] = h[0].map(function (t, e) {
            t = o.yAxis.formatter(t, e, o)
            d[0].width = Math.max(d[0].width, measureText(t, i, n) + 5)
            return t
        })
        d[0].width += 3 * o.pix
        if (o.yAxis.disabled === true) {
            d[0] = { position: "left", width: 0 }
            o.yAxis.data[0] = { disabled: true }
        } else {
            o.yAxis.data[0] = { disabled: false, position: "left", max: o.yAxis.max, min: o.yAxis.min, formatter: o.yAxis.formatter }
            if (o.type === "bar") {
                o.yAxis.data[0].categories = o.categories
                o.yAxis.data[0].type = "categories"
            }
        }
    }
    return { rangesFormat: c, ranges: h, yAxisWidth: d }
}
function calTooltipYAxisData(r, t, o, e, a) {
    let n = [].concat(o.chartData.yAxisData.ranges)
    let l = o.height - o.area[0] - o.area[2]
    let s = o.area[0]
    let h = []
    for (let i = 0; i < n.length; i++) {
        let t = Math.max.apply(this, n[i])
        let e = Math.min.apply(this, n[i])
        let a = t - ((t - e) * (r - s)) / l
        a = o.yAxis.data && o.yAxis.data[i].formatter ? o.yAxis.data[i].formatter(a, i, o) : a.toFixed(0)
        h.push(String(a))
    }
    return h
}
function calMarkLineData(i, r) {
    let o, n
    let l = r.height - r.area[0] - r.area[2]
    for (let a = 0; a < i.length; a++) {
        i[a].yAxisIndex = i[a].yAxisIndex ? i[a].yAxisIndex : 0
        let t = [].concat(r.chartData.yAxisData.ranges[i[a].yAxisIndex])
        o = t.pop()
        n = t.shift()
        let e = (l * (i[a].value - o)) / (n - o)
        i[a].y = r.height - Math.round(e) - r.area[2]
    }
    return i
}
function contextRotate(t, e) {
    if (e.rotateLock !== true) {
        t.translate(e.height, 0)
        t.rotate((90 * Math.PI) / 180)
    } else if (e._rotate_ !== true) {
        t.translate(e.height, 0)
        t.rotate((90 * Math.PI) / 180)
        e._rotate_ = true
    }
}
function drawPointShape(t, e, a, i, r) {
    i.beginPath()
    if (r.dataPointShapeType == "hollow") {
        i.setStrokeStyle(e)
        i.setFillStyle(r.background)
        i.setLineWidth(2 * r.pix)
    } else {
        i.setStrokeStyle("#ffffff")
        i.setFillStyle(e)
        i.setLineWidth(1 * r.pix)
    }
    if (a === "diamond") {
        t.forEach(function (t, e) {
            if (t !== null) {
                i.moveTo(t.x, t.y - 4.5)
                i.lineTo(t.x - 4.5, t.y)
                i.lineTo(t.x, t.y + 4.5)
                i.lineTo(t.x + 4.5, t.y)
                i.lineTo(t.x, t.y - 4.5)
            }
        })
    } else if (a === "circle") {
        t.forEach(function (t, e) {
            if (t !== null) {
                i.moveTo(t.x + 2.5 * r.pix, t.y)
                i.arc(t.x, t.y, 3 * r.pix, 0, 2 * Math.PI, false)
            }
        })
    } else if (a === "square") {
        t.forEach(function (t, e) {
            if (t !== null) {
                i.moveTo(t.x - 3.5, t.y - 3.5)
                i.rect(t.x - 3.5, t.y - 3.5, 7, 7)
            }
        })
    } else if (a === "triangle") {
        t.forEach(function (t, e) {
            if (t !== null) {
                i.moveTo(t.x, t.y - 4.5)
                i.lineTo(t.x - 4.5, t.y + 4.5)
                i.lineTo(t.x + 4.5, t.y + 4.5)
                i.lineTo(t.x, t.y - 4.5)
            }
        })
    } else if (a === "none") {
        return
    }
    i.closePath()
    i.fill()
    i.stroke()
}
function drawActivePoint(t, e, a, i, r, o, n) {
    if (!r.tooltip) {
        return
    }
    if (r.tooltip.group.length > 0 && r.tooltip.group.includes(n) == false) {
        return
    }
    var l = typeof r.tooltip.index === "number" ? r.tooltip.index : r.tooltip.index[r.tooltip.group.indexOf(n)]
    i.beginPath()
    if (o.activeType == "hollow") {
        i.setStrokeStyle(e)
        i.setFillStyle(r.background)
        i.setLineWidth(2 * r.pix)
    } else {
        i.setStrokeStyle("#ffffff")
        i.setFillStyle(e)
        i.setLineWidth(1 * r.pix)
    }
    if (a === "diamond") {
        t.forEach(function (t, e) {
            if (t !== null && l == e) {
                i.moveTo(t.x, t.y - 4.5)
                i.lineTo(t.x - 4.5, t.y)
                i.lineTo(t.x, t.y + 4.5)
                i.lineTo(t.x + 4.5, t.y)
                i.lineTo(t.x, t.y - 4.5)
            }
        })
    } else if (a === "circle") {
        t.forEach(function (t, e) {
            if (t !== null && l == e) {
                i.moveTo(t.x + 2.5 * r.pix, t.y)
                i.arc(t.x, t.y, 3 * r.pix, 0, 2 * Math.PI, false)
            }
        })
    } else if (a === "square") {
        t.forEach(function (t, e) {
            if (t !== null && l == e) {
                i.moveTo(t.x - 3.5, t.y - 3.5)
                i.rect(t.x - 3.5, t.y - 3.5, 7, 7)
            }
        })
    } else if (a === "triangle") {
        t.forEach(function (t, e) {
            if (t !== null && l == e) {
                i.moveTo(t.x, t.y - 4.5)
                i.lineTo(t.x - 4.5, t.y + 4.5)
                i.lineTo(t.x + 4.5, t.y + 4.5)
                i.lineTo(t.x, t.y - 4.5)
            }
        })
    } else if (a === "none") {
        return
    }
    i.closePath()
    i.fill()
    i.stroke()
}
function drawRingTitle(t, e, a, i) {
    var r = t.title.fontSize || e.titleFontSize
    var o = t.subtitle.fontSize || e.subtitleFontSize
    var n = t.title.name || ""
    var l = t.subtitle.name || ""
    var c = t.title.color || t.fontColor
    var d = t.subtitle.color || t.fontColor
    var x = n ? r : 0
    var f = l ? o : 0
    var s = 5
    if (l) {
        var p = measureText(l, o * t.pix, a)
        var u = i.x - p / 2 + (t.subtitle.offsetX || 0) * t.pix
        var h = i.y + (o * t.pix) / 2 + (t.subtitle.offsetY || 0) * t.pix
        if (n) {
            h += (x * t.pix + s) / 2
        }
        a.beginPath()
        a.setFontSize(o * t.pix)
        a.setFillStyle(d)
        a.fillText(l, u, h)
        a.closePath()
        a.stroke()
    }
    if (n) {
        var g = measureText(n, r * t.pix, a)
        var y = i.x - g / 2 + (t.title.offsetX || 0)
        var v = i.y + (r * t.pix) / 2 + (t.title.offsetY || 0) * t.pix
        if (l) {
            v -= (f * t.pix + s) / 2
        }
        a.beginPath()
        a.setFontSize(r * t.pix)
        a.setFillStyle(c)
        a.fillText(n, y, v)
        a.closePath()
        a.stroke()
    }
}
function drawPointText(t, o, n, l, s) {
    var h = o.data
    var c = o.textOffset ? o.textOffset : 0
    t.forEach(function (t, e) {
        if (t !== null) {
            l.beginPath()
            var a = o.textSize ? o.textSize * s.pix : n.fontSize
            l.setFontSize(a)
            l.setFillStyle(o.textColor || s.fontColor)
            var i = h[e]
            if (typeof h[e] === "object" && h[e] !== null) {
                if (h[e].constructor.toString().indexOf("Array") > -1) {
                    i = h[e][1]
                } else {
                    i = h[e].value
                }
            }
            var r = o.formatter ? o.formatter(i, e, o, s) : i
            l.setTextAlign("center")
            l.fillText(String(r), t.x, t.y - 4 + c * s.pix)
            l.closePath()
            l.stroke()
            l.setTextAlign("left")
        }
    })
}
function drawColumePointText(t, n, l, s, h) {
    var c = n.data
    var d = n.textOffset ? n.textOffset : 0
    var x = h.extra.column.labelPosition
    t.forEach(function (t, e) {
        if (t !== null) {
            s.beginPath()
            var a = n.textSize ? n.textSize * h.pix : l.fontSize
            s.setFontSize(a)
            s.setFillStyle(n.textColor || h.fontColor)
            var i = c[e]
            if (typeof c[e] === "object" && c[e] !== null) {
                if (c[e].constructor.toString().indexOf("Array") > -1) {
                    i = c[e][1]
                } else {
                    i = c[e].value
                }
            }
            var r = n.formatter ? n.formatter(i, e, n, h) : i
            s.setTextAlign("center")
            var o = t.y - 4 * h.pix + d * h.pix
            if (t.y > n.zeroPoints) {
                o = t.y + d * h.pix + a
            }
            if (x == "insideTop") {
                o = t.y + a + d * h.pix
                if (t.y > n.zeroPoints) {
                    o = t.y - d * h.pix - 4 * h.pix
                }
            }
            if (x == "center") {
                o = t.y + d * h.pix + (h.height - h.area[2] - t.y + a) / 2
                if (n.zeroPoints < h.height - h.area[2]) {
                    o = t.y + d * h.pix + (n.zeroPoints - t.y + a) / 2
                }
                if (t.y > n.zeroPoints) {
                    o = t.y - d * h.pix - (t.y - n.zeroPoints - a) / 2
                }
                if (h.extra.column.type == "stack") {
                    o = t.y + d * h.pix + (t.y0 - t.y + a) / 2
                }
            }
            if (x == "bottom") {
                o = h.height - h.area[2] + d * h.pix - 4 * h.pix
                if (n.zeroPoints < h.height - h.area[2]) {
                    o = n.zeroPoints + d * h.pix - 4 * h.pix
                }
                if (t.y > n.zeroPoints) {
                    o = n.zeroPoints - d * h.pix + a + 2 * h.pix
                }
                if (h.extra.column.type == "stack") {
                    o = t.y0 + d * h.pix - 4 * h.pix
                }
            }
            s.fillText(String(r), t.x, o)
            s.closePath()
            s.stroke()
            s.setTextAlign("left")
        }
    })
}
function drawMountPointText(t, n, l, s, h, c) {
    var e = n.data
    var d = n.textOffset ? n.textOffset : 0
    var a = h.extra.mount.labelPosition
    t.forEach(function (t, e) {
        if (t !== null) {
            s.beginPath()
            var a = n[e].textSize ? n[e].textSize * h.pix : l.fontSize
            s.setFontSize(a)
            s.setFillStyle(n[e].textColor || h.fontColor)
            var i = t.value
            var r = n[e].formatter ? n[e].formatter(i, e, n, h) : i
            s.setTextAlign("center")
            var o = t.y - 4 * h.pix + d * h.pix
            if (t.y > c) {
                o = t.y + d * h.pix + a
            }
            s.fillText(String(r), t.x, o)
            s.closePath()
            s.stroke()
            s.setTextAlign("left")
        }
    })
}
function drawBarPointText(t, o, n, l, s) {
    var h = o.data
    var e = o.textOffset ? o.textOffset : 0
    t.forEach(function (t, e) {
        if (t !== null) {
            l.beginPath()
            var a = o.textSize ? o.textSize * s.pix : n.fontSize
            l.setFontSize(a)
            l.setFillStyle(o.textColor || s.fontColor)
            var i = h[e]
            if (typeof h[e] === "object" && h[e] !== null) {
                i = h[e].value
            }
            var r = o.formatter ? o.formatter(i, e, o, s) : i
            l.setTextAlign("left")
            l.fillText(String(r), t.x + 4 * s.pix, t.y + a / 2 - 3)
            l.closePath()
            l.stroke()
        }
    })
}
function drawGaugeLabel(e, a, i, r, o, n) {
    a -= e.width / 2 + e.labelOffset * r.pix
    a = a < 10 ? 10 : a
    let t
    if (e.endAngle < e.startAngle) {
        t = 2 + e.endAngle - e.startAngle
    } else {
        t = e.startAngle - e.endAngle
    }
    let c = t / e.splitLine.splitNumber
    let d = e.endNumber - e.startNumber
    let x = d / e.splitLine.splitNumber
    let l = e.startAngle
    let s = e.startNumber
    for (let t = 0; t < e.splitLine.splitNumber + 1; t++) {
        var h = { x: a * Math.cos(l * Math.PI), y: a * Math.sin(l * Math.PI) }
        var f = e.formatter ? e.formatter(s, t, r) : s
        h.x += i.x - measureText(f, o.fontSize, n) / 2
        h.y += i.y
        var p = h.x
        var u = h.y
        n.beginPath()
        n.setFontSize(o.fontSize)
        n.setFillStyle(e.labelColor || r.fontColor)
        n.fillText(f, p, u + o.fontSize / 2)
        n.closePath()
        n.stroke()
        l += c
        if (l >= 2) {
            l = l % 2
        }
        s += x
    }
}
function drawRadarLabel(t, s, h, c, d, x) {
    var f = c.extra.radar || {}
    t.forEach(function (t, e) {
        if (f.labelPointShow === true && c.categories[e] !== "") {
            var a = { x: s * Math.cos(t), y: s * Math.sin(t) }
            var i = convertCoordinateOrigin(a.x, a.y, h)
            x.setFillStyle(f.labelPointColor)
            x.beginPath()
            x.arc(i.x, i.y, f.labelPointRadius * c.pix, 0, 2 * Math.PI, false)
            x.closePath()
            x.fill()
        }
        if (f.labelShow === true) {
            var r = { x: (s + d.radarLabelTextMargin * c.pix) * Math.cos(t), y: (s + d.radarLabelTextMargin * c.pix) * Math.sin(t) }
            var o = convertCoordinateOrigin(r.x, r.y, h)
            var n = o.x
            var l = o.y
            if (util.approximatelyEqual(r.x, 0)) {
                n -= measureText(c.categories[e] || "", d.fontSize, x) / 2
            } else if (r.x < 0) {
                n -= measureText(c.categories[e] || "", d.fontSize, x)
            }
            x.beginPath()
            x.setFontSize(d.fontSize)
            x.setFillStyle(f.labelColor || c.fontColor)
            x.fillText(c.categories[e] || "", n, l + d.fontSize / 2)
            x.closePath()
            x.stroke()
        }
    })
}
function drawPieText(n, d, x, f, t, l) {
    var p = x.pieChartLinePadding
    var u = []
    var g = null
    var y = n.map(function (t, e) {
        var a = t.formatter ? t.formatter(t, e, n, d) : util.toFixed(t._proportion_.toFixed(4) * 100) + "%"
        a = t.labelText ? t.labelText : a
        var i = 2 * Math.PI - (t._start_ + (2 * Math.PI * t._proportion_) / 2)
        if (t._rose_proportion_) {
            i = 2 * Math.PI - (t._start_ + (2 * Math.PI * t._rose_proportion_) / 2)
        }
        var r = t.color
        var o = t._radius_
        return { arc: i, text: a, color: r, radius: o, textColor: t.textColor, textSize: t.textSize, labelShow: t.labelShow }
    })
    for (let c = 0; c < y.length; c++) {
        let t = y[c]
        let e = Math.cos(t.arc) * (t.radius + p)
        let a = Math.sin(t.arc) * (t.radius + p)
        let i = Math.cos(t.arc) * t.radius
        let r = Math.sin(t.arc) * t.radius
        let o = e >= 0 ? e + x.pieChartTextPadding : e - x.pieChartTextPadding
        let n = a
        let l = measureText(t.text, t.textSize * d.pix || x.fontSize, f)
        let s = n
        if (g && util.isSameXCoordinateArea(g.start, { x: o })) {
            if (o > 0) {
                s = Math.min(n, g.start.y)
            } else if (e < 0) {
                s = Math.max(n, g.start.y)
            } else {
                if (n > 0) {
                    s = Math.max(n, g.start.y)
                } else {
                    s = Math.min(n, g.start.y)
                }
            }
        }
        if (o < 0) {
            o -= l
        }
        let h = { lineStart: { x: i, y: r }, lineEnd: { x: e, y: a }, start: { x: o, y: s }, width: l, height: x.fontSize, text: t.text, color: t.color, textColor: t.textColor, textSize: t.textSize }
        g = avoidCollision(h, g)
        u.push(g)
    }
    for (let n = 0; n < u.length; n++) {
        if (y[n].labelShow === false) {
            continue
        }
        let t = u[n]
        let e = convertCoordinateOrigin(t.lineStart.x, t.lineStart.y, l)
        let a = convertCoordinateOrigin(t.lineEnd.x, t.lineEnd.y, l)
        let i = convertCoordinateOrigin(t.start.x, t.start.y, l)
        f.setLineWidth(1 * d.pix)
        f.setFontSize(t.textSize * d.pix || x.fontSize)
        f.beginPath()
        f.setStrokeStyle(t.color)
        f.setFillStyle(t.color)
        f.moveTo(e.x, e.y)
        let r = t.start.x < 0 ? i.x + t.width : i.x
        let o = t.start.x < 0 ? i.x - 5 : i.x + 5
        f.quadraticCurveTo(a.x, a.y, r, i.y)
        f.moveTo(e.x, e.y)
        f.stroke()
        f.closePath()
        f.beginPath()
        f.moveTo(i.x + t.width, i.y)
        f.arc(r, i.y, 2 * d.pix, 0, 2 * Math.PI)
        f.closePath()
        f.fill()
        f.beginPath()
        f.setFontSize(t.textSize * d.pix || x.fontSize)
        f.setFillStyle(t.textColor || d.fontColor)
        f.fillText(t.text, o, i.y + 3)
        f.closePath()
        f.stroke()
        f.closePath()
    }
}
function drawToolTipSplitLine(r, o, n, l) {
    var s = o.extra.tooltip || {}
    s.gridType = s.gridType == undefined ? "solid" : s.gridType
    s.dashLength = s.dashLength == undefined ? 4 : s.dashLength
    var t = o.area[0]
    var h = o.height - o.area[2]
    if (s.gridType == "dash") {
        l.setLineDash([s.dashLength, s.dashLength])
    }
    l.setStrokeStyle(s.gridColor || "#cccccc")
    l.setLineWidth(1 * o.pix)
    l.beginPath()
    l.moveTo(r, t)
    l.lineTo(r, h)
    l.stroke()
    l.setLineDash([])
    if (s.xAxisLabel) {
        let t = o.categories[o.tooltip.index]
        l.setFontSize(n.fontSize)
        let e = measureText(t, n.fontSize, l)
        let a = r - 0.5 * e
        let i = h + 2 * o.pix
        l.beginPath()
        l.setFillStyle(hexToRgb(s.labelBgColor || n.toolTipBackground, s.labelBgOpacity || n.toolTipOpacity))
        l.setStrokeStyle(s.labelBgColor || n.toolTipBackground)
        l.setLineWidth(1 * o.pix)
        l.rect(a - s.boxPadding * o.pix, i, e + 2 * s.boxPadding * o.pix, n.fontSize + 2 * s.boxPadding * o.pix)
        l.closePath()
        l.stroke()
        l.fill()
        l.beginPath()
        l.setFontSize(n.fontSize)
        l.setFillStyle(s.labelFontColor || o.fontColor)
        l.fillText(String(t), a, i + s.boxPadding * o.pix + n.fontSize)
        l.closePath()
        l.stroke()
    }
}
function drawMarkLine(h, t, c) {
    let e = assign({}, { type: "solid", dashLength: 4, data: [] }, h.extra.markLine)
    let a = h.area[3]
    let i = h.width - h.area[1]
    let r = calMarkLineData(e.data, h)
    for (let t = 0; t < r.length; t++) {
        let s = assign({}, { lineColor: "#DE4A42", showLabel: false, labelFontSize: 13, labelPadding: 6, labelFontColor: "#666666", labelBgColor: "#DFE8FF", labelBgOpacity: 0.8, labelAlign: "left", labelOffsetX: 0, labelOffsetY: 0 }, r[t])
        if (e.type == "dash") {
            c.setLineDash([e.dashLength, e.dashLength])
        }
        c.setStrokeStyle(s.lineColor)
        c.setLineWidth(1 * h.pix)
        c.beginPath()
        c.moveTo(a, s.y)
        c.lineTo(i, s.y)
        c.stroke()
        c.setLineDash([])
        if (s.showLabel) {
            let t = s.labelFontSize * h.pix
            let e = s.labelText ? s.labelText : s.value
            c.setFontSize(t)
            let a = measureText(e, t, c)
            let i = a + s.labelPadding * h.pix * 2
            let r = s.labelAlign == "left" ? h.area[3] - i : h.width - h.area[1]
            r += s.labelOffsetX
            let o = s.y - 0.5 * t - s.labelPadding * h.pix
            o += s.labelOffsetY
            let n = r + s.labelPadding * h.pix
            let l = s.y
            c.setFillStyle(hexToRgb(s.labelBgColor, s.labelBgOpacity))
            c.setStrokeStyle(s.labelBgColor)
            c.setLineWidth(1 * h.pix)
            c.beginPath()
            c.rect(r, o, i, t + 2 * s.labelPadding * h.pix)
            c.closePath()
            c.stroke()
            c.fill()
            c.setFontSize(t)
            c.setTextAlign("left")
            c.setFillStyle(s.labelFontColor)
            c.fillText(String(e), n, o + t + (s.labelPadding * h.pix) / 2)
            c.stroke()
            c.setTextAlign("left")
        }
    }
}
function drawToolTipHorizentalLine(x, f, p, t, e) {
    var u = assign({}, { gridType: "solid", dashLength: 4 }, x.extra.tooltip)
    var a = x.area[3]
    var i = x.width - x.area[1]
    if (u.gridType == "dash") {
        p.setLineDash([u.dashLength, u.dashLength])
    }
    p.setStrokeStyle(u.gridColor || "#cccccc")
    p.setLineWidth(1 * x.pix)
    p.beginPath()
    p.moveTo(a, x.tooltip.offset.y)
    p.lineTo(i, x.tooltip.offset.y)
    p.stroke()
    p.setLineDash([])
    if (u.yAxisLabel) {
        let l = u.boxPadding * x.pix
        let s = calTooltipYAxisData(x.tooltip.offset.y, x.series, x, f, t)
        let h = x.chartData.yAxisData.yAxisWidth
        let c = x.area[3]
        let d = x.width - x.area[1]
        for (let n = 0; n < s.length; n++) {
            p.setFontSize(u.fontSize * x.pix)
            let t = measureText(s[n], u.fontSize * x.pix, p)
            let e, a, i
            if (h[n].position == "left") {
                e = c - (t + l * 2) - 2 * x.pix
                a = Math.max(e, e + t + l * 2)
            } else {
                e = d + 2 * x.pix
                a = Math.max(e + h[n].width, e + t + l * 2)
            }
            i = a - e
            let r = e + (i - t) / 2
            let o = x.tooltip.offset.y
            p.beginPath()
            p.setFillStyle(hexToRgb(u.labelBgColor || f.toolTipBackground, u.labelBgOpacity || f.toolTipOpacity))
            p.setStrokeStyle(u.labelBgColor || f.toolTipBackground)
            p.setLineWidth(1 * x.pix)
            p.rect(e, o - 0.5 * f.fontSize - l, i, f.fontSize + 2 * l)
            p.closePath()
            p.stroke()
            p.fill()
            p.beginPath()
            p.setFontSize(f.fontSize)
            p.setFillStyle(u.labelFontColor || x.fontColor)
            p.fillText(s[n], r, o + 0.5 * f.fontSize)
            p.closePath()
            p.stroke()
            if (h[n].position == "left") {
                c -= h[n].width + x.yAxis.padding * x.pix
            } else {
                d += h[n].width + x.yAxis.padding * x.pix
            }
        }
    }
}
function drawToolTipSplitArea(t, e, a, i, r) {
    var o = assign({}, { activeBgColor: "#000000", activeBgOpacity: 0.08, activeWidth: r }, e.extra.column)
    o.activeWidth = o.activeWidth > r ? r : o.activeWidth
    var n = e.area[0]
    var l = e.height - e.area[2]
    i.beginPath()
    i.setFillStyle(hexToRgb(o.activeBgColor, o.activeBgOpacity))
    i.rect(t - o.activeWidth / 2, n, o.activeWidth, l - n)
    i.closePath()
    i.fill()
    i.setFillStyle("#FFFFFF")
}
function drawBarToolTipSplitArea(t, e, a, i, r) {
    var o = assign({}, { activeBgColor: "#000000", activeBgOpacity: 0.08 }, e.extra.bar)
    var n = e.area[3]
    var l = e.width - e.area[1]
    i.beginPath()
    i.setFillStyle(hexToRgb(o.activeBgColor, o.activeBgOpacity))
    i.rect(n, t - r / 2, l - n, r)
    i.closePath()
    i.fill()
    i.setFillStyle("#FFFFFF")
}
function drawToolTip(e, r, o, a, n, i, f) {
    var l = assign({}, { showBox: true, showArrow: true, showCategory: false, bgColor: "#000000", bgOpacity: 0.7, borderColor: "#000000", borderWidth: 0, borderRadius: 0, borderOpacity: 0.7, boxPadding: 3, fontColor: "#FFFFFF", fontSize: 13, lineHeight: 20, legendShow: true, legendShape: "auto", splitLine: true }, o.extra.tooltip)
    if (l.showCategory == true && o.categories) {
        e.unshift({ text: o.categories[o.tooltip.index], color: null })
    }
    var s = l.fontSize * o.pix
    var p = l.lineHeight * o.pix
    var h = l.boxPadding * o.pix
    var c = s
    var u = 5 * o.pix
    if (l.legendShow == false) {
        c = 0
        u = 0
    }
    var d = l.showArrow ? 8 * o.pix : 0
    var g = false
    if (o.type == "line" || o.type == "mount" || o.type == "area" || o.type == "candle" || o.type == "mix") {
        if (l.splitLine == true) {
            drawToolTipSplitLine(o.tooltip.offset.x, o, a, n)
        }
    }
    r = assign({ x: 0, y: 0 }, r)
    r.y -= 8 * o.pix
    var y = e.map(function (t) {
        return measureText(t.text, s, n)
    })
    var x = c + u + 4 * h + Math.max.apply(null, y)
    var v = 2 * h + e.length * p
    if (l.showBox == false) {
        return
    }
    if (r.x - Math.abs(o._scrollDistance_ || 0) + d + x > o.width) {
        g = true
    }
    if (v + r.y > o.height) {
        r.y = o.height - v
    }
    n.beginPath()
    n.setFillStyle(hexToRgb(l.bgColor, l.bgOpacity))
    n.setLineWidth(l.borderWidth * o.pix)
    n.setStrokeStyle(hexToRgb(l.borderColor, l.borderOpacity))
    var t = l.borderRadius
    if (g) {
        if (x + d > o.width) {
            r.x = o.width + Math.abs(o._scrollDistance_ || 0) + d + (x - o.width)
        }
        if (x > r.x) {
            r.x = o.width + Math.abs(o._scrollDistance_ || 0) + d + (x - o.width)
        }
        if (l.showArrow) {
            n.moveTo(r.x, r.y + 10 * o.pix)
            n.lineTo(r.x - d, r.y + 10 * o.pix + 5 * o.pix)
        }
        n.arc(r.x - d - t, r.y + v - t, t, 0, Math.PI / 2, false)
        n.arc(r.x - d - Math.round(x) + t, r.y + v - t, t, Math.PI / 2, Math.PI, false)
        n.arc(r.x - d - Math.round(x) + t, r.y + t, t, -Math.PI, -Math.PI / 2, false)
        n.arc(r.x - d - t, r.y + t, t, -Math.PI / 2, 0, false)
        if (l.showArrow) {
            n.lineTo(r.x - d, r.y + 10 * o.pix - 5 * o.pix)
            n.lineTo(r.x, r.y + 10 * o.pix)
        }
    } else {
        if (l.showArrow) {
            n.moveTo(r.x, r.y + 10 * o.pix)
            n.lineTo(r.x + d, r.y + 10 * o.pix - 5 * o.pix)
        }
        n.arc(r.x + d + t, r.y + t, t, -Math.PI, -Math.PI / 2, false)
        n.arc(r.x + d + Math.round(x) - t, r.y + t, t, -Math.PI / 2, 0, false)
        n.arc(r.x + d + Math.round(x) - t, r.y + v - t, t, 0, Math.PI / 2, false)
        n.arc(r.x + d + t, r.y + v - t, t, Math.PI / 2, Math.PI, false)
        if (l.showArrow) {
            n.lineTo(r.x + d, r.y + 10 * o.pix + 5 * o.pix)
            n.lineTo(r.x, r.y + 10 * o.pix)
        }
    }
    n.closePath()
    n.fill()
    if (l.borderWidth > 0) {
        n.stroke()
    }
    if (l.legendShow) {
        e.forEach(function (t, e) {
            if (t.color !== null) {
                n.beginPath()
                n.setFillStyle(t.color)
                var a = r.x + d + 2 * h
                var i = r.y + (p - s) / 2 + p * e + h + 1
                if (g) {
                    a = r.x - x - d + 2 * h
                }
                switch (t.legendShape) {
                    case "line":
                        n.moveTo(a, i + 0.5 * c - 2 * o.pix)
                        n.fillRect(a, i + 0.5 * c - 2 * o.pix, c, 4 * o.pix)
                        break
                    case "triangle":
                        n.moveTo(a + 7.5 * o.pix, i + 0.5 * c - 5 * o.pix)
                        n.lineTo(a + 2.5 * o.pix, i + 0.5 * c + 5 * o.pix)
                        n.lineTo(a + 12.5 * o.pix, i + 0.5 * c + 5 * o.pix)
                        n.lineTo(a + 7.5 * o.pix, i + 0.5 * c - 5 * o.pix)
                        break
                    case "diamond":
                        n.moveTo(a + 7.5 * o.pix, i + 0.5 * c - 5 * o.pix)
                        n.lineTo(a + 2.5 * o.pix, i + 0.5 * c)
                        n.lineTo(a + 7.5 * o.pix, i + 0.5 * c + 5 * o.pix)
                        n.lineTo(a + 12.5 * o.pix, i + 0.5 * c)
                        n.lineTo(a + 7.5 * o.pix, i + 0.5 * c - 5 * o.pix)
                        break
                    case "circle":
                        n.moveTo(a + 7.5 * o.pix, i + 0.5 * c)
                        n.arc(a + 7.5 * o.pix, i + 0.5 * c, 5 * o.pix, 0, 2 * Math.PI)
                        break
                    case "rect":
                        n.moveTo(a, i + 0.5 * c - 5 * o.pix)
                        n.fillRect(a, i + 0.5 * c - 5 * o.pix, 15 * o.pix, 10 * o.pix)
                        break
                    case "square":
                        n.moveTo(a + 2 * o.pix, i + 0.5 * c - 5 * o.pix)
                        n.fillRect(a + 2 * o.pix, i + 0.5 * c - 5 * o.pix, 10 * o.pix, 10 * o.pix)
                        break
                    default:
                        n.moveTo(a, i + 0.5 * c - 5 * o.pix)
                        n.fillRect(a, i + 0.5 * c - 5 * o.pix, 15 * o.pix, 10 * o.pix)
                }
                n.closePath()
                n.fill()
            }
        })
    }
    e.forEach(function (t, e) {
        var a = r.x + d + 2 * h + c + u
        if (g) {
            a = r.x - x - d + 2 * h + c + u
        }
        var i = r.y + p * e + (p - s) / 2 - 1 + h + s
        n.beginPath()
        n.setFontSize(s)
        n.setTextBaseline("normal")
        n.setFillStyle(l.fontColor)
        n.fillText(t.text, a, i)
        n.closePath()
        n.stroke()
    })
}
function drawColumnDataPoints(T, b, S, w) {
    let A = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 1
    let t = b.chartData.xAxisData,
        P = t.xAxisPoints,
        C = t.eachSpacing
    let D = assign({}, { type: "group", width: C / 2, meterBorder: 4, meterFillColor: "#FFFFFF", barBorderCircle: false, barBorderRadius: [], seriesGap: 2, linearType: "none", linearOpacity: 1, customColor: [], colorStop: 0, labelPosition: "outside" }, b.extra.column)
    let M = []
    w.save()
    let L = -2
    let F = P.length + 2
    if (b._scrollDistance_ && b._scrollDistance_ !== 0 && b.enableScroll === true) {
        w.translate(b._scrollDistance_, 0)
        L = Math.floor(-b._scrollDistance_ / C) - 2
        F = L + b.xAxis.itemCount + 4
    }
    if (b.tooltip && b.tooltip.textList && b.tooltip.textList.length && A === 1) {
        drawToolTipSplitArea(b.tooltip.offset.x, b, S, w, C)
    }
    D.customColor = fillCustomColor(D.linearType, D.customColor, T, S)
    T.forEach(function (a, i) {
        let e, t, o
        e = [].concat(b.chartData.yAxisData.ranges[a.index])
        t = e.pop()
        o = e.shift()
        let x = b.height - b.area[0] - b.area[2]
        let f = (x * (0 - t)) / (o - t)
        let n = b.height - Math.round(f) - b.area[2]
        a.zeroPoints = n
        var p = a.data
        switch (D.type) {
            case "group":
                var r = getColumnDataPoints(p, t, o, P, C, b, S, n, A)
                var u = getStackDataPoints(p, t, o, P, C, b, S, i, T, A)
                M.push(u)
                r = fixColumeData(r, C, T.length, i, S, b)
                for (let t = 0; t < r.length; t++) {
                    let o = r[t]
                    if (o !== null && t > L && t < F) {
                        var l = o.x - o.width / 2
                        var s = b.height - o.y - b.area[2]
                        w.beginPath()
                        var g = o.color || a.color
                        var y = o.color || a.color
                        if (D.linearType !== "none") {
                            var v = w.createLinearGradient(l, o.y, l, n)
                            if (D.linearType == "opacity") {
                                v.addColorStop(0, hexToRgb(g, D.linearOpacity))
                                v.addColorStop(1, hexToRgb(g, 1))
                            } else {
                                v.addColorStop(0, hexToRgb(D.customColor[a.linearIndex], D.linearOpacity))
                                v.addColorStop(D.colorStop, hexToRgb(D.customColor[a.linearIndex], D.linearOpacity))
                                v.addColorStop(1, hexToRgb(g, 1))
                            }
                            g = v
                        }
                        if ((D.barBorderRadius && D.barBorderRadius.length === 4) || D.barBorderCircle === true) {
                            const h = l
                            const c = o.y > n ? n : o.y
                            const d = o.width
                            const s = Math.abs(n - o.y)
                            if (D.barBorderCircle) {
                                D.barBorderRadius = [d / 2, d / 2, 0, 0]
                            }
                            if (o.y > n) {
                                D.barBorderRadius = [0, 0, d / 2, d / 2]
                            }
                            let [t, e, a, i] = D.barBorderRadius
                            let r = Math.min(d / 2, s / 2)
                            t = t > r ? r : t
                            e = e > r ? r : e
                            a = a > r ? r : a
                            i = i > r ? r : i
                            t = t < 0 ? 0 : t
                            e = e < 0 ? 0 : e
                            a = a < 0 ? 0 : a
                            i = i < 0 ? 0 : i
                            w.arc(h + t, c + t, t, -Math.PI, -Math.PI / 2)
                            w.arc(h + d - e, c + e, e, -Math.PI / 2, 0)
                            w.arc(h + d - a, c + s - a, a, 0, Math.PI / 2)
                            w.arc(h + i, c + s - i, i, Math.PI / 2, Math.PI)
                        } else {
                            w.moveTo(l, o.y)
                            w.lineTo(l + o.width, o.y)
                            w.lineTo(l + o.width, n)
                            w.lineTo(l, n)
                            w.lineTo(l, o.y)
                            w.setLineWidth(1)
                            w.setStrokeStyle(y)
                        }
                        w.setFillStyle(g)
                        w.closePath()
                        w.fill()
                    }
                }
                break
            case "stack":
                var r = getStackDataPoints(p, t, o, P, C, b, S, i, T, A)
                M.push(r)
                r = fixColumeStackData(r, C, T.length, i, S, b, T)
                for (let e = 0; e < r.length; e++) {
                    let t = r[e]
                    if (t !== null && e > L && e < F) {
                        w.beginPath()
                        var g = t.color || a.color
                        var l = t.x - t.width / 2 + 1
                        var s = b.height - t.y - b.area[2]
                        var m = b.height - t.y0 - b.area[2]
                        if (i > 0) {
                            s -= m
                        }
                        w.setFillStyle(g)
                        w.moveTo(l, t.y)
                        w.fillRect(l, t.y, t.width, s)
                        w.closePath()
                        w.fill()
                    }
                }
                break
            case "meter":
                var r = getDataPoints(p, t, o, P, C, b, S, A)
                M.push(r)
                r = fixColumeMeterData(r, C, T.length, i, S, b, D.meterBorder)
                for (let t = 0; t < r.length; t++) {
                    let o = r[t]
                    if (o !== null && t > L && t < F) {
                        w.beginPath()
                        if (i == 0 && D.meterBorder > 0) {
                            w.setStrokeStyle(a.color)
                            w.setLineWidth(D.meterBorder * b.pix)
                        }
                        if (i == 0) {
                            w.setFillStyle(D.meterFillColor)
                        } else {
                            w.setFillStyle(o.color || a.color)
                        }
                        var l = o.x - o.width / 2
                        var s = b.height - o.y - b.area[2]
                        if ((D.barBorderRadius && D.barBorderRadius.length === 4) || D.barBorderCircle === true) {
                            const h = l
                            const c = o.y
                            const d = o.width
                            const s = n - o.y
                            if (D.barBorderCircle) {
                                D.barBorderRadius = [d / 2, d / 2, 0, 0]
                            }
                            let [t, e, a, i] = D.barBorderRadius
                            let r = Math.min(d / 2, s / 2)
                            t = t > r ? r : t
                            e = e > r ? r : e
                            a = a > r ? r : a
                            i = i > r ? r : i
                            t = t < 0 ? 0 : t
                            e = e < 0 ? 0 : e
                            a = a < 0 ? 0 : a
                            i = i < 0 ? 0 : i
                            w.arc(h + t, c + t, t, -Math.PI, -Math.PI / 2)
                            w.arc(h + d - e, c + e, e, -Math.PI / 2, 0)
                            w.arc(h + d - a, c + s - a, a, 0, Math.PI / 2)
                            w.arc(h + i, c + s - i, i, Math.PI / 2, Math.PI)
                            w.fill()
                        } else {
                            w.moveTo(l, o.y)
                            w.lineTo(l + o.width, o.y)
                            w.lineTo(l + o.width, n)
                            w.lineTo(l, n)
                            w.lineTo(l, o.y)
                            w.fill()
                        }
                        if (i == 0 && D.meterBorder > 0) {
                            w.closePath()
                            w.stroke()
                        }
                    }
                }
                break
        }
    })
    if (b.dataLabel !== false && A === 1) {
        T.forEach(function (t, e) {
            let a, i, r
            a = [].concat(b.chartData.yAxisData.ranges[t.index])
            i = a.pop()
            r = a.shift()
            var o = t.data
            switch (D.type) {
                case "group":
                    var n = getColumnDataPoints(o, i, r, P, C, b, S, A)
                    n = fixColumeData(n, C, T.length, e, S, b)
                    drawColumePointText(n, t, S, w, b)
                    break
                case "stack":
                    var n = getStackDataPoints(o, i, r, P, C, b, S, e, T, A)
                    drawColumePointText(n, t, S, w, b)
                    break
                case "meter":
                    var n = getDataPoints(o, i, r, P, C, b, S, A)
                    drawColumePointText(n, t, S, w, b)
                    break
            }
        })
    }
    w.restore()
    return { xAxisPoints: P, calPoints: M, eachSpacing: C }
}
function drawMountDataPoints(i, r, o, n) {
    let f = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 1
    let t = r.chartData.xAxisData,
        p = t.xAxisPoints,
        l = t.eachSpacing
    let s = assign({}, { type: "mount", widthRatio: 1, borderWidth: 1, barBorderCircle: false, barBorderRadius: [], linearType: "none", linearOpacity: 1, customColor: [], colorStop: 0 }, r.extra.mount)
    s.widthRatio = s.widthRatio <= 0 ? 0 : s.widthRatio
    s.widthRatio = s.widthRatio >= 2 ? 2 : s.widthRatio
    let e = []
    n.save()
    let u = -2
    let g = p.length + 2
    if (r._scrollDistance_ && r._scrollDistance_ !== 0 && r.enableScroll === true) {
        n.translate(r._scrollDistance_, 0)
        u = Math.floor(-r._scrollDistance_ / l) - 2
        g = u + r.xAxis.itemCount + 4
    }
    s.customColor = fillCustomColor(s.linearType, s.customColor, i, o)
    let y, v, m
    y = [].concat(r.chartData.yAxisData.ranges[0])
    v = y.pop()
    m = y.shift()
    let T = r.height - r.area[0] - r.area[2]
    let b = (T * (0 - v)) / (m - v)
    let h = r.height - Math.round(b) - r.area[2]
    var c = getMountDataPoints(i, v, m, p, l, r, s, h, f)
    switch (s.type) {
        case "bar":
            for (let t = 0; t < c.length; t++) {
                let o = c[t]
                if (o !== null && t > u && t < g) {
                    var d = o.x - (l * s.widthRatio) / 2
                    var S = r.height - o.y - r.area[2]
                    n.beginPath()
                    var a = o.color || i[t].color
                    var w = o.color || i[t].color
                    if (s.linearType !== "none") {
                        var x = n.createLinearGradient(d, o.y, d, h)
                        if (s.linearType == "opacity") {
                            x.addColorStop(0, hexToRgb(a, s.linearOpacity))
                            x.addColorStop(1, hexToRgb(a, 1))
                        } else {
                            x.addColorStop(0, hexToRgb(s.customColor[i[t].linearIndex], s.linearOpacity))
                            x.addColorStop(s.colorStop, hexToRgb(s.customColor[i[t].linearIndex], s.linearOpacity))
                            x.addColorStop(1, hexToRgb(a, 1))
                        }
                        a = x
                    }
                    if ((s.barBorderRadius && s.barBorderRadius.length === 4) || s.barBorderCircle === true) {
                        const A = d
                        const P = o.y > h ? h : o.y
                        const C = o.width
                        const S = Math.abs(h - o.y)
                        if (s.barBorderCircle) {
                            s.barBorderRadius = [C / 2, C / 2, 0, 0]
                        }
                        if (o.y > h) {
                            s.barBorderRadius = [0, 0, C / 2, C / 2]
                        }
                        let [t, e, a, i] = s.barBorderRadius
                        let r = Math.min(C / 2, S / 2)
                        t = t > r ? r : t
                        e = e > r ? r : e
                        a = a > r ? r : a
                        i = i > r ? r : i
                        t = t < 0 ? 0 : t
                        e = e < 0 ? 0 : e
                        a = a < 0 ? 0 : a
                        i = i < 0 ? 0 : i
                        n.arc(A + t, P + t, t, -Math.PI, -Math.PI / 2)
                        n.arc(A + C - e, P + e, e, -Math.PI / 2, 0)
                        n.arc(A + C - a, P + S - a, a, 0, Math.PI / 2)
                        n.arc(A + i, P + S - i, i, Math.PI / 2, Math.PI)
                    } else {
                        n.moveTo(d, o.y)
                        n.lineTo(d + o.width, o.y)
                        n.lineTo(d + o.width, h)
                        n.lineTo(d, h)
                        n.lineTo(d, o.y)
                    }
                    n.setStrokeStyle(w)
                    n.setFillStyle(a)
                    if (s.borderWidth > 0) {
                        n.setLineWidth(s.borderWidth * r.pix)
                        n.closePath()
                        n.stroke()
                    }
                    n.fill()
                }
            }
            break
        case "triangle":
            for (let e = 0; e < c.length; e++) {
                let t = c[e]
                if (t !== null && e > u && e < g) {
                    var d = t.x - (l * s.widthRatio) / 2
                    var S = r.height - t.y - r.area[2]
                    n.beginPath()
                    var a = t.color || i[e].color
                    var w = t.color || i[e].color
                    if (s.linearType !== "none") {
                        var x = n.createLinearGradient(d, t.y, d, h)
                        if (s.linearType == "opacity") {
                            x.addColorStop(0, hexToRgb(a, s.linearOpacity))
                            x.addColorStop(1, hexToRgb(a, 1))
                        } else {
                            x.addColorStop(0, hexToRgb(s.customColor[i[e].linearIndex], s.linearOpacity))
                            x.addColorStop(s.colorStop, hexToRgb(s.customColor[i[e].linearIndex], s.linearOpacity))
                            x.addColorStop(1, hexToRgb(a, 1))
                        }
                        a = x
                    }
                    n.moveTo(d, h)
                    n.lineTo(t.x, t.y)
                    n.lineTo(d + t.width, h)
                    n.setStrokeStyle(w)
                    n.setFillStyle(a)
                    if (s.borderWidth > 0) {
                        n.setLineWidth(s.borderWidth * r.pix)
                        n.stroke()
                    }
                    n.fill()
                }
            }
            break
        case "mount":
            for (let e = 0; e < c.length; e++) {
                let t = c[e]
                if (t !== null && e > u && e < g) {
                    var d = t.x - (l * s.widthRatio) / 2
                    var S = r.height - t.y - r.area[2]
                    n.beginPath()
                    var a = t.color || i[e].color
                    var w = t.color || i[e].color
                    if (s.linearType !== "none") {
                        var x = n.createLinearGradient(d, t.y, d, h)
                        if (s.linearType == "opacity") {
                            x.addColorStop(0, hexToRgb(a, s.linearOpacity))
                            x.addColorStop(1, hexToRgb(a, 1))
                        } else {
                            x.addColorStop(0, hexToRgb(s.customColor[i[e].linearIndex], s.linearOpacity))
                            x.addColorStop(s.colorStop, hexToRgb(s.customColor[i[e].linearIndex], s.linearOpacity))
                            x.addColorStop(1, hexToRgb(a, 1))
                        }
                        a = x
                    }
                    n.moveTo(d, h)
                    n.bezierCurveTo(t.x - t.width / 4, h, t.x - t.width / 4, t.y, t.x, t.y)
                    n.bezierCurveTo(t.x + t.width / 4, t.y, t.x + t.width / 4, h, d + t.width, h)
                    n.setStrokeStyle(w)
                    n.setFillStyle(a)
                    if (s.borderWidth > 0) {
                        n.setLineWidth(s.borderWidth * r.pix)
                        n.stroke()
                    }
                    n.fill()
                }
            }
            break
        case "sharp":
            for (let e = 0; e < c.length; e++) {
                let t = c[e]
                if (t !== null && e > u && e < g) {
                    var d = t.x - (l * s.widthRatio) / 2
                    var S = r.height - t.y - r.area[2]
                    n.beginPath()
                    var a = t.color || i[e].color
                    var w = t.color || i[e].color
                    if (s.linearType !== "none") {
                        var x = n.createLinearGradient(d, t.y, d, h)
                        if (s.linearType == "opacity") {
                            x.addColorStop(0, hexToRgb(a, s.linearOpacity))
                            x.addColorStop(1, hexToRgb(a, 1))
                        } else {
                            x.addColorStop(0, hexToRgb(s.customColor[i[e].linearIndex], s.linearOpacity))
                            x.addColorStop(s.colorStop, hexToRgb(s.customColor[i[e].linearIndex], s.linearOpacity))
                            x.addColorStop(1, hexToRgb(a, 1))
                        }
                        a = x
                    }
                    n.moveTo(d, h)
                    n.quadraticCurveTo(t.x - 0, h - S / 4, t.x, t.y)
                    n.quadraticCurveTo(t.x + 0, h - S / 4, d + t.width, h)
                    n.setStrokeStyle(w)
                    n.setFillStyle(a)
                    if (s.borderWidth > 0) {
                        n.setLineWidth(s.borderWidth * r.pix)
                        n.stroke()
                    }
                    n.fill()
                }
            }
            break
    }
    if (r.dataLabel !== false && f === 1) {
        let t, e, a
        t = [].concat(r.chartData.yAxisData.ranges[0])
        e = t.pop()
        a = t.shift()
        var c = getMountDataPoints(i, e, a, p, l, r, s, h, f)
        drawMountPointText(c, i, o, n, r, h)
    }
    n.restore()
    return { xAxisPoints: p, calPoints: c, eachSpacing: l }
}
function drawBarDataPoints(y, v, m, T) {
    let b = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 1
    let S = []
    let w = (v.height - v.area[0] - v.area[2]) / v.categories.length
    for (let t = 0; t < v.categories.length; t++) {
        S.push(v.area[0] + w / 2 + w * t)
    }
    let A = assign({}, { type: "group", width: w / 2, meterBorder: 4, meterFillColor: "#FFFFFF", barBorderCircle: false, barBorderRadius: [], seriesGap: 2, linearType: "none", linearOpacity: 1, customColor: [], colorStop: 0 }, v.extra.bar)
    let P = []
    T.save()
    let C = -2
    let D = S.length + 2
    if (v.tooltip && v.tooltip.textList && v.tooltip.textList.length && b === 1) {
        drawBarToolTipSplitArea(v.tooltip.offset.y, v, m, T, w)
    }
    A.customColor = fillCustomColor(A.linearType, A.customColor, y, m)
    y.forEach(function (a, t) {
        let o, e, d
        o = [].concat(v.chartData.xAxisData.ranges)
        d = o.pop()
        e = o.shift()
        var x = a.data
        switch (A.type) {
            case "group":
                var i = getBarDataPoints(x, e, d, S, w, v, m, b)
                var f = getBarStackDataPoints(x, e, d, S, w, v, m, t, y, b)
                P.push(f)
                i = fixBarData(i, w, y.length, t, m, v)
                for (let t = 0; t < i.length; t++) {
                    let o = i[t]
                    if (o !== null && t > C && t < D) {
                        var n = v.area[3]
                        var r = o.y - o.width / 2
                        var p = o.height
                        T.beginPath()
                        var l = o.color || a.color
                        var u = o.color || a.color
                        if (A.linearType !== "none") {
                            var s = T.createLinearGradient(n, o.y, o.x, o.y)
                            if (A.linearType == "opacity") {
                                s.addColorStop(0, hexToRgb(l, A.linearOpacity))
                                s.addColorStop(1, hexToRgb(l, 1))
                            } else {
                                s.addColorStop(0, hexToRgb(A.customColor[a.linearIndex], A.linearOpacity))
                                s.addColorStop(A.colorStop, hexToRgb(A.customColor[a.linearIndex], A.linearOpacity))
                                s.addColorStop(1, hexToRgb(l, 1))
                            }
                            l = s
                        }
                        if ((A.barBorderRadius && A.barBorderRadius.length === 4) || A.barBorderCircle === true) {
                            const g = n
                            const h = o.width
                            const c = o.y - o.width / 2
                            const p = o.height
                            if (A.barBorderCircle) {
                                A.barBorderRadius = [h / 2, h / 2, 0, 0]
                            }
                            let [t, e, a, i] = A.barBorderRadius
                            let r = Math.min(h / 2, p / 2)
                            t = t > r ? r : t
                            e = e > r ? r : e
                            a = a > r ? r : a
                            i = i > r ? r : i
                            t = t < 0 ? 0 : t
                            e = e < 0 ? 0 : e
                            a = a < 0 ? 0 : a
                            i = i < 0 ? 0 : i
                            T.arc(g + i, c + i, i, -Math.PI, -Math.PI / 2)
                            T.arc(o.x - t, c + t, t, -Math.PI / 2, 0)
                            T.arc(o.x - e, c + h - e, e, 0, Math.PI / 2)
                            T.arc(g + a, c + h - a, a, Math.PI / 2, Math.PI)
                        } else {
                            T.moveTo(n, r)
                            T.lineTo(o.x, r)
                            T.lineTo(o.x, r + o.width)
                            T.lineTo(n, r + o.width)
                            T.lineTo(n, r)
                            T.setLineWidth(1)
                            T.setStrokeStyle(u)
                        }
                        T.setFillStyle(l)
                        T.closePath()
                        T.fill()
                    }
                }
                break
            case "stack":
                var i = getBarStackDataPoints(x, e, d, S, w, v, m, t, y, b)
                P.push(i)
                i = fixBarStackData(i, w, y.length, t, m, v, y)
                for (let e = 0; e < i.length; e++) {
                    let t = i[e]
                    if (t !== null && e > C && e < D) {
                        T.beginPath()
                        var l = t.color || a.color
                        var n = t.x0
                        T.setFillStyle(l)
                        T.moveTo(n, t.y - t.width / 2)
                        T.fillRect(n, t.y - t.width / 2, t.height, t.width)
                        T.closePath()
                        T.fill()
                    }
                }
                break
        }
    })
    if (v.dataLabel !== false && b === 1) {
        y.forEach(function (t, e) {
            let a, i, r
            a = [].concat(v.chartData.xAxisData.ranges)
            r = a.pop()
            i = a.shift()
            var o = t.data
            switch (A.type) {
                case "group":
                    var n = getBarDataPoints(o, i, r, S, w, v, m, b)
                    n = fixBarData(n, w, y.length, e, m, v)
                    drawBarPointText(n, t, m, T, v)
                    break
                case "stack":
                    var n = getBarStackDataPoints(o, i, r, S, w, v, m, e, y, b)
                    drawBarPointText(n, t, m, T, v)
                    break
            }
        })
    }
    return { yAxisPoints: S, calPoints: P, eachSpacing: w }
}
function drawCandleDataPoints(e, t, h, c, d) {
    var g = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : 1
    var s = assign({}, { color: {}, average: {} }, h.extra.candle)
    s.color = assign({}, { upLine: "#f04864", upFill: "#f04864", downLine: "#2fc25b", downFill: "#2fc25b" }, s.color)
    s.average = assign({}, { show: false, name: [], day: [], color: c.color }, s.average)
    h.extra.candle = s
    let a = h.chartData.xAxisData,
        x = a.xAxisPoints,
        f = a.eachSpacing
    let y = []
    d.save()
    let p = -2
    let v = x.length + 2
    let u = 0
    let m = h.width + f
    if (h._scrollDistance_ && h._scrollDistance_ !== 0 && h.enableScroll === true) {
        d.translate(h._scrollDistance_, 0)
        p = Math.floor(-h._scrollDistance_ / f) - 2
        v = p + h.xAxis.itemCount + 4
        u = -h._scrollDistance_ - f * 2 + h.area[3]
        m = u + (h.xAxis.itemCount + 4) * f
    }
    if (s.average.show || t) {
        t.forEach(function (e, t) {
            let a, i, r
            a = [].concat(h.chartData.yAxisData.ranges[e.index])
            i = a.pop()
            r = a.shift()
            var o = e.data
            var n = getDataPoints(o, i, r, x, f, h, c, g)
            var l = splitPoints(n, e)
            for (let t = 0; t < l.length; t++) {
                let i = l[t]
                d.beginPath()
                d.setStrokeStyle(e.color)
                d.setLineWidth(1)
                if (i.length === 1) {
                    d.moveTo(i[0].x, i[0].y)
                    d.arc(i[0].x, i[0].y, 1, 0, 2 * Math.PI)
                } else {
                    d.moveTo(i[0].x, i[0].y)
                    let a = 0
                    for (let e = 0; e < i.length; e++) {
                        let t = i[e]
                        if (a == 0 && t.x > u) {
                            d.moveTo(t.x, t.y)
                            a = 1
                        }
                        if (e > 0 && t.x > u && t.x < m) {
                            var s = createCurveControlPoints(i, e - 1)
                            d.bezierCurveTo(s.ctrA.x, s.ctrA.y, s.ctrB.x, s.ctrB.y, t.x, t.y)
                        }
                    }
                    d.moveTo(i[0].x, i[0].y)
                }
                d.closePath()
                d.stroke()
            }
        })
    }
    e.forEach(function (t, e) {
        let a, i, r
        a = [].concat(h.chartData.yAxisData.ranges[t.index])
        i = a.pop()
        r = a.shift()
        var o = t.data
        var n = getCandleDataPoints(o, i, r, x, f, h, c, g)
        y.push(n)
        var l = splitPoints(n, t)
        for (let e = 0; e < l[0].length; e++) {
            if (e > p && e < v) {
                let t = l[0][e]
                d.beginPath()
                if (o[e][1] - o[e][0] > 0) {
                    d.setStrokeStyle(s.color.upLine)
                    d.setFillStyle(s.color.upFill)
                    d.setLineWidth(1 * h.pix)
                    d.moveTo(t[3].x, t[3].y)
                    d.lineTo(t[1].x, t[1].y)
                    d.lineTo(t[1].x - f / 4, t[1].y)
                    d.lineTo(t[0].x - f / 4, t[0].y)
                    d.lineTo(t[0].x, t[0].y)
                    d.lineTo(t[2].x, t[2].y)
                    d.lineTo(t[0].x, t[0].y)
                    d.lineTo(t[0].x + f / 4, t[0].y)
                    d.lineTo(t[1].x + f / 4, t[1].y)
                    d.lineTo(t[1].x, t[1].y)
                    d.moveTo(t[3].x, t[3].y)
                } else {
                    d.setStrokeStyle(s.color.downLine)
                    d.setFillStyle(s.color.downFill)
                    d.setLineWidth(1 * h.pix)
                    d.moveTo(t[3].x, t[3].y)
                    d.lineTo(t[0].x, t[0].y)
                    d.lineTo(t[0].x - f / 4, t[0].y)
                    d.lineTo(t[1].x - f / 4, t[1].y)
                    d.lineTo(t[1].x, t[1].y)
                    d.lineTo(t[2].x, t[2].y)
                    d.lineTo(t[1].x, t[1].y)
                    d.lineTo(t[1].x + f / 4, t[1].y)
                    d.lineTo(t[0].x + f / 4, t[0].y)
                    d.lineTo(t[0].x, t[0].y)
                    d.moveTo(t[3].x, t[3].y)
                }
                d.closePath()
                d.fill()
                d.stroke()
            }
        }
    })
    d.restore()
    return { xAxisPoints: x, calPoints: y, eachSpacing: f }
}
function drawAreaDataPoints(t, s, h, c) {
    var d = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 1
    var x = assign({}, { type: "straight", opacity: 0.2, addLine: false, width: 2, gradient: false, activeType: "none" }, s.extra.area)
    let e = s.chartData.xAxisData,
        f = e.xAxisPoints,
        p = e.eachSpacing
    let y = s.height - s.area[2]
    let v = []
    c.save()
    let u = 0
    let g = s.width + p
    if (s._scrollDistance_ && s._scrollDistance_ !== 0 && s.enableScroll === true) {
        c.translate(s._scrollDistance_, 0)
        u = -s._scrollDistance_ - p * 2 + s.area[3]
        g = u + (s.xAxis.itemCount + 4) * p
    }
    t.forEach(function (e, t) {
        let a, i, r
        a = [].concat(s.chartData.yAxisData.ranges[e.index])
        i = a.pop()
        r = a.shift()
        let o = e.data
        let n = getDataPoints(o, i, r, f, p, s, h, d)
        v.push(n)
        let l = splitPoints(n, e)
        for (let t = 0; t < l.length; t++) {
            let r = l[t]
            c.beginPath()
            c.setStrokeStyle(hexToRgb(e.color, x.opacity))
            if (x.gradient) {
                let t = c.createLinearGradient(0, s.area[0], 0, s.height - s.area[2])
                t.addColorStop("0", hexToRgb(e.color, x.opacity))
                t.addColorStop("1.0", hexToRgb("#FFFFFF", 0.1))
                c.setFillStyle(t)
            } else {
                c.setFillStyle(hexToRgb(e.color, x.opacity))
            }
            c.setLineWidth(x.width * s.pix)
            if (r.length > 1) {
                let t = r[0]
                let e = r[r.length - 1]
                c.moveTo(t.x, t.y)
                let i = 0
                if (x.type === "curve") {
                    for (let a = 0; a < r.length; a++) {
                        let e = r[a]
                        if (i == 0 && e.x > u) {
                            c.moveTo(e.x, e.y)
                            i = 1
                        }
                        if (a > 0 && e.x > u && e.x < g) {
                            let t = createCurveControlPoints(r, a - 1)
                            c.bezierCurveTo(t.ctrA.x, t.ctrA.y, t.ctrB.x, t.ctrB.y, e.x, e.y)
                        }
                    }
                }
                if (x.type === "straight") {
                    for (let e = 0; e < r.length; e++) {
                        let t = r[e]
                        if (i == 0 && t.x > u) {
                            c.moveTo(t.x, t.y)
                            i = 1
                        }
                        if (e > 0 && t.x > u && t.x < g) {
                            c.lineTo(t.x, t.y)
                        }
                    }
                }
                if (x.type === "step") {
                    for (let e = 0; e < r.length; e++) {
                        let t = r[e]
                        if (i == 0 && t.x > u) {
                            c.moveTo(t.x, t.y)
                            i = 1
                        }
                        if (e > 0 && t.x > u && t.x < g) {
                            c.lineTo(t.x, r[e - 1].y)
                            c.lineTo(t.x, t.y)
                        }
                    }
                }
                c.lineTo(e.x, y)
                c.lineTo(t.x, y)
                c.lineTo(t.x, t.y)
            } else {
                let t = r[0]
                c.moveTo(t.x - p / 2, t.y)
            }
            c.closePath()
            c.fill()
            if (x.addLine) {
                if (e.lineType == "dash") {
                    let t = e.dashLength ? e.dashLength : 8
                    t *= s.pix
                    c.setLineDash([t, t])
                }
                c.beginPath()
                c.setStrokeStyle(e.color)
                c.setLineWidth(x.width * s.pix)
                if (r.length === 1) {
                    c.moveTo(r[0].x, r[0].y)
                } else {
                    c.moveTo(r[0].x, r[0].y)
                    let i = 0
                    if (x.type === "curve") {
                        for (let a = 0; a < r.length; a++) {
                            let e = r[a]
                            if (i == 0 && e.x > u) {
                                c.moveTo(e.x, e.y)
                                i = 1
                            }
                            if (a > 0 && e.x > u && e.x < g) {
                                let t = createCurveControlPoints(r, a - 1)
                                c.bezierCurveTo(t.ctrA.x, t.ctrA.y, t.ctrB.x, t.ctrB.y, e.x, e.y)
                            }
                        }
                    }
                    if (x.type === "straight") {
                        for (let e = 0; e < r.length; e++) {
                            let t = r[e]
                            if (i == 0 && t.x > u) {
                                c.moveTo(t.x, t.y)
                                i = 1
                            }
                            if (e > 0 && t.x > u && t.x < g) {
                                c.lineTo(t.x, t.y)
                            }
                        }
                    }
                    if (x.type === "step") {
                        for (let e = 0; e < r.length; e++) {
                            let t = r[e]
                            if (i == 0 && t.x > u) {
                                c.moveTo(t.x, t.y)
                                i = 1
                            }
                            if (e > 0 && t.x > u && t.x < g) {
                                c.lineTo(t.x, r[e - 1].y)
                                c.lineTo(t.x, t.y)
                            }
                        }
                    }
                    c.moveTo(r[0].x, r[0].y)
                }
                c.stroke()
                c.setLineDash([])
            }
        }
        if (s.dataPointShape !== false) {
            drawPointShape(n, e.color, e.pointShape, c, s)
        }
        drawActivePoint(n, e.color, e.pointShape, c, s, x, t)
    })
    if (s.dataLabel !== false && d === 1) {
        t.forEach(function (t, e) {
            let a, i, r
            a = [].concat(s.chartData.yAxisData.ranges[t.index])
            i = a.pop()
            r = a.shift()
            var o = t.data
            var n = getDataPoints(o, i, r, f, p, s, h, d)
            drawPointText(n, t, h, c, s)
        })
    }
    c.restore()
    return { xAxisPoints: f, calPoints: v, eachSpacing: p }
}
function drawScatterDataPoints(t, s, h, c) {
    var d = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 1
    var i = assign({}, { type: "circle" }, s.extra.scatter)
    let e = s.chartData.xAxisData,
        x = e.xAxisPoints,
        f = e.eachSpacing
    var r = []
    c.save()
    let a = 0
    let o = s.width + f
    if (s._scrollDistance_ && s._scrollDistance_ !== 0 && s.enableScroll === true) {
        c.translate(s._scrollDistance_, 0)
        a = -s._scrollDistance_ - f * 2 + s.area[3]
        o = a + (s.xAxis.itemCount + 4) * f
    }
    t.forEach(function (t, e) {
        let a, i, r
        a = [].concat(s.chartData.yAxisData.ranges[t.index])
        i = a.pop()
        r = a.shift()
        var o = t.data
        var n = getDataPoints(o, i, r, x, f, s, h, d)
        c.beginPath()
        c.setStrokeStyle(t.color)
        c.setFillStyle(t.color)
        c.setLineWidth(1 * s.pix)
        var l = t.pointShape
        if (l === "diamond") {
            n.forEach(function (t, e) {
                if (t !== null) {
                    c.moveTo(t.x, t.y - 4.5)
                    c.lineTo(t.x - 4.5, t.y)
                    c.lineTo(t.x, t.y + 4.5)
                    c.lineTo(t.x + 4.5, t.y)
                    c.lineTo(t.x, t.y - 4.5)
                }
            })
        } else if (l === "circle") {
            n.forEach(function (t, e) {
                if (t !== null) {
                    c.moveTo(t.x + 2.5 * s.pix, t.y)
                    c.arc(t.x, t.y, 3 * s.pix, 0, 2 * Math.PI, false)
                }
            })
        } else if (l === "square") {
            n.forEach(function (t, e) {
                if (t !== null) {
                    c.moveTo(t.x - 3.5, t.y - 3.5)
                    c.rect(t.x - 3.5, t.y - 3.5, 7, 7)
                }
            })
        } else if (l === "triangle") {
            n.forEach(function (t, e) {
                if (t !== null) {
                    c.moveTo(t.x, t.y - 4.5)
                    c.lineTo(t.x - 4.5, t.y + 4.5)
                    c.lineTo(t.x + 4.5, t.y + 4.5)
                    c.lineTo(t.x, t.y - 4.5)
                }
            })
        } else if (l === "triangle") {
            return
        }
        c.closePath()
        c.fill()
        c.stroke()
    })
    if (s.dataLabel !== false && d === 1) {
        t.forEach(function (t, e) {
            let a, i, r
            a = [].concat(s.chartData.yAxisData.ranges[t.index])
            i = a.pop()
            r = a.shift()
            var o = t.data
            var n = getDataPoints(o, i, r, x, f, s, h, d)
            drawPointText(n, t, h, c, s)
        })
    }
    c.restore()
    return { xAxisPoints: x, calPoints: r, eachSpacing: f }
}
function drawBubbleDataPoints(a, l, s, h) {
    var c = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 1
    var d = assign({}, { opacity: 1, border: 2 }, l.extra.bubble)
    let t = l.chartData.xAxisData,
        x = t.xAxisPoints,
        f = t.eachSpacing
    var i = []
    h.save()
    let e = 0
    let r = l.width + f
    if (l._scrollDistance_ && l._scrollDistance_ !== 0 && l.enableScroll === true) {
        h.translate(l._scrollDistance_, 0)
        e = -l._scrollDistance_ - f * 2 + l.area[3]
        r = e + (l.xAxis.itemCount + 4) * f
    }
    a.forEach(function (i, t) {
        let e, a, r
        e = [].concat(l.chartData.yAxisData.ranges[i.index])
        a = e.pop()
        r = e.shift()
        var o = i.data
        var n = getDataPoints(o, a, r, x, f, l, s, c)
        h.beginPath()
        h.setStrokeStyle(i.color)
        h.setLineWidth(d.border * l.pix)
        h.setFillStyle(hexToRgb(i.color, d.opacity))
        n.forEach(function (t, e) {
            h.moveTo(t.x + t.r, t.y)
            h.arc(t.x, t.y, t.r * l.pix, 0, 2 * Math.PI, false)
        })
        h.closePath()
        h.fill()
        h.stroke()
        if (l.dataLabel !== false && c === 1) {
            n.forEach(function (t, e) {
                h.beginPath()
                var a = i.textSize * l.pix || s.fontSize
                h.setFontSize(a)
                h.setFillStyle(i.textColor || "#FFFFFF")
                h.setTextAlign("center")
                h.fillText(String(t.t), t.x, t.y + a / 2)
                h.closePath()
                h.stroke()
                h.setTextAlign("left")
            })
        }
    })
    h.restore()
    return { xAxisPoints: x, calPoints: i, eachSpacing: f }
}
function drawLineDataPoints(t, d, x, f) {
    var p = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 1
    var u = assign({}, { type: "straight", width: 2, activeType: "none", linearType: "none", onShadow: false, animation: "vertical" }, d.extra.line)
    u.width *= d.pix
    let e = d.chartData.xAxisData,
        g = e.xAxisPoints,
        y = e.eachSpacing
    var T = []
    f.save()
    let v = 0
    let m = d.width + y
    if (d._scrollDistance_ && d._scrollDistance_ !== 0 && d.enableScroll === true) {
        f.translate(d._scrollDistance_, 0)
        v = -d._scrollDistance_ - y * 2 + d.area[3]
        m = v + (d.xAxis.itemCount + 4) * y
    }
    t.forEach(function (e, c) {
        f.beginPath()
        f.setStrokeStyle(e.color)
        f.moveTo(-1e4, -1e4)
        f.lineTo(-10001, -10001)
        f.stroke()
        let t, a, i
        t = [].concat(d.chartData.yAxisData.ranges[e.index])
        a = t.pop()
        i = t.shift()
        var r = e.data
        var o = getLineDataPoints(r, a, i, g, y, d, x, u, p)
        T.push(o)
        var n = splitPoints(o, e)
        if (e.lineType == "dash") {
            let t = e.dashLength ? e.dashLength : 8
            t *= d.pix
            f.setLineDash([t, t])
        }
        f.beginPath()
        var l = e.color
        if (u.linearType !== "none" && e.linearColor && e.linearColor.length > 0) {
            var s = f.createLinearGradient(d.chartData.xAxisData.startX, d.height / 2, d.chartData.xAxisData.endX, d.height / 2)
            for (var h = 0; h < e.linearColor.length; h++) {
                s.addColorStop(e.linearColor[h][0], hexToRgb(e.linearColor[h][1], 1))
            }
            l = s
        }
        f.setStrokeStyle(l)
        if (u.onShadow == true && e.setShadow && e.setShadow.length > 0) {
            f.setShadow(e.setShadow[0], e.setShadow[1], e.setShadow[2], e.setShadow[3])
        } else {
            f.setShadow(0, 0, 0, "rgba(0,0,0,0)")
        }
        f.setLineWidth(u.width)
        n.forEach(function (i, t) {
            if (i.length === 1) {
                f.moveTo(i[0].x, i[0].y)
            } else {
                f.moveTo(i[0].x, i[0].y)
                let a = 0
                if (u.type === "curve") {
                    for (let e = 0; e < i.length; e++) {
                        let t = i[e]
                        if (a == 0 && t.x > v) {
                            f.moveTo(t.x, t.y)
                            a = 1
                        }
                        if (e > 0 && t.x > v && t.x < m) {
                            var r = createCurveControlPoints(i, e - 1)
                            f.bezierCurveTo(r.ctrA.x, r.ctrA.y, r.ctrB.x, r.ctrB.y, t.x, t.y)
                        }
                    }
                }
                if (u.type === "straight") {
                    for (let e = 0; e < i.length; e++) {
                        let t = i[e]
                        if (a == 0 && t.x > v) {
                            f.moveTo(t.x, t.y)
                            a = 1
                        }
                        if (e > 0 && t.x > v && t.x < m) {
                            f.lineTo(t.x, t.y)
                        }
                    }
                }
                if (u.type === "step") {
                    for (let e = 0; e < i.length; e++) {
                        let t = i[e]
                        if (a == 0 && t.x > v) {
                            f.moveTo(t.x, t.y)
                            a = 1
                        }
                        if (e > 0 && t.x > v && t.x < m) {
                            f.lineTo(t.x, i[e - 1].y)
                            f.lineTo(t.x, t.y)
                        }
                    }
                }
                f.moveTo(i[0].x, i[0].y)
            }
        })
        f.stroke()
        f.setLineDash([])
        if (d.dataPointShape !== false) {
            drawPointShape(o, e.color, e.pointShape, f, d)
        }
        drawActivePoint(o, e.color, e.pointShape, f, d, u)
    })
    if (d.dataLabel !== false && p === 1) {
        t.forEach(function (t, e) {
            let a, i, r
            a = [].concat(d.chartData.yAxisData.ranges[t.index])
            i = a.pop()
            r = a.shift()
            var o = t.data
            var n = getDataPoints(o, i, r, g, y, d, x, p)
            drawPointText(n, t, x, f, d)
        })
    }
    f.restore()
    return { xAxisPoints: g, calPoints: T, eachSpacing: y }
}
function drawMixDataPoints(t, v, m, T) {
    let D = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 1
    let e = v.chartData.xAxisData,
        b = e.xAxisPoints,
        S = e.eachSpacing
    let w = assign({}, { width: S / 2, barBorderCircle: false, barBorderRadius: [], seriesGap: 2, linearType: "none", linearOpacity: 1, customColor: [], colorStop: 0 }, v.extra.mix.column)
    let A = assign({}, { opacity: 0.2, gradient: false }, v.extra.mix.area)
    let M = assign({}, { width: 2 }, v.extra.mix.line)
    let L = v.height - v.area[2]
    let F = []
    var _ = 0
    var k = 0
    t.forEach(function (t, e) {
        if (t.type == "column") {
            k += 1
        }
    })
    T.save()
    let R = -2
    let I = b.length + 2
    let P = 0
    let C = v.width + S
    if (v._scrollDistance_ && v._scrollDistance_ !== 0 && v.enableScroll === true) {
        T.translate(v._scrollDistance_, 0)
        R = Math.floor(-v._scrollDistance_ / S) - 2
        I = R + v.xAxis.itemCount + 4
        P = -v._scrollDistance_ - S * 2 + v.area[3]
        C = P + (v.xAxis.itemCount + 4) * S
    }
    w.customColor = fillCustomColor(w.linearType, w.customColor, t, m)
    t.forEach(function (n, t) {
        let o, x, f
        o = [].concat(v.chartData.yAxisData.ranges[n.index])
        x = o.pop()
        f = o.shift()
        var p = n.data
        var a = getDataPoints(p, x, f, b, S, v, m, D)
        F.push(a)
        if (n.type == "column") {
            a = fixColumeData(a, S, k, _, m, v)
            for (let t = 0; t < a.length; t++) {
                let o = a[t]
                if (o !== null && t > R && t < I) {
                    var l = o.x - o.width / 2
                    var s = v.height - o.y - v.area[2]
                    T.beginPath()
                    var e = o.color || n.color
                    var u = o.color || n.color
                    if (w.linearType !== "none") {
                        var i = T.createLinearGradient(l, o.y, l, v.height - v.area[2])
                        if (w.linearType == "opacity") {
                            i.addColorStop(0, hexToRgb(e, w.linearOpacity))
                            i.addColorStop(1, hexToRgb(e, 1))
                        } else {
                            i.addColorStop(0, hexToRgb(w.customColor[n.linearIndex], w.linearOpacity))
                            i.addColorStop(w.colorStop, hexToRgb(w.customColor[n.linearIndex], w.linearOpacity))
                            i.addColorStop(1, hexToRgb(e, 1))
                        }
                        e = i
                    }
                    if ((w.barBorderRadius && w.barBorderRadius.length === 4) || w.barBorderCircle) {
                        const h = l
                        const c = o.y
                        const d = o.width
                        const s = v.height - v.area[2] - o.y
                        if (w.barBorderCircle) {
                            w.barBorderRadius = [d / 2, d / 2, 0, 0]
                        }
                        let [t, e, a, i] = w.barBorderRadius
                        let r = Math.min(d / 2, s / 2)
                        t = t > r ? r : t
                        e = e > r ? r : e
                        a = a > r ? r : a
                        i = i > r ? r : i
                        t = t < 0 ? 0 : t
                        e = e < 0 ? 0 : e
                        a = a < 0 ? 0 : a
                        i = i < 0 ? 0 : i
                        T.arc(h + t, c + t, t, -Math.PI, -Math.PI / 2)
                        T.arc(h + d - e, c + e, e, -Math.PI / 2, 0)
                        T.arc(h + d - a, c + s - a, a, 0, Math.PI / 2)
                        T.arc(h + i, c + s - i, i, Math.PI / 2, Math.PI)
                    } else {
                        T.moveTo(l, o.y)
                        T.lineTo(l + o.width, o.y)
                        T.lineTo(l + o.width, v.height - v.area[2])
                        T.lineTo(l, v.height - v.area[2])
                        T.lineTo(l, o.y)
                        T.setLineWidth(1)
                        T.setStrokeStyle(u)
                    }
                    T.setFillStyle(e)
                    T.closePath()
                    T.fill()
                }
            }
            _ += 1
        }
        if (n.type == "area") {
            let e = splitPoints(a, n)
            for (let t = 0; t < e.length; t++) {
                let i = e[t]
                T.beginPath()
                T.setStrokeStyle(n.color)
                T.setStrokeStyle(hexToRgb(n.color, A.opacity))
                if (A.gradient) {
                    let t = T.createLinearGradient(0, v.area[0], 0, v.height - v.area[2])
                    t.addColorStop("0", hexToRgb(n.color, A.opacity))
                    t.addColorStop("1.0", hexToRgb("#FFFFFF", 0.1))
                    T.setFillStyle(t)
                } else {
                    T.setFillStyle(hexToRgb(n.color, A.opacity))
                }
                T.setLineWidth(2 * v.pix)
                if (i.length > 1) {
                    var r = i[0]
                    let t = i[i.length - 1]
                    T.moveTo(r.x, r.y)
                    let a = 0
                    if (n.style === "curve") {
                        for (let e = 0; e < i.length; e++) {
                            let t = i[e]
                            if (a == 0 && t.x > P) {
                                T.moveTo(t.x, t.y)
                                a = 1
                            }
                            if (e > 0 && t.x > P && t.x < C) {
                                var g = createCurveControlPoints(i, e - 1)
                                T.bezierCurveTo(g.ctrA.x, g.ctrA.y, g.ctrB.x, g.ctrB.y, t.x, t.y)
                            }
                        }
                    } else {
                        for (let e = 0; e < i.length; e++) {
                            let t = i[e]
                            if (a == 0 && t.x > P) {
                                T.moveTo(t.x, t.y)
                                a = 1
                            }
                            if (e > 0 && t.x > P && t.x < C) {
                                T.lineTo(t.x, t.y)
                            }
                        }
                    }
                    T.lineTo(t.x, L)
                    T.lineTo(r.x, L)
                    T.lineTo(r.x, r.y)
                } else {
                    let t = i[0]
                    T.moveTo(t.x - S / 2, t.y)
                }
                T.closePath()
                T.fill()
            }
        }
        if (n.type == "line") {
            var y = splitPoints(a, n)
            y.forEach(function (i, t) {
                if (n.lineType == "dash") {
                    let t = n.dashLength ? n.dashLength : 8
                    t *= v.pix
                    T.setLineDash([t, t])
                }
                T.beginPath()
                T.setStrokeStyle(n.color)
                T.setLineWidth(M.width * v.pix)
                if (i.length === 1) {
                    T.moveTo(i[0].x, i[0].y)
                } else {
                    T.moveTo(i[0].x, i[0].y)
                    let a = 0
                    if (n.style == "curve") {
                        for (let e = 0; e < i.length; e++) {
                            let t = i[e]
                            if (a == 0 && t.x > P) {
                                T.moveTo(t.x, t.y)
                                a = 1
                            }
                            if (e > 0 && t.x > P && t.x < C) {
                                var r = createCurveControlPoints(i, e - 1)
                                T.bezierCurveTo(r.ctrA.x, r.ctrA.y, r.ctrB.x, r.ctrB.y, t.x, t.y)
                            }
                        }
                    } else {
                        for (let e = 0; e < i.length; e++) {
                            let t = i[e]
                            if (a == 0 && t.x > P) {
                                T.moveTo(t.x, t.y)
                                a = 1
                            }
                            if (e > 0 && t.x > P && t.x < C) {
                                T.lineTo(t.x, t.y)
                            }
                        }
                    }
                    T.moveTo(i[0].x, i[0].y)
                }
                T.stroke()
                T.setLineDash([])
            })
        }
        if (n.type == "point") {
            n.addPoint = true
        }
        if (n.addPoint == true && n.type !== "column") {
            drawPointShape(a, n.color, n.pointShape, T, v)
        }
    })
    if (v.dataLabel !== false && D === 1) {
        var _ = 0
        t.forEach(function (t, e) {
            let a, i, r
            a = [].concat(v.chartData.yAxisData.ranges[t.index])
            i = a.pop()
            r = a.shift()
            var o = t.data
            var n = getDataPoints(o, i, r, b, S, v, m, D)
            if (t.type !== "column") {
                drawPointText(n, t, m, T, v)
            } else {
                n = fixColumeData(n, S, k, _, m, v)
                drawPointText(n, t, m, T, v)
                _ += 1
            }
        })
    }
    T.restore()
    return { xAxisPoints: b, calPoints: F, eachSpacing: S }
}
function drawToolTipBridge(t, e, a, i, r, o) {
    var n = t.extra.tooltip || {}
    if (n.horizentalLine && t.tooltip && i === 1 && (t.type == "line" || t.type == "area" || t.type == "column" || t.type == "mount" || t.type == "candle" || t.type == "mix")) {
        drawToolTipHorizentalLine(t, e, a, r, o)
    }
    a.save()
    if (t._scrollDistance_ && t._scrollDistance_ !== 0 && t.enableScroll === true) {
        a.translate(t._scrollDistance_, 0)
    }
    if (t.tooltip && t.tooltip.textList && t.tooltip.textList.length && i === 1) {
        drawToolTip(t.tooltip.textList, t.tooltip.offset, t, e, a, r, o)
    }
    a.restore()
}
function drawXAxis(r, h, c, d) {
    let e = h.chartData.xAxisData,
        x = e.xAxisPoints,
        t = e.startX,
        a = e.endX,
        f = e.eachSpacing
    var p = "center"
    if (h.type == "bar" || h.type == "line" || h.type == "area" || h.type == "scatter" || h.type == "bubble") {
        p = h.xAxis.boundaryGap
    }
    var u = h.height - h.area[2]
    var i = h.area[0]
    if (h.enableScroll && h.xAxis.scrollShow) {
        var o = h.height - h.area[2] + c.xAxisHeight
        var n = a - t
        var l = f * (x.length - 1)
        if (h.type == "mount" && h.extra && h.extra.mount && h.extra.mount.widthRatio && h.extra.mount.widthRatio > 1) {
            if (h.extra.mount.widthRatio > 2) h.extra.mount.widthRatio = 2
            l += (h.extra.mount.widthRatio - 1) * f
        }
        var s = (n * n) / l
        var y = 0
        if (h._scrollDistance_) {
            y = (-h._scrollDistance_ * n) / l
        }
        d.beginPath()
        d.setLineCap("round")
        d.setLineWidth(6 * h.pix)
        d.setStrokeStyle(h.xAxis.scrollBackgroundColor || "#EFEBEF")
        d.moveTo(t, o)
        d.lineTo(a, o)
        d.stroke()
        d.closePath()
        d.beginPath()
        d.setLineCap("round")
        d.setLineWidth(6 * h.pix)
        d.setStrokeStyle(h.xAxis.scrollColor || "#A6A6A6")
        d.moveTo(t + y, o)
        d.lineTo(t + y + s, o)
        d.stroke()
        d.closePath()
        d.setLineCap("butt")
    }
    d.save()
    if (h._scrollDistance_ && h._scrollDistance_ !== 0) {
        d.translate(h._scrollDistance_, 0)
    }
    if (h.xAxis.calibration === true) {
        d.setStrokeStyle(h.xAxis.gridColor || "#cccccc")
        d.setLineCap("butt")
        d.setLineWidth(1 * h.pix)
        x.forEach(function (t, e) {
            if (e > 0) {
                d.beginPath()
                d.moveTo(t - f / 2, u)
                d.lineTo(t - f / 2, u + 3 * h.pix)
                d.closePath()
                d.stroke()
            }
        })
    }
    if (h.xAxis.disableGrid !== true) {
        d.setStrokeStyle(h.xAxis.gridColor || "#cccccc")
        d.setLineCap("butt")
        d.setLineWidth(1 * h.pix)
        if (h.xAxis.gridType == "dash") {
            d.setLineDash([h.xAxis.dashLength * h.pix, h.xAxis.dashLength * h.pix])
        }
        h.xAxis.gridEval = h.xAxis.gridEval || 1
        x.forEach(function (t, e) {
            if (e % h.xAxis.gridEval == 0) {
                d.beginPath()
                d.moveTo(t, u)
                d.lineTo(t, i)
                d.stroke()
            }
        })
        d.setLineDash([])
    }
    if (h.xAxis.disabled !== true) {
        let t = r.length
        if (h.xAxis.labelCount) {
            if (h.xAxis.itemCount) {
                t = Math.ceil((r.length / h.xAxis.itemCount) * h.xAxis.labelCount)
            } else {
                t = h.xAxis.labelCount
            }
            t -= 1
        }
        let e = Math.ceil(r.length / t)
        let a = []
        let i = r.length
        for (let t = 0; t < i; t++) {
            if (t % e !== 0) {
                a.push("")
            } else {
                a.push(r[t])
            }
        }
        a[i - 1] = r[i - 1]
        var g = h.xAxis.fontSize * h.pix || c.fontSize
        if (c._xAxisTextAngle_ === 0) {
            a.forEach(function (t, e) {
                var a = h.xAxis.formatter ? h.xAxis.formatter(t, e, h) : t
                var i = -measureText(String(a), g, d) / 2
                if (p == "center") {
                    i += f / 2
                }
                var r = 0
                if (h.xAxis.scrollShow) {
                    r = 6 * h.pix
                }
                var o = h._scrollDistance_ || 0
                var n = p == "center" ? x[e] + f / 2 : x[e]
                if (n - Math.abs(o) >= h.area[3] - 1 && n - Math.abs(o) <= h.width - h.area[1] + 1) {
                    d.beginPath()
                    d.setFontSize(g)
                    d.setFillStyle(h.xAxis.fontColor || h.fontColor)
                    d.fillText(String(a), x[e] + i, u + h.xAxis.marginTop * h.pix + ((h.xAxis.lineHeight - h.xAxis.fontSize) * h.pix) / 2 + h.xAxis.fontSize * h.pix)
                    d.closePath()
                    d.stroke()
                }
            })
        } else {
            a.forEach(function (t, e) {
                var a = h.xAxis.formatter ? h.xAxis.formatter(t) : t
                var i = h._scrollDistance_ || 0
                var r = p == "center" ? x[e] + f / 2 : x[e]
                if (r - Math.abs(i) >= h.area[3] - 1 && r - Math.abs(i) <= h.width - h.area[1] + 1) {
                    d.save()
                    d.beginPath()
                    d.setFontSize(g)
                    d.setFillStyle(h.xAxis.fontColor || h.fontColor)
                    var o = measureText(String(a), g, d)
                    var n = x[e]
                    if (p == "center") {
                        n = x[e] + f / 2
                    }
                    var l = 0
                    if (h.xAxis.scrollShow) {
                        l = 6 * h.pix
                    }
                    var s = u + h.xAxis.marginTop * h.pix + g - g * Math.abs(Math.sin(c._xAxisTextAngle_))
                    if (h.xAxis.rotateAngle < 0) {
                        n -= g / 2
                        o = 0
                    } else {
                        n += g / 2
                        o = -o
                    }
                    d.translate(n, s)
                    d.rotate(-1 * c._xAxisTextAngle_)
                    d.fillText(String(a), o, 0)
                    d.closePath()
                    d.stroke()
                    d.restore()
                }
            })
        }
    }
    d.restore()
    if (h.xAxis.title) {
        d.beginPath()
        d.setFontSize(h.xAxis.titleFontSize * h.pix)
        d.setFillStyle(h.xAxis.titleFontColor)
        d.fillText(String(h.xAxis.title), h.width - h.area[1] + h.xAxis.titleOffsetX * h.pix, h.height - h.area[2] + h.xAxis.marginTop * h.pix + ((h.xAxis.lineHeight - h.xAxis.titleFontSize) * h.pix) / 2 + (h.xAxis.titleFontSize + h.xAxis.titleOffsetY) * h.pix)
        d.closePath()
        d.stroke()
    }
    if (h.xAxis.axisLine) {
        d.beginPath()
        d.setStrokeStyle(h.xAxis.axisLineColor)
        d.setLineWidth(1 * h.pix)
        d.moveTo(t, h.height - h.area[2])
        d.lineTo(a, h.height - h.area[2])
        d.stroke()
    }
}
function drawYAxisGrid(c, e, d, a) {
    if (e.yAxis.disableGrid === true) {
        return
    }
    let t = e.height - e.area[0] - e.area[2]
    let i = t / e.yAxis.splitNumber
    let r = e.area[3]
    let o = e.chartData.xAxisData.xAxisPoints,
        n = e.chartData.xAxisData.eachSpacing
    let l = n * (o.length - 1)
    if (e.type == "mount" && e.extra && e.extra.mount && e.extra.mount.widthRatio && e.extra.mount.widthRatio > 1) {
        if (e.extra.mount.widthRatio > 2) e.extra.mount.widthRatio = 2
        l += (e.extra.mount.widthRatio - 1) * n
    }
    let x = r + l
    let s = []
    let h = 1
    if (e.xAxis.axisLine === false) {
        h = 0
    }
    for (let t = h; t < e.yAxis.splitNumber + 1; t++) {
        s.push(e.height - e.area[2] - i * t)
    }
    a.save()
    if (e._scrollDistance_ && e._scrollDistance_ !== 0) {
        a.translate(e._scrollDistance_, 0)
    }
    if (e.yAxis.gridType == "dash") {
        a.setLineDash([e.yAxis.dashLength * e.pix, e.yAxis.dashLength * e.pix])
    }
    a.setStrokeStyle(e.yAxis.gridColor)
    a.setLineWidth(1 * e.pix)
    s.forEach(function (t, e) {
        a.beginPath()
        a.moveTo(r, t)
        a.lineTo(x, t)
        a.stroke()
    })
    a.setLineDash([])
    a.restore()
}
function drawYAxis(e, h, a, c) {
    if (h.yAxis.disabled === true) {
        return
    }
    var i = h.height - h.area[0] - h.area[2]
    var r = i / h.yAxis.splitNumber
    var o = h.area[3]
    var n = h.width - h.area[1]
    var t = h.height - h.area[2]
    c.beginPath()
    c.setFillStyle(h.background)
    if (h.enableScroll == true && h.xAxis.scrollPosition && h.xAxis.scrollPosition !== "left") {
        c.fillRect(0, 0, o, t + 2 * h.pix)
    }
    if (h.enableScroll == true && h.xAxis.scrollPosition && h.xAxis.scrollPosition !== "right") {
        c.fillRect(n, 0, h.width, t + 2 * h.pix)
    }
    c.closePath()
    c.stroke()
    let d = h.area[3]
    let x = h.width - h.area[1]
    let f = h.area[3] + (h.width - h.area[1] - h.area[3]) / 2
    if (h.yAxis.data) {
        for (let e = 0; e < h.yAxis.data.length; e++) {
            let s = h.yAxis.data[e]
            var p = []
            if (s.type === "categories") {
                for (let t = 0; t <= s.categories.length; t++) {
                    p.push(h.area[0] + i / s.categories.length / 2 + (i / s.categories.length) * t)
                }
            } else {
                for (let t = 0; t <= h.yAxis.splitNumber; t++) {
                    p.push(h.area[0] + r * t)
                }
            }
            if (s.disabled !== true) {
                let t = h.chartData.yAxisData.rangesFormat[e]
                let o = s.fontSize ? s.fontSize * h.pix : a.fontSize
                let n = h.chartData.yAxisData.yAxisWidth[e]
                let l = s.textAlign || "right"
                t.forEach(function (t, e) {
                    var a = p[e]
                    c.beginPath()
                    c.setFontSize(o)
                    c.setLineWidth(1 * h.pix)
                    c.setStrokeStyle(s.axisLineColor || "#cccccc")
                    c.setFillStyle(s.fontColor || h.fontColor)
                    let i = 0
                    let r = 4 * h.pix
                    if (n.position == "left") {
                        if (s.calibration == true) {
                            c.moveTo(d, a)
                            c.lineTo(d - 3 * h.pix, a)
                            r += 3 * h.pix
                        }
                        switch (l) {
                            case "left":
                                c.setTextAlign("left")
                                i = d - n.width
                                break
                            case "right":
                                c.setTextAlign("right")
                                i = d - r
                                break
                            default:
                                c.setTextAlign("center")
                                i = d - n.width / 2
                        }
                        c.fillText(String(t), i, a + o / 2 - 3 * h.pix)
                    } else if (n.position == "right") {
                        if (s.calibration == true) {
                            c.moveTo(x, a)
                            c.lineTo(x + 3 * h.pix, a)
                            r += 3 * h.pix
                        }
                        switch (l) {
                            case "left":
                                c.setTextAlign("left")
                                i = x + r
                                break
                            case "right":
                                c.setTextAlign("right")
                                i = x + n.width
                                break
                            default:
                                c.setTextAlign("center")
                                i = x + n.width / 2
                        }
                        c.fillText(String(t), i, a + o / 2 - 3 * h.pix)
                    } else if (n.position == "center") {
                        if (s.calibration == true) {
                            c.moveTo(f, a)
                            c.lineTo(f - 3 * h.pix, a)
                            r += 3 * h.pix
                        }
                        switch (l) {
                            case "left":
                                c.setTextAlign("left")
                                i = f - n.width
                                break
                            case "right":
                                c.setTextAlign("right")
                                i = f - r
                                break
                            default:
                                c.setTextAlign("center")
                                i = f - n.width / 2
                        }
                        c.fillText(String(t), i, a + o / 2 - 3 * h.pix)
                    }
                    c.closePath()
                    c.stroke()
                    c.setTextAlign("left")
                })
                if (s.axisLine !== false) {
                    c.beginPath()
                    c.setStrokeStyle(s.axisLineColor || "#cccccc")
                    c.setLineWidth(1 * h.pix)
                    if (n.position == "left") {
                        c.moveTo(d, h.height - h.area[2])
                        c.lineTo(d, h.area[0])
                    } else if (n.position == "right") {
                        c.moveTo(x, h.height - h.area[2])
                        c.lineTo(x, h.area[0])
                    } else if (n.position == "center") {
                        c.moveTo(f, h.height - h.area[2])
                        c.lineTo(f, h.area[0])
                    }
                    c.stroke()
                }
                if (h.yAxis.showTitle) {
                    let t = s.titleFontSize * h.pix || a.fontSize
                    let e = s.title
                    c.beginPath()
                    c.setFontSize(t)
                    c.setFillStyle(s.titleFontColor || h.fontColor)
                    if (n.position == "left") {
                        c.fillText(e, d - measureText(e, t, c) / 2 + (s.titleOffsetX || 0), h.area[0] - (10 - (s.titleOffsetY || 0)) * h.pix)
                    } else if (n.position == "right") {
                        c.fillText(e, x - measureText(e, t, c) / 2 + (s.titleOffsetX || 0), h.area[0] - (10 - (s.titleOffsetY || 0)) * h.pix)
                    } else if (n.position == "center") {
                        c.fillText(e, f - measureText(e, t, c) / 2 + (s.titleOffsetX || 0), h.area[0] - (10 - (s.titleOffsetY || 0)) * h.pix)
                    }
                    c.closePath()
                    c.stroke()
                }
                if (n.position == "left") {
                    d -= n.width + h.yAxis.padding * h.pix
                } else {
                    x += n.width + h.yAxis.padding * h.pix
                }
            }
        }
    }
}
function drawLegend(t, l, y, s, e) {
    if (l.legend.show === false) {
        return
    }
    let h = e.legendData
    let a = h.points
    let c = h.area
    let d = l.legend.padding * l.pix
    let x = l.legend.fontSize * l.pix
    let f = 15 * l.pix
    let p = 5 * l.pix
    let u = l.legend.itemGap * l.pix
    let g = Math.max(l.legend.lineHeight * l.pix, x)
    s.beginPath()
    s.setLineWidth(l.legend.borderWidth * l.pix)
    s.setStrokeStyle(l.legend.borderColor)
    s.setFillStyle(l.legend.backgroundColor)
    s.moveTo(c.start.x, c.start.y)
    s.rect(c.start.x, c.start.y, c.width, c.height)
    s.closePath()
    s.fill()
    s.stroke()
    a.forEach(function (i, t) {
        let e = 0
        let a = 0
        e = h.widthArr[t]
        a = h.heightArr[t]
        let r = 0
        let o = 0
        if (l.legend.position == "top" || l.legend.position == "bottom") {
            switch (l.legend.float) {
                case "left":
                    r = c.start.x + d
                    break
                case "right":
                    r = c.start.x + c.width - e
                    break
                default:
                    r = c.start.x + (c.width - e) / 2
            }
            o = c.start.y + d + t * g
        } else {
            if (t == 0) {
                e = 0
            } else {
                e = h.widthArr[t - 1]
            }
            r = c.start.x + d + e
            o = c.start.y + d + (c.height - a) / 2
        }
        s.setFontSize(y.fontSize)
        for (let a = 0; a < i.length; a++) {
            let t = i[a]
            t.area = [0, 0, 0, 0]
            t.area[0] = r
            t.area[1] = o
            t.area[3] = o + g
            s.beginPath()
            s.setLineWidth(1 * l.pix)
            s.setStrokeStyle(t.show ? t.color : l.legend.hiddenColor)
            s.setFillStyle(t.show ? t.color : l.legend.hiddenColor)
            switch (t.legendShape) {
                case "line":
                    s.moveTo(r, o + 0.5 * g - 2 * l.pix)
                    s.fillRect(r, o + 0.5 * g - 2 * l.pix, 15 * l.pix, 4 * l.pix)
                    break
                case "triangle":
                    s.moveTo(r + 7.5 * l.pix, o + 0.5 * g - 5 * l.pix)
                    s.lineTo(r + 2.5 * l.pix, o + 0.5 * g + 5 * l.pix)
                    s.lineTo(r + 12.5 * l.pix, o + 0.5 * g + 5 * l.pix)
                    s.lineTo(r + 7.5 * l.pix, o + 0.5 * g - 5 * l.pix)
                    break
                case "diamond":
                    s.moveTo(r + 7.5 * l.pix, o + 0.5 * g - 5 * l.pix)
                    s.lineTo(r + 2.5 * l.pix, o + 0.5 * g)
                    s.lineTo(r + 7.5 * l.pix, o + 0.5 * g + 5 * l.pix)
                    s.lineTo(r + 12.5 * l.pix, o + 0.5 * g)
                    s.lineTo(r + 7.5 * l.pix, o + 0.5 * g - 5 * l.pix)
                    break
                case "circle":
                    s.moveTo(r + 7.5 * l.pix, o + 0.5 * g)
                    s.arc(r + 7.5 * l.pix, o + 0.5 * g, 5 * l.pix, 0, 2 * Math.PI)
                    break
                case "rect":
                    s.moveTo(r, o + 0.5 * g - 5 * l.pix)
                    s.fillRect(r, o + 0.5 * g - 5 * l.pix, 15 * l.pix, 10 * l.pix)
                    break
                case "square":
                    s.moveTo(r + 5 * l.pix, o + 0.5 * g - 5 * l.pix)
                    s.fillRect(r + 5 * l.pix, o + 0.5 * g - 5 * l.pix, 10 * l.pix, 10 * l.pix)
                    break
                case "none":
                    break
                default:
                    s.moveTo(r, o + 0.5 * g - 5 * l.pix)
                    s.fillRect(r, o + 0.5 * g - 5 * l.pix, 15 * l.pix, 10 * l.pix)
            }
            s.closePath()
            s.fill()
            s.stroke()
            r += f + p
            let e = 0.5 * g + 0.5 * x - 2
            const n = t.legendText ? t.legendText : t.name
            s.beginPath()
            s.setFontSize(x)
            s.setFillStyle(t.show ? l.legend.fontColor : l.legend.hiddenColor)
            s.fillText(n, r, o + e)
            s.closePath()
            s.stroke()
            if (l.legend.position == "top" || l.legend.position == "bottom") {
                r += measureText(n, x, s) + u
                t.area[2] = r
            } else {
                t.area[2] = r + measureText(n, x, s) + u
                r -= f + p
                o += g
            }
        }
    })
}
function drawPieDataPoints(t, r, e, o) {
    var a = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 1
    var n = assign({}, { activeOpacity: 0.5, activeRadius: 10, offsetAngle: 0, labelWidth: 15, ringWidth: 30, customRadius: 0, border: false, borderWidth: 2, borderColor: "#FFFFFF", centerColor: "#FFFFFF", linearType: "none", customColor: [] }, r.type == "pie" ? r.extra.pie : r.extra.ring)
    var l = { x: r.area[3] + (r.width - r.area[1] - r.area[3]) / 2, y: r.area[0] + (r.height - r.area[0] - r.area[2]) / 2 }
    if (e.pieChartLinePadding == 0) {
        e.pieChartLinePadding = n.activeRadius * r.pix
    }
    var i = Math.min((r.width - r.area[1] - r.area[3]) / 2 - e.pieChartLinePadding - e.pieChartTextPadding - e._pieTextMaxLength_, (r.height - r.area[0] - r.area[2]) / 2 - e.pieChartLinePadding - e.pieChartTextPadding)
    i = i < 10 ? 10 : i
    if (n.customRadius > 0) {
        i = n.customRadius * r.pix
    }
    t = getPieDataPoints(t, i, a)
    var h = n.activeRadius * r.pix
    n.customColor = fillCustomColor(n.linearType, n.customColor, t, e)
    t = t.map(function (t) {
        t._start_ += (n.offsetAngle * Math.PI) / 180
        return t
    })
    t.forEach(function (t, e) {
        if (r.tooltip) {
            if (r.tooltip.index == e) {
                o.beginPath()
                o.setFillStyle(hexToRgb(t.color, n.activeOpacity || 0.5))
                o.moveTo(l.x, l.y)
                o.arc(l.x, l.y, t._radius_ + h, t._start_, t._start_ + 2 * t._proportion_ * Math.PI)
                o.closePath()
                o.fill()
            }
        }
        o.beginPath()
        o.setLineWidth(n.borderWidth * r.pix)
        o.lineJoin = "round"
        o.setStrokeStyle(n.borderColor)
        var a = t.color
        if (n.linearType == "custom") {
            var i
            if (o.createCircularGradient) {
                i = o.createCircularGradient(l.x, l.y, t._radius_)
            } else {
                i = o.createRadialGradient(l.x, l.y, 0, l.x, l.y, t._radius_)
            }
            i.addColorStop(0, hexToRgb(n.customColor[t.linearIndex], 1))
            i.addColorStop(1, hexToRgb(t.color, 1))
            a = i
        }
        o.setFillStyle(a)
        o.moveTo(l.x, l.y)
        o.arc(l.x, l.y, t._radius_, t._start_, t._start_ + 2 * t._proportion_ * Math.PI)
        o.closePath()
        o.fill()
        if (n.border == true) {
            o.stroke()
        }
    })
    if (r.type === "ring") {
        var s = i * 0.6
        if (typeof n.ringWidth === "number" && n.ringWidth > 0) {
            s = Math.max(0, i - n.ringWidth * r.pix)
        }
        o.beginPath()
        o.setFillStyle(n.centerColor)
        o.moveTo(l.x, l.y)
        o.arc(l.x, l.y, s, 0, 2 * Math.PI)
        o.closePath()
        o.fill()
    }
    if (r.dataLabel !== false && a === 1) {
        drawPieText(t, r, e, o, i, l)
    }
    if (a === 1 && r.type === "ring") {
        drawRingTitle(r, e, o, l)
    }
    return { center: l, radius: i, series: t }
}
function drawRoseDataPoints(t, r, e, o) {
    var a = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 1
    var n = assign({}, { type: "area", activeOpacity: 0.5, activeRadius: 10, offsetAngle: 0, labelWidth: 15, border: false, borderWidth: 2, borderColor: "#FFFFFF", linearType: "none", customColor: [] }, r.extra.rose)
    if (e.pieChartLinePadding == 0) {
        e.pieChartLinePadding = n.activeRadius * r.pix
    }
    var l = { x: r.area[3] + (r.width - r.area[1] - r.area[3]) / 2, y: r.area[0] + (r.height - r.area[0] - r.area[2]) / 2 }
    var i = Math.min((r.width - r.area[1] - r.area[3]) / 2 - e.pieChartLinePadding - e.pieChartTextPadding - e._pieTextMaxLength_, (r.height - r.area[0] - r.area[2]) / 2 - e.pieChartLinePadding - e.pieChartTextPadding)
    i = i < 10 ? 10 : i
    var s = n.minRadius || i * 0.5
    if (i < s) {
        i = s + 10
    }
    t = getRoseDataPoints(t, n.type, s, i, a)
    var h = n.activeRadius * r.pix
    n.customColor = fillCustomColor(n.linearType, n.customColor, t, e)
    t = t.map(function (t) {
        t._start_ += ((n.offsetAngle || 0) * Math.PI) / 180
        return t
    })
    t.forEach(function (t, e) {
        if (r.tooltip) {
            if (r.tooltip.index == e) {
                o.beginPath()
                o.setFillStyle(hexToRgb(t.color, n.activeOpacity || 0.5))
                o.moveTo(l.x, l.y)
                o.arc(l.x, l.y, h + t._radius_, t._start_, t._start_ + 2 * t._rose_proportion_ * Math.PI)
                o.closePath()
                o.fill()
            }
        }
        o.beginPath()
        o.setLineWidth(n.borderWidth * r.pix)
        o.lineJoin = "round"
        o.setStrokeStyle(n.borderColor)
        var a = t.color
        if (n.linearType == "custom") {
            var i
            if (o.createCircularGradient) {
                i = o.createCircularGradient(l.x, l.y, t._radius_)
            } else {
                i = o.createRadialGradient(l.x, l.y, 0, l.x, l.y, t._radius_)
            }
            i.addColorStop(0, hexToRgb(n.customColor[t.linearIndex], 1))
            i.addColorStop(1, hexToRgb(t.color, 1))
            a = i
        }
        o.setFillStyle(a)
        o.moveTo(l.x, l.y)
        o.arc(l.x, l.y, t._radius_, t._start_, t._start_ + 2 * t._rose_proportion_ * Math.PI)
        o.closePath()
        o.fill()
        if (n.border == true) {
            o.stroke()
        }
    })
    if (r.dataLabel !== false && a === 1) {
        drawPieText(t, r, e, o, i, l)
    }
    return { center: l, radius: i, series: t }
}
function drawArcbarDataPoints(a, i, t, r) {
    var e = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 1
    var o = assign({}, { startAngle: 0.75, endAngle: 0.25, type: "default", direction: "cw", lineCap: "round", width: 12, gap: 2, linearType: "none", customColor: [] }, i.extra.arcbar)
    a = getArcbarDataPoints(a, o, e)
    var n
    if (o.centerX || o.centerY) {
        n = { x: o.centerX ? o.centerX : i.width / 2, y: o.centerY ? o.centerY : i.height / 2 }
    } else {
        n = { x: i.width / 2, y: i.height / 2 }
    }
    var l
    if (o.radius) {
        l = o.radius
    } else {
        l = Math.min(n.x, n.y)
        l -= 5 * i.pix
        l -= o.width / 2
    }
    l = l < 10 ? 10 : l
    o.customColor = fillCustomColor(o.linearType, o.customColor, a, t)
    for (let e = 0; e < a.length; e++) {
        let t = a[e]
        r.setLineWidth(o.width * i.pix)
        r.setStrokeStyle(o.backgroundColor || "#E9E9E9")
        r.setLineCap(o.lineCap)
        r.beginPath()
        if (o.type == "default") {
            r.arc(n.x, n.y, l - (o.width * i.pix + o.gap * i.pix) * e, o.startAngle * Math.PI, o.endAngle * Math.PI, o.direction == "ccw")
        } else {
            r.arc(n.x, n.y, l - (o.width * i.pix + o.gap * i.pix) * e, 0, 2 * Math.PI, o.direction == "ccw")
        }
        r.stroke()
        var s = t.color
        if (o.linearType == "custom") {
            var h = r.createLinearGradient(n.x - l, n.y, n.x + l, n.y)
            h.addColorStop(1, hexToRgb(o.customColor[t.linearIndex], 1))
            h.addColorStop(0, hexToRgb(t.color, 1))
            s = h
        }
        r.setLineWidth(o.width * i.pix)
        r.setStrokeStyle(s)
        r.setLineCap(o.lineCap)
        r.beginPath()
        r.arc(n.x, n.y, l - (o.width * i.pix + o.gap * i.pix) * e, o.startAngle * Math.PI, t._proportion_ * Math.PI, o.direction == "ccw")
        r.stroke()
    }
    drawRingTitle(i, t, r, n)
    return { center: n, radius: l, series: a }
}
function drawGaugeDataPoints(n, h, c, t, d) {
    var x = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : 1
    var f = assign({}, { type: "default", startAngle: 0.75, endAngle: 0.25, width: 15, labelOffset: 13, splitLine: { fixRadius: 0, splitNumber: 10, width: 15, color: "#FFFFFF", childNumber: 5, childWidth: 5 }, pointer: { width: 15, color: "auto" } }, c.extra.gauge)
    if (f.oldAngle == undefined) {
        f.oldAngle = f.startAngle
    }
    if (f.oldData == undefined) {
        f.oldData = 0
    }
    n = getGaugeAxisPoints(n, f.startAngle, f.endAngle)
    var p = { x: c.width / 2, y: c.height / 2 }
    var u = Math.min(p.x, p.y)
    u -= 5 * c.pix
    u -= f.width / 2
    u = u < 10 ? 10 : u
    var g = u - f.width
    var y = 0
    if (f.type == "progress") {
        var v = u - f.width * 3
        d.beginPath()
        let t = d.createLinearGradient(p.x, p.y - v, p.x, p.y + v)
        t.addColorStop("0", hexToRgb(h[0].color, 0.3))
        t.addColorStop("1.0", hexToRgb("#FFFFFF", 0.1))
        d.setFillStyle(t)
        d.arc(p.x, p.y, v, 0, 2 * Math.PI, false)
        d.fill()
        d.setLineWidth(f.width)
        d.setStrokeStyle(hexToRgb(h[0].color, 0.3))
        d.setLineCap("round")
        d.beginPath()
        d.arc(p.x, p.y, g, f.startAngle * Math.PI, f.endAngle * Math.PI, false)
        d.stroke()
        if (f.endAngle < f.startAngle) {
            y = 2 + f.endAngle - f.startAngle
        } else {
            y = f.startAngle - f.endAngle
        }
        let e = y / f.splitLine.splitNumber
        let a = y / f.splitLine.splitNumber / f.splitLine.childNumber
        let i = -u - f.width * 0.5 - f.splitLine.fixRadius
        let r = -u - f.width - f.splitLine.fixRadius + f.splitLine.width
        d.save()
        d.translate(p.x, p.y)
        d.rotate((f.startAngle - 1) * Math.PI)
        let o = f.splitLine.splitNumber * f.splitLine.childNumber + 1
        let n = h[0].data * x
        for (let t = 0; t < o; t++) {
            d.beginPath()
            if (n > t / o) {
                d.setStrokeStyle(hexToRgb(h[0].color, 1))
            } else {
                d.setStrokeStyle(hexToRgb(h[0].color, 0.3))
            }
            d.setLineWidth(3 * c.pix)
            d.moveTo(i, 0)
            d.lineTo(r, 0)
            d.stroke()
            d.rotate(a * Math.PI)
        }
        d.restore()
        h = getGaugeArcbarDataPoints(h, f, x)
        d.setLineWidth(f.width)
        d.setStrokeStyle(h[0].color)
        d.setLineCap("round")
        d.beginPath()
        d.arc(p.x, p.y, g, f.startAngle * Math.PI, h[0]._proportion_ * Math.PI, false)
        d.stroke()
        let l = u - f.width * 2.5
        d.save()
        d.translate(p.x, p.y)
        d.rotate((h[0]._proportion_ - 1) * Math.PI)
        d.beginPath()
        d.setLineWidth(f.width / 3)
        let s = d.createLinearGradient(0, -l * 0.6, 0, l * 0.6)
        s.addColorStop("0", hexToRgb("#FFFFFF", 0))
        s.addColorStop("0.5", hexToRgb(h[0].color, 1))
        s.addColorStop("1.0", hexToRgb("#FFFFFF", 0))
        d.setStrokeStyle(s)
        d.arc(0, 0, l, 0.85 * Math.PI, 1.15 * Math.PI, false)
        d.stroke()
        d.beginPath()
        d.setLineWidth(1)
        d.setStrokeStyle(h[0].color)
        d.setFillStyle(h[0].color)
        d.moveTo(-l - f.width / 3 / 2, -4)
        d.lineTo(-l - f.width / 3 / 2 - 4, 0)
        d.lineTo(-l - f.width / 3 / 2, 4)
        d.lineTo(-l - f.width / 3 / 2, -4)
        d.stroke()
        d.fill()
        d.restore()
    } else {
        d.setLineWidth(f.width)
        d.setLineCap("butt")
        for (let e = 0; e < n.length; e++) {
            let t = n[e]
            d.beginPath()
            d.setStrokeStyle(t.color)
            d.arc(p.x, p.y, u, t._startAngle_ * Math.PI, t._endAngle_ * Math.PI, false)
            d.stroke()
        }
        d.save()
        if (f.endAngle < f.startAngle) {
            y = 2 + f.endAngle - f.startAngle
        } else {
            y = f.startAngle - f.endAngle
        }
        let e = y / f.splitLine.splitNumber
        let a = y / f.splitLine.splitNumber / f.splitLine.childNumber
        let i = -u - f.width * 0.5 - f.splitLine.fixRadius
        let r = -u - f.width * 0.5 - f.splitLine.fixRadius + f.splitLine.width
        let o = -u - f.width * 0.5 - f.splitLine.fixRadius + f.splitLine.childWidth
        d.translate(p.x, p.y)
        d.rotate((f.startAngle - 1) * Math.PI)
        for (let t = 0; t < f.splitLine.splitNumber + 1; t++) {
            d.beginPath()
            d.setStrokeStyle(f.splitLine.color)
            d.setLineWidth(2 * c.pix)
            d.moveTo(i, 0)
            d.lineTo(r, 0)
            d.stroke()
            d.rotate(e * Math.PI)
        }
        d.restore()
        d.save()
        d.translate(p.x, p.y)
        d.rotate((f.startAngle - 1) * Math.PI)
        for (let t = 0; t < f.splitLine.splitNumber * f.splitLine.childNumber + 1; t++) {
            d.beginPath()
            d.setStrokeStyle(f.splitLine.color)
            d.setLineWidth(1 * c.pix)
            d.moveTo(i, 0)
            d.lineTo(o, 0)
            d.stroke()
            d.rotate(a * Math.PI)
        }
        d.restore()
        h = getGaugeDataPoints(h, n, f, x)
        for (let e = 0; e < h.length; e++) {
            let t = h[e]
            d.save()
            d.translate(p.x, p.y)
            d.rotate((t._proportion_ - 1) * Math.PI)
            d.beginPath()
            d.setFillStyle(t.color)
            d.moveTo(f.pointer.width, 0)
            d.lineTo(0, -f.pointer.width / 2)
            d.lineTo(-g, 0)
            d.lineTo(0, f.pointer.width / 2)
            d.lineTo(f.pointer.width, 0)
            d.closePath()
            d.fill()
            d.beginPath()
            d.setFillStyle("#FFFFFF")
            d.arc(0, 0, f.pointer.width / 6, 0, 2 * Math.PI, false)
            d.fill()
            d.restore()
        }
        if (c.dataLabel !== false) {
            drawGaugeLabel(f, u, p, c, t, d)
        }
    }
    drawRingTitle(c, t, d, p)
    if (x === 1 && c.type === "gauge") {
        c.extra.gauge.oldAngle = h[0]._proportion_
        c.extra.gauge.oldData = h[0].data
    }
    return { center: p, radius: u, innerRadius: g, categories: n, totalAngle: y }
}
function drawRadarDataPoints(o, n, i, l) {
    var t = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 1
    var s = assign({}, { gridColor: "#cccccc", gridType: "radar", gridEval: 1, axisLabel: false, axisLabelTofix: 0, labelShow: true, labelColor: "#666666", labelPointShow: false, labelPointRadius: 3, labelPointColor: "#cccccc", opacity: 0.2, gridCount: 3, border: false, borderWidth: 2, linearType: "none", customColor: [] }, n.extra.radar)
    var a = getRadarCoordinateSeries(n.categories.length)
    var h = { x: n.area[3] + (n.width - n.area[1] - n.area[3]) / 2, y: n.area[0] + (n.height - n.area[0] - n.area[2]) / 2 }
    var r = (n.width - n.area[1] - n.area[3]) / 2
    var d = (n.height - n.area[0] - n.area[2]) / 2
    var c = Math.min(r - (getMaxTextListLength(n.categories, i.fontSize, l) + i.radarLabelTextMargin), d - i.radarLabelTextMargin)
    c -= i.radarLabelTextMargin * n.pix
    c = c < 10 ? 10 : c
    c = s.radius ? s.radius : c
    l.beginPath()
    l.setLineWidth(1 * n.pix)
    l.setStrokeStyle(s.gridColor)
    a.forEach(function (t, e) {
        var a = convertCoordinateOrigin(c * Math.cos(t), c * Math.sin(t), h)
        l.moveTo(h.x, h.y)
        if (e % s.gridEval == 0) {
            l.lineTo(a.x, a.y)
        }
    })
    l.stroke()
    l.closePath()
    var x = function t(i) {
        var r = {}
        l.beginPath()
        l.setLineWidth(1 * n.pix)
        l.setStrokeStyle(s.gridColor)
        if (s.gridType == "radar") {
            a.forEach(function (t, e) {
                var a = convertCoordinateOrigin((c / s.gridCount) * i * Math.cos(t), (c / s.gridCount) * i * Math.sin(t), h)
                if (e === 0) {
                    r = a
                    l.moveTo(a.x, a.y)
                } else {
                    l.lineTo(a.x, a.y)
                }
            })
            l.lineTo(r.x, r.y)
        } else {
            var e = convertCoordinateOrigin((c / s.gridCount) * i * Math.cos(1.5), (c / s.gridCount) * i * Math.sin(1.5), h)
            l.arc(h.x, h.y, h.y - e.y, 0, 2 * Math.PI, false)
        }
        l.stroke()
        l.closePath()
    }
    for (var e = 1; e <= s.gridCount; e++) {
        x(e)
    }
    s.customColor = fillCustomColor(s.linearType, s.customColor, o, i)
    var f = getRadarDataPoints(a, h, c, o, n, t)
    f.forEach(function (t, e) {
        l.beginPath()
        l.setLineWidth(s.borderWidth * n.pix)
        l.setStrokeStyle(t.color)
        var a = hexToRgb(t.color, s.opacity)
        if (s.linearType == "custom") {
            var i
            if (l.createCircularGradient) {
                i = l.createCircularGradient(h.x, h.y, c)
            } else {
                i = l.createRadialGradient(h.x, h.y, 0, h.x, h.y, c)
            }
            i.addColorStop(0, hexToRgb(s.customColor[o[e].linearIndex], s.opacity))
            i.addColorStop(1, hexToRgb(t.color, s.opacity))
            a = i
        }
        l.setFillStyle(a)
        t.data.forEach(function (t, e) {
            if (e === 0) {
                l.moveTo(t.position.x, t.position.y)
            } else {
                l.lineTo(t.position.x, t.position.y)
            }
        })
        l.closePath()
        l.fill()
        if (s.border === true) {
            l.stroke()
        }
        l.closePath()
        if (n.dataPointShape !== false) {
            var r = t.data.map(function (t) {
                return t.position
            })
            drawPointShape(r, t.color, t.pointShape, l, n)
        }
    })
    if (s.axisLabel === true) {
        const p = Math.max(s.max, Math.max.apply(null, dataCombine(o)))
        const u = c / s.gridCount
        const g = n.fontSize * n.pix
        l.setFontSize(g)
        l.setFillStyle(n.fontColor)
        l.setTextAlign("left")
        for (var e = 0; e < s.gridCount + 1; e++) {
            let t = (e * p) / s.gridCount
            t = t.toFixed(s.axisLabelTofix)
            l.fillText(String(t), h.x + 3 * n.pix, h.y - e * u + g / 2)
        }
    }
    drawRadarLabel(a, c, h, n, i, l)
    if (n.dataLabel !== false && t === 1) {
        f.forEach(function (t, e) {
            l.beginPath()
            var a = t.textSize * n.pix || i.fontSize
            l.setFontSize(a)
            l.setFillStyle(t.textColor || n.fontColor)
            t.data.forEach(function (t, e) {
                if (Math.abs(t.position.x - h.x) < 2) {
                    if (t.position.y < h.y) {
                        l.setTextAlign("center")
                        l.fillText(t.value, t.position.x, t.position.y - 4)
                    } else {
                        l.setTextAlign("center")
                        l.fillText(t.value, t.position.x, t.position.y + a + 2)
                    }
                } else {
                    if (t.position.x < h.x) {
                        l.setTextAlign("right")
                        l.fillText(t.value, t.position.x - 4, t.position.y + a / 2 - 2)
                    } else {
                        l.setTextAlign("left")
                        l.fillText(t.value, t.position.x + 4, t.position.y + a / 2 - 2)
                    }
                }
            })
            l.closePath()
            l.stroke()
        })
        l.setTextAlign("left")
    }
    return { center: h, radius: c, angleList: a }
}
function lonlat2mercator(t, e) {
    var a = Array(2)
    var i = (t * 20037508.34) / 180
    var r = Math.log(Math.tan(((90 + e) * Math.PI) / 360)) / (Math.PI / 180)
    r = (r * 20037508.34) / 180
    a[0] = i
    a[1] = r
    return a
}
function mercator2lonlat(t, e) {
    var a = Array(2)
    var i = (t / 20037508.34) * 180
    var r = (e / 20037508.34) * 180
    r = (180 / Math.PI) * (2 * Math.atan(Math.exp((r * Math.PI) / 180)) - Math.PI / 2)
    a[0] = i
    a[1] = r
    return a
}
function getBoundingBox(t) {
    var e = {},
        a
    e.xMin = 180
    e.xMax = 0
    e.yMin = 90
    e.yMax = 0
    for (var i = 0; i < t.length; i++) {
        var r = t[i].geometry.coordinates
        for (var o = 0; o < r.length; o++) {
            a = r[o]
            if (a.length == 1) {
                a = a[0]
            }
            for (var n = 0; n < a.length; n++) {
                var l = a[n][0]
                var s = a[n][1]
                var h = { x: l, y: s }
                e.xMin = e.xMin < h.x ? e.xMin : h.x
                e.xMax = e.xMax > h.x ? e.xMax : h.x
                e.yMin = e.yMin < h.y ? e.yMin : h.y
                e.yMax = e.yMax > h.y ? e.yMax : h.y
            }
        }
    }
    return e
}
function coordinateToPoint(t, e, a, i, r, o) {
    return { x: (e - a.xMin) * i + r, y: (a.yMax - t) * i + o }
}
function pointToCoordinate(t, e, a, i, r, o) {
    return { x: (e - r) / i + a.xMin, y: a.yMax - (t - o) / i }
}
function isRayIntersectsSegment(t, e, a) {
    if (e[1] == a[1]) {
        return false
    }
    if (e[1] > t[1] && a[1] > t[1]) {
        return false
    }
    if (e[1] < t[1] && a[1] < t[1]) {
        return false
    }
    if (e[1] == t[1] && a[1] > t[1]) {
        return false
    }
    if (a[1] == t[1] && e[1] > t[1]) {
        return false
    }
    if (e[0] < t[0] && a[1] < t[1]) {
        return false
    }
    let i = a[0] - ((a[0] - e[0]) * (a[1] - t[1])) / (a[1] - e[1])
    if (i < t[0]) {
        return false
    } else {
        return true
    }
}
function isPoiWithinPoly(r, e, o) {
    let n = 0
    for (let t = 0; t < e.length; t++) {
        let i = e[t][0]
        if (e.length == 1) {
            i = e[t][0]
        }
        for (let a = 0; a < i.length - 1; a++) {
            let t = i[a]
            let e = i[a + 1]
            if (o) {
                t = lonlat2mercator(i[a][0], i[a][1])
                e = lonlat2mercator(i[a + 1][0], i[a + 1][1])
            }
            if (isRayIntersectsSegment(r, t, e)) {
                n += 1
            }
        }
    }
    if (n % 2 == 1) {
        return true
    } else {
        return false
    }
}
function drawMapDataPoints(a, i, d, r) {
    var o = assign({}, { border: true, mercator: false, borderWidth: 1, active: true, borderColor: "#666666", fillOpacity: 0.6, activeBorderColor: "#f04864", activeFillColor: "#facc14", activeFillOpacity: 1 }, i.extra.map)
    var t, n
    var l = a
    var s = getBoundingBox(l)
    if (o.mercator) {
        var x = lonlat2mercator(s.xMax, s.yMax)
        var f = lonlat2mercator(s.xMin, s.yMin)
        s.xMax = x[0]
        s.yMax = x[1]
        s.xMin = f[0]
        s.yMin = f[1]
    }
    var p = i.width / Math.abs(s.xMax - s.xMin)
    var u = i.height / Math.abs(s.yMax - s.yMin)
    var h = p < u ? p : u
    var g = i.width / 2 - (Math.abs(s.xMax - s.xMin) / 2) * h
    var y = i.height / 2 - (Math.abs(s.yMax - s.yMin) / 2) * h
    for (var c = 0; c < l.length; c++) {
        r.beginPath()
        r.setLineWidth(o.borderWidth * i.pix)
        r.setStrokeStyle(o.borderColor)
        r.setFillStyle(hexToRgb(a[c].color, a[c].fillOpacity || o.fillOpacity))
        if (o.active == true && i.tooltip) {
            if (i.tooltip.index == c) {
                r.setStrokeStyle(o.activeBorderColor)
                r.setFillStyle(hexToRgb(o.activeFillColor, o.activeFillOpacity))
            }
        }
        var v = l[c].geometry.coordinates
        for (var m = 0; m < v.length; m++) {
            t = v[m]
            if (t.length == 1) {
                t = t[0]
            }
            for (var e = 0; e < t.length; e++) {
                var T = Array(2)
                if (o.mercator) {
                    T = lonlat2mercator(t[e][0], t[e][1])
                } else {
                    T = t[e]
                }
                n = coordinateToPoint(T[1], T[0], s, h, g, y)
                if (e === 0) {
                    r.beginPath()
                    r.moveTo(n.x, n.y)
                } else {
                    r.lineTo(n.x, n.y)
                }
            }
            r.fill()
            if (o.border == true) {
                r.stroke()
            }
        }
    }
    if (i.dataLabel == true) {
        for (var c = 0; c < l.length; c++) {
            var b = l[c].properties.centroid
            if (b) {
                if (o.mercator) {
                    b = lonlat2mercator(l[c].properties.centroid[0], l[c].properties.centroid[1])
                }
                n = coordinateToPoint(b[1], b[0], s, h, g, y)
                let t = l[c].textSize * i.pix || d.fontSize
                let e = l[c].textColor || i.fontColor
                if (o.active && o.activeTextColor && i.tooltip && i.tooltip.index == c) {
                    e = o.activeTextColor
                }
                let a = l[c].properties.name
                r.beginPath()
                r.setFontSize(t)
                r.setFillStyle(e)
                r.fillText(a, n.x - measureText(a, t, r) / 2, n.y + t / 2)
                r.closePath()
                r.stroke()
            }
        }
    }
    i.chartData.mapData = { bounds: s, scale: h, xoffset: g, yoffset: y, mercator: o.mercator }
    drawToolTipBridge(i, d, r, 1)
    r.draw()
}
function normalInt(t, e, a) {
    a = a == 0 ? 1 : a
    var i = []
    for (var r = 0; r < a; r++) {
        i[r] = Math.random()
    }
    return (
        Math.floor(
            (i.reduce(function (t, e) {
                return t + e
            }) /
                a) *
                (e - t)
        ) + t
    )
}
function collisionNew(e, a, i, r) {
    var o = false
    for (let t = 0; t < a.length; t++) {
        if (a[t].area) {
            if (e[3] < a[t].area[1] || e[0] > a[t].area[2] || e[1] > a[t].area[3] || e[2] < a[t].area[0]) {
                if (e[0] < 0 || e[1] < 0 || e[2] > i || e[3] > r) {
                    o = true
                    break
                } else {
                    o = false
                }
            } else {
                o = true
                break
            }
        }
    }
    return o
}
function getWordCloudPoint(c, t, d) {
    let x = c.series
    switch (t) {
        case "normal":
            for (let l = 0; l < x.length; l++) {
                let t = x[l].name
                let e = x[l].textSize * c.pix
                let a = measureText(t, e, d)
                let i, r
                let o
                let n = 0
                while (true) {
                    n++
                    i = normalInt(-c.width / 2, c.width / 2, 5) - a / 2
                    r = normalInt(-c.height / 2, c.height / 2, 5) + e / 2
                    o = [i - 5 + c.width / 2, r - 5 - e + c.height / 2, i + a + 5 + c.width / 2, r + 5 + c.height / 2]
                    let t = collisionNew(o, x, c.width, c.height)
                    if (!t) break
                    if (n == 1e3) {
                        o = [-100, -100, -100, -100]
                        break
                    }
                }
                x[l].area = o
            }
            break
        case "vertical":
            function f() {
                if (Math.random() > 0.7) {
                    return true
                } else {
                    return false
                }
            }
            for (let h = 0; h < x.length; h++) {
                let t = x[h].name
                let e = x[h].textSize * c.pix
                let a = measureText(t, e, d)
                let i = f()
                let r, o, n, l
                let s = 0
                while (true) {
                    s++
                    let t
                    if (i) {
                        r = normalInt(-c.width / 2, c.width / 2, 5) - a / 2
                        o = normalInt(-c.height / 2, c.height / 2, 5) + e / 2
                        n = [o - 5 - a + c.width / 2, -r - 5 + c.height / 2, o + 5 + c.width / 2, -r + e + 5 + c.height / 2]
                        l = [c.width - (c.width / 2 - c.height / 2) - (-r + e + 5 + c.height / 2) - 5, c.height / 2 - c.width / 2 + (o - 5 - a + c.width / 2) - 5, c.width - (c.width / 2 - c.height / 2) - (-r + e + 5 + c.height / 2) + e, c.height / 2 - c.width / 2 + (o - 5 - a + c.width / 2) + a + 5]
                        t = collisionNew(l, x, c.height, c.width)
                    } else {
                        r = normalInt(-c.width / 2, c.width / 2, 5) - a / 2
                        o = normalInt(-c.height / 2, c.height / 2, 5) + e / 2
                        n = [r - 5 + c.width / 2, o - 5 - e + c.height / 2, r + a + 5 + c.width / 2, o + 5 + c.height / 2]
                        t = collisionNew(n, x, c.width, c.height)
                    }
                    if (!t) break
                    if (s == 1e3) {
                        n = [-1e3, -1e3, -1e3, -1e3]
                        break
                    }
                }
                if (i) {
                    x[h].area = l
                    x[h].areav = n
                } else {
                    x[h].area = n
                }
                x[h].rotate = i
            }
            break
    }
    return x
}
function drawWordCloudDataPoints(t, r, e, o) {
    let n = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 1
    let a = assign({}, { type: "normal", autoColors: true }, r.extra.word)
    if (!r.chartData.wordCloudData) {
        r.chartData.wordCloudData = getWordCloudPoint(r, a.type, o)
    }
    o.beginPath()
    o.setFillStyle(r.background)
    o.rect(0, 0, r.width, r.height)
    o.fill()
    o.save()
    let l = r.chartData.wordCloudData
    o.translate(r.width / 2, r.height / 2)
    for (let i = 0; i < l.length; i++) {
        o.save()
        if (l[i].rotate) {
            o.rotate((90 * Math.PI) / 180)
        }
        let t = l[i].name
        let e = l[i].textSize * r.pix
        let a = measureText(t, e, o)
        o.beginPath()
        o.setStrokeStyle(l[i].color)
        o.setFillStyle(l[i].color)
        o.setFontSize(e)
        if (l[i].rotate) {
            if (l[i].areav[0] > 0) {
                if (r.tooltip) {
                    if (r.tooltip.index == i) {
                        o.strokeText(t, (l[i].areav[0] + 5 - r.width / 2) * n - (a * (1 - n)) / 2, (l[i].areav[1] + 5 + e - r.height / 2) * n)
                    } else {
                        o.fillText(t, (l[i].areav[0] + 5 - r.width / 2) * n - (a * (1 - n)) / 2, (l[i].areav[1] + 5 + e - r.height / 2) * n)
                    }
                } else {
                    o.fillText(t, (l[i].areav[0] + 5 - r.width / 2) * n - (a * (1 - n)) / 2, (l[i].areav[1] + 5 + e - r.height / 2) * n)
                }
            }
        } else {
            if (l[i].area[0] > 0) {
                if (r.tooltip) {
                    if (r.tooltip.index == i) {
                        o.strokeText(t, (l[i].area[0] + 5 - r.width / 2) * n - (a * (1 - n)) / 2, (l[i].area[1] + 5 + e - r.height / 2) * n)
                    } else {
                        o.fillText(t, (l[i].area[0] + 5 - r.width / 2) * n - (a * (1 - n)) / 2, (l[i].area[1] + 5 + e - r.height / 2) * n)
                    }
                } else {
                    o.fillText(t, (l[i].area[0] + 5 - r.width / 2) * n - (a * (1 - n)) / 2, (l[i].area[1] + 5 + e - r.height / 2) * n)
                }
            }
        }
        o.stroke()
        o.restore()
    }
    o.restore()
}
function drawFunnelDataPoints(t, e, c, a) {
    let d = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 1
    let i = assign({}, { type: "funnel", activeWidth: 10, activeOpacity: 0.3, border: false, borderWidth: 2, borderColor: "#FFFFFF", fillOpacity: 1, minSize: 0, labelAlign: "right", linearType: "none", customColor: [] }, e.extra.funnel)
    let r = (e.height - e.area[0] - e.area[2]) / t.length
    let o = { x: e.area[3] + (e.width - e.area[1] - e.area[3]) / 2, y: e.height - e.area[2] }
    let n = i.activeWidth * e.pix
    let x = Math.min((e.width - e.area[1] - e.area[3]) / 2 - n, (e.height - e.area[0] - e.area[2]) / 2 - n)
    let l = getFunnelDataPoints(t, x, i, r, d)
    a.save()
    a.translate(o.x, o.y)
    i.customColor = fillCustomColor(i.linearType, i.customColor, t, c)
    if (i.type == "pyramid") {
        for (let t = 0; t < l.length; t++) {
            if (t == l.length - 1) {
                if (e.tooltip) {
                    if (e.tooltip.index == t) {
                        a.beginPath()
                        a.setFillStyle(hexToRgb(l[t].color, i.activeOpacity))
                        a.moveTo(-n, -r)
                        a.lineTo(-l[t].radius - n, 0)
                        a.lineTo(l[t].radius + n, 0)
                        a.lineTo(n, -r)
                        a.lineTo(-n, -r)
                        a.closePath()
                        a.fill()
                    }
                }
                l[t].funnelArea = [o.x - l[t].radius, o.y - r * (t + 1), o.x + l[t].radius, o.y - r * t]
                a.beginPath()
                a.setLineWidth(i.borderWidth * e.pix)
                a.setStrokeStyle(i.borderColor)
                var s = hexToRgb(l[t].color, i.fillOpacity)
                if (i.linearType == "custom") {
                    var h = a.createLinearGradient(l[t].radius, -r, -l[t].radius, -r)
                    h.addColorStop(0, hexToRgb(l[t].color, i.fillOpacity))
                    h.addColorStop(0.5, hexToRgb(i.customColor[l[t].linearIndex], i.fillOpacity))
                    h.addColorStop(1, hexToRgb(l[t].color, i.fillOpacity))
                    s = h
                }
                a.setFillStyle(s)
                a.moveTo(0, -r)
                a.lineTo(-l[t].radius, 0)
                a.lineTo(l[t].radius, 0)
                a.lineTo(0, -r)
                a.closePath()
                a.fill()
                if (i.border == true) {
                    a.stroke()
                }
            } else {
                if (e.tooltip) {
                    if (e.tooltip.index == t) {
                        a.beginPath()
                        a.setFillStyle(hexToRgb(l[t].color, i.activeOpacity))
                        a.moveTo(0, 0)
                        a.lineTo(-l[t].radius - n, 0)
                        a.lineTo(-l[t + 1].radius - n, -r)
                        a.lineTo(l[t + 1].radius + n, -r)
                        a.lineTo(l[t].radius + n, 0)
                        a.lineTo(0, 0)
                        a.closePath()
                        a.fill()
                    }
                }
                l[t].funnelArea = [o.x - l[t].radius, o.y - r * (t + 1), o.x + l[t].radius, o.y - r * t]
                a.beginPath()
                a.setLineWidth(i.borderWidth * e.pix)
                a.setStrokeStyle(i.borderColor)
                var s = hexToRgb(l[t].color, i.fillOpacity)
                if (i.linearType == "custom") {
                    var h = a.createLinearGradient(l[t].radius, -r, -l[t].radius, -r)
                    h.addColorStop(0, hexToRgb(l[t].color, i.fillOpacity))
                    h.addColorStop(0.5, hexToRgb(i.customColor[l[t].linearIndex], i.fillOpacity))
                    h.addColorStop(1, hexToRgb(l[t].color, i.fillOpacity))
                    s = h
                }
                a.setFillStyle(s)
                a.moveTo(0, 0)
                a.lineTo(-l[t].radius, 0)
                a.lineTo(-l[t + 1].radius, -r)
                a.lineTo(l[t + 1].radius, -r)
                a.lineTo(l[t].radius, 0)
                a.lineTo(0, 0)
                a.closePath()
                a.fill()
                if (i.border == true) {
                    a.stroke()
                }
            }
            a.translate(0, -r)
        }
    } else {
        a.translate(0, -(l.length - 1) * r)
        for (let t = 0; t < l.length; t++) {
            if (t == l.length - 1) {
                if (e.tooltip) {
                    if (e.tooltip.index == t) {
                        a.beginPath()
                        a.setFillStyle(hexToRgb(l[t].color, i.activeOpacity))
                        a.moveTo(-n - i.minSize / 2, 0)
                        a.lineTo(-l[t].radius - n, -r)
                        a.lineTo(l[t].radius + n, -r)
                        a.lineTo(n + i.minSize / 2, 0)
                        a.lineTo(-n - i.minSize / 2, 0)
                        a.closePath()
                        a.fill()
                    }
                }
                l[t].funnelArea = [o.x - l[t].radius, o.y - r, o.x + l[t].radius, o.y]
                a.beginPath()
                a.setLineWidth(i.borderWidth * e.pix)
                a.setStrokeStyle(i.borderColor)
                var s = hexToRgb(l[t].color, i.fillOpacity)
                if (i.linearType == "custom") {
                    var h = a.createLinearGradient(l[t].radius, -r, -l[t].radius, -r)
                    h.addColorStop(0, hexToRgb(l[t].color, i.fillOpacity))
                    h.addColorStop(0.5, hexToRgb(i.customColor[l[t].linearIndex], i.fillOpacity))
                    h.addColorStop(1, hexToRgb(l[t].color, i.fillOpacity))
                    s = h
                }
                a.setFillStyle(s)
                a.moveTo(0, 0)
                a.lineTo(-i.minSize / 2, 0)
                a.lineTo(-l[t].radius, -r)
                a.lineTo(l[t].radius, -r)
                a.lineTo(i.minSize / 2, 0)
                a.lineTo(0, 0)
                a.closePath()
                a.fill()
                if (i.border == true) {
                    a.stroke()
                }
            } else {
                if (e.tooltip) {
                    if (e.tooltip.index == t) {
                        a.beginPath()
                        a.setFillStyle(hexToRgb(l[t].color, i.activeOpacity))
                        a.moveTo(0, 0)
                        a.lineTo(-l[t + 1].radius - n, 0)
                        a.lineTo(-l[t].radius - n, -r)
                        a.lineTo(l[t].radius + n, -r)
                        a.lineTo(l[t + 1].radius + n, 0)
                        a.lineTo(0, 0)
                        a.closePath()
                        a.fill()
                    }
                }
                l[t].funnelArea = [o.x - l[t].radius, o.y - r * (l.length - t), o.x + l[t].radius, o.y - r * (l.length - t - 1)]
                a.beginPath()
                a.setLineWidth(i.borderWidth * e.pix)
                a.setStrokeStyle(i.borderColor)
                var s = hexToRgb(l[t].color, i.fillOpacity)
                if (i.linearType == "custom") {
                    var h = a.createLinearGradient(l[t].radius, -r, -l[t].radius, -r)
                    h.addColorStop(0, hexToRgb(l[t].color, i.fillOpacity))
                    h.addColorStop(0.5, hexToRgb(i.customColor[l[t].linearIndex], i.fillOpacity))
                    h.addColorStop(1, hexToRgb(l[t].color, i.fillOpacity))
                    s = h
                }
                a.setFillStyle(s)
                a.moveTo(0, 0)
                a.lineTo(-l[t + 1].radius, 0)
                a.lineTo(-l[t].radius, -r)
                a.lineTo(l[t].radius, -r)
                a.lineTo(l[t + 1].radius, 0)
                a.lineTo(0, 0)
                a.closePath()
                a.fill()
                if (i.border == true) {
                    a.stroke()
                }
            }
            a.translate(0, r)
        }
    }
    a.restore()
    if (e.dataLabel !== false && d === 1) {
        drawFunnelText(l, e, a, r, i.labelAlign, n, o)
    }
    if (d === 1) {
        drawFunnelCenterText(l, e, a, r, i.labelAlign, n, o)
    }
    return { center: o, radius: x, series: l }
}
function drawFunnelText(l, s, h, c, d, x, f) {
    for (let n = 0; n < l.length; n++) {
        let t = l[n]
        if (t.labelShow === false) {
            continue
        }
        let e, a, i, r
        let o = t.formatter ? t.formatter(t, n, l, s) : util.toFixed(t._proportion_ * 100) + "%"
        o = t.labelText ? t.labelText : o
        if (d == "right") {
            if (n == l.length - 1) {
                e = (t.funnelArea[2] + f.x) / 2
            } else {
                e = (t.funnelArea[2] + l[n + 1].funnelArea[2]) / 2
            }
            a = e + x * 2
            i = t.funnelArea[1] + c / 2
            r = t.textSize * s.pix || s.fontSize * s.pix
            h.setLineWidth(1 * s.pix)
            h.setStrokeStyle(t.color)
            h.setFillStyle(t.color)
            h.beginPath()
            h.moveTo(e, i)
            h.lineTo(a, i)
            h.stroke()
            h.closePath()
            h.beginPath()
            h.moveTo(a, i)
            h.arc(a, i, 2 * s.pix, 0, 2 * Math.PI)
            h.closePath()
            h.fill()
            h.beginPath()
            h.setFontSize(r)
            h.setFillStyle(t.textColor || s.fontColor)
            h.fillText(o, a + 5, i + r / 2 - 2)
            h.closePath()
            h.stroke()
            h.closePath()
        }
        if (d == "left") {
            if (n == l.length - 1) {
                e = (t.funnelArea[0] + f.x) / 2
            } else {
                e = (t.funnelArea[0] + l[n + 1].funnelArea[0]) / 2
            }
            a = e - x * 2
            i = t.funnelArea[1] + c / 2
            r = t.textSize * s.pix || s.fontSize * s.pix
            h.setLineWidth(1 * s.pix)
            h.setStrokeStyle(t.color)
            h.setFillStyle(t.color)
            h.beginPath()
            h.moveTo(e, i)
            h.lineTo(a, i)
            h.stroke()
            h.closePath()
            h.beginPath()
            h.moveTo(a, i)
            h.arc(a, i, 2, 0, 2 * Math.PI)
            h.closePath()
            h.fill()
            h.beginPath()
            h.setFontSize(r)
            h.setFillStyle(t.textColor || s.fontColor)
            h.fillText(o, a - 5 - measureText(o, r, h), i + r / 2 - 2)
            h.closePath()
            h.stroke()
            h.closePath()
        }
    }
}
function drawFunnelCenterText(r, o, n, l, t, e, s) {
    for (let i = 0; i < r.length; i++) {
        let t = r[i]
        let e, a
        if (t.centerText) {
            e = t.funnelArea[1] + l / 2
            a = t.centerTextSize * o.pix || o.fontSize * o.pix
            n.beginPath()
            n.setFontSize(a)
            n.setFillStyle(t.centerTextColor || "#FFFFFF")
            n.fillText(t.centerText, s.x - measureText(t.centerText, a, n) / 2, e + a / 2 - 2)
            n.closePath()
            n.stroke()
            n.closePath()
        }
    }
}
function drawCanvas(t, e) {
    e.save()
    e.translate(0, 0.5)
    e.restore()
    e.draw()
}
var Timing = {
    easeIn: function t(e) {
        return Math.pow(e, 3)
    },
    easeOut: function t(e) {
        return Math.pow(e - 1, 3) + 1
    },
    easeInOut: function t(e) {
        if ((e /= 0.5) < 1) {
            return 0.5 * Math.pow(e, 3)
        } else {
            return 0.5 * (Math.pow(e - 2, 3) + 2)
        }
    },
    linear: function t(e) {
        return e
    }
}
function Animation(r) {
    this.isStop = false
    r.duration = typeof r.duration === "undefined" ? 1e3 : r.duration
    r.timing = r.timing || "easeInOut"
    var o = 17
    function t() {
        if (typeof setTimeout !== "undefined") {
            return function (e, t) {
                setTimeout(function () {
                    var t = +new Date()
                    e(t)
                }, t)
            }
        } else if (typeof requestAnimationFrame !== "undefined") {
            return requestAnimationFrame
        } else {
            return function (t) {
                t(null)
            }
        }
    }
    var n = t()
    var l = null
    var s = function t(e) {
        if (e === null || this.isStop === true) {
            r.onProcess && r.onProcess(1)
            r.onAnimationFinish && r.onAnimationFinish()
            return
        }
        if (l === null) {
            l = e
        }
        if (e - l < r.duration) {
            var a = (e - l) / r.duration
            var i = Timing[r.timing]
            a = i(a)
            r.onProcess && r.onProcess(a)
            n(s, o)
        } else {
            r.onProcess && r.onProcess(1)
            r.onAnimationFinish && r.onAnimationFinish()
        }
    }
    s = s.bind(this)
    n(s, o)
}
Animation.prototype.stop = function () {
    this.isStop = true
}
function drawCharts(t, l, n, s) {
    var h = this
    var c = l.series
    if (t === "pie" || t === "ring" || t === "mount" || t === "rose" || t === "funnel") {
        c = fixPieSeries(c, l, n)
    }
    var d = l.categories
    if (t === "mount") {
        d = []
        for (let t = 0; t < c.length; t++) {
            if (c[t].show !== false) d.push(c[t].name)
        }
        l.categories = d
    }
    c = fillSeries(c, l, n)
    var e = l.animation ? l.duration : 0
    h.animationInstance && h.animationInstance.stop()
    var x = null
    if (t == "candle") {
        let t = assign({}, l.extra.candle.average)
        if (t.show) {
            x = calCandleMA(t.day, t.name, t.color, c[0].data)
            x = fillSeries(x, l, n)
            l.seriesMA = x
        } else if (l.seriesMA) {
            x = l.seriesMA = fillSeries(l.seriesMA, l, n)
        } else {
            x = c
        }
    } else {
        x = c
    }
    l._series_ = c = filterSeries(c)
    l.area = new Array(4)
    for (let t = 0; t < 4; t++) {
        l.area[t] = l.padding[t] * l.pix
    }
    var a = calLegendData(x, l, n, l.chartData, s),
        r = a.area.wholeHeight,
        o = a.area.wholeWidth
    switch (l.legend.position) {
        case "top":
            l.area[0] += r
            break
        case "bottom":
            l.area[2] += r
            break
        case "left":
            l.area[3] += o
            break
        case "right":
            l.area[1] += o
            break
    }
    let f = {},
        i = 0
    if (l.type === "line" || l.type === "column" || l.type === "mount" || l.type === "area" || l.type === "mix" || l.type === "candle" || l.type === "scatter" || l.type === "bubble" || l.type === "bar") {
        f = calYAxisData(c, l, n, s)
        i = f.yAxisWidth
        if (l.yAxis.showTitle) {
            let e = 0
            for (let t = 0; t < l.yAxis.data.length; t++) {
                e = Math.max(e, l.yAxis.data[t].titleFontSize ? l.yAxis.data[t].titleFontSize * l.pix : n.fontSize)
            }
            l.area[0] += e
        }
        let e = 0,
            a = 0
        for (let t = 0; t < i.length; t++) {
            if (i[t].position == "left") {
                if (a > 0) {
                    l.area[3] += i[t].width + l.yAxis.padding * l.pix
                } else {
                    l.area[3] += i[t].width
                }
                a += 1
            } else if (i[t].position == "right") {
                if (e > 0) {
                    l.area[1] += i[t].width + l.yAxis.padding * l.pix
                } else {
                    l.area[1] += i[t].width
                }
                e += 1
            }
        }
    } else {
        n.yAxisWidth = i
    }
    l.chartData.yAxisData = f
    if (l.categories && l.categories.length && l.type !== "radar" && l.type !== "gauge" && l.type !== "bar") {
        l.chartData.xAxisData = getXAxisPoints(l.categories, l, n)
        let t = calCategoriesData(l.categories, l, n, l.chartData.xAxisData.eachSpacing, s),
            e = t.xAxisHeight,
            a = t.angle
        n.xAxisHeight = e
        n._xAxisTextAngle_ = a
        l.area[2] += e
        l.chartData.categoriesData = t
    } else {
        if (l.type === "line" || l.type === "area" || l.type === "scatter" || l.type === "bubble" || l.type === "bar") {
            l.chartData.xAxisData = calXAxisData(c, l, n, s)
            d = l.chartData.xAxisData.rangesFormat
            let t = calCategoriesData(d, l, n, l.chartData.xAxisData.eachSpacing, s),
                e = t.xAxisHeight,
                a = t.angle
            n.xAxisHeight = e
            n._xAxisTextAngle_ = a
            l.area[2] += e
            l.chartData.categoriesData = t
        } else {
            l.chartData.xAxisData = { xAxisPoints: [] }
        }
    }
    if (l.enableScroll && l.xAxis.scrollAlign == "right" && l._scrollDistance_ === undefined) {
        let t = 0,
            e = l.chartData.xAxisData.xAxisPoints,
            a = l.chartData.xAxisData.startX,
            i = l.chartData.xAxisData.endX,
            r = l.chartData.xAxisData.eachSpacing
        let o = r * (e.length - 1)
        let n = i - a
        t = n - o
        h.scrollOption.currentOffset = t
        h.scrollOption.startTouchX = t
        h.scrollOption.distance = 0
        h.scrollOption.lastMoveTime = 0
        l._scrollDistance_ = t
    }
    if (t === "pie" || t === "ring" || t === "rose") {
        n._pieTextMaxLength_ = l.dataLabel === false ? 0 : getPieTextMaxLength(x, n, s, l)
    }
    switch (t) {
        case "word":
            this.animationInstance = new Animation({
                timing: l.timing,
                duration: e,
                onProcess: function (t) {
                    s.clearRect(0, 0, l.width, l.height)
                    if (l.rotate) {
                        contextRotate(s, l)
                    }
                    drawWordCloudDataPoints(c, l, n, s, t)
                    drawCanvas(l, s)
                },
                onAnimationFinish: function t() {
                    h.uevent.trigger("renderComplete")
                }
            })
            break
        case "map":
            s.clearRect(0, 0, l.width, l.height)
            drawMapDataPoints(c, l, n, s)
            setTimeout(() => {
                this.uevent.trigger("renderComplete")
            }, 50)
            break
        case "funnel":
            this.animationInstance = new Animation({
                timing: l.timing,
                duration: e,
                onProcess: function (t) {
                    s.clearRect(0, 0, l.width, l.height)
                    if (l.rotate) {
                        contextRotate(s, l)
                    }
                    l.chartData.funnelData = drawFunnelDataPoints(c, l, n, s, t)
                    drawLegend(l.series, l, n, s, l.chartData)
                    drawToolTipBridge(l, n, s, t)
                    drawCanvas(l, s)
                },
                onAnimationFinish: function t() {
                    h.uevent.trigger("renderComplete")
                }
            })
            break
        case "line":
            this.animationInstance = new Animation({
                timing: l.timing,
                duration: e,
                onProcess: function t(e) {
                    s.clearRect(0, 0, l.width, l.height)
                    if (l.rotate) {
                        contextRotate(s, l)
                    }
                    drawYAxisGrid(d, l, n, s)
                    drawXAxis(d, l, n, s)
                    var a = drawLineDataPoints(c, l, n, s, e),
                        i = a.xAxisPoints,
                        r = a.calPoints,
                        o = a.eachSpacing
                    l.chartData.xAxisPoints = i
                    l.chartData.calPoints = r
                    l.chartData.eachSpacing = o
                    drawYAxis(c, l, n, s)
                    if (l.enableMarkLine !== false && e === 1) {
                        drawMarkLine(l, n, s)
                    }
                    drawLegend(l.series, l, n, s, l.chartData)
                    drawToolTipBridge(l, n, s, e, o, i)
                    drawCanvas(l, s)
                },
                onAnimationFinish: function t() {
                    h.uevent.trigger("renderComplete")
                }
            })
            break
        case "scatter":
            this.animationInstance = new Animation({
                timing: l.timing,
                duration: e,
                onProcess: function t(e) {
                    s.clearRect(0, 0, l.width, l.height)
                    if (l.rotate) {
                        contextRotate(s, l)
                    }
                    drawYAxisGrid(d, l, n, s)
                    drawXAxis(d, l, n, s)
                    var a = drawScatterDataPoints(c, l, n, s, e),
                        i = a.xAxisPoints,
                        r = a.calPoints,
                        o = a.eachSpacing
                    l.chartData.xAxisPoints = i
                    l.chartData.calPoints = r
                    l.chartData.eachSpacing = o
                    drawYAxis(c, l, n, s)
                    if (l.enableMarkLine !== false && e === 1) {
                        drawMarkLine(l, n, s)
                    }
                    drawLegend(l.series, l, n, s, l.chartData)
                    drawToolTipBridge(l, n, s, e, o, i)
                    drawCanvas(l, s)
                },
                onAnimationFinish: function t() {
                    h.uevent.trigger("renderComplete")
                }
            })
            break
        case "bubble":
            this.animationInstance = new Animation({
                timing: l.timing,
                duration: e,
                onProcess: function t(e) {
                    s.clearRect(0, 0, l.width, l.height)
                    if (l.rotate) {
                        contextRotate(s, l)
                    }
                    drawYAxisGrid(d, l, n, s)
                    drawXAxis(d, l, n, s)
                    var a = drawBubbleDataPoints(c, l, n, s, e),
                        i = a.xAxisPoints,
                        r = a.calPoints,
                        o = a.eachSpacing
                    l.chartData.xAxisPoints = i
                    l.chartData.calPoints = r
                    l.chartData.eachSpacing = o
                    drawYAxis(c, l, n, s)
                    if (l.enableMarkLine !== false && e === 1) {
                        drawMarkLine(l, n, s)
                    }
                    drawLegend(l.series, l, n, s, l.chartData)
                    drawToolTipBridge(l, n, s, e, o, i)
                    drawCanvas(l, s)
                },
                onAnimationFinish: function t() {
                    h.uevent.trigger("renderComplete")
                }
            })
            break
        case "mix":
            this.animationInstance = new Animation({
                timing: l.timing,
                duration: e,
                onProcess: function t(e) {
                    s.clearRect(0, 0, l.width, l.height)
                    if (l.rotate) {
                        contextRotate(s, l)
                    }
                    drawYAxisGrid(d, l, n, s)
                    drawXAxis(d, l, n, s)
                    var a = drawMixDataPoints(c, l, n, s, e),
                        i = a.xAxisPoints,
                        r = a.calPoints,
                        o = a.eachSpacing
                    l.chartData.xAxisPoints = i
                    l.chartData.calPoints = r
                    l.chartData.eachSpacing = o
                    drawYAxis(c, l, n, s)
                    if (l.enableMarkLine !== false && e === 1) {
                        drawMarkLine(l, n, s)
                    }
                    drawLegend(l.series, l, n, s, l.chartData)
                    drawToolTipBridge(l, n, s, e, o, i)
                    drawCanvas(l, s)
                },
                onAnimationFinish: function t() {
                    h.uevent.trigger("renderComplete")
                }
            })
            break
        case "column":
            this.animationInstance = new Animation({
                timing: l.timing,
                duration: e,
                onProcess: function t(e) {
                    s.clearRect(0, 0, l.width, l.height)
                    if (l.rotate) {
                        contextRotate(s, l)
                    }
                    drawYAxisGrid(d, l, n, s)
                    drawXAxis(d, l, n, s)
                    var a = drawColumnDataPoints(c, l, n, s, e),
                        i = a.xAxisPoints,
                        r = a.calPoints,
                        o = a.eachSpacing
                    l.chartData.xAxisPoints = i
                    l.chartData.calPoints = r
                    l.chartData.eachSpacing = o
                    drawYAxis(c, l, n, s)
                    if (l.enableMarkLine !== false && e === 1) {
                        drawMarkLine(l, n, s)
                    }
                    drawLegend(l.series, l, n, s, l.chartData)
                    drawToolTipBridge(l, n, s, e, o, i)
                    drawCanvas(l, s)
                },
                onAnimationFinish: function t() {
                    h.uevent.trigger("renderComplete")
                }
            })
            break
        case "mount":
            this.animationInstance = new Animation({
                timing: l.timing,
                duration: e,
                onProcess: function t(e) {
                    s.clearRect(0, 0, l.width, l.height)
                    if (l.rotate) {
                        contextRotate(s, l)
                    }
                    drawYAxisGrid(d, l, n, s)
                    drawXAxis(d, l, n, s)
                    var a = drawMountDataPoints(c, l, n, s, e),
                        i = a.xAxisPoints,
                        r = a.calPoints,
                        o = a.eachSpacing
                    l.chartData.xAxisPoints = i
                    l.chartData.calPoints = r
                    l.chartData.eachSpacing = o
                    drawYAxis(c, l, n, s)
                    if (l.enableMarkLine !== false && e === 1) {
                        drawMarkLine(l, n, s)
                    }
                    drawLegend(l.series, l, n, s, l.chartData)
                    drawToolTipBridge(l, n, s, e, o, i)
                    drawCanvas(l, s)
                },
                onAnimationFinish: function t() {
                    h.uevent.trigger("renderComplete")
                }
            })
            break
        case "bar":
            this.animationInstance = new Animation({
                timing: l.timing,
                duration: e,
                onProcess: function t(e) {
                    s.clearRect(0, 0, l.width, l.height)
                    if (l.rotate) {
                        contextRotate(s, l)
                    }
                    drawXAxis(d, l, n, s)
                    var a = drawBarDataPoints(c, l, n, s, e),
                        i = a.yAxisPoints,
                        r = a.calPoints,
                        o = a.eachSpacing
                    l.chartData.yAxisPoints = i
                    l.chartData.xAxisPoints = l.chartData.xAxisData.xAxisPoints
                    l.chartData.calPoints = r
                    l.chartData.eachSpacing = o
                    drawYAxis(c, l, n, s)
                    if (l.enableMarkLine !== false && e === 1) {
                        drawMarkLine(l, n, s)
                    }
                    drawLegend(l.series, l, n, s, l.chartData)
                    drawToolTipBridge(l, n, s, e, o, i)
                    drawCanvas(l, s)
                },
                onAnimationFinish: function t() {
                    h.uevent.trigger("renderComplete")
                }
            })
            break
        case "area":
            this.animationInstance = new Animation({
                timing: l.timing,
                duration: e,
                onProcess: function t(e) {
                    s.clearRect(0, 0, l.width, l.height)
                    if (l.rotate) {
                        contextRotate(s, l)
                    }
                    drawYAxisGrid(d, l, n, s)
                    drawXAxis(d, l, n, s)
                    var a = drawAreaDataPoints(c, l, n, s, e),
                        i = a.xAxisPoints,
                        r = a.calPoints,
                        o = a.eachSpacing
                    l.chartData.xAxisPoints = i
                    l.chartData.calPoints = r
                    l.chartData.eachSpacing = o
                    drawYAxis(c, l, n, s)
                    if (l.enableMarkLine !== false && e === 1) {
                        drawMarkLine(l, n, s)
                    }
                    drawLegend(l.series, l, n, s, l.chartData)
                    drawToolTipBridge(l, n, s, e, o, i)
                    drawCanvas(l, s)
                },
                onAnimationFinish: function t() {
                    h.uevent.trigger("renderComplete")
                }
            })
            break
        case "ring":
            this.animationInstance = new Animation({
                timing: l.timing,
                duration: e,
                onProcess: function t(e) {
                    s.clearRect(0, 0, l.width, l.height)
                    if (l.rotate) {
                        contextRotate(s, l)
                    }
                    l.chartData.pieData = drawPieDataPoints(c, l, n, s, e)
                    drawLegend(l.series, l, n, s, l.chartData)
                    drawToolTipBridge(l, n, s, e)
                    drawCanvas(l, s)
                },
                onAnimationFinish: function t() {
                    h.uevent.trigger("renderComplete")
                }
            })
            break
        case "pie":
            this.animationInstance = new Animation({
                timing: l.timing,
                duration: e,
                onProcess: function t(e) {
                    s.clearRect(0, 0, l.width, l.height)
                    if (l.rotate) {
                        contextRotate(s, l)
                    }
                    l.chartData.pieData = drawPieDataPoints(c, l, n, s, e)
                    drawLegend(l.series, l, n, s, l.chartData)
                    drawToolTipBridge(l, n, s, e)
                    drawCanvas(l, s)
                },
                onAnimationFinish: function t() {
                    h.uevent.trigger("renderComplete")
                }
            })
            break
        case "rose":
            this.animationInstance = new Animation({
                timing: l.timing,
                duration: e,
                onProcess: function t(e) {
                    s.clearRect(0, 0, l.width, l.height)
                    if (l.rotate) {
                        contextRotate(s, l)
                    }
                    l.chartData.pieData = drawRoseDataPoints(c, l, n, s, e)
                    drawLegend(l.series, l, n, s, l.chartData)
                    drawToolTipBridge(l, n, s, e)
                    drawCanvas(l, s)
                },
                onAnimationFinish: function t() {
                    h.uevent.trigger("renderComplete")
                }
            })
            break
        case "radar":
            this.animationInstance = new Animation({
                timing: l.timing,
                duration: e,
                onProcess: function t(e) {
                    s.clearRect(0, 0, l.width, l.height)
                    if (l.rotate) {
                        contextRotate(s, l)
                    }
                    l.chartData.radarData = drawRadarDataPoints(c, l, n, s, e)
                    drawLegend(l.series, l, n, s, l.chartData)
                    drawToolTipBridge(l, n, s, e)
                    drawCanvas(l, s)
                },
                onAnimationFinish: function t() {
                    h.uevent.trigger("renderComplete")
                }
            })
            break
        case "arcbar":
            this.animationInstance = new Animation({
                timing: l.timing,
                duration: e,
                onProcess: function t(e) {
                    s.clearRect(0, 0, l.width, l.height)
                    if (l.rotate) {
                        contextRotate(s, l)
                    }
                    l.chartData.arcbarData = drawArcbarDataPoints(c, l, n, s, e)
                    drawCanvas(l, s)
                },
                onAnimationFinish: function t() {
                    h.uevent.trigger("renderComplete")
                }
            })
            break
        case "gauge":
            this.animationInstance = new Animation({
                timing: l.timing,
                duration: e,
                onProcess: function t(e) {
                    s.clearRect(0, 0, l.width, l.height)
                    if (l.rotate) {
                        contextRotate(s, l)
                    }
                    l.chartData.gaugeData = drawGaugeDataPoints(d, c, l, n, s, e)
                    drawCanvas(l, s)
                },
                onAnimationFinish: function t() {
                    h.uevent.trigger("renderComplete")
                }
            })
            break
        case "candle":
            this.animationInstance = new Animation({
                timing: l.timing,
                duration: e,
                onProcess: function t(e) {
                    s.clearRect(0, 0, l.width, l.height)
                    if (l.rotate) {
                        contextRotate(s, l)
                    }
                    drawYAxisGrid(d, l, n, s)
                    drawXAxis(d, l, n, s)
                    var a = drawCandleDataPoints(c, x, l, n, s, e),
                        i = a.xAxisPoints,
                        r = a.calPoints,
                        o = a.eachSpacing
                    l.chartData.xAxisPoints = i
                    l.chartData.calPoints = r
                    l.chartData.eachSpacing = o
                    drawYAxis(c, l, n, s)
                    if (l.enableMarkLine !== false && e === 1) {
                        drawMarkLine(l, n, s)
                    }
                    if (x) {
                        drawLegend(x, l, n, s, l.chartData)
                    } else {
                        drawLegend(l.series, l, n, s, l.chartData)
                    }
                    drawToolTipBridge(l, n, s, e, o, i)
                    drawCanvas(l, s)
                },
                onAnimationFinish: function t() {
                    h.uevent.trigger("renderComplete")
                }
            })
            break
    }
}
function uChartsEvent() {
    this.events = {}
}
uChartsEvent.prototype.addEventListener = function (t, e) {
    this.events[t] = this.events[t] || []
    this.events[t].push(e)
}
uChartsEvent.prototype.delEventListener = function (t) {
    this.events[t] = []
}
uChartsEvent.prototype.trigger = function () {
    for (var t = arguments.length, e = Array(t), a = 0; a < t; a++) {
        e[a] = arguments[a]
    }
    var i = e[0]
    var r = e.slice(1)
    if (!!this.events[i]) {
        this.events[i].forEach(function (t) {
            try {
                t.apply(null, r)
            } catch (t) {}
        })
    }
}
var uCharts = function t(a) {
    a.pix = a.pixelRatio ? a.pixelRatio : 1
    a.fontSize = a.fontSize ? a.fontSize : 13
    a.fontColor = a.fontColor ? a.fontColor : config.fontColor
    if (a.background == "" || a.background == "none") {
        a.background = "#FFFFFF"
    }
    a.title = assign({}, a.title)
    a.subtitle = assign({}, a.subtitle)
    a.duration = a.duration ? a.duration : 1e3
    a.yAxis = assign({}, { data: [], showTitle: false, disabled: false, disableGrid: false, gridSet: "number", splitNumber: 5, gridType: "solid", dashLength: 4 * a.pix, gridColor: "#cccccc", padding: 10, fontColor: "#666666" }, a.yAxis)
    a.xAxis = assign({}, { rotateLabel: false, rotateAngle: 45, disabled: false, disableGrid: false, splitNumber: 5, calibration: false, fontColor: "#666666", fontSize: 13, lineHeight: 20, marginTop: 0, gridType: "solid", dashLength: 4, scrollAlign: "left", boundaryGap: "center", axisLine: true, axisLineColor: "#cccccc", titleFontSize: 13, titleOffsetY: 0, titleOffsetX: 0, titleFontColor: "#666666" }, a.xAxis)
    a.xAxis.scrollPosition = a.xAxis.scrollAlign
    a.legend = assign({}, { show: true, position: "bottom", float: "center", backgroundColor: "rgba(0,0,0,0)", borderColor: "rgba(0,0,0,0)", borderWidth: 0, padding: 5, margin: 5, itemGap: 10, fontSize: a.fontSize, lineHeight: a.fontSize, fontColor: a.fontColor, formatter: {}, hiddenColor: "#CECECE" }, a.legend)
    a.extra = assign({ tooltip: { legendShape: "auto" } }, a.extra)
    a.rotate = a.rotate ? true : false
    a.animation = a.animation ? true : false
    a.rotate = a.rotate ? true : false
    a.canvas2d = a.canvas2d ? true : false
    let e = assign({}, config)
    e.color = a.color ? a.color : e.color
    if (a.type == "pie") {
        e.pieChartLinePadding = a.dataLabel === false ? 0 : a.extra.pie.labelWidth * a.pix || e.pieChartLinePadding * a.pix
    }
    if (a.type == "ring") {
        e.pieChartLinePadding = a.dataLabel === false ? 0 : a.extra.ring.labelWidth * a.pix || e.pieChartLinePadding * a.pix
    }
    if (a.type == "rose") {
        e.pieChartLinePadding = a.dataLabel === false ? 0 : a.extra.rose.labelWidth * a.pix || e.pieChartLinePadding * a.pix
    }
    e.pieChartTextPadding = a.dataLabel === false ? 0 : e.pieChartTextPadding * a.pix
    e.rotate = a.rotate
    if (a.rotate) {
        let t = a.width
        let e = a.height
        a.width = e
        a.height = t
    }
    a.padding = a.padding ? a.padding : e.padding
    e.yAxisWidth = config.yAxisWidth * a.pix
    e.fontSize = a.fontSize * a.pix
    e.titleFontSize = config.titleFontSize * a.pix
    e.subtitleFontSize = config.subtitleFontSize * a.pix
    if (!a.context) {
        throw new Error("[uCharts] 未获取到context！注意：v2.0版本后，需要自行获取canvas的绘图上下文并传入opts.context！")
    }
    this.context = a.context
    if (!this.context.setTextAlign) {
        this.context.setStrokeStyle = function (t) {
            return (this.strokeStyle = t)
        }
        this.context.setLineWidth = function (t) {
            return (this.lineWidth = t)
        }
        this.context.setLineCap = function (t) {
            return (this.lineCap = t)
        }
        this.context.setFontSize = function (t) {
            return (this.font = t + "px sans-serif")
        }
        this.context.setFillStyle = function (t) {
            return (this.fillStyle = t)
        }
        this.context.setTextAlign = function (t) {
            return (this.textAlign = t)
        }
        this.context.setTextBaseline = function (t) {
            return (this.textBaseline = t)
        }
        this.context.setShadow = function (t, e, a, i) {
            this.shadowColor = i
            this.shadowOffsetX = t
            this.shadowOffsetY = e
            this.shadowBlur = a
        }
        this.context.draw = function () {}
    }
    if (!this.context.setLineDash) {
        this.context.setLineDash = function (t) {}
    }
    a.chartData = {}
    this.uevent = new uChartsEvent()
    this.scrollOption = { currentOffset: 0, startTouchX: 0, distance: 0, lastMoveTime: 0 }
    this.opts = a
    this.config = e
    drawCharts.call(this, a.type, a, e, this.context)
}
uCharts.prototype.updateData = function () {
    let t = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {}
    this.opts = assign({}, this.opts, t)
    this.opts.updateData = true
    let c = t.scrollPosition || "current"
    switch (c) {
        case "current":
            this.opts._scrollDistance_ = this.scrollOption.currentOffset
            break
        case "left":
            this.opts._scrollDistance_ = 0
            this.scrollOption = { currentOffset: 0, startTouchX: 0, distance: 0, lastMoveTime: 0 }
            break
        case "right":
            let t = calYAxisData(this.opts.series, this.opts, this.config, this.context),
                e = t.yAxisWidth
            this.config.yAxisWidth = e
            let a = 0
            let i = getXAxisPoints(this.opts.categories, this.opts, this.config),
                r = i.xAxisPoints,
                o = i.startX,
                n = i.endX,
                l = i.eachSpacing
            let s = l * (r.length - 1)
            let h = n - o
            a = h - s
            this.scrollOption = { currentOffset: a, startTouchX: a, distance: 0, lastMoveTime: 0 }
            this.opts._scrollDistance_ = a
            break
    }
    drawCharts.call(this, this.opts.type, this.opts, this.config, this.context)
}
uCharts.prototype.zoom = function () {
    var t = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : this.opts.xAxis.itemCount
    if (this.opts.enableScroll !== true) {
        console.log("[uCharts] 请启用滚动条后使用")
        return
    }
    let e = Math.round(Math.abs(this.scrollOption.currentOffset) / this.opts.chartData.eachSpacing) + Math.round(this.opts.xAxis.itemCount / 2)
    this.opts.animation = false
    this.opts.xAxis.itemCount = t.itemCount
    let a = calYAxisData(this.opts.series, this.opts, this.config, this.context),
        i = a.yAxisWidth
    this.config.yAxisWidth = i
    let r = 0
    let o = getXAxisPoints(this.opts.categories, this.opts, this.config),
        h = o.xAxisPoints,
        c = o.startX,
        d = o.endX,
        n = o.eachSpacing
    let x = n * e
    let l = d - c
    let s = l - n * (h.length - 1)
    r = l / 2 - x
    if (r > 0) {
        r = 0
    }
    if (r < s) {
        r = s
    }
    this.scrollOption = { currentOffset: r, startTouchX: 0, distance: 0, lastMoveTime: 0 }
    calValidDistance(this, r, this.opts.chartData, this.config, this.opts)
    this.opts._scrollDistance_ = r
    drawCharts.call(this, this.opts.type, this.opts, this.config, this.context)
}
uCharts.prototype.dobuleZoom = function (t) {
    if (this.opts.enableScroll !== true) {
        console.log("[uCharts] 请启用滚动条后使用")
        return
    }
    const a = t.changedTouches
    if (a.length < 2) {
        return
    }
    for (var e = 0; e < a.length; e++) {
        a[e].x = a[e].x ? a[e].x : a[e].clientX
        a[e].y = a[e].y ? a[e].y : a[e].clientY
    }
    const i = [getTouches(a[0], this.opts, t), getTouches(a[1], this.opts, t)]
    const c = Math.abs(i[0].x - i[1].x)
    if (!this.scrollOption.moveCount) {
        let t = { changedTouches: [{ x: a[0].x, y: this.opts.area[0] / this.opts.pix + 2 }] }
        let e = { changedTouches: [{ x: a[1].x, y: this.opts.area[0] / this.opts.pix + 2 }] }
        if (this.opts.rotate) {
            t = { changedTouches: [{ x: this.opts.height / this.opts.pix - this.opts.area[0] / this.opts.pix - 2, y: a[0].y }] }
            e = { changedTouches: [{ x: this.opts.height / this.opts.pix - this.opts.area[0] / this.opts.pix - 2, y: a[1].y }] }
        }
        const s = this.getCurrentDataIndex(t).index
        const h = this.getCurrentDataIndex(e).index
        const v = Math.abs(s - h)
        this.scrollOption.moveCount = v
        this.scrollOption.moveCurrent1 = Math.min(s, h)
        this.scrollOption.moveCurrent2 = Math.max(s, h)
        return
    }
    let d = c / this.scrollOption.moveCount
    let r = (this.opts.width - this.opts.area[1] - this.opts.area[3]) / d
    r = r <= 2 ? 2 : r
    r = r >= this.opts.categories.length ? this.opts.categories.length : r
    this.opts.animation = false
    this.opts.xAxis.itemCount = r
    let o = 0
    let n = getXAxisPoints(this.opts.categories, this.opts, this.config),
        x = n.xAxisPoints,
        f = n.startX,
        p = n.endX,
        l = n.eachSpacing
    let u = l * this.scrollOption.moveCurrent1
    let g = p - f
    let y = g - l * (x.length - 1)
    o = -u + Math.min(i[0].x, i[1].x) - this.opts.area[3] - l
    if (o > 0) {
        o = 0
    }
    if (o < y) {
        o = y
    }
    this.scrollOption.currentOffset = o
    this.scrollOption.startTouchX = 0
    this.scrollOption.distance = 0
    calValidDistance(this, o, this.opts.chartData, this.config, this.opts)
    this.opts._scrollDistance_ = o
    drawCharts.call(this, this.opts.type, this.opts, this.config, this.context)
}
uCharts.prototype.stopAnimation = function () {
    this.animationInstance && this.animationInstance.stop()
}
uCharts.prototype.addEventListener = function (t, e) {
    this.uevent.addEventListener(t, e)
}
uCharts.prototype.delEventListener = function (t) {
    this.uevent.delEventListener(t)
}
uCharts.prototype.getCurrentDataIndex = function (e) {
    var a = null
    if (e.changedTouches) {
        a = e.changedTouches[0]
    } else {
        a = e.mp.changedTouches[0]
    }
    if (a) {
        let t = getTouches(a, this.opts, e)
        if (this.opts.type === "pie" || this.opts.type === "ring") {
            return findPieChartCurrentIndex({ x: t.x, y: t.y }, this.opts.chartData.pieData, this.opts)
        } else if (this.opts.type === "rose") {
            return findRoseChartCurrentIndex({ x: t.x, y: t.y }, this.opts.chartData.pieData, this.opts)
        } else if (this.opts.type === "radar") {
            return findRadarChartCurrentIndex({ x: t.x, y: t.y }, this.opts.chartData.radarData, this.opts.categories.length)
        } else if (this.opts.type === "funnel") {
            return findFunnelChartCurrentIndex({ x: t.x, y: t.y }, this.opts.chartData.funnelData)
        } else if (this.opts.type === "map") {
            return findMapChartCurrentIndex({ x: t.x, y: t.y }, this.opts)
        } else if (this.opts.type === "word") {
            return findWordChartCurrentIndex({ x: t.x, y: t.y }, this.opts.chartData.wordCloudData)
        } else if (this.opts.type === "bar") {
            return findBarChartCurrentIndex({ x: t.x, y: t.y }, this.opts.chartData.calPoints, this.opts, this.config, Math.abs(this.scrollOption.currentOffset))
        } else {
            return findCurrentIndex({ x: t.x, y: t.y }, this.opts.chartData.calPoints, this.opts, this.config, Math.abs(this.scrollOption.currentOffset))
        }
    }
    return -1
}
uCharts.prototype.getLegendDataIndex = function (e) {
    var a = null
    if (e.changedTouches) {
        a = e.changedTouches[0]
    } else {
        a = e.mp.changedTouches[0]
    }
    if (a) {
        let t = getTouches(a, this.opts, e)
        return findLegendIndex({ x: t.x, y: t.y }, this.opts.chartData.legendData)
    }
    return -1
}
uCharts.prototype.touchLegend = function (t) {
    var e = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {}
    var a = null
    if (t.changedTouches) {
        a = t.changedTouches[0]
    } else {
        a = t.mp.changedTouches[0]
    }
    if (a) {
        var i = getTouches(a, this.opts, t)
        var r = this.getLegendDataIndex(t)
        if (r >= 0) {
            if (this.opts.type == "candle") {
                this.opts.seriesMA[r].show = !this.opts.seriesMA[r].show
            } else {
                this.opts.series[r].show = !this.opts.series[r].show
            }
            this.opts.animation = e.animation ? true : false
            this.opts._scrollDistance_ = this.scrollOption.currentOffset
            drawCharts.call(this, this.opts.type, this.opts, this.config, this.context)
        }
    }
}
uCharts.prototype.showToolTip = function (t) {
    var e = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {}
    var c = null
    if (t.changedTouches) {
        c = t.changedTouches[0]
    } else {
        c = t.mp.changedTouches[0]
    }
    if (!c) {
        console.log("[uCharts] 未获取到event坐标信息")
    }
    var a = getTouches(c, this.opts, t)
    var d = this.scrollOption.currentOffset
    var i = assign({}, this.opts, { _scrollDistance_: d, animation: false })
    if (this.opts.type === "line" || this.opts.type === "area" || this.opts.type === "column" || this.opts.type === "scatter" || this.opts.type === "bubble") {
        var r = this.getCurrentDataIndex(t)
        var o = e.index == undefined ? r.index : e.index
        if (o > -1 || o.length > 0) {
            var n = getSeriesDataItem(this.opts.series, o, r.group)
            if (n.length !== 0) {
                var l = getToolTipData(n, this.opts, o, r.group, this.opts.categories, e),
                    s = l.textList,
                    h = l.offset
                h.y = a.y
                i.tooltip = { textList: e.textList !== undefined ? e.textList : s, offset: e.offset !== undefined ? e.offset : h, option: e, index: o, group: r.group }
            }
        }
        drawCharts.call(this, i.type, i, this.config, this.context)
    }
    if (this.opts.type === "mount") {
        var o = e.index == undefined ? this.getCurrentDataIndex(t).index : e.index
        if (o > -1) {
            var i = assign({}, this.opts, { animation: false })
            var n = assign({}, i._series_[o])
            var s = [{ text: e.formatter ? e.formatter(n, undefined, o, i) : n.name + ": " + n.data, color: n.color, legendShape: this.opts.extra.tooltip.legendShape == "auto" ? n.legendShape : this.opts.extra.tooltip.legendShape }]
            var h = { x: i.chartData.calPoints[o].x, y: a.y }
            i.tooltip = { textList: e.textList ? e.textList : s, offset: e.offset !== undefined ? e.offset : h, option: e, index: o }
        }
        drawCharts.call(this, i.type, i, this.config, this.context)
    }
    if (this.opts.type === "bar") {
        var r = this.getCurrentDataIndex(t)
        var o = e.index == undefined ? r.index : e.index
        if (o > -1 || o.length > 0) {
            var n = getSeriesDataItem(this.opts.series, o, r.group)
            if (n.length !== 0) {
                var l = getToolTipData(n, this.opts, o, r.group, this.opts.categories, e),
                    s = l.textList,
                    h = l.offset
                h.x = a.x
                i.tooltip = { textList: e.textList !== undefined ? e.textList : s, offset: e.offset !== undefined ? e.offset : h, option: e, index: o }
            }
        }
        drawCharts.call(this, i.type, i, this.config, this.context)
    }
    if (this.opts.type === "mix") {
        var r = this.getCurrentDataIndex(t)
        var o = e.index == undefined ? r.index : e.index
        if (o > -1) {
            var d = this.scrollOption.currentOffset
            var i = assign({}, this.opts, { _scrollDistance_: d, animation: false })
            var n = getSeriesDataItem(this.opts.series, o)
            if (n.length !== 0) {
                var x = getMixToolTipData(n, this.opts, o, this.opts.categories, e),
                    s = x.textList,
                    h = x.offset
                h.y = a.y
                i.tooltip = { textList: e.textList ? e.textList : s, offset: e.offset !== undefined ? e.offset : h, option: e, index: o }
            }
        }
        drawCharts.call(this, i.type, i, this.config, this.context)
    }
    if (this.opts.type === "candle") {
        var r = this.getCurrentDataIndex(t)
        var o = e.index == undefined ? r.index : e.index
        if (o > -1) {
            var d = this.scrollOption.currentOffset
            var i = assign({}, this.opts, { _scrollDistance_: d, animation: false })
            var n = getSeriesDataItem(this.opts.series, o)
            if (n.length !== 0) {
                var l = getCandleToolTipData(this.opts.series[0].data, n, this.opts, o, this.opts.categories, this.opts.extra.candle, e),
                    s = l.textList,
                    h = l.offset
                h.y = a.y
                i.tooltip = { textList: e.textList ? e.textList : s, offset: e.offset !== undefined ? e.offset : h, option: e, index: o }
            }
        }
        drawCharts.call(this, i.type, i, this.config, this.context)
    }
    if (this.opts.type === "pie" || this.opts.type === "ring" || this.opts.type === "rose" || this.opts.type === "funnel") {
        var o = e.index == undefined ? this.getCurrentDataIndex(t) : e.index
        if (o > -1) {
            var i = assign({}, this.opts, { animation: false })
            var n = assign({}, i._series_[o])
            var s = [{ text: e.formatter ? e.formatter(n, undefined, o, i) : n.name + ": " + n.data, color: n.color, legendShape: this.opts.extra.tooltip.legendShape == "auto" ? n.legendShape : this.opts.extra.tooltip.legendShape }]
            var h = { x: a.x, y: a.y }
            i.tooltip = { textList: e.textList ? e.textList : s, offset: e.offset !== undefined ? e.offset : h, option: e, index: o }
        }
        drawCharts.call(this, i.type, i, this.config, this.context)
    }
    if (this.opts.type === "map") {
        var o = e.index == undefined ? this.getCurrentDataIndex(t) : e.index
        if (o > -1) {
            var i = assign({}, this.opts, { animation: false })
            var n = assign({}, this.opts.series[o])
            n.name = n.properties.name
            var s = [{ text: e.formatter ? e.formatter(n, undefined, o, this.opts) : n.name, color: n.color, legendShape: this.opts.extra.tooltip.legendShape == "auto" ? n.legendShape : this.opts.extra.tooltip.legendShape }]
            var h = { x: a.x, y: a.y }
            i.tooltip = { textList: e.textList ? e.textList : s, offset: e.offset !== undefined ? e.offset : h, option: e, index: o }
        }
        i.updateData = false
        drawCharts.call(this, i.type, i, this.config, this.context)
    }
    if (this.opts.type === "word") {
        var o = e.index == undefined ? this.getCurrentDataIndex(t) : e.index
        if (o > -1) {
            var i = assign({}, this.opts, { animation: false })
            var n = assign({}, this.opts.series[o])
            var s = [{ text: e.formatter ? e.formatter(n, undefined, o, this.opts) : n.name, color: n.color, legendShape: this.opts.extra.tooltip.legendShape == "auto" ? n.legendShape : this.opts.extra.tooltip.legendShape }]
            var h = { x: a.x, y: a.y }
            i.tooltip = { textList: e.textList ? e.textList : s, offset: e.offset !== undefined ? e.offset : h, option: e, index: o }
        }
        i.updateData = false
        drawCharts.call(this, i.type, i, this.config, this.context)
    }
    if (this.opts.type === "radar") {
        var o = e.index == undefined ? this.getCurrentDataIndex(t) : e.index
        if (o > -1) {
            var i = assign({}, this.opts, { animation: false })
            var n = getSeriesDataItem(this.opts.series, o)
            if (n.length !== 0) {
                var s = n.map((t) => {
                    return { text: e.formatter ? e.formatter(t, this.opts.categories[o], o, this.opts) : t.name + ": " + t.data, color: t.color, legendShape: this.opts.extra.tooltip.legendShape == "auto" ? t.legendShape : this.opts.extra.tooltip.legendShape }
                })
                var h = { x: a.x, y: a.y }
                i.tooltip = { textList: e.textList ? e.textList : s, offset: e.offset !== undefined ? e.offset : h, option: e, index: o }
            }
        }
        drawCharts.call(this, i.type, i, this.config, this.context)
    }
}
uCharts.prototype.translate = function (t) {
    this.scrollOption = { currentOffset: t, startTouchX: t, distance: 0, lastMoveTime: 0 }
    let e = assign({}, this.opts, { _scrollDistance_: t, animation: false })
    drawCharts.call(this, this.opts.type, e, this.config, this.context)
}
uCharts.prototype.scrollStart = function (t) {
    var e = null
    if (t.changedTouches) {
        e = t.changedTouches[0]
    } else {
        e = t.mp.changedTouches[0]
    }
    var a = getTouches(e, this.opts, t)
    if (e && this.opts.enableScroll === true) {
        this.scrollOption.startTouchX = a.x
    }
}
uCharts.prototype.scroll = function (t) {
    if (this.scrollOption.lastMoveTime === 0) {
        this.scrollOption.lastMoveTime = Date.now()
    }
    let e = this.opts.touchMoveLimit || 60
    let a = Date.now()
    let i = a - this.scrollOption.lastMoveTime
    if (i < Math.floor(1e3 / e)) return
    if (this.scrollOption.startTouchX == 0) return
    this.scrollOption.lastMoveTime = a
    var r = null
    if (t.changedTouches) {
        r = t.changedTouches[0]
    } else {
        r = t.mp.changedTouches[0]
    }
    if (r && this.opts.enableScroll === true) {
        var o = getTouches(r, this.opts, t)
        var n
        n = o.x - this.scrollOption.startTouchX
        var l = this.scrollOption.currentOffset
        var s = calValidDistance(this, l + n, this.opts.chartData, this.config, this.opts)
        this.scrollOption.distance = n = s - l
        var h = assign({}, this.opts, { _scrollDistance_: l + n, animation: false })
        this.opts = h
        drawCharts.call(this, h.type, h, this.config, this.context)
        return l + n
    }
}
uCharts.prototype.scrollEnd = function (t) {
    if (this.opts.enableScroll === true) {
        var e = this.scrollOption,
            a = e.currentOffset,
            i = e.distance
        this.scrollOption.currentOffset = a + i
        this.scrollOption.distance = 0
        this.scrollOption.moveCount = 0
    }
}
export default uCharts
