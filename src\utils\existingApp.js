// 已有的应用筛选
const appShow = ["officialDocs","smartCard", "parentsReleaseQRCode", "collectTable", "sanctions", "antiBullying", "attendance", "campusStyle", "clubManage", "dormManage", "evalActivity", "Intelligence", "leaveSchool", "moralEducationEvaluation", "myStudent", "notice", "patrol", "punchTheClock", "registration", "schedule", "schoolAssignment", "schoolTable", "scoreManage", "siteBooking", "studentAttendance", "teacherAttendance", "traffic", "videoAlbum", "visitorSystem", "weeklyPublication", "library", "canteenMachine", "oaApprove", "vote", "allotRoom", "dormTransfer", "dormRanking", "dormAttendance", "dormTraffic", "todo"]

// 宿管写死的应用："allotRoom", "dormTransfer", "dormRanking", "dormAttendance", "dormTraffic"

function existingApp(apps) {
    if (apps && apps.length > 0) {
        const flag = apps.find((i) => appShow.includes(i.routePath))
        return flag ? true : false
    }
    return false
}

export { existingApp, appShow }
