import dayjs from "dayjs"
import weekOfYear from "dayjs/plugin/weekOfYear"
dayjs.extend(weekOfYear)

/**
 * 获取当前日期  开始结束时间
 * @param {*} dateString
 * @returns
 */
// export function getWeekOfMonth(dateString = '2023-12-31') {
export function getWeekOfMonth(dateString = dayjs()) {
    const date = dayjs(dateString)
    const startOfWeek = date.startOf("week").format("YYYY-MM-DD")
    const endOfWeek = date.endOf("week").format("YYYY-MM-DD")
    const weekOfMonth = date.week() - date.startOf("month").week() + 1 > 0 ? date.week() - date.startOf("month").week() + 1 : 1
    return {
        weekOfMonth,
        startOfWeek,
        endOfWeek
    }
}

/**
 * 获取指定周期的开始和结束日期
 * @param {*} weekNumber 第几周
 * @param {*} year 年份
 * @param {*} month 月份
 * @returns
 */
export function getNthWeekDates(weekNumber, year = dayjs().year(), month = dayjs().month() + 1) {
    // 创建指定年份和月份的 Date 对象
    var date = new Date(year, month - 1, 1) // 月份从 0 开始，所以要减去 1

    // 获取该月份的第一天是星期几（0-6，0 表示星期日）
    var firstDay = date.getDay()

    // 计算第一周的开始日期
    var startDate = new Date(year, month - 1, 1 - firstDay)

    // 计算第 n 周的开始日期和结束日期
    var weekStart = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate() + 7 * (weekNumber - 1) + 1)
    var weekEnd = new Date(weekStart.getFullYear(), weekStart.getMonth(), weekStart.getDate() + 6)

    // 返回结果
    return {
        weekStart: dayjs(weekStart).format("YYYY-MM-DD"),
        weekEnd: dayjs(weekEnd).format("YYYY-MM-DD")
    }
}

// 获取指定月份周期
export function getMonthWeek(dateString = dayjs()) {
    const lastDate = dayjs(dateString).date(dayjs(dateString).daysInMonth())
    const { weekOfMonth } = getWeekOfMonth(lastDate)
    return weekOfMonth
}

const capitals = {
    0: "一",
    1: "二",
    2: "三",
    3: "四",
    4: "五",
    5: "六"
}

/**
 * 获取指定月份的周期积合
 * @param {*} dateString
 * @returns
 */
export function getMonthWeekList(dateString = dayjs()) {
    const num = getMonthWeek(dateString) + 1
    let weekList = []
    const year = dayjs(dateString).year()
    const month = dayjs(dateString).month() + 1
    for (let i = 1; i < num; i++) {
        weekList.push(getNthWeekDates(i, year, month))
    }

    weekList = weekList.map((i, index) => ({
        value: index + 1,
        name: `第${capitals[index]}周`,
        startDate: i.weekStart,
        endDate: i.weekEnd
    }))
    return weekList
}

/**
 * 获取月区间列表
 * @returns
 */
export function getMonthSAndE() {
    return [...Array(12)].map((_, index) => ({
        name: `${index + 1}月`,
        value: index + 1,
        startDate: dayjs().month(index).startOf("month").format("YYYY-MM-DD"),
        endDate: dayjs().month(index).endOf("month").format("YYYY-MM-DD")
    }))
}

/**
 * 数字转成汉字
 * @params num === 要转换的数字
 * @return 汉字
 * */
export function toChinesNum(num) {
    let changeNum = ["零", "一", "二", "三", "四", "五", "六", "七", "八", "九"]
    let unit = ["", "十", "百", "千", "万"]
    num = parseInt(num)
    let getWan = (temp) => {
        let strArr = temp.toString().split("").reverse()
        let newNum = ""
        let newArr = []
        strArr.forEach((item, index) => {
            newArr.unshift(item === "0" ? changeNum[item] : changeNum[item] + unit[index])
        })
        let numArr = []
        newArr.forEach((m, n) => {
            if (m !== "零") numArr.push(n)
        })
        if (newArr.length > 1) {
            newArr.forEach((m, n) => {
                if (newArr[newArr.length - 1] === "零") {
                    if (n <= numArr[numArr.length - 1]) {
                        newNum += m
                    }
                } else {
                    newNum += m
                }
            })
        } else {
            newNum = newArr[0]
        }

        return newNum
    }
    let overWan = Math.floor(num / 10000)
    let noWan = num % 10000
    if (noWan.toString().length < 4) {
        noWan = "0" + noWan
    }
    return overWan ? getWan(overWan) + "万" + getWan(noWan) : getWan(num)
}
