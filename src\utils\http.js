import { getToken } from "@/utils/storageToken.js"
// #ifdef H5-WEIXIN || H5
import { EventSourceParserStream } from "eventsource-parser/stream"
// #endif
const host = import.meta.env.VITE_BASE_API
const myToken = () => {
    let token = getToken()
    if (token && token.indexOf("Bearer") == -1) {
        token = "Bearer " + token
    }
    return token
}
const toast = {
    start: () => {
        uni.showToast({
            title: "加载中..",
            duration: 10000,
            icon: "loading"
        })
    },
    error: (msg) => {
        uni.showToast({
            title: msg,
            icon: "none",
            duration: 2000
        })
    },
    end: () => {
        uni.hideToast()
    }
}

// 需要跳转到登录页的code
const toLoginCode = [401, 403, 1001001001, 1002002012, 1002002013, 1002002014, 1002002024, 1002017013]

// 清空跳转到登录页面
const errorHandle = (message) => {
    toast.error(message)
    setTimeout(() => {
        uni.clearStorageSync()
        uni.reLaunch({ url: "/pages/login/index" })
    }, 1000)
}

function get(url, data) {
    return new Promise((resolve, reject) => {
        uni.request({
            url: host + url,
            data,
            header: {
                Authorization: myToken()
            },
            success: (res) => {
                const resdata = res.data
                if (resdata.status == 404) {
                    toast.error("服务器异常")
                    reject(resdata)
                } else if (resdata.code == 0) {
                    resolve(resdata)
                } else if (toLoginCode.includes(resdata?.code)) {
                    errorHandle(resdata?.message)
                    reject(resdata)
                } else if (resdata?.code == 1003001002) {
                    // #ifdef MP-WEIXIN
                    uni.showModal({
                        content: resdata.message,
                        showCancel: false
                    })
                    // #endif
                    // #ifdef H5 || APP-PLUS
                    toast.error(resdata.message)
                    // #endif
                    reject(resdata)
                } else {
                    toast.error(resdata.message)
                    reject(resdata)
                }
            },
            fail: (error) => {
                toast.error(error.errMsg)
                reject(error)
            }
        })
    })
}
function post(url, data, option = {}) {
    let header = {
        Authorization: myToken(),
        ...option
    }
    return new Promise((resolve, reject) => {
        uni.request({
            url: host + url,
            method: "POST",
            data,
            header,
            success: (res) => {
                const resdata = res.data
                if (resdata.code == 0) {
                    resolve(resdata)
                } else if (toLoginCode.includes(resdata?.code)) {
                    errorHandle(resdata?.message)
                    reject(resdata)
                } else {
                    toast.error(resdata.message)
                    reject(resdata)
                }
            },
            fail: (error) => {
                console.log(error)
                toast.error(error.errMsg)
                reject(error)
            }
        })
    })
}

function uploadFile(url, path, data, file) {
    return new Promise((resolve, reject) => {
        const obj = {}
        if (path) {
            obj.filePath = path
        } else if (file) {
            obj.file = file
        }
        uni.uploadFile({
            header: {
                Authorization: myToken()
            },
            url: host + url,
            ...obj,
            formData: data,
            name: "file",
            success: (res) => {
                const r = JSON.parse(res.data)
                console.log(r)
                if (r.code == 0) {
                    resolve(r.data[0].url)
                } else {
                    uni.showToast({
                        title: r.data?.detailMessage || r.data?.message || r.message,
                        icon: "none",
                        duration: 2000
                    })
                    reject(r.data)
                }
            },
            fail: (error) => {
                console.log(error)
                toast.error(error.errMsg)
                reject(error)
            },
            complete: () => {
                uni.hideLoading()
            }
        })
    })
}

// 小程序专用 - 将临时文件路径转为File对象
function tempFilePathToFile(url, tempFilePath, fileName) {
    return new Promise((resolve, reject) => {
        uni.getFileInfo({
            filePath: tempFilePath,
            success: (fileInfo) => {
                uni.uploadFile({
                    header: {
                        Authorization: myToken()
                    },
                    url: host + url, // 替换为实际接收文件的URL
                    filePath: tempFilePath,
                    name: "file",
                    formData: {
                        name: fileName
                    },
                    success: (uploadRes) => {
                        // 这里根据你的后端接口返回构造File对象
                        const file = new File([], fileName, { type: "image/png" })
                        resolve(file)
                    },
                    fail: (err) => {
                        reject(err)
                    }
                })
            },
            fail: (err) => {
                reject(err)
            }
        })
    })
}
function _uploadFile(url, path, data, file) {
    return new Promise((resolve, reject) => {
        const obj = {}
        if (path) {
            obj.filePath = path
        } else if (file) {
            obj.file = file
        }
        uni.uploadFile({
            header: {
                Authorization: myToken()
            },
            url: host + url,
            ...obj,
            formData: data,
            name: "file",
            success: (res) => {
                const r = JSON.parse(res.data)
                console.log(r)
                if (r.code == 0) {
                    resolve(r)
                } else {
                    uni.showToast({
                        title: r.data?.detailMessage || r.data?.message || r.message,
                        icon: "none",
                        duration: 2000
                    })
                    reject(r.data)
                }
            },
            fail: (error) => {
                console.log(error)
                toast.error(error.errMsg)
                reject(error)
            }
        })
    })
}
// 下载 导出
function exportInfo(url, method = "POST", data, header = {}, fileName) {
    return new Promise((resolve, reject) => {
        // #ifdef MP-WEIXIN
        // 第一步：下载文件
        wx.downloadFile({
            url: host + url,
            header: {
                Authorization: myToken(),
                ...header
            },
            responseType: "arraybuffer", // 关键：声明需要二进制数据
            success: (res) => {
                console.log(res,'111111111111111下载')
                if (res.statusCode === 200) {
                    uni.saveFile({
                        tempFilePath: res.tempFilePath,
                        success(savedRes) {
                            console.log("文件已保存至:", savedRes.savedFilePath)
                            resolve(savedRes)
                        },
                        fail(saveError) {
                            toast.error("保存文件失败")
                            reject(saveError)
                        }
                    })
                } else {
                    reject(res)
                }
            },
            fail(downloadError) {
                console.log(downloadError,'下载失败了')

                reject(downloadError)
            }
        })
        // #endif

        // #ifndef MP-WEIXIN
        uni.request({
            url: host + url,
            method,
            header: {
                Authorization: myToken(),
                ...header
            },
            data,
            responseType: "arraybuffer", // 关键：声明需要二进制数据
            success(res) {
                debugger
                // 创建 Blob 对象（注意：uni-app 中需用 uni.downloadFile 或兼容方案）
                const blob = new Blob([res.data], {
                    type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                })
                const url = window.URL.createObjectURL(blob)
                const a = document.createElement("a")
                // 将链接地址字符内容转变成blob地址
                a.href = url
                a.download = fileName // 下载文件的名字
                document.body.appendChild(a)
                a.click()
            },
            fail: (error) => {
                console.log(error)
                toast.error(error.errMsg)
                reject(error)
            }
        })
        // #endif
    })
}
// 无需token等参数的API
function _fetch(url, data, method = "POST", header = {}) {
    return new Promise((resolve, reject) => {
        uni.request({
            url: host + url,
            data,
            method,
            header,
            success(res) {
                if (res.data.code == 0) {
                    resolve(res.data)
                } else {
                    toast.error(res.data.message)
                    reject(res.data)
                }
            },
            fail: (error) => {
                console.log(error)
                toast.error(error.errMsg)
                reject(error)
            }
        })
    })
}
let controller = null // 存储当前控制器
// 终止请求
function cancelRequest() {
    if (controller) {
        controller.abort()
        controller = null
        console.log("已终止请求")
    }
}
async function fetchSSE(url, data, onMessage) {
    // 终止之前的请求（如果存在）
    if (controller) {
        controller.abort()
    }
    // 创建新控制器
    controller = new AbortController()
    const response = await fetch(`${host}${url}`, {
        method: "POST",
        body: JSON.stringify(data),
        headers: {
            Authorization: myToken(),
            "Content-Type": "application/json"
        },
        signal: controller.signal // 绑定中止信号
    })
    let reader = null
    const res = response.clone().json()
    // #ifdef H5-WEIXIN || H5
    reader = response?.body?.pipeThrough(new TextDecoderStream()).pipeThrough(new EventSourceParserStream()).getReader()
    // #endif
    // #ifdef MP-WEIXIN
    reader = response?.body?.pipeThrough(new TextDecoderStream()).pipeThrough().getReader()
    // #endif
    while (true) {
        const x = await reader?.read()
        if (x) {
            const { done, value } = x
            try {
                const val = value?.data ? JSON.parse(value.data) : ""
                if (!["ping"].includes(val?.event)) {
                    onMessage({
                        ...val
                    })
                }
            } catch (e) {
                console.warn(e)
            }
            if (done) {
                console.info("done")
                break
            }
        }
    }
}

export default { get, post, uploadFile, _uploadFile, _fetch, fetchSSE, cancelRequest, exportInfo, tempFilePathToFile }
