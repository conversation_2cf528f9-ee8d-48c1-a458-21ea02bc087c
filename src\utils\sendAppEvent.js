// #ifdef H5 || H5-WEIXIN
import * as dd from "dingtalk-jsapi"
// #endif
import { setToken } from "@/utils/storageToken.js"
import useStore from "@/store"

// ios滚动条高度
function IOSHeightFn() {
    // #ifdef H5 || H5-WEIXIN
    ;/iphone|ipod|ipad/i.test(navigator.userAgent) &&
        document.addEventListener(
            "blur",
            (event) => {
                // 当页面没出现滚动条时才执行，因为有滚动条时，不会出现这问题
                // input textarea 标签才执行，因为 a 等标签也会触发 blur 事件
                if (document.documentElement.offsetHeight <= document.documentElement.clientHeight && ["input", "textarea"].includes(event.target.localName)) {
                    document.body.scrollIntoView() // 回顶部
                }
            },
            true
        )
    // #endif
}

// 判断当前在哪个环境下
export function checkPlatform() {
    // #ifdef H5 || H5-WEIXIN
    IOSHeightFn()
    const ua = navigator.userAgent.toLowerCase()
    if (dd.env.platform !== "notInDingTalk") {
        console.log("当前环境 ——— 钉钉环境")
        return "dingding"
    }
    if (ua.match(/wxwork/i) == "wxwork") {
        console.warn("当前环境 ——— 企业微信环境")
        return "wxwork"
    }
    if (ua.match(/MicroMessenger/i) == "micromessenger") {
        console.warn("当前环境 ——— 微信浏览器环境")
        return "wx-miniprogram"
    }
    if (navigator.userAgent.includes("yide-ios-app")) {
        console.warn("当前环境 ——— IOSApp")
        return "yide-ios-app"
    }
    if (navigator.userAgent.includes("yide-android-app")) {
        console.warn("当前环境 ——— AndroidApp")
        return "yide-android-app"
    }
    if (navigator.userAgent.includes("yide-Harmony-app")) {
        console.warn("当前环境 ——— HarmonyApp------checkPlatform")
        alert("当前环境 ——— HarmonyApp----checkPlatform")
        return "yide-Harmony-app"
    }

    console.warn("当前环境 ——— 普通浏览器")
    return "browser"
    // #endif

    // #ifdef MP-WEIXIN
    console.log("当前环境 ——— 微信小程序")
    return "mp-weixin"
    // #endif
}

// 给IOS和Android发送事件
export const sendAppEvent = (enentName, params = {}) => {
    // 调用原生App事件
    console.log(checkPlatform())

    switch (checkPlatform()) {
        case "wx-miniprogram":
            document.addEventListener("UniAppJSBridgeReady", () => {
                //传递的消息信息，必须写在 data 对象中。
                uni.postMessage({
                    enentName,
                    data: params
                })
            })
        case "yide-android-app":
            // 处理Android逻辑
            return (
                window.android &&
                window.android.postMessage &&
                window.android.postMessage(
                    JSON.stringify({
                        enentName,
                        params
                    })
                )
            )
        case "yide-ios-app":
            // 处理IOS逻辑
            return window.webkit?.messageHandlers.call.postMessage(
                JSON.stringify({
                    enentName,
                    params
                })
            )
        case "yide-Harmony-app":
            // 处理Harmony逻辑
            alert("当前环境 ——— HarmonyApp---sendAppEvent")
            // HarmonyOS 使用 window.NativeApp 调用原生方法
            if (!window.NativeApp) {
                uni.showToast({
                    title: `${enentName}-404`,
                    duration: 10000
                })
                console.error("NativeApp interface not available")
                return null
            }
            if (enentName === "getToken") {
                try {
                    const token = window.NativeApp.getToken() // 同步调用
                    return token
                } catch (error) {
                    console.error("Error calling getToken:", error)
                    return null
                }
            } else if (enentName === "backApp") {
                try {
                    window.NativeApp.backApp()
                    return true
                } catch (error) {
                    console.error("Error calling backApp:", error)
                    return false
                }
            } else if (enentName === "getUserInfo") {
                try {
                    const getUserInfo = window.NativeApp.getUserInfo() // 同步调用
                    return getUserInfo
                } catch (error) {
                    console.error("Error calling backApp:", error)
                    return null
                }
            } else {
                uni.showToast({
                    title: `${enentName}-nullApp`,
                    duration: 10000
                })
                console.error("Unsupported eventName:", enentName)
                return null
            }
        default:
            break
    }
}
// 存储IOS-App给的数据
async function getIosData() {
    const token = window.prompt("getToken")
    const newToken = token.replace("Bearer ", "")
    await setToken(newToken)
    const userInfo = window.prompt("getUserInfo")
    if (userInfo) {
        console.warn(JSON.parse(userInfo), newToken, "IosData")
        const { identityInfo, schoolId } = JSON.parse(userInfo)
        await getSchoolFn(schoolId)
        await selectRole(JSON.parse(identityInfo), schoolId)
    }
}

// 存储Android-App给的数据
async function getAndroidData() {
    const token = sendAppEvent("getToken", {})
    const newToken = token.replace("Bearer ", "")
    await setToken(newToken)
    const userInfo = sendAppEvent("getUserInfo", {})
    if (userInfo) {
        console.warn(JSON.parse(userInfo), newToken, "AndroidData")
        const { identityInfo, schoolId } = JSON.parse(userInfo)
        await getSchoolFn(schoolId)
        await selectRole(JSON.parse(identityInfo), schoolId)
    }
}
// 存储Harmony-App给的数据
async function getHarmonyData() {
    const token = sendAppEvent("getToken", {})
    const newToken = token.replace("Bearer ", "")
    await setToken(newToken)
    const userInfo = sendAppEvent("getUserInfo", {})
    if (userInfo) {
        const { identityInfo, schoolId } = JSON.parse(userInfo)
        console.warn(JSON.parse(userInfo), newToken, "HarmonyData")
        await getSchoolFn(schoolId)
        await selectRole(JSON.parse(identityInfo), schoolId)
    }
}
export async function browserEnvironment() {
    console.log("调用APP的用户信息缓存______________________________________________________________")
    switch (checkPlatform()) {
        case "yide-android-app":
            // 获取Android数据
            await getAndroidData()

        case "yide-ios-app":
            // 获取IOS数据
            await getIosData()

        case "yide-Harmony-app":
            // 获取Harmony数据
            // alert("当前环境 ——— HarmonyApp=----browserEnvironment")
            await getHarmonyData()

        default:
            false
            break
    }
}

// 获取当前学校类型 K12/大学
const getSchoolType = async () => {
    const { user } = useStore()
    await http
        .post("/app/school/template/getCommentKey", { commentKey: ["schoolType"] })
        .then((res) => {
            // 设置学校类型,schoolType: 1是K12，2是大学
            const info = { ...user.schoolInfo, schoolType: res.data.schoolType == 2 ? "university" : "K12" }
            user.setSchoolInfo(info)
        })
        .catch(() => {
            user.setSchoolInfo({ ...user.schoolInfo, schoolType: "K12" })
        })
}

// 任课老师角色下管理的班级
function queryClassTeachList() {
    const { user } = useStore()
    http.get("/cloud/v3/classes/queryClassTeachList").then((res) => {
        user.setClassTeachList(res.data)
    })
}

// 班主任角色下管理的班级
function queryClassMaterList() {
    const { user } = useStore()
    http.get("/app/master/class/queryClassMaterList").then((res) => {
        user.setClassMaterList(res.data)
    })
}

// 获取学生
const getYourChildrenInfo = async () => {
    const { user } = useStore()
    await http.get("/app/student/getStudentList").then((res) => {
        const arr = res.data?.map((i) => {
            return {
                studentName: i.name,
                studentId: i.id
            }
        })
        user.setStudentInfo(arr)
    })
}

const roleCodeType = (roleCode) => {
    console.log(roleCode, "roleCoderoleCoderoleCode")
    if (roleCode == "eltern") {
        return "eltern"
    } else if (roleCode == "student") {
        return "student"
    } else if (roleCode == "dorm_admin") {
        return "dorm_admin"
    } else {
        return "ordinaryTeacher"
    }
}

const identityType = (identity) => {
    if (identity == "eltern") {
        return 2
    } else if (identity == "student") {
        return 0
    } else {
        return 1
    }
}

// 选择角色
async function selectRole(item, schoolId) {
    if (item) {
        const { user, home, system } = useStore()
        await http.post("/app/mobile/user/login", { schoolId, id: item.id, roleCode: item.roleCode }).then(async (res) => {
            const userInfo = {
                ...res.data,
                roleCode: roleCodeType(res.data?.roleCode),
                identity: identityType(res.data?.identity)
            }
            const identityInfo = {
                ...item,
                roleCode: roleCodeType(item?.roleCode),
                identity: identityType(item?.identity)
            }
            user.setUserInfo(userInfo)
            user.setIdentityInfo(identityInfo)
            // 如果选择了家长的角色 调用接口吧孩子都查出来 然后存入store
            item.roleCode == "eltern" ? await getYourChildrenInfo() : user.setStudentInfo([])
            // 获取任课老师和班主任任教的班级
            if (item.roleCode == "headmaster") {
                queryClassMaterList()
            } else if (item.roleCode == "teacher") {
                queryClassTeachList()
            }
            // 设置学校类型 K12/大学
            getSchoolType()
            // 获取首页快捷入口
            home.queryQuickList()
            // #ifdef H5 || MP-WEIXIN
            if (user.identityInfo.roleCode === "dorm_admin") {
                system.setPrimaryColor("#4566d5")
            } else {
                system.setPrimaryColor("#00B781")
            }
            // #endif
            // #ifdef H5 || MP-WEIXIN
            if (user.identityInfo.roleCode === "dorm_admin") {
                system.setPrimaryColor("#4566d5")
            } else {
                system.setPrimaryColor("#00b781")
            }
            // #endif
        })
    }
}

async function getSchoolFn(schoolId) {
    const { user } = useStore()
    await http.get("/app/mobile/user/school").then((res) => {
        if (res.data && res.data.length > 0) {
            console.log(schoolId)
            const obj = res.data.find((i) => i.id === schoolId)
            // 默认选择第一个学校
            user.setSchoolInfo(obj)
        }
    })
}
