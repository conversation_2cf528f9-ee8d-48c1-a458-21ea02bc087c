//  获取token
export const getToken = () => {
    let item = uni.getStorageSync("yide-mobile-token")
    try {
        item = item && JSON.parse(item)
    } catch (error) {
        console.log(error)
    }
    return item?.token
}

// 设置token
export const setToken = (token) => {
    uni.setStorageSync("yide-mobile-token", JSON.stringify({ token }))
}

// 删除token
export const removeToken = () => {
    uni.removeStorageSync("yide-mobile-token")
}
