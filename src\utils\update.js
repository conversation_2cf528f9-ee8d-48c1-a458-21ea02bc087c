//TODO: 更新升级逻辑
import { debounce } from "@/utils"
const isDev = process.env.NODE_ENV === "development"

function wxUpdate() {
    if (wx.canIUse("getUpdateManager")) {
        const updateManager = uni.getUpdateManager()
        updateManager.onCheckForUpdate(function (res) {
            // console.log(res, "是否有新版本")
            if (res.hasUpdate) {
                updateManager.onUpdateReady(function () {
                    uni.showModal({
                        title: "更新提示",
                        content: "新版本已经准备好，是否重启应用？",
                        success: function (res) {
                            if (res.confirm) {
                                updateManager.applyUpdate()
                            }
                        }
                    })
                })
                updateManager.onUpdateFailed(function () {
                    uni.showModal({
                        title: "已经有新版本了哟~",
                        content: "新版本已经上线，请您删除当前小程序，重新搜索打开"
                    })
                })
            }
        })
    } else {
        uni.showModal({
            title: "提示",
            content: "当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试。"
        })
    }
}

function h5Update() {
    const listenFn = debounce(function (e) {
        uni.request({
            url: `/version.json?v=${+new Date()}`,
            data: {},
            method: "GET",
            headers: {
                "Cache-Control": "no-cache"
            },
            success: (res) => {
                const clientVersion = document.querySelector("meta[name*='version']")
                if (clientVersion) {
                    const isEquation = res.data?.version === Number(clientVersion.content || "")
                    if (isEquation) {
                        uni.showModal({
                            title: "温馨提示!",
                            content: `检测到最新版本，刷新后立即使用`,
                            success: function (ev) {
                                e.stopPropagation()
                                if (ev.confirm) {
                                    window.location.reload()
                                } else if (ev.cancel) {
                                    // console.log('用户点击取消');
                                    document.removeEventListener("click", listenFn, false)
                                }
                            }
                        })
                    }
                } else {
                    //
                    document.removeEventListener("click", listenFn, false)
                }
            }
        })
    }, 5000)
    document.addEventListener("click", listenFn)
}

function appUpdate() {}

export default () => {
    if (isDev) return
    // #ifdef MP-WEIXIN
    // wxUpdate()
    // #endif

    //  #ifdef H5
    // h5Update()
    //  #endif

    //  #ifdef APP-PLUS
    // appUpdate()
    //  #endif
}
