import http from "@/utils/http.js"

// 微信相关工具函数
export function getOpenId() {
    return uni.getStorageSync("wechat-openid")
}

export function setOpenId(openid) {
    uni.setStorageSync("wechat-openid", openid)
}

export function removeOpenId() {
    uni.removeStorageSync("wechat-openid")
}
/**
 * 检测是否在微信内置浏览器中
 * @returns {boolean} 是否在微信浏览器中
 */
export function isWechatBrowser() {
    // #ifdef H5
    const ua = navigator.userAgent.toLowerCase()
    return ua.includes("micromessenger")
    // #endif

    // #ifndef H5
    return false
    // #endif
}

// 获取浏览器参数
export const getUrlParams = (isShare, _url) => {
    const params = {}
    const url = _url || window.location.search || window.location.hash
    if (url.indexOf("?") > -1) {
        const str = url.split("?")[1]
        const arr = str.split("&")
        arr.forEach((item) => {
            const [key, value] = item.split("=")
            params[key] = value
        })
        return params
    } else {
        return params
    }
}

/**
 * 微信公众号授权
 * @param {string} appId 微信公众号appId
 * @param {string} scope 授权作用域，默认为snsapi_base
 * @param {string} toUrl web-app 中的文件名受权页面
 */
export function wechatAuth(appId, scope = "snsapi_base", toUrl) {
    debugger
    if (!isWechatBrowser()) {
        console.warn("不在微信浏览器环境中，无法进行微信授权")
        uni.showToast({
            title: "必须使用微信app扫报到二维码进行微信支付!",
            icon: "error",
            duration: 2000
        })
        return
    }

    // 检查是否已经有openid
    const existingOpenId = getOpenId()
    if (existingOpenId) {
        console.log("已存在openid:", existingOpenId)
        return Promise.resolve(existingOpenId)
    }
    const urlParams = getUrlParams()

    if (urlParams.code) {
        console.log("已存在微信code:", urlParams.code)
        return exchangeCodeForOpenId(urlParams.code)
    }

    // 这里是去正式环境的公众号项目的地址 目的就是让mcloud.yyide.com这个人帮我带个code回来
    // 去的时候告诉你要回来我这里 back_url
    debugger
    navigateTo({
        url: `/apps/smartCard/payAuth/index`,
        query: {
            back_url: encodeURIComponent(window.location.origin)
        }
    })
    // window.location.href = `https://local-h5.1yide.com/#/${toUrl}?back_url=${encodeURIComponent(window.location.origin)}`
    // window.location.href = `https://mcloud.yyide.com/#/${toUrl}?back_url=${encodeURIComponent(window.location.origin)}`
}

/**
 * 用code换取openid
 * @param {string} code 微信授权code
 * @returns {Promise<string>} openid
 */
export function exchangeCodeForOpenId(code) {
    return new Promise((resolve, reject) => {
        // 这里需要调用后端接口，用code换取openid
        // 因为access_token和openid的获取需要appSecret，不能在前端进行
        http.get("/campuspay/callback/wechat/gzh/getAccessToken", {
            code: code
        }).then((res) => {
            // 把openid存起来
            setOpenId(res.data.openid)
            resolve(res.data.openid)
        })
    })
}

/**
 * 初始化微信授权
 * 在应用启动时调用
 */
export function initWechatAuth() {
    // #ifdef H5
    if (!isWechatBrowser()) {
        uni.showToast({
            title: "必须使用微信app扫报到二维码进行微信支付!",
            icon: "error",
            duration: 2000
        })
        return Promise.reject(new Error("不在微信浏览器环境中"))
    }

    // 检查是否已经有openid
    const existingOpenId = getOpenId()
    if (existingOpenId) {
        console.log("已存在openid:", existingOpenId)
        return Promise.resolve(existingOpenId)
    }

    // 检查URL中是否有code参数
    const urlParams = getUrlParams()
    const code = urlParams.code
    if (code) {
        // 如果有code，用code换取openid
        return exchangeCodeForOpenId(code)
    }

    // 如果没有openid也没有code，需要进行授权
    // 这里需要从后端获取appId和当前页面URL作为回调地址
    // const { appId } = res.data.data;

    wechatAuth("wx8936b0f9a7bbbfcf")
    // #endif

    // #ifndef H5
    return Promise.reject(new Error("非H5环境"))
    // #endif
}
// h5支付
export function payH5(params = {}) {
    const { VITE_APP_NAME } = import.meta.env
    const hash = window.location.hash
    const { tradeNo, payMethodId, resultInfo } = params
    let _redirect_url = ""
    if (["development", "uat"].includes(VITE_APP_NAME)) {
        _redirect_url = `https://local-h5.1yide.com/${hash}&tradeNo=${tradeNo}&payMethodId=${payMethodId}`
    } else if (["uatrelease"].includes(VITE_APP_NAME)) {
        // http://wisdomappuat.1yide.com 预发布
        _redirect_url = `https://wisdomappuat.1yide.com/${hash}&tradeNo=${tradeNo}&payMethodId=${payMethodId}`
    } else {
        // 正式环境
        _redirect_url = `https://mclouds.1yide.com/${hash}&tradeNo=${tradeNo}&payMethodId=${payMethodId}`
    }
    window.location.href = `${resultInfo.h5Url}&redirect_url=${encodeURIComponent(_redirect_url)}`
}
